FROM node:18-alpine AS build

WORKDIR /CARYTOO-UI

COPY package*.json ./

RUN npm install

COPY . .
ARG VITE_API_URL
ENV VITE_API_URL=$VITE_API_URL

RUN npm run build

# Use a multi-stage build for a smaller final image

FROM nginx:alpine

# Copy the custom Nginx config
COPY ./nginx.conf /etc/nginx/conf.d/default.conf

COPY --from=build /CARYTOO-UI/dist /usr/share/nginx/html

EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]