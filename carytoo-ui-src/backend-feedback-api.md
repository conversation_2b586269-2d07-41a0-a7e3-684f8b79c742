# Backend API for Feedback Submission

To handle feedback submissions from the FeedbackPopup component, you'll need to implement the following API endpoint on your backend server.

## Feedback API Endpoint

### POST /api/feedback

This endpoint receives feedback submissions from users.

#### Request Body

```json
{
  "rating": 5,           // Number (1-5)
  "feedback": "Great app, love the UI!", // String (optional)
  "email": "<EMAIL>",  // String (optional)
  "timestamp": "2023-06-15T10:30:00.000Z" // ISO date string
}
```

#### Response

**Success (200 OK)**

```json
{
  "success": true,
  "message": "Feedback submitted successfully",
  "id": "feedback_id_12345" // Optional: ID of the created feedback entry
}
```

**Error (400 Bad Request)**

```json
{
  "success": false,
  "message": "Invalid feedback data",
  "errors": ["Rating is required"]
}
```

**Error (500 Internal Server Error)**

```json
{
  "success": false,
  "message": "Failed to save feedback"
}
```

## Database Schema

Here's a suggested schema for storing feedback in your database:

### Feedback Collection/Table

| Field       | Type     | Description                           |
|-------------|----------|---------------------------------------|
| id          | String   | Unique identifier                     |
| rating      | Number   | Rating value (1-5)                    |
| feedback    | String   | User's feedback text (optional)       |
| email       | String   | User's email (optional)               |
| timestamp   | DateTime | When the feedback was submitted       |
| user_id     | String   | User ID if authenticated (optional)   |
| user_agent  | String   | Browser/device info (optional)        |
| ip_address  | String   | User's IP address (optional)          |
| page_url    | String   | URL where feedback was given (optional)|

## Implementation Example (Node.js/Express)

```javascript
const express = require('express');
const router = express.Router();
const Feedback = require('../models/Feedback'); // Your database model

// POST /api/feedback
router.post('/feedback', async (req, res) => {
  try {
    const { rating, feedback, email, timestamp } = req.body;
    
    // Validate required fields
    if (!rating || rating < 1 || rating > 5) {
      return res.status(400).json({
        success: false,
        message: 'Invalid rating value',
        errors: ['Rating must be between 1 and 5']
      });
    }
    
    // Create new feedback entry
    const newFeedback = new Feedback({
      rating,
      feedback: feedback || '',
      email: email || null,
      timestamp: timestamp || new Date(),
      user_agent: req.headers['user-agent'],
      ip_address: req.ip,
      page_url: req.headers.referer
    });
    
    // If user is authenticated, add user ID
    if (req.user) {
      newFeedback.user_id = req.user.id;
    }
    
    // Save to database
    const savedFeedback = await newFeedback.save();
    
    // Return success response
    return res.status(200).json({
      success: true,
      message: 'Feedback submitted successfully',
      id: savedFeedback._id
    });
    
  } catch (error) {
    console.error('Error saving feedback:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to save feedback'
    });
  }
});

module.exports = router;
```

## Additional Features to Consider

1. **Email Notifications**: Send email notifications to administrators when new feedback is received, especially for low ratings.

2. **Feedback Dashboard**: Create an admin dashboard to view and manage feedback submissions.

3. **Analytics**: Implement analytics to track feedback trends over time.

4. **Rate Limiting**: Implement rate limiting to prevent abuse of the feedback system.

5. **Spam Detection**: Add spam detection to filter out spam submissions.

6. **Sentiment Analysis**: Use natural language processing to analyze feedback sentiment.

7. **Follow-up System**: Implement a system to follow up with users who provided their email address.

8. **Export Functionality**: Allow administrators to export feedback data for further analysis.

## Security Considerations

1. **Input Validation**: Validate all input data to prevent injection attacks.

2. **Rate Limiting**: Implement rate limiting to prevent abuse.

3. **CORS**: Configure CORS properly to prevent cross-site request forgery.

4. **Data Privacy**: Handle user emails and other personal information according to privacy regulations.

5. **Authentication**: Consider requiring authentication for feedback submission to prevent spam.

6. **Logging**: Implement proper logging for security auditing.

7. **Data Encryption**: Encrypt sensitive data in transit and at rest.
