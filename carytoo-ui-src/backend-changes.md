# Backend API Changes for Email Verification

To ensure that users can only log in after verifying their email, the following changes need to be made to the backend API:

## 1. User Model Changes

Add a `verified` field to the User model:

```javascript
const userSchema = new mongoose.Schema({
  // existing fields
  username: { type: String, required: true },
  email: { type: String, required: true, unique: true },
  password: { type: String, required: true },
  phoneNumber: { type: String, required: true },
  // new field
  verified: { type: Boolean, default: false },
  // other fields
});
```

## 2. Registration Endpoint Changes

The registration endpoint should create a user with `verified: false` and send a verification email:

```javascript
// api/auth/register
router.post('/register', async (req, res) => {
  try {
    const { username, email, password, phoneNumber } = req.body;
    
    // Check if user already exists
    const existingUser = await User.findOne({ email });
    if (existingUser) {
      return res.status(400).json({ message: 'User already exists' });
    }
    
    // Hash password
    const hashedPassword = await bcrypt.hash(password, 10);
    
    // Create new user with verified: false
    const newUser = new User({
      username,
      email,
      password: hashedPassword,
      phoneNumber,
      verified: false
    });
    
    await newUser.save();
    
    // Generate OTP for email verification
    const otp = generateOTP(); // 6-digit OTP
    
    // Save OTP to database with expiry
    await OTP.create({
      email,
      otp,
      type: 'register',
      expiresAt: new Date(Date.now() + 10 * 60 * 1000) // 10 minutes
    });
    
    // Send verification email with OTP
    await sendVerificationEmail(email, otp);
    
    res.status(201).json({ 
      message: 'Registration successful! Please verify your email.' 
    });
  } catch (error) {
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});
```

## 3. Email Verification Endpoint Changes

The email verification endpoint should update the user's `verified` status:

```javascript
// api/auth/mail-verify
router.post('/mail-verify', async (req, res) => {
  try {
    const { email, otp } = req.body;
    
    // Find the OTP record
    const otpRecord = await OTP.findOne({ 
      email, 
      otp: parseInt(otp),
      type: 'register',
      expiresAt: { $gt: new Date() }
    });
    
    if (!otpRecord) {
      return res.status(400).json({ message: 'Invalid or expired OTP' });
    }
    
    // Update user's verified status
    const user = await User.findOneAndUpdate(
      { email },
      { verified: true },
      { new: true }
    );
    
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }
    
    // Delete the used OTP
    await OTP.deleteOne({ _id: otpRecord._id });
    
    res.status(200).json({ message: 'Email verified successfully' });
  } catch (error) {
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});
```

## 4. Login Endpoint Changes

The login endpoint should check if the user's email is verified:

```javascript
// api/auth/login
router.post('/login', async (req, res) => {
  try {
    const { email, password } = req.body;
    
    // Find user
    const user = await User.findOne({ email });
    
    if (!user) {
      return res.status(401).json({ message: 'Invalid email or password' });
    }
    
    // Check if email is verified
    if (!user.verified) {
      return res.status(403).json({ 
        message: 'Email not verified. Please verify your email before logging in.',
        needsVerification: true,
        email: email
      });
    }
    
    // Check password
    const isPasswordValid = await bcrypt.compare(password, user.password);
    
    if (!isPasswordValid) {
      return res.status(401).json({ message: 'Invalid email or password' });
    }
    
    // Generate JWT token
    const token = jwt.sign(
      { userId: user._id, email: user.email },
      process.env.JWT_SECRET,
      { expiresIn: '24h' }
    );
    
    res.status(200).json({
      message: 'Login successful',
      token,
      user: {
        id: user._id,
        username: user.username,
        email: user.email,
        role: user.role || 'user'
      }
    });
  } catch (error) {
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});
```

## 5. Resend Verification Email Endpoint

```javascript
// api/auth/resend-verification
router.post('/resend-verification', async (req, res) => {
  try {
    const { email } = req.body;
    
    // Check if user exists and is not verified
    const user = await User.findOne({ email });
    
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }
    
    if (user.verified) {
      return res.status(400).json({ message: 'Email already verified' });
    }
    
    // Delete any existing OTPs for this email
    await OTP.deleteMany({ email, type: 'register' });
    
    // Generate new OTP
    const otp = generateOTP();
    
    // Save OTP to database with expiry
    await OTP.create({
      email,
      otp,
      type: 'register',
      expiresAt: new Date(Date.now() + 10 * 60 * 1000) // 10 minutes
    });
    
    // Send verification email with OTP
    await sendVerificationEmail(email, otp);
    
    res.status(200).json({ message: 'Verification email sent' });
  } catch (error) {
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});
```
