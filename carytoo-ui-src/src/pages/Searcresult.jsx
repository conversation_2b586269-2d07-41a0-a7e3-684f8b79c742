"use client";
import { Box, Container, Typography, useMediaQuery, useTheme, Fade, Slide, Grow } from "@mui/material";
import React, { useEffect, useState } from "react";
import FlightDetailsSection from "../components/Searchresultcard";
import SearchResultSection from "../components/searcresultheader";
import FlightSkeleton from "../components/Searchresultskeleton";
import { useNavigate, useSearchParams } from "react-router-dom";

const Searchresult = () => {
  const [searchParams] = useSearchParams();
  // const navigate = useNavigate();
  const source = searchParams.get("source");
  const destination = searchParams.get("destination");
  const date = searchParams.get("date");
  const mode = searchParams.get("modeOfTransport");
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));
  const navigate = useNavigate();

  const [searchData, setSearchData] = useState(null);
  const [loading, setLoading] = useState(false);

  // Animation states
  const [titleVisible, setTitleVisible] = useState(false);
  const [headerVisible, setHeaderVisible] = useState(false);
  const [resultsVisible, setResultsVisible] = useState(false);

  // Trigger animations on component mount
  useEffect(() => {
    setTitleVisible(true);

    const headerTimer = setTimeout(() => setHeaderVisible(true), 300);
    const resultsTimer = setTimeout(() => setResultsVisible(true), 600);

    return () => {
      clearTimeout(headerTimer);
      clearTimeout(resultsTimer);
    };
  }, []);
  // const [token, setToken] = useState(null);
  // const tokens = localStorage.getItem('authToken')
  const tokens =localStorage.getItem('authToken')
  // console.log('token',token)
  // console.log("API URL:", import.meta.env.VITE_API_URL);
  // useEffect(() => {
  //   if (typeof window !== "undefined") {
  //     const storedToken = localStorage.getItem("authToken");
  //     setToken(storedToken);
  //   }
  // }, []);

  useEffect(() => {
    // if (token) {
    // console.log("token",token)
      fetchSearchResults();
    // }
  }, [searchParams]);

  const fetchSearchResults = async () => {

    if (!source || !destination || !date || !mode) {
      console.error("Missing search parameters");
      return;
    }

    setLoading(true);
    try {
      const response = await fetch(
        `${import.meta.env.VITE_API_URL}/api/journeys/search?source=${(source).toLowerCase()}&destination=${(destination).toLowerCase()}&start_date=${date}&modeOfTransport=${mode}`,
        {
          method: "GET",
          headers: {
            "Authorization":tokens ,
            "Content-Type": "application/json",
          },
        }
      );
      if(response.status === 401){ navigate('/login') }
      if (!response.ok) throw new Error("Failed to fetch search results");

      const data = await response.json();
      // console.log("data", data);
      setSearchData(data);
      setLoading(false);
    } catch (error) {
      console.error("Error fetching search data:", error);
      setSearchData(null);
    } finally {
      setLoading(false);
    }
  };

  // if (loading) {
  //   <Box sx={{display:'flex',justifyContent:'center',alignItems:'center'}}><FlightSkeleton /></Box>
  //   return
  // }

  // if (!searchData) {
  //   return <NoData/>;
  // }

  return (
    <Box className='main-container' sx={{ bgcolor: "#ffffff", width: "100%" }}>
      <Slide direction="down" in={titleVisible} timeout={800}>
        <Typography align="center" variant="h3"sx={{color:'#EFEFEF',fontWeight:'700',fontSize:'40px',lineHeight:'48px',position:'relative',top:'-180px'}} gutterBottom>
          Search Result
        </Typography>
      </Slide>
      <Box className='body-container' sx={{ position: "relative", width: "100%",display:'flex',alignItems:'center',justifyContent:'center',margin:'0px' }}>

        <Container className='container----' maxWidth="lg" sx={{ position: "relative",marginLeft:isMobile?'0px !important':'50px !important',margin:'0px' }}>
          <Fade in={headerVisible} timeout={800}>
            <Box>
              <SearchResultSection Data={{ source, destination, date, mode }} />
            </Box>
          </Fade>

          {loading ? (
            <FlightSkeleton />
          ) : (
            <Grow in={resultsVisible} timeout={1000} style={{ transformOrigin: '0 0 0' }}>
              <Box>
                <FlightDetailsSection SearchData={searchData} TransPort={mode} />
              </Box>
            </Grow>
          )}
        </Container>

      </Box>
    </Box>
  );
};

export default Searchresult;
