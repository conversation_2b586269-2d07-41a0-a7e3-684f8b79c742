import { Box, Typography, Slide, Grow, Pagination } from '@mui/material'
import React, { useEffect, useState } from 'react'
import OrderCard from '../components/Ordercard'
import JourneyCardSkeleton from '../components/JourneySkeleton';
import './css/TabletImages.css';
import { useNavigate } from 'react-router-dom';

const Myorders = () => {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [reload, setReload] = useState(false);
  const navigate = useNavigate()
  const [page, setPage] = useState(1)
  const [count, setCount] = useState(0)
  // const [journeyMode, setJourneyMode] = useState(1);
  // const [token, setToken] = useState(null);
  // const [user, setUser] = useState(null);
  const user = JSON.parse(localStorage.getItem("user")); // Get and parse user object
  const username = user ? user.userId : null; // Extract username safely

  // Animation states
  const [titleVisible, setTitleVisible] = useState(false);
  const [contentVisible, setContentVisible] = useState(false);

  // Trigger animations on component mount
  useEffect(() => {
    setTitleVisible(true);
    const contentTimer = setTimeout(() => setContentVisible(true), 400);

    return () => clearTimeout(contentTimer);
  }, []);

  // console.log(username);
  const token = localStorage.getItem('authToken');
  const mode = 'order'


  const fetchOrders = async () => {
    try {
      const response = await fetch(`${import.meta.env.VITE_API_URL}/api/orders/${username}?page=${page}`, {
        method: 'GET',
        headers: {
          'Authorization': token,
          'Content-Type': 'application/json',
        },
      });
      const result = await response.json();

      if (response.ok) {
        setData(result.data);
        setPage(result.pagination.page)
        setCount(result.pagination.totalPage)
      }
      if (response.status === 401) {
        navigate('/login')
      }
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchOrders();
  }, [reload,page]);

  const handleRefresh = () => {
    setReload(!reload);
  };

  return (
    <Box className='main-body-order-card' p={1} pb={8} sx={{ display: "flex", justifyContent: 'center', alignItems: 'center' }}>
      <Box sx={{ maxWidth: '1200px', width: '100%' }}>
        {/* {error && <p style={{ color: 'red' }}>{error}</p>} */}
        <Slide direction="down" in={titleVisible} timeout={800}>
          <Typography align="center" variant="h3" sx={{ color: '#EFEFEF', fontWeight: '700', fontSize: '40px', lineHeight: '48px', position: 'relative', top: '-180px' }} gutterBottom>
            My Orders
          </Typography>
        </Slide>
        {loading ? (
          <JourneyCardSkeleton />
        ) : (<>
          {/* <Box sx={{display:'flex',justifyContent:'flex-end',alignItems:'center',gap:'10px',margin:'15px'}}>
          <Typography sx={{color:'#000000',fontWeight:'700',fontSize:'16px',lineHeight:'24px',alignItems:'center',textTransform:'none',marginTop:'14px'}}>
            Choose your Transportation Mode:
          </Typography>
          <Stack >
            <Select
            value={journeyMode}
            sx={{width:'175px !important','& .MuiSelect-select':{padding:'10px !important'}}}
            fullWidth
            onChange={(e)=>setJourneyMode(e.target.value)}
            >
            <MenuItem value={1}>Flight</MenuItem>
            <MenuItem value={2}>Train</MenuItem>
            </Select>
          </Stack>
        </Box> */}
          <Grow in={contentVisible} timeout={1000} style={{ transformOrigin: '0 0 0' }}>
            <Box sx={{ width: '100%', display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
              <OrderCard orderDate={data} Mode={mode} fetchOrders={handleRefresh} />
             
            </Box>
          </Grow>
           <Box mt={3} sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                <Pagination page={page} count={count} onChange={(e, newPage) => setPage(newPage)} />
              </Box>
        </>)}
      </Box>
    </Box>
  )
}

export default Myorders