import { useContext, useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { <PERSON><PERSON>ield, Button, Box, Typography, Container, Checkbox, FormControlLabel, Alert, Snackbar, Stack, useTheme, useMediaQuery, InputAdornment, IconButton, Fade, Slide, Link as MuiLink } from "@mui/material";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { Visibility, VisibilityOff } from "@mui/icons-material";
import { Link, useNavigate } from "react-router-dom";
import arrow from '../assets/Arrowe.png'
import { GlobalContext } from "../context";
import UseGlobalContext from "../lib/hooks/Useglobelcontext";
import PageAnimation from "../components/PageAnimation";
import TermsDialog from "../components/TermsDialog";

const schema = yup.object().shape({
    email: yup.string().email("Enter a valid email").required("Email is required"),
    password: yup.string()
      .min(6, "Password must be at least 6 characters")
      .required("Password is required"),
    terms: yup.bool().oneOf([true], "You must agree to continue"),
  });

export default function LoginPage() {
  const { register, handleSubmit, formState: { errors } } = useForm({resolver:yupResolver(schema)});
  const { updateUser,updateToken   } = UseGlobalContext();
  const [showPassword, setShowPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const [message, setMessage]=useState('');
  const [success, setSuccess] = useState(false);
  const [termsDialogOpen, setTermsDialogOpen] = useState(false);
  const [termsCategory, setTermsCategory] = useState('Train');
  const [countdown, setCountdown] = useState(0);
  const theme = useTheme()
  const IsMobile = useMediaQuery(theme.breakpoints.down('sm'))
  const navigate = useNavigate();

  // Add countdown timer effect
  useEffect(() => {
    let timer;
    if (countdown > 0) {
      timer = setInterval(() => {
        setCountdown(prev => prev - 1);
      }, 1000);
    }
    return () => clearInterval(timer);
  }, [countdown]);

  // Animation states
  const [titleVisible, setTitleVisible] = useState(false);
  const [formVisible, setFormVisible] = useState(false);

  // Trigger animations on component mount
  useEffect(() => {
    setTitleVisible(true);
    const formTimer = setTimeout(() => setFormVisible(true), 300);

    return () => clearTimeout(formTimer);
  }, []);

  const saveToLocalStorage = (key, value) => {
    if (typeof window !== "undefined") {
      localStorage.setItem(key, value);
    }
  };
  const onSubmit = async (data) => {
    setLoading(true);
    setError("");
    try {
      const response = await fetch(`${import.meta.env.VITE_API_URL}/api/auth/login`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          email: data.email.toLowerCase(),
          password: data.password,
        }),
      });

      const result = await response.json();
      
      if (response.status === 429) {
        setMessage("Too many attempts. Please try again after 1 minute.");
        setSuccess(true);
        setCountdown(60); // Start 60 second countdown
        setLoading(true);
        setTimeout(() => {
          setLoading(false);
          setCountdown(0);
        }, 60000);
        return;
      }

      if(response.status === 401){
        setMessage("Incorrect email or password")
        setSuccess(true)
      }

      // Handle unverified email case
      if(response.status === 403 && result.needsVerification){
        setMessage("Please verify your email before logging in")
        setSuccess(true)

        // Save email to localStorage for verification
        localStorage.setItem("pendingVerificationEmail", data.email);

        // Redirect to email verification page
        setTimeout(() => {
          navigate('/verify-email', {
            state: { email: data.email }
          });
        }, 1500);
        return;
      }
      if(response.ok){
        // saveToLocalStorage("authToken",result.token)
        // saveToLocalStorage("id",result.user.id)
        // saveToLocalStorage("username",result.user.username)
        // saveToLocalStorage("email",result.user.email)
        updateToken(result.token);
        updateUser({
           userId: result.user.id,
          name: result.user.username,
          email: result.user.email,
          role:result.user.role })

        setMessage(result.message)
        setSuccess(true)
        setTimeout(() => {
            window.location.href='/';
        }, 1000);
      }
      if (!response.ok) {
        throw new Error(result.message || "Login failed");
      }
      // alert("Login successful!");

    } catch (err) {
      setError(err.message || "Something went wrong");
    } finally {
      setLoading(false);
    }
  };

  const handleRegister = () =>{
    navigate('/register')
  }
  return (
    <Box sx={{ background: '#FFFFFF' }}>
      <Slide direction="down" in={titleVisible} timeout={800}>
        <Typography  variant="h3"align="center" sx={{color:'#EFEFEF',fontWeight:'700',fontSize:'40px',lineHeight:'48px',position:'relative',top:'-180px'}} gutterBottom>
          Account
        </Typography>
      </Slide>

      <Box sx={{ display: 'flex', justifyContent: 'center', padding: '40px', flexDirection: 'column', alignItems: 'center' }}>
        <PageAnimation type="fade" timeout={1000}>
          <Typography variant="h3"  mt={5} sx={{ color: '#000000', fontWeight: '700', fontSize: '30px', lineHeight: '38px', fontFamily: 'Plus Jakarta Sans', }}>
            Login
          </Typography>
        </PageAnimation>

        <PageAnimation type="grow" timeout={800} delay={400}>
          <Container maxWidth="xs" sx={{ marginBottom:'25px',padding: '15px', boxShadow: 'inherit' }}>
            <Box
              sx={{
                display: "flex",
                flexDirection: "column",
                alignItems: "center",
                p: 4,
                boxShadow: 3,
                borderRadius: 2,
                bgcolor: "background.paper",
                transition: 'all 0.3s ease-in-out',
                '&:hover': {
                  boxShadow: 6,
                }
              }}
            >
              <Fade in={formVisible} timeout={1000}>
                <form onSubmit={handleSubmit(onSubmit)} style={{ width: "100%", alignItems: 'center', display: 'flex', justifyContent: 'center', flexDirection: 'column' }}>
                  <TextField
                    fullWidth
                    label="Email"
                    margin="normal"
                    variant="outlined"
                    {...register("email")}
                    error={!!errors.email}
                    helperText={errors.email?.message}
                    InputLabelProps={{ shrink: true, }}
                    InputProps={{
                      sx: {
                        "& input:-webkit-autofill": {
                          backgroundColor: "white !important",
                          WebkitBoxShadow: "0 0 0px 1000px white inset !important",
                          WebkitTextFillColor: "black !important",
                        },
                      },
                    }}
                  />

                  <TextField
                    fullWidth
                    label="Password"
                    type={showPassword ? "text" : "password"}
                    margin="normal"
                    variant="outlined"
                    {...register("password")}
                    error={!!errors.password}
                    helperText={errors.password?.message}
                    InputLabelProps={{ shrink: true }}
                    InputProps={{
                      endAdornment: (
                        <InputAdornment position="end">
                          <IconButton onClick={() => setShowPassword(!showPassword)} edge="end">
                            {showPassword ? <VisibilityOff sx={{color:'#DC143C'}}/> : <Visibility sx={{color:'#DC143C'}}/>}
                          </IconButton>
                        </InputAdornment>
                       ),
                      sx: {
                        "& input:-webkit-autofill": {
                          backgroundColor: "white !important",
                          WebkitBoxShadow: "0 0 0px 1000px white inset !important",
                          WebkitTextFillColor: "black !important",
                        },

                      },
                    }}
                  />

                  <Box sx={{ width: '100%', display: 'flex', justifyContent: 'flex-end', mt: 0.5 }}>
                    <Link to="/forget-password" style={{ color: '#DC143C', fontSize: '12px', textDecoration: 'none' }}>
                      Forgot Password?
                    </Link>
                  </Box>
                  <FormControlLabel sx={{marginRight:IsMobile ? '30px' :'65px',}}

                    control={<Checkbox {...register("terms")}
                    size="small"
                    sx={{borderRadius:'50px','&.Mui-checked': { color: '#DC143C' } }}/>}
                    label={
                      <Typography sx={{fontFamily:'Plus Jakarta Sans', fontWeight: "400", fontSize: IsMobile ? '7px' :"12px", lineHeight: "24px", color: "#000000",width:'max-content' }}>
                        I Agree with <MuiLink
                          component="span"
                          onClick={(e) => {
                            e.preventDefault();
                            setTermsCategory('Train');
                            setTermsDialogOpen(true);
                          }}
                          sx={{
                            color: '#DC143C',
                            cursor: 'pointer',
                            textDecoration: 'none',
                            '&:hover': {
                              textDecoration: 'underline'
                            }
                          }}
                        >
                          Terms & Conditions
                        </MuiLink>
                      </Typography>
                    }
                  />
                  {errors.terms && <Typography sx={{marginRight:'100px',fontSize:'10px',fontWeight:'400'}}color="error">{errors.terms.message}</Typography>}

                 <Stack direction={'column'} spacing={2.5} sx={{alignItems:'center',marginTop:'10px'}}>
                  <Button
                   endIcon={<img src={arrow} width={15} height={15}/>}
                   variant="contained"
                   sx={{
                     textTransform: 'none',
                     mt: 2,
                     background: '#DC143C',
                     color: '#FFFFFF',
                     '&.Mui-disabled': {
                       background: '#ffcdd2',
                       color: '#666'
                     }
                   }}
                   type="submit"
                   disabled={loading}
                  >
                    {loading && countdown > 0 
                      ? `Try again in ${countdown}s` 
                      : loading 
                        ? "Logging in..." 
                        : "Login"}
                  </Button>
                   <Typography variant="body1" sx={{fontSize:IsMobile? '9px':'11px',fontWeight:'400'}}>
                   Don't have an account? <Link style={{fontSize:IsMobile? '9px':'11px',color:'#DC143C'}}  to={'/register'}>Create free account</Link>
                   </Typography>
                  </Stack>
                </form>
              </Fade>
            </Box>
          </Container>
        </PageAnimation>
      </Box>
      <Snackbar
       anchorOrigin={{ vertical: "top", horizontal: "right" }}
        open={success} autoHideDuration={1000} onClose={() => setSuccess(false)}>
        <Alert onClose={() => setSuccess(false)}  icon={false} sx={{ width: "100%",borderLeft:'15px solid #DC143C',borderRadius:'10px',background:'#EFEFEF' }}>
        {message}
        </Alert>
      </Snackbar>

      {/* Terms and Conditions Dialog */}
      <TermsDialog
        open={termsDialogOpen}
        onClose={() => setTermsDialogOpen(false)}
        initialCategory={termsCategory}
      />
    </Box>
  );
}
