/* Home.css - Tablet-friendly image adjustments */

/* Tablet-specific styles for images */
@media screen and (min-width: 601px) and (max-width: 960px) {
  /* Typography adjustments for tablets */
  .section-title {
    font-size: 32px !important;
    line-height: 40px !important;
    padding: 10px !important;
  }

  .section-description {
    font-size: 15px !important;
    line-height: 22px !important;
    padding: 0 15px !important;
  }
  .home-image {
    width: 380px !important;
    height: 280px !important;
    object-fit: contain !important;
    transition: transform 0.3s ease-in-out !important;
  }

  .step-image-container {
    width: 250px !important;
    height: 250px !important;
    margin: 0 auto !important;
    transition: transform 0.3s ease-in-out !important;
  }

  .home-images-container {
    display: flex;
    flex-direction: row;
    justify-content: center;
    gap: 15px !important;
    padding: 15px !important;
    flex-wrap: wrap !important;
    max-width: 800px !important;
    margin: 0 auto !important;
  }

  .steps-container {
    display: flex;
    flex-direction: row;
    justify-content: center;
    gap: 10px !important;
    flex-wrap: wrap !important;
    padding: 10px !important;
    max-width: 800px !important;
    margin: 0 auto !important;
  }

  .step-text {
    font-size: 20px !important;
    line-height: 28px !important;
  }

  /* FAQ section tablet adjustments */
  .faq-main {
    padding: 20px 10px !important;
  }

  .faq-main > div {
    width: 90% !important;
    max-width: 700px !important;
  }

  /* Accordion adjustments */
  .MuiAccordionSummary-content {
    margin: 10px 0 !important;
  }

  .MuiAccordionDetails-root {
    padding: 8px 16px 16px !important;
  }
}

/* Animation for image hover */
.home-image:hover, .step-image-container:hover {
  transform: scale(1.03);
  transition: transform 0.3s ease-in-out;
}
