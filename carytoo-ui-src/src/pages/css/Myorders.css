/* Myorders.css - Mobile and tablet friendly styles */

.main-body-order-card {
  min-height: 100vh;
  background-color: #ffffff;
}

/* Mobile styles */
@media (max-width: 600px) {
  .main-body-order-card {
    padding-top: 60px !important;
  }
}

/* Tablet styles */
@media (min-width: 601px) and (max-width: 960px) {
  .main-body-order-card {
    padding-top: 80px !important;
  }
}

/* Desktop styles */
@media (min-width: 961px) {
  .main-body-order-card {
    padding-top: 100px !important;
  }
}

/* Animation for content loading */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fadeIn 0.5s ease-out forwards;
}

/* Loading animation */
@keyframes pulse {
  0% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.6;
  }
}

.loading-pulse {
  animation: pulse 1.5s infinite ease-in-out;
}
