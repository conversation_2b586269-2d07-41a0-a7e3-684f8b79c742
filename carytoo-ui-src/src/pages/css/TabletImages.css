/* TabletImages.css - Tablet-friendly image adjustments */

/* Tablet-specific styles for images */
@media screen and (min-width: 601px) and (max-width: 960px) {
  /* Home page images */
  .home-image {
    width: 380px !important;
    height: 280px !important;
    object-fit: contain !important;
  }
  
  .step-image {
    width: 280px !important;
    height: 280px !important;
    background-size: contain !important;
  }
  
  /* Journey page images */
  .journey-image {
    width: 350px !important;
    height: 250px !important;
    object-fit: contain !important;
  }
  
  /* Order page images */
  .order-image {
    width: 320px !important;
    height: 240px !important;
    object-fit: contain !important;
  }
  
  /* Profile page images */
  .profile-image {
    width: 120px !important;
    height: 120px !important;
    object-fit: cover !important;
  }
  
  /* Common image adjustments */
  img.responsive-img {
    max-width: 100% !important;
    height: auto !important;
  }
}
