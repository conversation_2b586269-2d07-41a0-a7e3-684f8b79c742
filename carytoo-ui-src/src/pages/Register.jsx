"use client";

import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { TextField, Button, Box, Typography, Container, Checkbox, FormControlLabel, InputAdornment, Stack, IconButton, Snackbar, Alert, Link as MuiLink, useTheme, useMediaQuery, Slide, getOffsetLeft } from "@mui/material";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { Visibility, VisibilityOff } from "@mui/icons-material";
import { Link, useNavigate } from "react-router-dom";
import arrow from "../assets/Arrowe.png";
import TermsDialog from "../components/TermsDialog";
import Spinner from "../components/Spinner";

const schema = yup.object().shape({
  username: yup.string()
    .required('Username is required')
    .test(
      'is-not-email',
      'Username cannot be an email address',
      value => {
        if (!value) return true; // Handled by required rule
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return !emailRegex.test(value);
      }
    ),
  PhoneNumber: yup.string().matches(/^[0-9]{10}$/, "Phone number must be exactly 10 digits").required("Phone number is required"),
  email: yup.string().email("Enter a valid email").required("Email is required"),
  password: yup.string()
    .min(6, "Password must be at least 6 characters")
    .matches(/\d/, "Password must contain at least one number")
    .required("Password is required"),
  confirmPassword: yup.string()
    .required('Confirm Password is required')
    .oneOf([yup.ref('password'), null], 'Passwords must match'),
  terms: yup.bool().oneOf([true], "You must agree to continue"),
});

export default function RegisterPage() {
  const { register, handleSubmit, formState: { errors } } = useForm({ resolver: yupResolver(schema) });
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [success, setSuccess] = useState(false);
  const [message, setMessage] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const [termsDialogOpen, setTermsDialogOpen] = useState(false);
  const [termsCategory, setTermsCategory] = useState('Train');
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const navigate = useNavigate();
  // Animation states
  const [titleVisible, setTitleVisible] = useState(false);
  const [formVisible, setFormVisible] = useState(false);

  // Trigger animations on component mount
  useEffect(() => {
    setTitleVisible(true);
    const formTimer = setTimeout(() => setFormVisible(true), 300);

    return () => clearTimeout(formTimer);
  }, []);


  // const saveToLocalStorage = (key, value) => {
  //   if (typeof window !== "undefined"){
  //     localStorage.setItem(key, value);
  //   }
  // };
 
  const getotp = async (data) => {
    setLoading(true)
    try {
      const response = await fetch(`${import.meta.env.VITE_API_URL}/api/auth/get-mail-reg`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          email: data.email.toLowerCase(),
          type: 'register'
        }),
      });

      const result = await response.json();

      if (response.ok) {
        setMessage(result.message || "OTP resent to your email");
        setSuccess(true);
        navigate('/verify-email', {
          state: {
            email: data.email.toLowerCase(),
            password: data.password,
            phoneNumber: data.PhoneNumber,
            username: data.username
          }
        });
      } else {
        setMessage(result.message)
        setSuccess(true);
        setError(result.message || "Failed to resend OTP");
        throw new Error(result.message || "Failed to resend OTP");
      }
    } catch (err) {


      setError(err.message || "Failed to resend OTP");
    } finally {
      setLoading(true);
    }
  };


  const onSubmit = async (data) => {
    getotp(data)
    // navigate('/verify-email', {
    //   state: { email: data.email,
    //     password: data.password,
    //     phoneNumber: data.PhoneNumber,
    //     username: data.username }
    // });
  };
  return (
    <Box sx={{ background: '#FFFFFF' }}>
      {loading ? (
        <Box mt={4} sx={{display:'flex',justifyContent:'center',alignItems:'center'}}>
        <Spinner/>
        </Box>
      ):(
        <>
      <Slide direction="down" in={titleVisible} timeout={800}>
        <Typography align="center" variant="h3" sx={{ color: '#EFEFEF', fontWeight: '700', fontSize: '40px', lineHeight: '48px', position: 'relative', top: '-180px' }} gutterBottom>
          Register
        </Typography>
      </Slide>
      <Box sx={{ display: 'flex', justifyContent: 'center', padding: '40px', flexDirection: 'column', alignItems: 'center' }}>
        <Typography variant="h3" mt={5} sx={{ color: '#000000', fontWeight: '700', fontSize: '30px', lineHeight: '38px', }}>
          Register
        </Typography>

        <Container maxWidth="xs" sx={{ marginBottom: '25px', padding: '15px', boxShadow: 'inherit' }}>
          <Box
            sx={{
              display: "flex",
              flexDirection: "column",
              alignItems: "center",
              p: 4,
              boxShadow: 3,
              borderRadius: 2,
              bgcolor: "background.paper",
              border: '2px solid #DC143C'
            }}
          >

            <form onSubmit={handleSubmit(onSubmit)} style={{ width: "100%", alignItems: 'center', display: 'flex', justifyContent: 'center', flexDirection: 'column' }}>
              <TextField
                fullWidth
                label="UserName"
                type="text"
                margin="normal"
                variant="outlined"
                {...register("username", { required: "username is required" })}
                error={!!errors.username}
                helperText={errors.username?.message}
                InputLabelProps={{ shrink: true }}
                InputProps={{
                  sx: {
                    "& input:-webkit-autofill": {
                      backgroundColor: "white !important",
                      WebkitBoxShadow: "0 0 0px 1000px white inset !important",
                      WebkitTextFillColor: "black !important",
                    },
                  },
                }}
              />

              <TextField
                fullWidth
                label="PhoneNumber"
                type="tel"
                margin="normal"
                variant="outlined"
                {...register("PhoneNumber")}
                inputProps={{
                  maxLength: 10,
                  inputMode: "numeric",
                  pattern: "[0-9]*",
                }}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">+91</InputAdornment>
                  ),
                  sx: {
                    "& input:-webkit-autofill": {
                      backgroundColor: "white !important",
                      WebkitBoxShadow: "0 0 0px 1000px white inset !important",
                      WebkitTextFillColor: "black !important",
                    },
                  }
                }}
                onKeyPress={(e) => {
                  if (/[^0-9]/.test(e.key)) {
                    e.preventDefault();
                  }
                }}
                error={!!errors.PhoneNumber}
                helperText={errors.PhoneNumber?.message}
                InputLabelProps={{ shrink: true }}
              />

              <TextField
                fullWidth
                label="Email"
                margin="normal"
                variant="outlined"
                {...register("email")}
                error={!!errors.email}
                helperText={errors.email?.message}
                InputLabelProps={{ shrink: true }}
                InputProps={{
                  sx: {
                    "& input:-webkit-autofill": {
                      backgroundColor: "white !important",
                      WebkitBoxShadow: "0 0 0px 1000px white inset !important",
                      WebkitTextFillColor: "black !important",
                    },
                  },
                }}
              />

              <TextField
                fullWidth
                label="Password"
                type={showPassword ? "text" : "password"}
                margin="normal"
                variant="outlined"
                {...register("password")}
                error={!!errors.password}
                helperText={errors.password?.message}
                InputLabelProps={{ shrink: true }}
                InputProps={{
                  sx: {
                    "& input:-webkit-autofill": {
                      backgroundColor: "white !important",
                      WebkitBoxShadow: "0 0 0px 1000px white inset !important",
                      WebkitTextFillColor: "black !important",
                    },
                  },
                  endAdornment: (
                    <InputAdornment position="end">
                      <IconButton onClick={() => setShowPassword(!showPassword)} edge="end">
                        {showPassword ? <VisibilityOff sx={{ color: '#DC143C' }} /> : <Visibility sx={{ color: '#DC143C' }} />}
                      </IconButton>
                    </InputAdornment>
                  ),
                }}
              />

              <TextField
                fullWidth
                label="confirm Password"
                type={showConfirmPassword ? "text" : "password"}
                margin="normal"
                variant="outlined"
                {...register("confirmPassword")}
                error={!!errors.confirmPassword}
                helperText={errors.confirmPassword?.message}
                InputLabelProps={{ shrink: true }}
                InputProps={{
                  sx: {
                    "& input:-webkit-autofill": {
                      backgroundColor: "white !important",
                      WebkitBoxShadow: "0 0 0px 1000px white inset !important",
                      WebkitTextFillColor: "black !important",
                    },
                  },
                  endAdornment: (
                    <InputAdornment position="end">
                      <IconButton onClick={() => setShowConfirmPassword(!showConfirmPassword)} edge="end">
                        {showConfirmPassword ? <VisibilityOff sx={{ color: '#DC143C' }} /> : <Visibility sx={{ color: '#DC143C' }} />}
                      </IconButton>
                    </InputAdornment>
                  ),

                }}
              />
              <FormControlLabel sx={{ marginRight: isMobile ? '30px' : '65px' }}
                control={<Checkbox {...register("terms")}
                  size="small"
                  sx={{ borderRadius: '50px', '&.Mui-checked': { color: '#DC143C' }, }} />}
                label={
                  <Typography sx={{ fontFamily: 'Plus Jakarta Sans', fontWeight: "400", fontSize: isMobile ? '10px !important  ' : "16px", lineHeight: "24px", color: "#000000", width: 'max-content' }}>
                    I Agree with <MuiLink
                      component="span"
                      onClick={(e) => {
                        e.preventDefault();
                        setTermsCategory('Train');
                        setTermsDialogOpen(true);
                      }}
                      sx={{
                        color: '#DC143C',
                        cursor: 'pointer',
                        textDecoration: 'none',
                        '&:hover': {
                          textDecoration: 'underline'
                        }
                      }}
                    >
                      Terms & Conditions
                    </MuiLink>
                  </Typography>
                }
              />
              {errors.terms && <Typography sx={{ marginRight: '50px', fontSize: '10px', fontWeight: '500' }} color="error">{errors.terms.message}</Typography>}
              <Stack direction={'column'} spacing={2.5} sx={{ margin: '10px 0px' }}>
                <Button endIcon={<img src={arrow} width={15} height={15} />} variant="contained" sx={{ textTransform: 'none', mt: 2, background: '#DC143C', color: '#FFFFFF' }} type="submit" disabled={loading}>
                  {loading ? "Register..." : "Register"}
                </Button>

                <Typography sx={{ fontSize: isMobile ? '9px' : '11px', fontWeight: '400' }}>
                  Already have an account?{' '} <a style={{ fontSize: isMobile ? '10px' : '11px', color: '#DC143C' }} href={'login'}>Login</a>
                </Typography>
              </Stack>

            </form>
          </Box>
        </Container>
      </Box>
      <Snackbar
        anchorOrigin={{ vertical: "top", horizontal: "right" }}
        open={success} autoHideDuration={1000} onClose={() => setSuccess(false)}>
        <Alert onClose={() => setSuccess(false)} icon={false} sx={{ width: "100%", borderLeft: '15px solid #DC143C', borderRadius: '10px', background: '#EFEFEF' }}>
          {message}
        </Alert>
      </Snackbar>

      {/* Terms and Conditions Dialog */}
      <TermsDialog
        open={termsDialogOpen}
        onClose={() => setTermsDialogOpen(false)}
        initialCategory={termsCategory}
      />
      </>
    )}
    </Box>
  );
}
