import FlightIcon from "@mui/icons-material/Flight";
import TrainIcon from "@mui/icons-material/Train";
import {
  <PERSON><PERSON>,
  <PERSON>,
  Button,
  Divider,
  <PERSON><PERSON><PERSON><PERSON>,
  Stack,
  Typography,
  useMediaQuery,
  useTheme,
  Fade,
  Slide,
  Grow,
} from "@mui/material";
import React, { useEffect, useState } from "react";
import FlightRaid from "../components/Flightregister";
import TrainRid from "../components/Trainregister";
import { useSearchParams } from "react-router-dom";
import PageAnimation from "../components/PageAnimation";
import InternationalFlightReg from "../components/InternationalFlightReg";
import PopupDialog from "../components/Popupdilog";

const Journeyregister = () => {
  const [searchparams] = useSearchParams();
  const mode = searchparams.get("mode_of_transport");
  const edit = searchparams.get("mode");
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));
  const [message,setMessage] = useState('')
  const [success,setSuccess] = useState(false)
  const [transportType, setTransportType] = useState( mode || "flight");
  const [data,setData] = useState([])
  const [popupOpen, setPopupOpen] = useState(true);

  // Animation states
  const [titleVisible, setTitleVisible] = useState(false);
  const [formVisible, setFormVisible] = useState(false);
  const [buttonsVisible, setButtonsVisible] = useState(false);

  // Trigger animations on component mount
  useEffect(() => {
    setPopupOpen(true)
    if(mode){
      setTransportType(mode)
    }
    setTitleVisible(true);

    const buttonsTimer = setTimeout(() => setButtonsVisible(true), 300);
    const formTimer = setTimeout(() => setFormVisible(true), 600);

    return () => {
      clearTimeout(buttonsTimer);
      clearTimeout(formTimer);
    };
    
  }, []);

  // console.log('response ',data)
    const token = localStorage.getItem("authToken");

  const handleTransportChange = (type) => {
    if (mode){
      setMessage('Transportation type cannot be changed while editing a journey'); setSuccess(true); return;}
    if (transportType !== type) {
      setTransportType(type);
    }
  };


  const handleConfirm = () => {
    console.log("User acknowledged the popup");
    // Any additional actions you want to take
    setPopupOpen(false);
  };



  return (<>
    <Slide direction="down" in={titleVisible} timeout={800}>
      <Typography variant="h3" align="center" sx={{color:'#EFEFEF',fontWeight:'700',fontSize:'40px',lineHeight:'48px',position:'relative',top:'-180px'}} gutterBottom>
        Post Ride
      </Typography>
    </Slide>
    <Box sx={{display:'flex',justifyContent:'center',alignItems:'center',
      //backgroundImage:transportType === "flight" ?"url('/assets/Flight-background.png')":"url('/assets/Train_background.png')",
      background:"common.white",
      backgroundSize:'cover',backgroundRepeat:'no-repeat',
      transition:' background-image 0.5s ease-in-out, opacity 0.5s ease-in-out'
    }}>

    <Box
      // width='50vw'
      display="flex"
      flexDirection="column"
      alignItems="center"
      justifyContent="center"
      gap={5}
      // p={10}
      bgcolor="common.white"
      sx={{padding:{sm:'40px 25px',lg:'40px 80px',md:'40px 80px'},borderRadius:'25px',margin:isMobile? '0px':'50px ',maxWidth:{sm:'100%',md:"820px",lg:'820px'},border:isMobile ? 'none':'2px solid #DC143C'}}
    >

      <PageAnimation type="fade" timeout={1000}>
        <Stack alignItems="center" gap={5}>
          <Typography
            variant="h3"
            color="black"
            textAlign="center"
            sx={{ mt: -1 }}
          >
            Travelling By
          </Typography>

          <Fade in={buttonsVisible} timeout={800}>
            <Stack direction={{xs:"column",sm:"row"}} alignItems="center" gap={4}>
          <Button
            variant={transportType === "flight" ? "contained" : "outlined"}
            color="primary"
            size="small"
            sx={{
              // width: "121px",
              px: 2.2,
              py: 1,
              borderRadius: "10px",
              border: transportType === "flight" ? "1px solid" : undefined,
              borderColor:
                transportType === "flight" ? "primary.main" : undefined,
            }}
            onClick={() => handleTransportChange("flight")}
            startIcon={<FlightIcon />}
          >
            <Typography
              variant="subtitle1"
              color={transportType === "flight" ? "common.white" : "black"}
              sx={{ whiteSpace: "nowrap" }}
            >
              Flight
            </Typography>
          </Button>

          <Button
            variant={transportType === "train" ? "contained" : "outlined"}
            color={transportType === "train" ? "primary" : "inherit"}
            size="small"
            sx={{
              px: 2.5,
              py: 1,
              borderRadius: "10px",
              border: "1px solid",
              borderColor: transportType === "train" ? "primary.main" : "#DC143C",
            }}
            onClick={() => handleTransportChange("train")}
            startIcon={<TrainIcon sx={{ color:  transportType === "train" ? '#EFEFEF': "#DC143C" }} />}
          >
            <Typography
              variant="subtitle1"
              color={transportType === "train" ? "common.white" : "black"}
              sx={{ whiteSpace: "nowrap" }}
            >
              Train
            </Typography>
          </Button>

          <Button
            variant={transportType === "flight-inter" ? "contained" : "outlined"}
            color="primary"
            size="small"
            sx={{
              // width: "121px",
              px: 2.2,
              py: 1,
              borderRadius: "10px",
              border: transportType === "flight" ? "1px solid" : undefined,
              borderColor:
                transportType === "flight" ? "primary.main" : undefined,
            }}
            onClick={() => handleTransportChange("flight-inter")}
            startIcon={<FlightIcon />}
          >
            <Typography
              variant="subtitle1"
              color={transportType === "flight-inter" ? "common.white" : "black"}
              sx={{ whiteSpace: "nowrap" }}
            >
              Flight-International
            </Typography>
          </Button>
          
            </Stack>
          </Fade>
        </Stack>
      </PageAnimation>

      <Divider sx={{ width:isMobile ? "300px" : "700px" }} />

      <Grow in={formVisible} timeout={1000} style={{ transformOrigin: 'center top' }}>
        <Box className='main-body-form' sx={{width:'100%',display:'flex',justifyContent:'center'}}>
          {/* {transportType === "flight" ? (
            <FlightRaid data={data} />
          ) : (
            <TrainRid data={data} />
          )} */}
          {transportType === "flight" ? (
             <FlightRaid data={data} />
           ) : transportType === "train" ? (
             <TrainRid data={data} />
           ) : (<InternationalFlightReg/>)}
        </Box>
      </Grow>

    </Box>
     <Snackbar anchorOrigin={{ horizontal: 'right', vertical: 'top' }} open={success} onClose={() => setSuccess(false)}
            autoHideDuration={1000}>
            <Alert icon={false} onClose={() => setSuccess(false)} sx={{ background: '#EFEFEF', width: '100%', border: '1px solid #DC143C', borderRadius: '10px',color:"#000000" }}>
              {message}
            </Alert>
          </Snackbar>
    </Box>

    <PopupDialog
        open={popupOpen}
        onClose={() => setPopupOpen(false)}
        title="Important Notice"
        message="Have the ticket handy and fill the details accurately."
        onConfirm={handleConfirm}
        confirmText="Got it"
      />
    </>
  );
};

export default Journeyregister;