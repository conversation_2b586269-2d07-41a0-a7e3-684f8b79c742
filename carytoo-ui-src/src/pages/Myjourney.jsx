import { Box, Typography, Slide, Grow, Pagination } from '@mui/material'
import React, { useEffect, useState } from 'react'
import JourneyCard from '../components/Journeycard';
import JourneyCardSkeleton from '../components/JourneySkeleton';
import './css/TabletImages.css';
import { Navigate, useNavigate } from 'react-router-dom';

const Myjourney = () => {
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [reload, setReload] = useState(false);
  const navigate = useNavigate()

  // State for journey mode (commented out as not currently used)
  // const [journeyMode, setJourneyMode] = useState(1);
  // const [token, setToken] = useState(null);
  // const [user, setUser] = useState(null);
  const [error, setError] = useState(null);
  const user = JSON.parse(localStorage.getItem("user")); // Get and parse user object
  const username = user ? user.userId : null; // Extract username safely

  // Animation states
  const [titleVisible, setTitleVisible] = useState(false);
  const [contentVisible, setContentVisible] = useState(false);
  const [page,setPage]=useState(1)
  const [count,setCount]= useState(0)
  console.log("data",data)

  // Trigger animations on component mount
useEffect(() => {
  setTitleVisible(true);
  console.log("Setting titleVisible to true");
    console.log("Setting contentVisible to true",contentVisible);
  const contentTimer = setTimeout(() => {
    setContentVisible(true);
    console.log("Setting contentVisible to true");
  }, 400);

  return () => {
    clearTimeout(contentTimer);
    console.log("Clearing contentTimer");
  };
  
}, []);

    console.log("contentVisible",contentVisible);


// console.log(username);

  // const user = localStorage.getItem('id')
  const token = localStorage.getItem('authToken')
  const mode = 'journey'

  const fetchJourney = async () => {
    if (typeof window === "undefined") return; // Prevent running on server


    try {
      const response = await fetch(`${import.meta.env.VITE_API_URL}/api/journeys/${username}?page=${page}`, {
        method: 'GET',
        headers: {
          'Authorization': token,
          'Content-Type': 'application/json',
        },
      });

      const result = await response.json();
      // if(response.status == 204){
      //   setData([])
      // }
      if (response.ok) {
        setData(Array.isArray(result.data) ? result.data : [result.data]);
        setPage(result.pagination.page)
        setCount(result.pagination.totalPage)
        // setData(result);
        setLoading(false);
        console.log("response",result)
      }
      if (response.status === 401) {
        navigate('/login')
      }
    } catch (err) {
      setError(err.message);
    }
  };

  useEffect(() => {
    fetchJourney();
  }, [reload,page]);

  const handleRefresh = () => {
    setReload(!reload);
  };

  return (
    <Box p={1} pb={8} sx={{ display: "flex", justifyContent: 'center', alignItems: 'center' }}>
      <Box sx={{ maxWidth: '1200px', width: '100%' }}>


        <Slide direction="down" in={titleVisible} timeout={800}>
          <Typography align="center" variant="h3" sx={{color:'#EFEFEF',fontWeight:'700',fontSize:'40px',lineHeight:'48px',position:'relative',top:'-180px'}} gutterBottom>
            My Trips
          </Typography>
        </Slide>
        {loading ? (
          <JourneyCardSkeleton/>
        ):(
          <>
        {/* <Box sx={{display:'flex',justifyContent:'flex-end',alignItems:'center',gap:'10px',margin:'15px'}}>
         <Typography sx={{color:'#000000',fontWeight:'700',fontSize:'16px',lineHeight:'24px',alignItems:'center',textTransform:'none',marginTop:'14px'}}>
         Choose your Transportation Mode:
         </Typography>
         <Stack >
        <Select
        value={journeyMode}
        sx={{width:'175px !important','& .MuiSelect-select':{padding:'10px !important'}}}
        fullWidth
        onChange={(e)=>setJourneyMode(e.target.value)}
        >
        <MenuItem value={1}>Flight</MenuItem>
        <MenuItem value={2}>Train</MenuItem>
        </Select>
         </Stack>
       </Box> */}

        {/* <Box  alignItems={'center'} sx={{display:'flex',justifyContent:'center'}}> */}
        {/* <Mycard/> */}
        <Grow in={contentVisible} timeout={1000} style={{ transformOrigin: '0 0 0' }}>
          <Box sx={{ width: '100%' ,display:'flex',justifyContent:'center'}}>
            <JourneyCard journeyData={data} Mode={mode} fetchJourney={handleRefresh} />
          </Box>
         
        </Grow>
        {/* </Box> */}
         <Box mt={3} sx={{display:'flex',alignItems:'center',justifyContent:'center'}}>
          <Pagination page={page} count={count} onChange={(e, newPage) => setPage(newPage)}/>
            </Box>
        </>
      )}
      </Box>
    </Box>
  )
}

export default Myjourney