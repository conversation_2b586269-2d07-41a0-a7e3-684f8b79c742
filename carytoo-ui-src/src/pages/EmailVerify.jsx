import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { <PERSON><PERSON>ield, Button, Box, Typography, Container, Alert, <PERSON><PERSON>kbar, Stack, useTheme, useMediaQuery, InputAdornment, Fade, Slide, Paper, Divider } from "@mui/material";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { Email, VerifiedUser } from "@mui/icons-material";
import { Link, useNavigate, useLocation } from "react-router-dom";
import arrow from '../assets/Arrowe.png';
import PageAnimation from "../components/PageAnimation";
import { use } from "react";

// Validation schema for OTP verification
const otpSchema = yup.object().shape({
  otp: yup.string()
    .required("OTP is required")
    .matches(/^[0-9]{6}$/, "OTP must be 6 digits")
});

const EmailVerify = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  // Get email from location state or localStorage
  const [email, setEmail] = useState(location.state?.email);
  const [phoneNumber, setPhoneNumber] = useState(location.state?.phoneNumber);
  const [userName, setUserName] = useState(location.state?.username);
  const [password, setPassword] = useState(location.state?.password);
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState('');
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState("");

  // Animation states
  const [titleVisible, setTitleVisible] = useState(false);
  const [formVisible, setFormVisible] = useState(false);

  // Form setup
  const { register, handleSubmit, formState: { errors } } =
    useForm({ resolver: yupResolver(otpSchema) });

  // Effect to get email from location state or localStorage
  useEffect(() => {
    // Try to get email from location state
    const stateEmail = location.state?.email;

    if (stateEmail) {
      setEmail(stateEmail);
    } else {
      // If not in state, try to get from localStorage
      const storedEmail = localStorage.getItem("pendingVerificationEmail");
      if (storedEmail) {
        setEmail(storedEmail);
      } else {
        // If no email is found, redirect to register page
        navigate('/register', {
          state: { error: "Please register first to verify your email" }
        });
      }
    }

    // Trigger animations
    setTitleVisible(true);
    const formTimer = setTimeout(() => setFormVisible(true), 300);

    return () => clearTimeout(formTimer);
  }, [location, navigate]);

  // const getotp = async () => {
  //   try {
  //     const response = await fetch(`${import.meta.env.VITE_API_URL}/api/auth/get-mail-reg`, {
  //       method: "POST",
  //       headers: {
  //         "Content-Type": "application/json",
  //       },
  //       body: JSON.stringify({
  //         email: email,
  //         type: 'register'
  //       }),
  //     });

  //     const result = await response.json();

  //     if (response.ok) {
  //       setMessage(result.message || "OTP resent to your email");
  //       setSuccess(true);
  //     } else {
  //       throw new Error(result.message || "Failed to resend OTP");
  //     }
  //   } catch (err) {
  //     setError(err.message || "Failed to resend OTP");
  //   } finally {
  //     setLoading(false);
  //   }
  // };

  // useEffect(() => {
  //   getotp();
  // }, []);

  const onSubmit = async (data) => {
    setLoading(true);
    setError("");
    try {
      const response = await fetch(`${import.meta.env.VITE_API_URL}/api/auth/register`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          email: email,
          password: password,
          phoneNumber: phoneNumber,
          username: userName
        }),
      });

      const result = await response.json();
      if (response.ok) {
        // Save email to localStorage for email verification
        saveToLocalStorage("pendingVerificationEmail", data.email);

        // Show success message
        setMessage(result.message || "Registration successful! Please verify your email.")
        setSuccess(true)
      }

      if (!response.ok) {
        setMessage(result.message)
        setSuccess((true))
        throw new Error(result.message || "Login failed");
      }
      // alert("Login successful!");
    } catch (err) {
      // setError(err.message || "Something went wrong");
      console.error("error", error);
    } finally {
      setLoading(false);
    }
  };

  // Handle OTP verification
  const onSubmitOtp = async (data) => {
    setLoading(true);
    setError("");
    try {
      // Make API call to verify OTP
      const response = await fetch(`${import.meta.env.VITE_API_URL}/api/auth/mail-verify`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          email: email,
          otp: parseInt(data.otp),
        }),
      });

      const result = await response.json();

      if (response.ok) {
        setMessage(result.message || "Email verified successfully");
        setSuccess(true);
        await onSubmit();
        // Remove the pending verification email from localStorage
        localStorage.removeItem("pendingVerificationEmail");

        // Redirect to login page after successful verification
        setTimeout(() => {
          navigate('/login', {
            state: { message: "Email verified successfully. You can now log in." }
          });
        }, 2000);
      } else {
        throw new Error(result.message || "Failed to verify email");
      }
    } catch (err) {
      setError(err.message || "Failed to verify email");
      console.error("error", error);
    } finally {
      setLoading(false);
    }
  };

  // Handle resend OTP
  const handleResendOtp = async () => {
    setLoading(true);
    try {
      // Make API call to resend OTP
      const response = await fetch(`${import.meta.env.VITE_API_URL}/api/auth/get-mail-reg`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          email: email,
          type: 'resend otp'
        }),
      });

      const result = await response.json();

      if (response.ok) {
        setMessage(result.message || "OTP resent to your email");
        setSuccess(true);
      } else {
        throw new Error(result.message || "Failed to resend OTP");
      }
    } catch (err) {
      // setError(err.message || "Failed to resend OTP");
      console.error("error", error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Box sx={{ background: '#FFFFFF' }}>
      <Slide direction="down" in={titleVisible} timeout={800}>
        <Typography align="center" sx={{ color: '#EFEFEF', fontWeight: '700', fontSize: '40px', lineHeight: '48px', position: 'relative', top: '-180px' }} gutterBottom>
          Email Verification
        </Typography>
      </Slide>

      <Box sx={{ display: 'flex', justifyContent: 'center', padding: '40px', flexDirection: 'column', alignItems: 'center' }}>
        <PageAnimation type="fade" timeout={1000}>
          <Typography mt={5} sx={{ color: '#000000', fontWeight: '700', fontSize: '30px', lineHeight: '38px', fontFamily: 'Plus Jakarta Sans', }}>
            Verify Your Email
          </Typography>
        </PageAnimation>

        <PageAnimation type="grow" timeout={800} delay={400}>
          <Container maxWidth="xs" sx={{ marginBottom: '25px', padding: '15px', boxShadow: 'inherit' }}>
            <Box
              sx={{
                display: "flex",
                flexDirection: "column",
                alignItems: "center",
                p: 4,
                boxShadow: 3,
                borderRadius: 2,
                bgcolor: "background.paper",
                transition: 'all 0.3s ease-in-out',
                '&:hover': {
                  boxShadow: 6,
                }
              }}
            >
              <Fade in={formVisible} timeout={1000}>
                <Box width="100%">
                  <Box sx={{ mb: 3, width: '100%' }}>
                    <Paper elevation={0} sx={{ p: 2, bgcolor: '#f5f5f5', borderRadius: 2 }}>
                      <Typography variant="body2" sx={{ fontWeight: '500' }}>
                        We've sent a 6-digit OTP to <span style={{ fontWeight: '700', color: '#DC143C' }}>{email}</span>
                      </Typography>
                    </Paper>
                  </Box>

                  <form onSubmit={handleSubmit(onSubmitOtp)} style={{ width: "100%", alignItems: 'center', display: 'flex', justifyContent: 'center', flexDirection: 'column' }}>
                    <TextField
                      fullWidth
                      label="Enter 6-digit OTP"
                      margin="normal"
                      variant="outlined"
                      {...register("otp")}
                      type="number"
                      error={!!errors.otp}
                      helperText={errors.otp?.message}
                      InputLabelProps={{ shrink: true }}
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <VerifiedUser sx={{ color: '#DC143C' }} />
                          </InputAdornment>
                        ),
                        sx: {
                          "& input:-webkit-autofill": {
                            backgroundColor: "white !important",
                            WebkitBoxShadow: "0 0 0px 1000px white inset !important",
                            WebkitTextFillColor: "black !important",
                          },
                        },
                      }}
                    />

                    <Stack direction={'column'} spacing={2.5} sx={{ alignItems: 'center', marginTop: '20px', width: '100%' }}>
                      <Button
                        endIcon={<img src={arrow} width={15} height={15} />}
                        variant="contained"
                        sx={{
                          textTransform: 'none',
                          mt: 2,
                          background: '#DC143C',
                          color: '#FFFFFF',
                          transition: 'all 0.3s ease',
                          width: '100%',
                          '&:hover': {
                            transform: 'translateY(-3px)',
                            boxShadow: '0 4px 8px rgba(220, 20, 60, 0.4)'
                          }
                        }}
                        type="submit"
                        disabled={loading}
                      >
                        {loading ? "Verifying..." : "Verify Email"}
                      </Button>

                      <Divider sx={{ width: '100%', my: 1 }}>
                        <Typography variant="body2" sx={{ color: 'text.secondary', px: 1 }}>
                          OR
                        </Typography>
                      </Divider>

                      <Button
                        variant="outlined"
                        color="primary"
                        onClick={handleResendOtp}
                        disabled={loading}
                        sx={{
                          textTransform: 'none',
                          width: '100%',
                          borderColor: '#DC143C',
                          color: '#DC143C',
                          '&:hover': {
                            borderColor: '#DC143C',
                            backgroundColor: 'rgba(220, 20, 60, 0.04)'
                          }
                        }}
                      >
                        Resend OTP
                      </Button>

                      <Button
                        variant="text"
                        component={Link}
                        to="/register"
                        sx={{
                          textTransform: 'none',
                          color: 'text.secondary',
                          '&:hover': {
                            backgroundColor: 'transparent',
                            textDecoration: 'underline'
                          }
                        }}
                      >
                        Back to Register
                      </Button>
                    </Stack>
                  </form>
                </Box>
              </Fade>
            </Box>
          </Container>
        </PageAnimation>
      </Box>

      <Snackbar
        anchorOrigin={{ vertical: "top", horizontal: "right" }}
        open={success}
        autoHideDuration={3000}
        onClose={() => setSuccess(false)}
      >
        <Alert
          onClose={() => setSuccess(false)}
          icon={false}
          sx={{
            width: "100%",
            borderLeft: '15px solid #DC143C',
            borderRadius: '10px',
            background: '#EFEFEF'
          }}
        >
          {message}
        </Alert>
      </Snackbar>

      {/* {error && (
        <Snackbar
          anchorOrigin={{ vertical: "bottom", horizontal: "right" }}
          open={!!error}
          autoHideDuration={3000}
          onClose={() => setError("")}
        >
          <Alert
            onClose={() => setError("")}
            severity="error"
            sx={{
              width: "100%",
              borderRadius: '10px',
            }}
          >
            {error}
          </Alert>
        </Snackbar>
      )} */}
    </Box>
  );
};

export default EmailVerify;