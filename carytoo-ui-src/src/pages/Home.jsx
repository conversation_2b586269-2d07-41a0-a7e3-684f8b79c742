import { Accordion, AccordionDetails, AccordionSummary, Box, Fade, Grow, Slide, Typography, Zoom, useMediaQuery, useTheme } from '@mui/material'
import AddIcon from '@mui/icons-material/Add'; // Plus Icon
import RemoveIcon from '@mui/icons-material/Remove'; // Minus Icon
import { useEffect, useState } from 'react'
import LandingPage from '../components/Landingpage';
import './css/TabletImages.css';
import './css/Home.css';
import trainHome from '../assets/Home-train.png'
import flightHome from '../assets/Home-flight.png'
import home1 from '../assets/Web-search.png'
import home2 from '../assets/Messenger-cuate.png'
import home3 from '../assets/Hotel-Booking-cuate.png'

const Home = () => {
  const [expanded, setExpanded] = useState(null);
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  // Animation states
  const [heroVisible, setHeroVisible] = useState(false);
  const [whoWeAreVisible, setWhoWeAreVisible] = useState(false);
  const [howItWorksVisible, setHowItWorksVisible] = useState(false);
  const [stepsVisible, setStepsVisible] = useState(false);
  const [faqVisible, setFaqVisible] = useState(false);

  // Trigger animations on component mount
  useEffect(() => {
    // Stagger the animations for a nice effect
    setHeroVisible(true);

    const whoWeAreTimer = setTimeout(() => setWhoWeAreVisible(true), 500);
    const howItWorksTimer = setTimeout(() => setHowItWorksVisible(true), 1000);
    const stepsTimer = setTimeout(() => setStepsVisible(true), 1500);
    const faqTimer = setTimeout(() => setFaqVisible(true), 2000);

    // Clean up timers
    return () => {
      clearTimeout(whoWeAreTimer);
      clearTimeout(howItWorksTimer);
      clearTimeout(stepsTimer);
      clearTimeout(faqTimer);
    };
  }, []);

  useEffect(() => {
    window.scrollTo(0, 0);
  }, [])

  const handleChange = (panel) => (_, isExpanded) => {
    setExpanded(isExpanded ? panel : null);
  };

  const faqData = [
    {
      question: "How does the package delivery by travelers work?",
      answer: "Travelers who have extra space in their luggage can carry and deliver packages to your destination in exchange for a fee."
    },
    {
      question: "Is the service safe and reliable?",
      answer: "Yes, we verify travelers and ensure secure communication between senders and travelers. However, both parties should verify each other before proceeding."
    },
    {
      question: "How do I find a traveler going to my destination?",
      answer: "You can browse available travelers on our platform, check their routes, and connect with them directly to arrange delivery."
    },
    {
      question: "What kind of items can I send?",
      answer: "You can send small, legal, and non-perishable items like documents, gifts, clothes, or electronics. Prohibited items include hazardous materials and illegal substances."
    },
    {
      question: "how much can i earn?",
      answer: "For a train journey Rs 100 and for domestic flight journey Rs 500."
    },
    {
      question: "How much does it cost to send a package?",
      answer: `
      <p>You can send small, legal, and non-perishable items. Here's a breakdown of service categories:</p>
<table border="1" cellpadding="8" cellspacing="0">
  <thead>
    <tr>
      <th>Category</th>
      <th>Train</th>
      <th>Flight - Domestic</th>
      <th>Flight - International</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td><strong>Weight</strong></td>
      <td>≤ 5 kg</td>
      <td>≤ 2 kg</td>
      <td>≤ 2 kg</td>
    </tr>
    <tr>
      <td><strong>Volume</strong></td>
      <td>Fits in Backpack</td>
      <td>Fits in Backpack</td>
      <td>Fits in Backpack</td>
    </tr>
    <tr>
      <td><strong>Price</strong></td>
      <td>₹99*</td>
      <td>₹499*</td>
      <td>₹1599</td>
    </tr>
    <tr style="padding-top: 20px; padding-bottom: 20px;">
      <td style="padding: 10px;"><strong>Allowed Items</strong></td>
      <td style="padding: 10px;">Everything allowed by law</td>
      <td style="padding: 10px;">Everything allowed by law</td>
      <td style="padding: 10px;">Documents Only</td>
    </tr>
  </tbody>
</table>
<p><em>* Platform charges and GST extra.</em></p>
`
    },
    {
      question: "How can I trust the traveler with my package?",
      answer: "We encourage users to check traveler profiles, reviews, and ratings before finalizing the delivery."
    },
    {
      question: "What if my package gets lost or damaged?",
      answer: "Responsibility and compensation should be clearly agreed upon before sending a package. For high-value items."
    }
  ];

  const stepsData = [
    { image: home1, text: "Search Travellers" },
    { image: home2, text: "Hand over the parcel" },
    { image: home3, text: "Get it delivered Realtime" }
  ];

  return (
    <Box>
      {/* Hero Section */}
      <Fade in={heroVisible} timeout={1000}>
        <Box sx={{ backgroundImage: "url('/assets/Home-background.png')", backgroundSize: 'cover', backgroundRepeat: 'no-repeat', height: '700px !important', alignItems: 'center' }}>
          <Box>
            <LandingPage />
          </Box>
        </Box>
      </Fade>

      {/* Who We Are Section */}
      <Grow in={whoWeAreVisible} timeout={1000}>
        <Box sx={{ background: '#FFFFFF', alignItems: 'center', display: 'flex', justifyContent: 'center', gap: '10px', textAlign: 'center', flexDirection: 'column', paddingTop: '30px' }}>
          <Typography
            className="section-title"
            sx={{ color: '#000000', fontWeight: '700', fontSize: '40px !important', lineHeight: '48px', alignItems: 'center', padding: '15px', textTransform: 'none', maxWidth: '800px' }}>
            Who we are
            <Typography
              className="section-description"
              sx={{ color: '#000000', fontWeight: '400', fontSize: '16px', lineHeight: '24px', alignItems: 'center', textTransform: 'none' }}>
              Carytoo is a smart delivery platform that connects senders with frequent travelers who have extra space in their luggage. Instead of relying on traditional couriers, we make deliveries faster, cheaper, and more efficient by utilizing travelers already heading to your package's destination. With verified users and secure communication, Carytoo offers a convenient and community-driven way to send and receive packages worldwide
            </Typography>
          </Typography>

          <Box mb={2} className="home-images-container" sx={{ display: 'flex', gap: '20px', maxWidth: '995px', flexDirection: isMobile ? 'column' : 'row', padding: '30px' }}>
            <Zoom in={whoWeAreVisible} timeout={1500} style={{ transitionDelay: whoWeAreVisible ? '500ms' : '0ms' }}>
              <img className="home-image responsive-img" src={trainHome} alt="Train" width={isMobile ? 350 : 486} height={isMobile ? 300 : 351} />
            </Zoom>
            <Zoom in={whoWeAreVisible} timeout={1500} style={{ transitionDelay: whoWeAreVisible ? '1000ms' : '0ms' }}>
              <img className="home-image responsive-img" src={flightHome} alt="Flight" width={isMobile ? 350 : 486} height={isMobile ? 300 : 351} style={{ borderRadius: '20px' }} />
            </Zoom>
          </Box>
        </Box>
      </Grow>

      {/* How It Works Section */}
      <Slide direction="right" in={howItWorksVisible} timeout={1000}>
        <Box sx={{ background: '#F5F5F5', alignItems: 'center', display: 'flex', justifyContent: 'center', gap: '10px', textAlign: 'center', flexDirection: 'column', paddingTop: '30px' }}>
          <Typography
            className="section-title"
            sx={{ color: '#000000', fontWeight: '700', fontSize: '40px !important', lineHeight: '48px', alignItems: 'center', padding: '15px', textTransform: 'none', maxWidth: '800px', display: 'flex', flexDirection: 'column' }}>
            How it works
            <Typography
              className="section-description"
              sx={{ color: '#000000', fontWeight: '400', fontSize: '16px', lineHeight: '24px', alignItems: 'center', textTransform: 'none', width: 'fit-content', marginTop: '14px' }}>
              Carytoo makes delivery simple by connecting you with travelers already heading to your package's destination. Just search for a traveler And confirm the details, and hand over your package securely. The traveler carries it along their journey, and you stay updated until it reaches its destination. It's a faster, cost-effective, and eco-friendly way to send packages with trusted travelers
            </Typography>
          </Typography>

          <Box mb={2} className="steps-container" sx={{ display: 'flex', flexDirection: isMobile ? 'column' : 'row' }}>
            {stepsData.map((step, index) => (
              <Grow
                key={index}
                in={stepsVisible}
                timeout={1000}
                style={{ transformOrigin: '0 0 0', transitionDelay: stepsVisible ? `${index * 300}ms` : '0ms' }}
              >
                <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
                  <Box
                    className="step-image-container step-image"
                    sx={{
                      width: "350px",
                      height: isMobile ? '300px' : "350px",
                      backgroundImage: `url(${step.image})`,
                      backgroundSize: "cover",
                      backgroundRepeat: "no-repeat",
                      color: "#000000",
                      transition: 'transform 0.3s ease-in-out',
                      '&:hover': {
                        transform: 'scale(1.05)'
                      }
                    }}
                  ></Box>
                  <Typography className="step-text" sx={{ fontWeight: '700', fontSize: '24px', lineHeight: '32px', paddingTop: '15px', marginTop: '15px' }}>
                    {step.text}
                  </Typography>
                </Box>
              </Grow>
            ))}
          </Box>
        </Box>
      </Slide>

      {/* FAQ Section */}
      <Fade in={faqVisible} timeout={1500}>
        <Box className='faq-main' sx={{ background: '#FFFFFF', alignItems: 'center', display: 'flex', justifyContent: 'center', gap: '10px', textAlign: 'center', flexDirection: 'column', paddingTop: '30px', paddingBottom: '30px', width: '100%' }}>
          <Box sx={{ width: isMobile ? '360px' : '783px' }}>
            <Typography
              className="section-title"
              sx={{ color: '#000000', fontWeight: '700', fontSize: '40px !important', lineHeight: '48px', alignItems: 'center', padding: '15px', textTransform: 'none', maxWidth: '800px' }}>
              Frequently asked questions
              <Typography
                className="section-description"
                sx={{ color: '#000000', fontWeight: '400', fontSize: '16px', lineHeight: '24px', alignItems: 'center', textTransform: 'none', marginTop: '14px' }}>
                Got questions? We&apos;ve got answers! Here&apos;s everything you need to know about sending packages with trusted travelers through Carytoo.
              </Typography>
            </Typography>
            <Box>
              {faqData.map((faq, index) => (
                <Accordion
                  expanded={expanded === index}
                  onChange={handleChange(index)}
                  sx={{ background: '#FFFFFF', border: 'none', boxShadow: 'none' }}
                  key={index}
                >
                  <AccordionSummary
                    sx={{ display: 'flex', justifyContent: 'flex-start' }}
                    expandIcon={expanded === index ? <RemoveIcon /> : <AddIcon />}
                  >
                    <Typography
                      sx={{ fontWeight: isMobile ? '400' : '500', fontSize: isMobile ? '16px' : '20px', lineHeight: isMobile ? '26px' : '32px' }}
                      variant="body2"
                    >
                      {faq.question}
                    </Typography>
                  </AccordionSummary>
                  <AccordionDetails aria-controls="panel2-content">
                    {/* <Typography sx={{fontWeight:'400',fontSize:'16px',lineHeight:'24px'}}>
                      {faq.answer}
                    </Typography> */}
                    <Typography
                      component="div"
                      dangerouslySetInnerHTML={{ __html: faq.answer }}
                    />
                  </AccordionDetails>
                </Accordion>
              ))}
            </Box>
          </Box>
        </Box>
      </Fade>
    </Box>
  )
}

export default Home
