import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { TextField, Button, Box, Typography, Container, Alert, <PERSON>nack<PERSON>, Stack, useTheme, useMediaQuery, InputAdornment, IconButton, Fade, Slide, Stepper, Step, StepLabel, Paper, Divider } from "@mui/material";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { Visibility, VisibilityOff, Email, LockReset, VerifiedUser, ArrowForward } from "@mui/icons-material";
import { Link, useNavigate } from "react-router-dom";
import arrow from '../assets/Arrowe.png';
import PageAnimation from "../components/PageAnimation";

// Validation schemas for each step
const emailSchema = yup.object().shape({
  email: yup.string().email("Enter a valid email").required("Email is required"),
});

// Combined OTP verification and password reset schema
const otpPasswordSchema = yup.object().shape({
  otp: yup.string()
    .required("OTP is required")
    .matches(/^[0-9]{6}$/, "OTP must be 6 digits"),
  password: yup.string()
    .min(6, "Password must be at least 6 characters")
    .matches(/\d/, "Password must contain at least one number")
    .required("Password is required"),
  confirmPassword: yup.string()
    .oneOf([yup.ref('password')], "Passwords must match")
    .required("Confirm password is required"),
});

const ForgotPassword = () => {
  const [activeStep, setActiveStep] = useState(0);
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState('');
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [email, setEmail] = useState("");
  const theme = useTheme();
  const navigate = useNavigate();

  // Animation states
  const [titleVisible, setTitleVisible] = useState(false);
  const [formVisible, setFormVisible] = useState(false);

  // Trigger animations on component mount
  useEffect(() => {
    setTitleVisible(true);
    const formTimer = setTimeout(() => setFormVisible(true), 300);

    return () => clearTimeout(formTimer);
  }, []);

  // Form setup for each step
  const { register: registerEmail, handleSubmit: handleSubmitEmail, formState: { errors: errorsEmail } } =
    useForm({ resolver: yupResolver(emailSchema) });

  const { register: registerOtpPassword, handleSubmit: handleSubmitOtpPassword, formState: { errors: errorsOtpPassword } } =
    useForm({ resolver: yupResolver(otpPasswordSchema) });

  // Step 1: Request OTP
  const onSubmitEmail = async (data) => {
    setLoading(true);
    setError("");
    try {
      const request = await fetch(`${import.meta.env.VITE_API_URL}/api/auth/get-mail`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          email: data.email,
          type:"forgot password"
        }),
      });
      // In a real application, you would make an API call to request OTP
      // For demo purposes, we'll simulate a successful response
      const response = await request.json();
      if(request.ok){
      setEmail(data.email);
      setMessage(response.message);
      setSuccess(true);
      setTimeout(() => {
        setActiveStep(1);
      }, 1000);
    }
    setMessage(response.message);
    setSuccess(true);
    } catch (err) {
      setError(err.message || "Something went wrong");
      setMessage(response.message);
      setSuccess(true);
    } finally {
      setLoading(false);
    }
  };

  // Step 2: Verify OTP and Reset Password in one step
  const onSubmitOtpPassword = async (data) => {
    setLoading(true);
    setError("");
    try {const request = await fetch(`${import.meta.env.VITE_API_URL}/api/auth/otp-verify`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        mail: email,
        otp: parseInt(data.otp),
        password: data.password,
      }),
    });
 
    const response = await request.json(); 
    setMessage(response.message);
    setSuccess(true);
    
    if(request.ok){
      setMessage(response.message);
      setSuccess(true);
      setTimeout(() => {
        navigate('/login');
      }, 1500);} 

    } catch (err) {
      setError(err.message || "Failed to verify OTP or reset password");
      console.error("error",error);
    } finally {
      setLoading(false);
    }
  };

  // Handle resend OTP
  const handleResendOtp = async () => {
    setLoading(true);
    const request = await fetch(`${import.meta.env.VITE_API_URL}/api/auth/get-mail`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        email: email,
        type:"forgot password"
      }),
    });
    
      const response = await request.json();
      setMessage(response.message);
      setSuccess(true);
      setLoading(false);
    
  };

  // Steps configuration
  const steps = [
    { label: 'Email Verification', description: 'Enter your email to receive OTP' },
    { label: 'Verify & Reset', description: 'Verify OTP and create a new password' },
  ];

  return (
    <Box sx={{ background: '#FFFFFF' }}>
      <Slide direction="down" in={titleVisible} timeout={800}>
        <Typography align="center" sx={{color:'#EFEFEF',fontWeight:'700',fontSize:'40px',lineHeight:'48px',position:'relative',top:'-180px'}} gutterBottom>
          Account Recovery
        </Typography>
      </Slide>

      <Box sx={{ display: 'flex', justifyContent: 'center', padding: '40px', flexDirection: 'column', alignItems: 'center' }}>
        <PageAnimation type="fade" timeout={1000}>
          <Typography mt={5} sx={{ color: '#000000', fontWeight: '700', fontSize: '30px', lineHeight: '38px', fontFamily: 'Plus Jakarta Sans', }}>
            Forgot Password
          </Typography>
        </PageAnimation>

        <PageAnimation type="grow" timeout={800} delay={400}>
          <Container maxWidth="xs" sx={{ marginBottom:'25px', padding: '15px', boxShadow: 'inherit' }}>
            <Box
              sx={{
                display: "flex",
                flexDirection: "column",
                alignItems: "center",
                p: 4,
                boxShadow: 3,
                borderRadius: 2,
                bgcolor: "background.paper",
                transition: 'all 0.3s ease-in-out',
                '&:hover': {
                  boxShadow: 6,
                }
              }}
            >
              <Fade in={formVisible} timeout={1000}>
                <Box width="100%">
                  {/* Stepper */}
                  <Stepper activeStep={activeStep} alternativeLabel sx={{ mb: 4, display: { xs: 'none', sm: 'flex' } }}>
                    {steps.map((step) => (
                      <Step key={step.label}>
                        <StepLabel>{step.label}</StepLabel>
                      </Step>
                    ))}
                  </Stepper>

                  {/* Mobile Stepper Alternative */}
                  <Box sx={{ display: { xs: 'block', sm: 'none' }, mb: 3 }}>
                    <Typography variant="subtitle1" fontWeight="600" textAlign="center">
                      Step {activeStep + 1}: {steps[activeStep].label}
                    </Typography>
                    <Typography variant="body2" color="text.secondary" textAlign="center" mt={1}>
                      {steps[activeStep].description}
                    </Typography>
                  </Box>

                  {/* Step 1: Email Form */}
                  {activeStep === 0 && (
                    <form onSubmit={handleSubmitEmail(onSubmitEmail)} style={{ width: "100%", alignItems: 'center', display: 'flex', justifyContent: 'center', flexDirection: 'column' }}>
                      <TextField
                        fullWidth
                        label="Email"
                        margin="normal"
                        variant="outlined"
                        {...registerEmail("email")}
                        error={!!errorsEmail.email}
                        helperText={errorsEmail.email?.message}
                        InputLabelProps={{ shrink: true }}
                        InputProps={{
                          startAdornment: (
                            <InputAdornment position="start">
                              <Email sx={{ color: '#DC143C' }} />
                            </InputAdornment>
                          ),
                          sx: {
                            "& input:-webkit-autofill": {
                              backgroundColor: "white !important",
                              WebkitBoxShadow: "0 0 0px 1000px white inset !important",
                              WebkitTextFillColor: "black !important",
                            },
                          },
                        }}
                      />

                      <Stack direction={'column'} spacing={2.5} sx={{ alignItems: 'center', marginTop: '20px', width: '100%' }}>
                        <Button
                          endIcon={<img src={arrow} width={15} height={15}/>}
                          variant="contained"
                          sx={{
                            textTransform: 'none',
                            mt: 2,
                            background: '#DC143C',
                            color: '#FFFFFF',
                            transition: 'all 0.3s ease',
                            width: '100%',
                            '&:hover': {
                              transform: 'translateY(-3px)',
                              boxShadow: '0 4px 8px rgba(220, 20, 60, 0.4)'
                            }
                          }}
                          type="submit"
                          disabled={loading}
                        >
                          {loading ? "Sending OTP..." : "Send OTP"}
                        </Button>
                        <Typography variant="body1" sx={{ fontSize: '11px', fontWeight: '400' }}>
                          Remember your password? <a style={{ color: '#DC143C' }} href={'login'} >Login</a>
                        </Typography>
                      </Stack>
                    </form>
                  )}

                  {/* Step 2: Combined OTP Verification and Password Reset Form */}
                  {activeStep === 1 && (
                    <form onSubmit={handleSubmitOtpPassword(onSubmitOtpPassword)} style={{ width: "100%", alignItems: 'center', display: 'flex', justifyContent: 'center', flexDirection: 'column' }}>
                      <Box sx={{ mb: 3, width: '100%' }}>
                        <Paper elevation={0} sx={{ p: 2, bgcolor: '#f5f5f5', borderRadius: 2 }}>
                          <Typography variant="body2" sx={{ fontWeight: '500' }}>
                            We've sent a 6-digit OTP to <span style={{ fontWeight: '700', color: '#DC143C' }}>{email}</span>
                          </Typography>
                        </Paper>
                      </Box>

                      <TextField
                        fullWidth
                        label="Enter 6-digit OTP"
                        margin="normal"
                        variant="outlined"
                        {...registerOtpPassword("otp")}
                        error={!!errorsOtpPassword.otp}
                        helperText={errorsOtpPassword.otp?.message}
                        InputLabelProps={{ shrink: true }}
                        InputProps={{
                          startAdornment: (
                            <InputAdornment position="start">
                              <VerifiedUser sx={{ color: '#DC143C' }} />
                            </InputAdornment>
                          ),
                          sx: {
                            "& input:-webkit-autofill": {
                              backgroundColor: "white !important",
                              WebkitBoxShadow: "0 0 0px 1000px white inset !important",
                              WebkitTextFillColor: "black !important",
                            },
                          },
                        }}
                      />

                      <TextField
                        fullWidth
                        label="New Password"
                        type={showPassword ? "text" : "password"}
                        margin="normal"
                        variant="outlined"
                        {...registerOtpPassword("password")}
                        error={!!errorsOtpPassword.password}
                        helperText={errorsOtpPassword.password?.message}
                        InputLabelProps={{ shrink: true }}
                        InputProps={{
                          endAdornment: (
                            <InputAdornment position="end">
                              <IconButton onClick={() => setShowPassword(!showPassword)} edge="end">
                                {showPassword ? <VisibilityOff sx={{color:'#DC143C'}}/> : <Visibility sx={{color:'#DC143C'}}/>}
                              </IconButton>
                            </InputAdornment>
                          ),
                          startAdornment: (
                            <InputAdornment position="start">
                              <LockReset sx={{ color: '#DC143C' }} />
                            </InputAdornment>
                          ),
                          sx: {
                            "& input:-webkit-autofill": {
                              backgroundColor: "white !important",
                              WebkitBoxShadow: "0 0 0px 1000px white inset !important",
                              WebkitTextFillColor: "black !important",
                            },
                          },
                        }}
                      />

                      <TextField
                        fullWidth
                        label="Confirm Password"
                        type={showConfirmPassword ? "text" : "password"}
                        margin="normal"
                        variant="outlined"
                        {...registerOtpPassword("confirmPassword")}
                        error={!!errorsOtpPassword.confirmPassword}
                        helperText={errorsOtpPassword.confirmPassword?.message}
                        InputLabelProps={{ shrink: true }}
                        InputProps={{
                          endAdornment: (
                            <InputAdornment position="end">
                              <IconButton onClick={() => setShowConfirmPassword(!showConfirmPassword)} edge="end">
                                {showConfirmPassword ? <VisibilityOff sx={{color:'#DC143C'}}/> : <Visibility sx={{color:'#DC143C'}}/>}
                              </IconButton>
                            </InputAdornment>
                          ),
                          startAdornment: (
                            <InputAdornment position="start">
                              <LockReset sx={{ color: '#DC143C' }} />
                            </InputAdornment>
                          ),
                          sx: {
                            "& input:-webkit-autofill": {
                              backgroundColor: "white !important",
                              WebkitBoxShadow: "0 0 0px 1000px white inset !important",
                              WebkitTextFillColor: "black !important",
                            },
                          },
                        }}
                      />

                      <Stack direction={'column'} spacing={2.5} sx={{ alignItems: 'center', marginTop: '20px', width: '100%' }}>
                        <Button
                          endIcon={<img src={arrow} width={15} height={15}/>}
                          variant="contained"
                          sx={{
                            textTransform: 'none',
                            mt: 2,
                            background: '#DC143C',
                            color: '#FFFFFF',
                            transition: 'all 0.3s ease',
                            width: '100%',
                            '&:hover': {
                              transform: 'translateY(-3px)',
                              boxShadow: '0 4px 8px rgba(220, 20, 60, 0.4)'
                            }
                          }}
                          type="submit"
                          disabled={loading}
                        >
                          {loading ? "Verifying & Resetting..." : "Verify & Reset Password"}
                        </Button>

                        <Divider sx={{ width: '100%', my: 1 }}>
                          <Typography variant="body2" sx={{ color: 'text.secondary', px: 1 }}>
                            OR
                          </Typography>
                        </Divider>

                        <Button
                          variant="outlined"
                          color="primary"
                          onClick={handleResendOtp}
                          disabled={loading}
                          sx={{
                            textTransform: 'none',
                            width: '100%',
                            borderColor: '#DC143C',
                            color: '#DC143C',
                            '&:hover': {
                              borderColor: '#DC143C',
                              backgroundColor: 'rgba(220, 20, 60, 0.04)'
                            }
                          }}
                        >
                          Resend OTP
                        </Button>

                        <Button
                          variant="text"
                          onClick={() => setActiveStep(0)}
                          sx={{
                            textTransform: 'none',
                            color: 'text.secondary',
                            '&:hover': {
                              backgroundColor: 'transparent',
                              textDecoration: 'underline'
                            }
                          }}
                        >
                          Change Email
                        </Button>
                      </Stack>
                    </form>
                  )}
                </Box>
              </Fade>
            </Box>
          </Container>
        </PageAnimation>
      </Box>

      <Snackbar
        anchorOrigin={{ vertical: "top", horizontal: "right" }}
        open={success}
        autoHideDuration={3000}
        onClose={() => setSuccess(false)}
      >
        <Alert
          onClose={() => setSuccess(false)}
          icon={false}
          sx={{
            width: "100%",
            borderLeft: '15px solid #DC143C',
            borderRadius: '10px',
            background: '#EFEFEF'
          }}
        >
          {message}
        </Alert>
      </Snackbar>

      {error && (
        <Snackbar
          anchorOrigin={{ vertical: "top", horizontal: "right" }}
          open={!!error}
          autoHideDuration={3000}
          onClose={() => setError("")}
        >
          <Alert
            onClose={() => setError("")}
            severity="error"
            sx={{
              width: "100%",
              borderRadius: '10px',
            }}
          >
            {error}
          </Alert>
        </Snackbar>
      )}
    </Box>
  );
};

export default ForgotPassword;