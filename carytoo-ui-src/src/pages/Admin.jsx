import { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Tabs,
  Tab,
  TextField,
  MenuItem,
  Select,
  FormControl,
  InputLabel,
  Button,
  Stack,
  Paper,
  Grid,
  Divider,
  Alert,
  Snackbar,

  Card,
  CardContent,
  Chip,
  Slide,
  Fade,
  Grow,
  Pagination
} from '@mui/material';
import PageAnimation from '../components/PageAnimation';
import {
  FilterList,
  Search,
  Refresh,
  Dashboard,
  ShoppingCart,
  Person,
  Flight,
  Train
} from '@mui/icons-material';
import JourneyCard from '../components/Journeycard';
import OrderCard from '../components/Ordercard';
import JourneyCardSkeleton from '../components/JourneySkeleton';
import { useNavigate } from 'react-router-dom';
import { checkAdminAccess } from '../utils/adminUtils';

const Admin = () => {
  // Using responsive breakpoints directly in the sx props
  const navigate = useNavigate();

  // Tab state
  const [tabValue, setTabValue] = useState(0);

  // Data states
  const [journeys, setJourneys] = useState([]);
  const [orders, setOrders] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [reload, setReload] = useState(false);
  const [page,setPage]=useState(1)
  const [count,setCount ] = useState()
  const [orderPage,setOrderPage]=useState(1)
  const [orderCount,setOrderCount ] = useState()

  // Using responsive breakpoints directly in the sx props

  // Dashboard stats
  const [stats, setStats] = useState({
    totalJourneys: 0,
    totalOrders: 0,
    activeJourneys: 0,
    completedOrders: 0,
    trainJourneys: 0,
    flightJourneys: 0,
    flight_inter:0,
    Users:0
  });
  console.log("stats",stats)
  // Filter states
  const [showFilters, setShowFilters] = useState(false);
  const [dateFrom, setDateFrom] = useState('');
  const [dateTo, setDateTo] = useState('');
  const [transportMode, setTransportMode] = useState("all");
  const [status, setStatus] = useState('all');
  const [source, setSource] = useState('');
  const [destination, setDestination] = useState('');

  // Animation states
  const [titleVisible, setTitleVisible] = useState(false);
  const [statsVisible, setStatsVisible] = useState(false);
  const [dataVisible, setDataVisible] = useState(false);
  const [contentVisible, setContentVisible] = useState(false);

  // Notification state
  const [message, setMessage] = useState('');
  const [showMessage, setShowMessage] = useState(false);

  // Check if user is admin
  useEffect(() => {
    // Check admin access and redirect if not admin
    const isAdmin = checkAdminAccess(navigate);
    if (!isAdmin) {
      return;
    }

    // Animation timing
    setTimeout(() => setTitleVisible(true), 100);
    setTimeout(() => setStatsVisible(true), 300);
    setTimeout(() => setDataVisible(true), 600);
    setTimeout(() => setContentVisible(true), 900);

    // Initial data fetch
    fetchData();

    // Fetch users (mock implementation - replace with actual API call)
    fetchUsers();
  }, [navigate, tabValue, page, orderPage]);

//  useEffect(()=>{
//   fetchJourneys();
//  },[page])

//  useEffect(()=>{
//   fetchOrders();
//  },[orderPage])

  // Calculate dashboard stats whenever data changes
  // useEffect(() => {
  //   if (journeys.length > 0 || orders.length > 0) {
  //     calculateStats();
  //   }
  // }, [journeys, orders]);

  const fetchData = async () => {
    setLoading(true);
    try {
      // Always fetch both journeys and orders for dashboard stats
      if (tabValue === 0) {
        await fetchJourneys();
      } else {
        await fetchOrders();
      }
    } catch (err) {
      setError(err.message);
      setMessage('Error fetching data: ' + err.message);
      setShowMessage(true);
    } finally {
      setLoading(false);
    }
  };

  // Calculate dashboard statistics
  // const calculateStats = () => {
  //   const activeJourneys = journeys.filter(journey =>
  //     ['pending', 'trip confirmed', 'in_progress'].includes(journey.journey_status)
  //   ).length;

  //   const completedOrders = orders.filter(order =>
  //     order.order_status === 'delivered'
  //   ).length;

  //   const trainJourneys = journeys.filter(journey =>
  //     journey.mode_of_transport === 'train'
  //   ).length;

  //   const flightJourneys = journeys.filter(journey =>
  //     journey.mode_of_transport === 'flight'
  //   ).length;

  //   setStats((pre)=>({...pre,
  //     activeJourneys,
  //     completedOrders,
  //     trainJourneys,
  //     flightJourneys
  //   }));
  // };

  // Mock function to fetch users - replace with actual API call
  const fetchUsers = async () => {
    try {
      const token = localStorage.getItem('authToken');

      // This is a placeholder - replace with your actual API endpoint
      const response = await fetch(`${import.meta.env.VITE_API_URL}/api/orders/details`,
        {
          method: 'GET',
          headers: {
            'Authorization': token,
            'Content-Type': 'application/json',
          },
        }
      )

      const result = await response.json();
      if(response.ok){
        console.log("Details for admin", result.details.journeyStats);
        setStats((pre) => ({
          ...pre,
          Users: result.details?.userCount,
          activeJourneys: result.details?.journeyStats?.active_status_count ?? 0,
          trainJourneys: result.details?.journeyStats?.train_count ?? 0,
          flightJourneys: result.details?.journeyStats?.flight_count ?? 0,
          totalOrders:result.details?.orderCount,
          totalJourneys:result.details?.journeyCount,
          flight_inter:result.details?.journeyStats?.flight_inter_count ?? 0
        }));
        
      }
    } catch (err) {
      console.error('Error fetching users:', err);
      // Use mock data on error
    }
  };
// console.log("date from",dateFrom);

  const fetchJourneys = async () => {
    setLoading(true)
    try {

      const filterParams = new URLSearchParams({
        start_date: dateFrom,
        end_date:dateTo,
        status:status !=='all' ? status :'',
        source,
        destination,
        mode:transportMode !== "all" ? transportMode:'',
        page
      }).toString();

      const token = localStorage.getItem('authToken');

      // Using the existing journeys API endpoint
      // We'll fetch all journeys instead of just user-specific ones
      const response = await fetch(`${import.meta.env.VITE_API_URL}/api/journeys?${filterParams}`,
        {
          method: 'GET',
          headers: {
            'Authorization': token,
            'Content-Type': 'application/json',
          },
        }
      );

      if (!response.ok) {
        throw new Error('Failed to fetch journeys');
      }

      const result = await response.json();
      console.log("result",result);
      setJourneys(Array.isArray(result.data) ? result.data : []);
      setPage(result.pagination?.page)
      setCount(result.pagination?.totalPages)
      setLoading(false)
    } catch (err) {
      console.error('Error fetching journeys:', err);
      throw err;
    }
  };

  const fetchOrders = async () => {
    setLoading(true)
    try {
      const token = localStorage.getItem('authToken');

      
      const filterParams = new URLSearchParams({
        start_date: dateFrom,
        end_date:dateTo,
        status :status !=='all' ?status :'',
        source,
        destination,
        page:orderPage
      }).toString();

      // We'll fetch all orders instead of just user-specific ones
      const response = await fetch(
        `${import.meta.env.VITE_API_URL}/api/orders?${filterParams}`,
        {
          method: 'GET',
          headers: {
            'Authorization': token,
            'Content-Type': 'application/json',
          },
        }
      );

      if (!response.ok) {
        throw new Error('Failed to fetch orders');
      }

      let result = await response.json();

      setOrders(Array.isArray(result.data) ? result.data : []);
      setOrderPage(result.pagination?.page)
      setOrderCount(result.pagination?.totalPages || 0)
      console.log("orders",stats)
      setLoading(false)
    } catch (err) {
      console.error('Error fetching orders:', err);
      throw err;
    }
  };

  const handleTabChange = (_, newValue) => {
    setTabValue(newValue);
    // Reset filters when switching tabs
    resetFilters();
    // Fetch data for the selected tab
    setReload(!reload);
  };

  const handleRefresh = () => {
    setReload(!reload);
  };

  const toggleFilters = () => {
    setShowFilters(!showFilters);
  };

  const resetFilters = () => {
    setDateFrom('');
    setDateTo('');
    setTransportMode('all');
    setStatus('all');
    setSource('');
    setDestination('');
  };

  const applyFilters = () => {
    // Don't toggle filters visibility when applying filters
    fetchData();
  };
  // Journey status options
  const journeyStatusOptions = [
    { value: 'all', label: 'All Statuses' },
    { value: 'pending', label: 'Pending' },
    { value: 'trip confirmed', label: 'Trip Confirmed' },
    // { value: 'in_progress', label: 'In Progress' },
    // { value: 'completed', label: 'Completed' },
    { value: 'trip cancelled', label: 'Trip Cancelled' },
    { value: 'Archived', label: 'Archived' },
    { value: 'order confirmed', label: 'Order Confirmed' }
  ];

  // Order status options
  const orderStatusOptions = [
    { value: 'all', label: 'All Statuses' },
    { value: 'order confirmed', label: 'Order Confirmed' },
    // { value: 'in transit', label: 'In Transit' },
    { value: 'order cancelled', label: 'Order Cancelled' },
    { value: 'Journey cancelled', label: 'Trip Cancelled' }
  ];

  return (
    <Box sx={{
      padding: { xs: '10px', sm: '15px', md: '20px' },
      maxWidth: '1600px',
      width: '100%',
      minWidth: { xs: 'auto', md: 'auto' },
      margin: '0 auto',
      overflowX: 'auto'
    }}>
      <Slide direction="down" in={titleVisible} timeout={800}>
        <Typography align='center' sx={{
          color:'#EFEFEF',
          fontWeight:'700',
          fontSize: { xs: '28px', sm: '32px', md: '40px' },
          lineHeight: { xs: '36px', sm: '40px', md: '48px' },
          position:'absolute',
          top: { xs: '80px', sm: '120px', md: '180px' },
          left: '0',
          transform: 'translateX(-50%)',
          width: '100%',
          zIndex: 1
        }}>
          Admin Dashboard
        </Typography>
      </Slide>

      {/* Dashboard Stats Section */}
      <PageAnimation type="grow" timeout={1000} delay={300} in={statsVisible}>
        <Paper elevation={2} sx={{ p: { xs: 2, sm: 3 }, mb: 4, borderRadius: '10px', width: '100%' }}>
        <Typography variant="h6" sx={{ mb: 2, fontWeight: 'bold', fontSize: { xs: '1.1rem', sm: '1.25rem' } }}>
          Dashboard Overview
        </Typography>

        <Grid container spacing={{ xs: 2, sm: 3 }} sx={{ mb: 4 }}>
          {/* Total Journeys */}
          <Grid item xs={12} sm={6} md={3}>
            <Card sx={{ bgcolor: '#f5f5f5', height: '100%', overflow: 'hidden' }}>
              <CardContent >
                <Stack direction="row" justifyContent="space-between" alignItems="center">
                  <Typography variant="subtitle1" color="text.secondary" sx={{ fontSize: { xs: '0.875rem', sm: '1rem' } }}>
                    Total Journeys
                  </Typography>
                  <Flight sx={{ color: '#DC143C' }} />
                </Stack>
                <Typography variant="h4" sx={{ mt: 2, fontWeight: 'bold', fontSize: { xs: '1.5rem', sm: '2rem' } }}>
                  {stats.totalJourneys}
                </Typography>
                <Stack direction="row" spacing={1} sx={{ mt: 1 }}>
                  <Chip
                    size="small"
                    icon={<Train sx={{ fontSize: '16px !important', color: '#DC143C !important' }} />}
                    label={`Train: ${stats.trainJourneys}`}
                    sx={{ bgcolor: 'white', borderColor: '#DC143C', color: '#DC143C' }}
                  />
                  <Chip
                    size="small"
                    icon={<Flight sx={{ fontSize: '16px !important', color: '#DC143C !important' }} />}
                    label={`Flight: ${stats.flightJourneys}`}
                    sx={{ bgcolor: 'white', borderColor: '#DC143C', color: '#DC143C' }}
                  />
                   <Chip
                    size="small"
                    icon={<Flight sx={{ fontSize: '16px !important', color: '#DC143C !important' }} />}
                    label={`Flight-Inter: ${stats.flight_inter}`}
                    sx={{ bgcolor: 'white', borderColor: '#DC143C', color: '#DC143C' }}
                  />
                </Stack>
              </CardContent>
            </Card>
          </Grid>

          {/* Total Orders */}
          <Grid item xs={12} sm={6} md={3}>
            <Card sx={{ bgcolor: '#f5f5f5', height: '100%', overflow: 'hidden',width:'210px' }}>
                <CardContent>
                  <Stack direction="row" justifyContent="space-between" alignItems="center">
                    <Typography variant="subtitle1" color="text.secondary" sx={{ fontSize: { xs: '0.875rem', sm: '1rem' } }}>
                      Total Orders
                    </Typography>
                    <ShoppingCart sx={{ color: '#DC143C' }} />
                  </Stack>
                  <Typography variant="h4" sx={{ mt: 2, fontWeight: 'bold', fontSize: { xs: '1.5rem', sm: '2rem' } }}>
                    {stats.totalOrders}
                  </Typography>
                  <Typography variant="body2" color="text.secondary" sx={{ mt: 1, fontSize: { xs: '0.75rem', sm: '0.875rem' } }}>
                    {/* {stats.completedOrders} orders delivered */}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>

            {/* Active Journeys */}
            <Grid item xs={12} sm={6} md={3}>
              <Card sx={{ bgcolor: '#f5f5f5', height: '100%', overflow: 'hidden',width:'210px' }}>
                <CardContent>
                  <Stack direction="row" justifyContent="space-between" alignItems="center">
                    <Typography variant="subtitle1" color="text.secondary" sx={{ fontSize: { xs: '0.875rem', sm: '1rem' } }}>
                      Active Journeys
                    </Typography>
                    <Dashboard sx={{ color: '#DC143C' }} />
                  </Stack>
                  <Typography variant="h4" sx={{ mt: 2, fontWeight: 'bold', fontSize: { xs: '1.5rem', sm: '2rem' } }}>
                    {stats.activeJourneys}
                  </Typography>
                  <Typography variant="body2" color="#DC143C" sx={{ mt: 1, fontSize: { xs: '0.75rem', sm: '0.875rem' } }}>
                    Currently in progress
                  </Typography>
                </CardContent>
              </Card>
            </Grid>

            {/* Users */}
            <Grid item xs={12} sm={6} md={3}>
              <Card sx={{ bgcolor: '#f5f5f5', height: '100%', overflow: 'hidden',width:'210px' }}>
                <CardContent>
                  <Stack direction="row" justifyContent="space-between" alignItems="center">
                    <Typography variant="subtitle1" color="text.secondary" sx={{ fontSize: { xs: '0.875rem', sm: '1rem' } }}>
                      Total Users
                    </Typography>
                    <Person sx={{ color: '#DC143C' }} />
                  </Stack>
                  <Typography variant="h4" sx={{ mt: 2, fontWeight: 'bold', fontSize: { xs: '1.5rem', sm: '2rem' } }}>
                    {stats.Users}
                  </Typography>
                  <Typography variant="body2" color="#DC143C" sx={{ mt: 1, fontSize: { xs: '0.75rem', sm: '0.875rem' } }}>
                    Registered users
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </Paper>
      </PageAnimation>

      {/* Data Management Section */}
      <PageAnimation type="grow" timeout={1000} delay={600} in={dataVisible}>
        <Paper elevation={2} sx={{ p: { xs: 2, sm: 3 }, mb: 4, borderRadius: '10px', width: '100%' }}>
          <Typography variant="h6" sx={{ mb: 2, fontWeight: 'bold', fontSize: { xs: '1.1rem', sm: '1.25rem' } }}>
            Data Management
          </Typography>

          <Tabs
            value={tabValue}
            onChange={handleTabChange}
            centered
            variant="scrollable"
            scrollButtons="auto"
            allowScrollButtonsMobile
            sx={{
              mb: 3,
              '& .MuiTab-root': {
                fontWeight: 'bold',
                fontSize: { xs: '14px', sm: '16px' },
                minWidth: { xs: '100px', sm: '160px' }
              },
              '& .Mui-selected': {
                color: '#DC143C'
              },
              '& .MuiTabs-indicator': {
                backgroundColor: '#DC143C'
              }
            }}
          >
            <Tab label="All Journeys" />
            <Tab label="All Orders" />
          </Tabs>

          <Stack
            direction={{ xs: 'column', sm: 'row' }}
            spacing={2}
            sx={{ mb: 2 }}
            justifyContent="space-between"
            alignItems={{ xs: 'stretch', sm: 'center' }}
          >
            <Button
              variant="outlined"
              startIcon={<FilterList />}
              onClick={toggleFilters}
              sx={{
                borderColor: '#DC143C',
                color: '#DC143C',
                '&:hover': {
                  borderColor: '#DC143C',
                  backgroundColor: 'rgba(220, 20, 60, 0.04)'
                }
              }}
            >
              {showFilters ? 'Hide Filters' : 'Show Filters'}
            </Button>

            <Button
              variant="outlined"
              startIcon={<Refresh />}
              onClick={handleRefresh}
              sx={{
                borderColor: '#DC143C',
                color: '#DC143C',
                '&:hover': {
                  borderColor: '#DC143C',
                  backgroundColor: 'rgba(220, 20, 60, 0.04)'
                }
              }}
            >
              Refresh Data
            </Button>
          </Stack>

          {showFilters && (
            <Grow in={showFilters} timeout={500}>
              <Box sx={{ mb: 3 }}>
                <Grid container spacing={{ xs: 1, sm: 1, lg: 1 }}>
                  {/* Text fields at the top */}
                  <Grid item xs={12} sm={6} md={4} lg={3}>
                    <TextField
                      type="date"
                      fullWidth
                      value={dateFrom}
                      onChange={(e) => setDateFrom(e.target.value)}
                      label="Date From"
                      InputLabelProps={{ shrink: true }}
                      sx={{
                        '& input[type="date"]::-webkit-calendar-picker-indicator': {
                          filter: 'invert(18%) sepia(92%) saturate(7456%) hue-rotate(341deg) brightness(92%) contrast(102%)',
                          cursor: 'pointer',
                        }
                      }}
                    />
                  </Grid>
                  <Grid item xs={12} sm={6} md={4} lg={3}>
                    <TextField
                      type="date"
                      fullWidth
                      value={dateTo}
                      InputLabelProps={{ shrink: true }}
                      onChange={(e) => setDateTo(e.target.value)}
                      label="Date To"
                      sx={{
                        '& input[type="date"]::-webkit-calendar-picker-indicator': {
                          filter: 'invert(18%) sepia(92%) saturate(7456%) hue-rotate(341deg) brightness(92%) contrast(102%)',
                          cursor: 'pointer',
                        }
                      }}
                    />
                  </Grid>
                  {tabValue === 0 && (
                    <Grid item xs={12} sm={6} md={4} lg={3}>
                      <FormControl fullWidth>
                        <InputLabel>Transport Mode</InputLabel>
                        <Select
                          value={transportMode}
                          placeholder={"select your journey mode"}
                          onChange={(e) => setTransportMode(e.target.value)}
                          label="Transport Mode"
                        >
                          <MenuItem value="all">All Modes</MenuItem>
                          <MenuItem value="train">Train</MenuItem>
                          <MenuItem value="flight">Flight</MenuItem>
                          <MenuItem value="flight-inter">Flight International</MenuItem>
                        </Select>
                      </FormControl>
                    </Grid>
                  )}
                  <Grid item xs={12} sm={6} md={4} lg={3}>
                    <FormControl fullWidth>
                      <InputLabel>Status</InputLabel>
                      <Select
                        value={status}
                        onChange={(e) => setStatus(e.target.value)}
                        label="Status"
                      >
                        {tabValue === 0
                          ? journeyStatusOptions.map(option => (
                              <MenuItem key={option.value} value={option.value}>
                                {option.label}
                              </MenuItem>
                            ))
                          : orderStatusOptions.map(option => (
                              <MenuItem key={option.value} value={option.value}>
                                {option.label}
                              </MenuItem>
                            ))
                        }
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xs={12} sm={6} md={3}>
                    <TextField
                      label="Source"
                      fullWidth
                      placeholder='Source'
                      InputLabelProps={{ shrink: true }}
                      value={source}
                      onChange={(e) => setSource(e.target.value)}
                    />
                  </Grid>
                  <Grid item xs={12} sm={6} md={3}>
                    <TextField
                      label="Destination"
                      fullWidth
                      placeholder='Destination'
                      InputLabelProps={{ shrink: true }}
                      value={destination}
                      onChange={(e) => setDestination(e.target.value)}
                    />
                  </Grid>

                  {/* Buttons at the bottom */}
                  <Grid className="filter-buttons" item xs={12} alignItems={'center'}>
                    <Box >
                      <Stack
                        direction={{ xs: 'column', sm: 'row' }}
                        spacing={2}
                        justifyContent="center"
                        alignItems={{ xs: 'stretch', sm: 'center' }}
                      >
                        <Button
                          variant="contained"
                          startIcon={<Search />}
                          onClick={applyFilters}
                          sx={{
                            backgroundColor: '#DC143C',
                            '&:hover': {
                              backgroundColor: '#b30000'
                            }
                          }}
                        >
                          Apply Filters
                        </Button>
                        <Button
                          variant="outlined"
                          onClick={resetFilters}
                          sx={{
                            borderColor: '#DC143C',
                            color: '#DC143C',
                            '&:hover': {
                              borderColor: '#DC143C',
                              backgroundColor: 'rgba(220, 20, 60, 0.04)'
                            }
                          }}
                        >
                          Reset Filters
                        </Button>
                      </Stack>
                    </Box>
                  </Grid>
                </Grid>
              </Box>
            </Grow>
          )}

          <Divider sx={{ mb: 3 }} />

          {loading ? (
            <Box sx={{display:'flex',alignItems:'center',justifyContent:'center'}}>
            <JourneyCardSkeleton />
            </Box>
          ) : (
            <Fade in={contentVisible} timeout={1000}>
              <Box sx={{ width: '100%', overflowX: 'auto', display: 'flex', justifyContent: 'center' }}>
                {tabValue === 0 ? (
                journeys.length > 0 ? (
                  <Box sx={{ width: { xs: '100%', sm: '100%', md: '100%' }, display: 'flex', justifyContent: 'center',flexDirection:'column',alignItems:'center' }}>
                    <JourneyCard journeyData={journeys} Mode="journey" fetchJourney={handleRefresh} />
                    <Box m={5}>
                      <Pagination page={page} count={count} onChange={(e, newPage) => setPage(newPage)}/>
                    </Box> 
                  </Box>
                ) : (
                  <Typography variant="body1" sx={{ textAlign: 'center', py: 4, fontSize: { xs: '0.875rem', sm: '1rem' } }}>
                    No journeys found. Try adjusting your filters.
                  </Typography>
                )
              ) : (
                orders.length > 0 ? (
                  <Box sx={{ width: { xs: '100%', sm: '100%', md: '100%' }, display: 'flex', justifyContent: 'center',flexDirection:'column',alignItems:'center' }}>
                    <OrderCard orderDate={orders} Mode="order" fetchOrders={handleRefresh} />
                    <Box m={5}>
                      <Pagination page={orderPage} count={orderCount} onChange={(e, newPage) => setOrderPage(newPage)}/>
                    </Box> 
                  </Box>
                ) : (
                  <Typography variant="body1" sx={{ textAlign: 'center', py: 4, fontSize: { xs: '0.875rem', sm: '1rem' } }}>
                    No orders found. Try adjusting your filters.
                  </Typography>
                )
                )}
              </Box>
            </Fade>
          )}
        </Paper>
      </PageAnimation>
      {/* <Snackbar
        open={showMessage}
        autoHideDuration={6000}
        onClose={() => setShowMessage(false)}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert
          onClose={() => setShowMessage(false)}
          severity={error ? "error" : "success"}
          sx={{ width: '100%', border: '1px solid #DC143C', borderRadius: '10px' }}
          icon={false}
        >
          {message}
        </Alert>
      </Snackbar> */}
    </Box>
  );
};

export default Admin;
