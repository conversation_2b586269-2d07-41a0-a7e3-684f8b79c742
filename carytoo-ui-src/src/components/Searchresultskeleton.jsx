import React from "react";
import { Grid, <PERSON>, Box, Stack, Typography, Skeleton } from "@mui/material";

const FlightSkeleton = () => {
  return (
    <>
    <Grid className='main-skeleton-container' sx={{justifyContent:'center'}}container spacing={2.5}>
      {[...Array(8)].map((_, index) => (
        <Grid item xs={12} md={6} key={index}>
          <Paper
            elevation={0}
            sx={{
              bgcolor: "grey.100",
              borderRadius: "10px",
              p: 2,
              height: "94px",
              position: "relative",
              margin:'15px'
            }}
          >
            <Box sx={{ display: "flex", alignItems: "center", height: "100%",justifyContent:'center' }}>
              <Stack spacing={0.5}>
                <Skeleton variant="text" width={80} height={20} />
                <Skeleton variant="text" width={100} height={25} />
              </Stack>

              <Stack spacing={0.5} sx={{ ml: 4 }}>
                <Skeleton variant="text" width={80} height={20} />
                <Skeleton variant="text" width={100} height={25} />
              </Stack>

              <Box sx={{ ml: 4, position: "relative", width: 114 }}>
                <Skeleton variant="text" width={80} height={20} />
                <Box
                  sx={{
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                    mt: 0.5,
                  }}
                >
                  <Skeleton variant="rectangular" width={100} height={5} />
                </Box>
                <Skeleton variant="text" width={40} height={20} sx={{ mt: 1 }} />
              </Box>

              <Stack spacing={0.5} sx={{ ml: 4 }}>
                <Skeleton variant="text" width={80} height={20} />
                <Skeleton variant="text" width={100} height={25} />
              </Stack>

              <Box sx={{ ml: "auto", borderRadius: "50%", bgcolor: "#FFFFFF", display: "flex", alignItems: "center" }}>
                <Skeleton variant="circular" width={30} height={30} />
              </Box>
            </Box>
          </Paper>
        </Grid>
      ))}
    </Grid>
    </>
  );
};

export default FlightSkeleton;
