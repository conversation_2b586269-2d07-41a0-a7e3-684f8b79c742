import React, { useEffect, useState } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Typography,
  Rating,
  Box,
  Stack,
  IconButton,
  Snackbar,
  Alert,
  useMediaQuery,
  useTheme,
  Select,
  MenuItem
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import Spinner from './Spinner';
import * as Yup from "yup";
import { yupResolver } from '@hookform/resolvers/yup';
import { Controller, useForm } from 'react-hook-form';

  const schema = Yup.object().shape({
    Title: Yup.string().required('Feedback title is required').notOneOf(['select'], 'Please select a valid feedback title'),
    des: Yup.string().required('Feedback is required').min(10, 'Minimum 10 characters').max(500, 'Maximum 500 characters'),
  });

  
const FeedbackPopup = ({ orderId,type,journeyId,open, onClose, title = "We'd love your feedback!",journeyUserId,feedbackValue,orderUserId,FeedTitle }) => {

  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState(false);
  const [message, setMessage] = useState('');
  console.log("feedback",feedbackValue,FeedTitle)
  const theme = useTheme();
  const fullScreen = useMediaQuery(theme.breakpoints.down('sm'));
  const token = localStorage.getItem('authToken');

    const {
    handleSubmit, register, control, reset, getValues,formState: { errors }
  } = useForm({
    defaultValues: {
      Title:  FeedTitle || 'select',
      des:'',
    },
    resolver: yupResolver(schema),
  });

  console.log("values",getValues())
  console.log("error",errors)

  const  fetchFeedbackByUserType= async() =>{
    setLoading(true);
    try {
      const response = await fetch(`${import.meta.env.VITE_API_URL}/api/feedback-master/group/${type}`, {
        method: 'GET', 
        headers: {
          'Content-Type': 'application/json',
          'Authorization': token,
        },
      });
  
      const data = await response.json();
  
      if (response.ok) {
       setData(data);
       setLoading(false);
      }
  
      console.log('Feedback List:', data);
      return data;
    } catch (error) {
      console.error('Error fetching feedback by user_type:', error.message);
      return null;
    }
  }
  
  useEffect(() => {
    fetchFeedbackByUserType();
  }, []);

  const onSubmit = async (data) => {
    
    try {
        // For regular orders
        const response = await fetch(`${import.meta.env.VITE_API_URL}/api/feedback/create`, {
          method: 'POST',
          headers: {
            'Authorization': token,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            des:data.des,
            Title:data.Title,
            journeyId: journeyId,
            orderId: orderId,
            journeyUserId: journeyUserId,
            orderUserId: orderUserId,
          }),
        });
      const request = await response.json();
      if (response.ok) {
        setMessage(request.message);
        setSuccess(true);
        
        // Reset form after successful submission
        setTimeout(() => {
          onClose();
          reset();
        }, 2000);
      } else {
        throw new Error('Failed to submit feedback');
      }
    } catch (err) {
      console.error('Error submitting feedback:', err);
      setError(true);
      setMessage('Failed to submit feedback. Please try again later.');
    } finally {
      setLoading(false);
    }
  };
  
  const handleClose = () => {
    // Reset form when closing
    reset();
    onClose();
  };
  
  return (
    <>
    
      <Dialog 
        open={open} 
        onClose={handleClose}
        fullScreen={fullScreen}
        PaperProps={{
          sx: {
            borderRadius: '10px',
            maxWidth: '500px',
            width: '100%'
          }
        }}
      >
        <DialogTitle sx={{ 
          display: 'flex', 
          justifyContent: 'space-between', 
          alignItems: 'center',
          borderBottom: '1px solid #e0e0e0',
          pb: 1
        }}>
          <Typography variant="h6" component="div" sx={{ fontWeight: 'bold', color: '#DC143C' }}>
            {title}
          </Typography>
          <IconButton 
            edge="end" 
            color="inherit" 
            onClick={handleClose} 
            aria-label="close"
          >
            <CloseIcon />
          </IconButton>
        </DialogTitle>
        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
      <Spinner/>
      </Box>
    ):(
      <>
          <form onSubmit={handleSubmit(onSubmit)}>
        <DialogContent sx={{ pt: 3 }}>
          <Stack spacing={3}>
            <Box>
                  <Typography mt={2} mb={1} variant="subtitle1" gutterBottom sx={{ fontWeight: 'medium' }}>
                 select your Feedback
              </Typography>
            <Controller
                    name="Title"
                    control={control}
                    render={({ field }) => (
                      <Select {...field} fullWidth disabled={!!feedbackValue}>
                        <MenuItem value="select">Please select your feedback</MenuItem>
                        {data.map((item) => (
                          <MenuItem key={item.id} value={item.id}>{item.title}</MenuItem>
                        ))}
                      </Select>
                    )}
                  />
                  {errors.Title && <Typography color="error" variant="caption">{errors.Title?.message}</Typography>}
            </Box>
            <Box>
              <Typography mt={2} mb={1} variant="subtitle1" gutterBottom sx={{ fontWeight: 'medium' }}>
                 your Feedback
              </Typography>
           
            
            <TextField
              multiline
              disabled={!!feedbackValue}
              rows={4}
              value={feedbackValue }
              // onChange={(e) => setFeedback(e.target.value)}
              error={!!errors.des?.message}
              helperText={errors.des?.message}
              {...register('des')}
              placeholder="Tell us what you liked or how we can improve..."
              fullWidth
              onKeyPress={(e) => {
                const regex = /^[a-zA-Z0-9 ]+$/;
                if (!regex.test(e.key)) {
                  e.preventDefault();
                }
              }}
              inputProps={{ maxLength: 500 }}
              variant="outlined"
            />
             </Box>
            
          </Stack>
        </DialogContent>
        
        <DialogActions sx={{ px: 3, pb: 3 }}>
          {feedbackValue ?(
            <>
            <Button 
            onClick={handleClose} 
            color="inherit"
            sx={{ 
              borderRadius: '8px',
              textTransform: 'none',
              fontWeight: 'medium'
            }}
          >
            Close
          </Button></>
          ):(
            <>
          <Button 
            onClick={handleClose} 
            color="inherit"
            sx={{ 
              borderRadius: '8px',
              textTransform: 'none',
              fontWeight: 'medium'
            }}
          >
            Cancel
          </Button>
          <Button 
            type='submit'
            variant="contained"
            disabled={loading}
            sx={{ 
              bgcolor: '#DC143C',
              '&:hover': {
                bgcolor: '#b01030',
              },
              borderRadius: '8px',
              textTransform: 'none',
              fontWeight: 'medium'
            }}
          >
            {loading ? 'Submitting...' : 'Submit Feedback'}
          </Button></>)}
        </DialogActions>
        </form>
        </>)}
      </Dialog>

       <Snackbar anchorOrigin={{ horizontal: 'right', vertical: 'top' }} open={success || error} onClose={() => {setSuccess(false);setError(false);}}
              autoHideDuration={1000}>
              <Alert icon={false} onClose={() => {setSuccess(false);setError(false);}} sx={{ background: '#EFEFEF', width: '100%', border: '1px solid #DC143C', borderRadius: '10px', color: "#000000" }}>
                {message}
              </Alert>
            </Snackbar>
    </>
  );
};

export default FeedbackPopup;
