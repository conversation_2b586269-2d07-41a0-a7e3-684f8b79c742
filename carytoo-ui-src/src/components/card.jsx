import TimeIcon from "@mui/icons-material/AccessTime";
import ArrowForwardIcon from "@mui/icons-material/ArrowForward";
import DeleteIcon from "@mui/icons-material/Delete";
import CoachIcon from "@mui/icons-material/DirectionsRailway";
import TrainIcon from "@mui/icons-material/DirectionsTransit";
import PersonOutlineOutlinedIcon from '@mui/icons-material/PersonOutlineOutlined';
import PhoneOutlinedIcon from '@mui/icons-material/PhoneOutlined';
import EditIcon from "@mui/icons-material/Edit";
import SeatIcon from "@mui/icons-material/EventSeat";
import FlightTakeoffOutlinedIcon from '@mui/icons-material/FlightTakeoffOutlined';
import { Box, Button, Divider, IconButton, Stack, Typography } from '@mui/material'
import React, { useState } from 'react'
import './css/Journeycard.css'
import ProgressBar from "./Progressbar";
import WhatsAppIcon from '@mui/icons-material/WhatsApp';
import JourneyNoData from "./journeynodata";


const Mycard = (Data) => {
  const Mode = 'order'
  const newArray = Array.isArray(Data) ? Data : [Data];

  if(!Data){
     <JourneyNoData/>
     return;
  }

  const data = [
    {
      "order_id": 10,
      "journey_id": 9,
      "user_id": 2,
      "package_category": 3,
      "price": "100.00",
      "order_description": "hxdhdferhiuh",
      "payment_status": "paid",
      "order_status": "trip cancelled",
      "created_at": "2025-04-07T05:29:10.809Z",
      "updated_at": "2025-04-07T05:29:10.809Z",
      "Journey": {
          "id": 9,
          "user_id": 1,
          "start_date": "2025-04-09",
          "end_date": "2025-04-12",
          "source": "LSW",
          "destination": "CAN",
          "mode_of_transport": "train",
          "train_number": "DD123",
          "coach_number": "M13",
          "seat_number": "5896",
          "flight_number": null,
          "departure_time": "23:06:00",
          "arrival_time": "11:06:00",
          "journey_status": "Archived",
          "order_id": null,
          "ticket_image_url": "https://carytoo-s3.s3.ap-south-1.amazonaws.com/uploads/1743744980517_03%20%2845%29.jpg",
          "createdAt": "2025-04-04T05:36:22.338Z",
          "updatedAt": "2025-04-07T06:49:48.282Z",
          "user": {
              "username": "prathap",
              "phoneNumber": "6374550093",
              "email": "<EMAIL>"
          }
      }
  },
  {
      "order_id": 9,
      "journey_id": 10,
      "user_id": 2,
      "package_category": 1,
      "price": "100.00",
      "order_description": "zdflgdl",
      "payment_status": "paid",
      "order_status": "order confirm",
      "created_at": "2025-04-04T12:38:55.804Z",
      "updated_at": "2025-04-04T12:38:55.804Z",
      "Journey": {
          "id": 10,
          "user_id": 1,
          "start_date": "2025-04-30",
          "end_date": "2025-05-01",
          "source": "LAW",
          "destination": "CAN",
          "mode_of_transport": "flight",
          "train_number": null,
          "coach_number": null,
          "seat_number": null,
          "flight_number": "{\"flight_number1\":\"78953\",\"flight_number2\":\"66523\",\"flight_number3\":\"\"}",
          "departure_time": "18:03:00",
          "arrival_time": "06:03:00",
          "journey_status": "order conformed",
          "order_id": null,
          "ticket_image_url": "https://carytoo-s3.s3.ap-south-1.amazonaws.com/uploads/1743770233010_4K%20WALLPAPER.jpeg",
          "createdAt": "2025-04-04T12:37:13.428Z",
          "updatedAt": "2025-04-07T06:49:20.990Z",
          "user": {
              "username": "prathap",
              "phoneNumber": "6374550093",
              "email": "<EMAIL>"
          }
      }
  },
  ]
 console.log("user",data[0].Journey.user.username)
  
  const [expandedCards, setExpandedCards] = useState('');
  function formatDate(inputDate) {
    const dateObj = new Date(inputDate);
    if (isNaN(dateObj)) return "Invalid Date";

    const yearShort = String(dateObj.getFullYear()).slice(-2); // Last two digits of the year
    const monthShort = dateObj.toLocaleString('en-US', { month: 'short' }); // Short month name
    const day = String(dateObj.getDate()).padStart(2, '0'); // Day with leading zero

    return `${yearShort} ${monthShort} ${day}`;
  }

  const formateFlightNumber = (flightNumber) => {
    const flightNumbers = JSON.parse(flightNumber);
    return flightNumbers.flight_number1;
  }

  const handleClick = (order_id) => {
    setExpandedCards((prev) => ({
      ...prev,
      [order_id]: !prev[order_id],
    }));
  };

  return (
    <Box width={'fit-content'} sx={{ display: 'flex', flexDirection: 'column',gap:'25px' }}>
      {data.map((trip, index) => (
        <Box key={index} sx={{ paddingTop:'10px', display: 'flex', flexDirection: 'column',background: '#f5f5f5', border: '2px solid #E5E5E5', borderRadius: '15px', padding: '16px', }}>
        <Box className='journey-card-map-body'  sx={{ display: 'flex', flexDirection: 'row',gap:'80px'  }}>
          <Stack sx={{ display: 'flex', flexDirection: 'column' }}>
            <Typography variant="h6" fontWeight={700} sx={{ fontSize: '24px' }}>
              {formatDate(trip.Journey.start_date)}
            </Typography>

            <Stack direction="row" sx={{ mt: 1, mr: 2, width: '100%' }}>
              {/* Train details section */}
              <Stack spacing={2} sx={{ width: 200 }}>
                <Stack direction="row" spacing={1} alignItems="center">
                    {trip.Journey.train_number ? <TrainIcon /> : <FlightTakeoffOutlinedIcon />}
                  <Typography variant="subtitle1" fontWeight={600}>
                    {trip.train_number ? "Train No" : "Flight No"}:
                    {/* {tripData.train_number || (tripData.flightNumbers ? JSON.parse(tripData.flightNumbers).flightNumber1 : '')} */}
                    {trip.Journey.train_number || formateFlightNumber(trip.Journey.flight_number)}
                  </Typography>
                </Stack>

                {(trip.Journey.train_number) ? (
                  <>
                    <Stack direction="row" spacing={1} alignItems="center">
                      <SeatIcon />
                      <Typography variant="subtitle1" fontWeight={600}>
                        Seat No: {trip.Journey.seat_number}
                      </Typography>
                    </Stack>

                    <Stack direction="row" spacing={1} alignItems="center">
                      <CoachIcon />
                      <Typography variant="subtitle1" fontWeight={600}>
                        Coach No: {trip.Journey.coach_number}
                      </Typography>
                    </Stack>
                    <Stack >
                    </Stack>
                  </>
                ) : (null)}
              </Stack>
            </Stack>

          </Stack>
          <Stack direction="column" >
          <Divider orientation="vertical" sx={{ pr: 1, height: "100%",color:'E5E5E5E' }} /></Stack>  

         <Stack direction={"row"} spacing={8} pt={1} width={600}>
          <Stack direction="column" spacing={2} alignItems="center">
            <Stack spacing={1}>

              {/* Source Title */}
              <Typography variant="h6" fontWeight={700}>
                {trip.Journey.source}
              </Typography>

              {/* Source Info (Body) */}
              <Stack  >
                <Typography variant="body1">
                  {trip.Journey.source}
                </Typography>
              </Stack>

              {/* Departure Time */}
              <Stack direction="row" spacing={1} alignItems="center">
                <TimeIcon sx={{ color: "#DC143C" }} />
                <Typography variant="body1" fontWeight={700}>
                  {trip.Journey.departure_time}
                </Typography>
              </Stack>

            </Stack>
          </Stack>

          <ArrowForwardIcon sx={{ mx: 3, my: 'auto' }}/>
          
          <Stack  direction="column" spacing={2} alignItems="center">
            <Stack spacing={1}>

              {/* Source Title */}
              <Typography variant="h6" fontWeight={700}>
                {trip.Journey.destination}
              </Typography>

              {/* Source Info (Body) */}
              <Stack  >
                <Typography variant="body1">
                  {trip.Journey.destination}
                </Typography>
              </Stack>

              {/* Departure Time */}
              <Stack direction="row" spacing={1} alignItems="center">
                <TimeIcon sx={{ color: "#DC143C" }} />
                <Typography variant="body1" fontWeight={700}>
                  {trip.Journey.arrival_time}
                </Typography>
              </Stack>

            </Stack>
          </Stack>
          </Stack>
    <Stack direction={'column'} spacing={5}>
               <Stack direction="row" height={'fit-content'} spacing={2}>
                  <Button 
                  onClick={() => handleDelete(trip.id)} 
                  startIcon={<DeleteIcon />} sx={{ color: "black" }}>
                    Delete
                  </Button>
                  <Button onClick={() =>console.log(trip.id)} startIcon={<EditIcon />} sx={{ color: "black" }}>
                    SupportCall
                  </Button>
                  {/* <Typography>your journey</Typography> */}
                </Stack>

                <Typography onClick={() => handleClick(trip.order_id)} sx={{ cursor: 'pointer' }}>{expandedCards[ trip.id] ? "View Less" : "View More"}</Typography>
                </Stack>      
        </Box>
        {expandedCards[trip.order_id] && (
              <Box sx={{ display: 'flex' }}>
            
              { trip.Journey.user ? (
              <Box sx={{ backgroundColor: '#FFFFFF', width: 'fit-content', padding: 2, borderRadius: '15px', gap: '10px' }}>
                 <Stack direction="row" spacing={2} sx={{alignItems:'center'}}>
                  {/* <IconButton disabled={true}> */}
                  <PersonOutlineOutlinedIcon  />
                  {/* </IconButton> */}
                  <Typography>{trip.Journey.user?.username || 'N/A'}</Typography>
                </Stack>
                <Stack direction="row" spacing={2} sx={{alignItems:'center'}}>
                  <IconButton
                  sx={{padding:'0px !important'}}
                   href={`https://wa.me/${trip.Journey.user?.phoneNumber}`}
                  >
                  <WhatsAppIcon />
                  </IconButton>
                  <Typography
                  onClick={() =>`tel:${trip.Journey.user?.phoneNumber}`}
                  >  {trip.Journey.user?.phoneNumber || 'N/A'}</Typography>
                </Stack>
              </Box>
               ) :(null)} 
               <ProgressBar mode={Mode} status={trip.order_status} id={trip.id} />
              </Box>
            )}
        </Box>
      ))}
    </Box>
  )
}

export default Mycard