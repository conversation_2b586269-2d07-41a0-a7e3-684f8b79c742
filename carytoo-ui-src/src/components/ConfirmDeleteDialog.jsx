import React from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  Button,
} from '@mui/material';

const ConfirmDeleteDialog = ({
  open,
  title = "Confirm Delete",
  message,
  onClose,
  onConfirm,
}) => {
  return (
    <Dialog   BackdropProps={{
      style: {
        backgroundColor: 'transparent',
        boxShadow: 'none',
        
      },
    }} open={open} onClose={onClose}>
      <DialogTitle>{title}</DialogTitle>
      <DialogContent>
        <DialogContentText>{message}</DialogContentText>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose} color="primary" variant="outlined">
          Cancel
        </Button>
        <Button onClick={()=>{onConfirm();onClose();}} color="error" variant="contained">
          Delete
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default ConfirmDeleteDialog;
