import * as React from "react";
import { styled } from "@mui/material/styles";
import { <PERSON>, <PERSON>ack, Stepper, Step, StepLabel, StepConnector, useTheme, useMediaQuery, Typography } from "@mui/material";
import StepIcon from "@mui/material/StepIcon"; // Import MUI's StepIcon
import CancelIcon from '@mui/icons-material/Cancel';

// Custom Connector with transition effect and responsive styling
const CustomConnector = styled(StepConnector)(({ orientation }) => ({
  [`& .MuiStepConnector-line`]: {
    borderColor: "#e0e0e0",
    borderTopWidth: 3,
    transition: "all 0.3s ease-in-out",
    ...(orientation === 'vertical' && {
      minHeight: 40,
      borderLeftWidth: 3,
      marginLeft: 1,
      borderTopWidth: 0,
    }),
  },
  [`&.Mui-active .MuiStepConnector-line`]: {
    borderColor: "#DC143C",
  },
  [`&.Mui-completed .MuiStepConnector-line`]: {
    borderColor: "#DC143C",
  },
}));

// Custom StepIcon component using MUI's default StepIcon for completed state
const CustomStepIcon = (props) => {
  const { active, completed, canceled, icon } = props;
  
  if (canceled) {
    return <CancelIcon style={{ color: '#DC143C' }} />;
  }
  
  if (completed) {
    // Use MUI's default StepIcon with completed state
    return <StepIcon completed={true} icon={icon} style={{ color: '#DC143C' }} />;
  }
  
  return (
    <div
      style={{
        backgroundColor: active ? '#DC143C' : '#e0e0e0',
        width: 24,
        height: 24,
        borderRadius: '50%',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
      }}
    />
  );
};

// Custom StepLabel for better mobile display
const CustomStepLabel = styled(StepLabel)(({ theme, active, completed, canceled, orientation }) => ({
  '& .MuiStepLabel-label': {
    fontWeight: active ? 600 : 400,
    fontSize: orientation === 'vertical' ? '0.875rem' : '1rem',
    color: active ? '#DC143C' : canceled ? '#DC143C' : theme.palette.text.primary,
    transition: 'all 0.3s ease',
    ...(completed && {
      color: '#DC143C',
    }),
    [theme.breakpoints.down('sm')]: {
      fontSize: orientation === 'vertical' ? '0.875rem' : '0.75rem',
      lineHeight: 1.2,
      marginTop: orientation === 'horizontal' ? '4px' : 0,
    },
  },
  '& .MuiStepLabel-iconContainer': {
    transition: 'all 0.3s ease',
    [theme.breakpoints.down('sm')]: {
      paddingRight: orientation === 'vertical' ? '8px' : '0',
    },
  },
  '& .MuiStepIcon-root': {
    color: '#e0e0e0',
    transition: 'all 0.3s ease',
    [theme.breakpoints.down('sm')]: {
      fontSize: '1.25rem',
    },
  },
  '& .MuiStepIcon-root.Mui-active': {
    color: '#DC143C',
  },
  '& .MuiStepIcon-root.Mui-completed': {
    color: '#DC143C',
  },
}));

// Function to get progress steps dynamically
const getProgressSteps = (mode, status) => {
  if (mode === "journey") {
    const steps = [
      { label: "Journey Posted", key: "trip confirmed" },
      { label: "Order Received", key: "order confirmed" },
    ];

    if (status === "order canceled") {
      steps.push({ label: "Order Canceled", key: "order canceled" });
    }
    return steps;
  } else if (mode === "order") {
    const steps = [
      { label: "Ordered", key: "order placed" },
      { label: "Assigned", key: "order confirmed" }
    ];
    if (status === "trip cancelled") {
      steps.push({ label: "Cancelled", key: "trip cancelled" });
    }

    return steps;
  }
  return [];
};

// Main Component
const ProgressStepper = ({ mode, status }) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));
  const progressSteps = getProgressSteps(mode, status);
  const activeStep = progressSteps.findIndex((step) => step.key === status);
  const safeActiveStep = activeStep === -1 ? 0 : activeStep;
  
  // Check if current status is a cancellation
  const isCancelled = status === "order canceled" || status === "trip cancelled";

  // Determine orientation based on screen size and number of steps
  const useVertical = isMobile && progressSteps.length > 2;
  const orientation = useVertical ? "vertical" : "horizontal";

  return (
    <Box
      className='progressbar-main'
      sx={{
        width: "100%",
        display: "flex",
        justifyContent: "end",
        mt: isMobile ? 2 : 3,
        px: isMobile ? 1 : 0
      }}>
      <Stack
        className='progressbar-main-body' 
        sx={{
          width: "100%",
          maxWidth: useVertical ? "100%" : "md",
          mx: "auto",
          marginLeft: '0px !important',
          marginRight: '0px !important',
        }}>
        {/* Mobile title for vertical orientation */}
        {useVertical && (
          <Typography
            variant="subtitle1"
            sx={{
              mb: 1,
              fontWeight: 600,
              textAlign: "center",
              color: theme.palette.text.secondary
            }}
          >
            {mode === "journey" ? "Journey Status" : "Order Status"}
          </Typography>
        )}

        <Stepper
          orientation={orientation}
          alternativeLabel={!useVertical}
          activeStep={safeActiveStep}
          connector={<CustomConnector orientation={orientation} />}
          sx={{
            '.MuiStepConnector-root': {
              marginLeft: useVertical ? 0 : undefined,
              marginTop: useVertical ? 0 : undefined,
            },
            '.MuiStep-root': {
              padding: useVertical ? '8px 0' : undefined,
            }
          }}
        >
          {progressSteps.map((step, index) => {
            // Determine if this step is a cancellation step
            const isCancelStep = step.key === "order canceled" || step.key === "trip cancelled";
            
            return (
              <Step 
                key={step.key} 
                completed={!isCancelStep && index <= safeActiveStep}
              >
                <CustomStepLabel
                  active={index === safeActiveStep}
                  completed={!isCancelStep && index < safeActiveStep}
                  canceled={isCancelStep}
                  orientation={orientation}
                  status={step.key}
                  StepIconComponent={(iconProps) => 
                    <CustomStepIcon 
                      {...iconProps} 
                      canceled={isCancelStep && index === safeActiveStep}
                    />
                  }
                >
                  {step.label}
                </CustomStepLabel>
              </Step>
            );
          })}
        </Stepper>
      </Stack>
    </Box>
  );
};

export default ProgressStepper;