import { Box, Stack, Typography, Skeleton, Di<PERSON>r, Button } from "@mui/material";
import ArrowForwardIcon from '@mui/icons-material/ArrowForward';

const JourneyCardSkeleton = ({ isMobile = false }) => {
  return (
    <Box width={'fit-content'} sx={{ display: 'flex', flexDirection: 'column', gap: '25px' }}>
      {[...Array(5)].map((_, index) => (
        <Box
          key={index}
          sx={{
            paddingTop: '10px',
            display: 'flex',
            flexDirection: 'column',
            background: '#f5f5f5',
            border: '2px solid #E5E5E5',
            borderRadius: '15px',
            padding: '16px',
          }}
        >
          <Box
            className="journey-card-map-body"
            sx={{
              display: 'flex',
              flexDirection: isMobile ? 'column' : 'row',
              gap: isMobile ? '20px' : '80px',
            }}
          >
            {/* Left Card Section */}
            <Stack sx={{ display: 'flex', flexDirection: 'column' }}>
              <Stack direction="row" sx={{ mt: 1, mr: 2, width: '100%' }}>
                <Stack spacing={2} sx={{ width: 200 }}>
                  <Skeleton variant="text" width={100} height={30} />
                  <Skeleton variant="text" width={140} height={24} />
                  <Skeleton variant="text" width={120} height={24} />
                  <Skeleton variant="text" width={120} height={24} />
                </Stack>

                {/* Buttons (Mobile) */}
                <Stack className="icon-mobile-view" direction="column" spacing={1}>
                  <Skeleton variant="rectangular" width={80} height={36} />
                  <Skeleton variant="rectangular" width={80} height={36} />
                  <Skeleton variant="text" width={70} />
                </Stack>
              </Stack>
            </Stack>

            <Stack direction="column">
              <Divider orientation="vertical" sx={{ pr: 1, height: "100%", color: 'E5E5E5E' }} />
            </Stack>

            {/* Source - Arrow - Destination Section */}
            <Stack direction={"row"} spacing={8} pt={1} mb={'15px'} width={isMobile ? '98%' : 540}>
              <Stack direction="column" spacing={2} alignItems="center">
                <Skeleton variant="text" width={80} />
                <Skeleton variant="text" width={100} />
                <Skeleton variant="text" width={100} />
              </Stack>

              {/* <ArrowForwardIcon sx={{ mx: 3, my: 'auto' }} /> */}

              <Stack direction="column" spacing={2} alignItems="center">
                <Skeleton variant="text" width={80} />
                <Skeleton variant="text" width={100} />
                <Skeleton variant="text" width={100} />
              </Stack>
            </Stack>

            {/* Buttons (Web) */}
            <Stack className="icon-web-view" direction="column" spacing={5}>
              <Stack direction="row" height={'fit-content'} spacing={2}>
                <Skeleton variant="rectangular" width={80} height={36} />
                <Skeleton variant="rectangular" width={80} height={36} />
              </Stack>
              <Skeleton variant="text" width={70} />
            </Stack>
          </Box>

          {/* Expanded Section (Simulated) */}
         
        </Box>
      ))}
    </Box>
  );
};

export default JourneyCardSkeleton;
