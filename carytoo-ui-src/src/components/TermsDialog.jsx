import React, { useState } from 'react';
import { 
  <PERSON>alog, 
  DialogTitle, 
  DialogContent, 
  DialogActions, 
  Button, 
  Typography, 
  Box, 
  Tabs, 
  Tab, 
  List, 
  ListItem, 
  ListItemIcon, 
  ListItemText,
  IconButton,
  useTheme,
  useMediaQuery,
  Divider
} from '@mui/material';
import { Close, ArrowRight, Train, Flight, Info } from '@mui/icons-material';
import { termData } from '../utils/Terms';

/**
 * Terms and Conditions Dialog Component
 * 
 * @param {Object} props - Component props
 * @param {boolean} props.open - Whether the dialog is open
 * @param {Function} props.onClose - Function to close the dialog
 * @param {string} props.initialCategory - Initial category to display (Train, Flight, or General)
 * @returns {React.ReactElement} Terms and conditions dialog
 */
const TermsDialog = ({ open, onClose, initialCategory = 'Train' }) => {
  const theme = useTheme();
  const fullScreen = useMediaQuery(theme.breakpoints.down('sm'));
  const [activeTab, setActiveTab] = useState(
    initialCategory === 'Train' ? 0 : initialCategory === 'Flight' ? 1 : 2
  );

  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  // Get the icon for each category
  const getCategoryIcon = (category) => {
    switch (category) {
      case 'Train':
        return <Train sx={{ color: '#DC143C' }} />;
      case 'Flight':
        return <Flight sx={{ color: '#DC143C' }} />;
      case 'General':
        return <Info sx={{ color: '#DC143C' }} />;
      default:
        return <Info sx={{ color: '#DC143C' }} />;
    }
  };

  return (
    <Dialog
      open={open}
      // onClose={onClose}
      fullScreen={fullScreen}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: fullScreen ? 0 : '12px',
          maxHeight: fullScreen ? '100%' : '80vh',
        }
      }}
    >
      <DialogTitle sx={{ 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'center',
        bgcolor: '#f5f5f5',
        borderBottom: '1px solid #e0e0e0',
        py: 2
      }}>
        <Typography variant="h6" component="div" sx={{ fontWeight: 600, color: '#DC143C' }}>
          Terms & Conditions
        </Typography>
        {/* <IconButton edge="end" color="inherit" onClick={onClose} aria-label="close">
          <Close />
        </IconButton> */}
      </DialogTitle>

      <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
        <Tabs 
          value={activeTab} 
          onChange={handleTabChange} 
          variant={fullScreen ? "fullWidth" : "standard"}
          centered={!fullScreen}
          sx={{ 
            '& .MuiTab-root': { 
              fontWeight: 500,
              fontSize: fullScreen ? '0.875rem' : '1rem',
              textTransform: 'none'
            },
            '& .Mui-selected': { 
              color: '#DC143C',
              fontWeight: 600
            },
            '& .MuiTabs-indicator': { 
              backgroundColor: '#DC143C' 
            }
          }}
        >
          <Tab 
            label="Train" 
            icon={<Train />} 
            iconPosition="start"
            sx={{ minHeight: fullScreen ? 48 : 64 }}
          />
          <Tab 
            label="Flight" 
            icon={<Flight />} 
            iconPosition="start"
            sx={{ minHeight: fullScreen ? 48 : 64 }}
          />
          <Tab 
            label="General" 
            icon={<Info />} 
            iconPosition="start"
            sx={{ minHeight: fullScreen ? 48 : 64 }}
          />
        </Tabs>
      </Box>

      <DialogContent sx={{ p: fullScreen ? 2 : 3 }}>
        {termData.map((category, index) => (
          <Box key={category.category} sx={{ display: activeTab === index ? 'block' : 'none' }}>
            <Typography 
              variant="h6" 
              gutterBottom 
              sx={{ 
                fontWeight: 600, 
                color: '#DC143C',
                display: 'flex',
                alignItems: 'center',
                gap: 1,
                mb: 2
              }}
            >
              {getCategoryIcon(category.category)}
              {category.category} Terms
            </Typography>
            
            <Divider sx={{ mb: 2 }} />
            
            <List disablePadding>
              {category.items.map((item, itemIndex) => (
                <ListItem 
                  key={itemIndex} 
                  alignItems="flex-start"
                  sx={{ 
                    py: 1,
                    px: 0,
                    borderBottom: itemIndex < category.items.length - 1 ? '1px solid #f0f0f0' : 'none'
                  }}
                >
                  <ListItemIcon sx={{ minWidth: 36 }}>
                    <ArrowRight sx={{ color: '#DC143C' }} />
                  </ListItemIcon>
                  <ListItemText 
                    primary={item} 
                    primaryTypographyProps={{ 
                      variant: 'body2',
                      sx: { 
                        fontWeight: 400,
                        lineHeight: 1.6
                      }
                    }}
                  />
                </ListItem>
              ))}
            </List>
          </Box>
        ))}
      </DialogContent>

      <DialogActions sx={{ 
        p: 2, 
        borderTop: '1px solid #e0e0e0',
        justifyContent: 'center'
      }}>
        <Button 
          onClick={onClose} 
          variant="contained"
          sx={{ 
            bgcolor: '#DC143C',
            color: 'white',
            px: 4,
            '&:hover': {
              bgcolor: '#b30000',
            }
          }}
        >
          I Understand
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default TermsDialog;
