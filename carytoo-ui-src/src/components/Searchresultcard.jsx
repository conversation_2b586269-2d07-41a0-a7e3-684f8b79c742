import ArrowForwardIcon from "@mui/icons-material/ArrowForward";
import ChevronRightIcon from "@mui/icons-material/ChevronRight";
import { Box, Button, Dialog, DialogContent, Grid, Paper, Stack, Typography, useMediaQuery, useTheme } from "@mui/material";
import React, { useState } from "react";
import PopupResult from "../components/Popupresult";
import SearchNoData from "../components/SearchNodata";
import './css/searchresultcard.css';
import PopupDialog from "./Popupdilog";
import { useNavigate } from "react-router-dom";

const FlightDetailsSection = ({SearchData,TransPort}) => {
("search data",SearchData)
  const [selectedFlight, setSelectedFlight] = useState(null);
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("md"));
  const navigate = useNavigate();
  const [popupOpen, setPopupOpen] = useState({orderModel:false,onSuccess:false});
  
  function removeSeconds(timeString) {
    // Check if the input is in the expected format
    if (!timeString || typeof timeString !== 'string') {
      console.error("time is not a string")
      return timeString;
    }
    // Split the time string by colon
    const parts = timeString.split(':');
    // If the string has at least 2 parts (hours and minutes), return only those parts
    if (parts.length >= 2) {
      return `${parts[0]}:${parts[1]}`;
    }
    // If the format is unexpected, return the original string
    return timeString;
  }

  const convertTimeTo12HourFormat = (timeString) => {
    const [hours, minutes] = timeString.split(":").map(Number);
    const period = hours >= 12 ? "PM" : "AM";
    const formattedHours = hours % 12 || 12; // Convert 0-23 to 12-hour format
    return `${formattedHours}:${minutes.toString().padStart(2, "0")} ${period}`;
  };

  const handleOpen = (flight) => {
    // if (!user) {
    //   alert("Please login to book a journey");
    //   return;
    // }
    setSelectedFlight(flight);
    setPopupOpen((prev) => ({ ...prev, orderModel: true }));
    // setOpen(true);
  };

  const handleClose = (onSuccess) => {
  
    setPopupOpen((prev) => ({ ...prev, orderModel: false }));
    setSelectedFlight(null);
    
  };

  if (!SearchData || !SearchData.journeys || SearchData.journeys.length === 0) {
    return <SearchNoData Mode={TransPort}/>;
  }
  
  return (
    <Box sx={{ width: "100%", mt: 4, mb: 4 }}>
      <Grid container spacing={2.5}>
        {SearchData.journeys.map((flight, index) => (
          <Grid item xs={12} md={6} key={index}>
            <Paper
              elevation={0}
              sx={{
                bgcolor: "grey.100",
                borderRadius: "10px",
                p: 2,
                height: isMobile ? "100%" : "94px",
                position: "relative",
              }}
            >
              <Box
              className='search-result-web-card'
                sx={{ display: "flex", alignItems: "center", height: "100%" }}
              >
                <Stack spacing={0.5}>
                  <Typography variant="body2" color="text.primary">
                  {flight.train_number ? 'Train No':'Flight No'}
                  </Typography>
                  <Typography variant="subtitle1" fontWeight={700}>
                  {flight.train_number ||
                  (() => {
                    try {
                      const parsedOnce = JSON.parse(flight.flight_number); // Removes outer string
                      const parsedTwice = JSON.parse(parsedOnce); // Parses actual JSON
                      return parsedTwice.flight_number1 || "";
                    } catch {
                      return "";
                    }
                  })()}
                  </Typography>
                </Stack>

                <Stack spacing={0.5} sx={{ ml: 4 }}>
                  <Typography variant="body2" color="text.primary">
                    {flight.source}
                  </Typography>
                  <Typography variant="subtitle1" fontWeight={700}>
                    {removeSeconds(flight.departure_time) || ''}
                  </Typography>
                </Stack>

                <Box sx={{ ml: 4, position: "relative", }}>
                  <Typography variant="body1" sx={{ textAlign: "center" }}>
                    {flight.duration || ''}
                  </Typography>
                  <Box
                    sx={{
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                      mt: 0.5,
                    }}
                  >
                    <Box
                      sx={{
                        display: "flex",
                        alignItems: "center",
                        width: "100%",
                      }}
                    >
                      <Box sx={{ flex: 1, height: 1, bgcolor: "grey.400" }} />
                      <ArrowForwardIcon color="action" />
                    </Box>
                  </Box>
                  <Typography
                    variant="body2"
                    fontWeight={700}
                    sx={{
                      position: "absolute",
                      bottom: -20,
                      left: "50%",
                      transform: "translateX(-50%)",
                    }}
                  >
                    {flight.nextDay ? "(+1D)" : ""}
                  </Typography>
                </Box>

                <Stack spacing={0.5} sx={{ ml: 4,mr: 4 }}>
                  <Typography variant="body2" color="text.primary">
                  {flight.destination}
                  </Typography>
                  <Typography variant="subtitle1" fontWeight={700}>
                    {removeSeconds(flight.arrival_time) || ''}
                  </Typography>
                </Stack>

                <Box sx={{ ml: "auto", borderRadius:'62%',bgcolor:'#FFFFFF',display:'flex',alignItems:'center' }}>
                  <ChevronRightIcon  onClick={() => handleOpen(flight)} />
                </Box>
              </Box>

              <Box className='search-result-mobile-card'
                sx={{ display: "flex", alignItems: "center", height: "100%", }}
              >
                {/* <Stack spacing={0.5}>
                  <Typography variant="body2" color="text.primary">
                  {flight.train_number ? 'Train No':'Flight No'}
                  </Typography>
                  <Typography variant="subtitle1" fontWeight={700}>
                  {flight.train_number ||
                  (() => {
                    try {
                      const parsedOnce = JSON.parse(flight.flight_number); // Removes outer string
                      const parsedTwice = JSON.parse(parsedOnce); // Parses actual JSON
                      return parsedTwice.flight_number1 || "";
                    } catch {
                      return "";
                    }
                  })()}
                  </Typography>
                </Stack> */}
  <Stack direction={'row'}>
                <Stack spacing={0.5} sx={{ ml: 0 }}>
                  <Typography variant="body2" color="text.primary">
                    Start Time 
                  </Typography>
                  <Typography variant="subtitle1" fontWeight={700}>
                    {removeSeconds(flight.departure_time || '')}
                  </Typography>
                </Stack>

                <Box className='main-arrow' sx={{ ml: 4, position: "relative", width:isMobile? 14 : 114 }}>
                  <Typography variant="body1" sx={{ textAlign: "center" }}>
                    {flight.duration || ''}
                  </Typography>
                  <Box
                    sx={{
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                      mt: 0.5,
                    }}
                  >
                    <Box
                      sx={{
                        display: "flex",
                        alignItems: "center",
                        width: "100%",
                      }}
                    >
                      <Box sx={{  bgcolor: "grey.400" }} />
                      <ArrowForwardIcon color="action" />
                    </Box>
                  </Box>
                  <Typography
                    variant="body2"
                    fontWeight={700}
                    sx={{
                      position: "absolute",
                      bottom: -20,
                      left: "50%",
                      transform: "translateX(-50%)",
                    }}
                  >
                    {flight.nextDay ? "(+1D)" : ""}
                  </Typography>
                </Box>

                <Stack spacing={0.5} sx={{ ml: 4 }}>
                  <Typography variant="body2" color="text.primary">
                    Arrival Time
                  </Typography>
                  <Typography variant="subtitle1" fontWeight={700}>
                    {removeSeconds(flight.arrival_time || '')}
                  </Typography>
                </Stack>
                </Stack>

                <Stack mt={2} direction={'row'} justifyContent={'space-between'} width={'100%'}>

                   <Stack spacing={0.5}>
                  <Typography variant="body2" color="text.primary">
                  {flight.train_number ? 'Train No':'Flight No'}
                  </Typography>
                  <Typography variant="subtitle1" fontWeight={700}>
                  {flight.train_number ||
                  (() => {
                    try {
                      const parsedOnce = JSON.parse(flight.flight_number); // Removes outer string
                      const parsedTwice = JSON.parse(parsedOnce); // Parses actual JSON
                      return parsedTwice.flight_number1 || "";
                    } catch {
                      return "";
                    }
                  })()}
                  </Typography>
                </Stack>

                <Box sx={{ height:'28px', ml: "auto", borderRadius:'62%',bgcolor:'#FFFFFF',display:'flex',alignItems:'center' }}>
                  <ChevronRightIcon  onClick={() => handleOpen(flight)} />
                </Box>
                </Stack>
              </Box>

            </Paper>
          </Grid>
        ))}
      </Grid>
      <Dialog className="dialog-main" open={popupOpen.orderModel} sx={{borderRadius:'15px'}} 
      onClose={handleClose}>
         {/* <DialogTitle>Flight Details</DialogTitle>  */}
        <DialogContent className="dialog-body">
          {selectedFlight && (
            <>
            <PopupResult journeyData={selectedFlight} 
            setPopupOpen={() =>  setPopupOpen((prev)=>({...prev,orderModel:false,onSuccess:true}))}  />
            </>
          )}
        </DialogContent>
       {/* <DialogActions> */}
          {/* <Button onClick={handleClose} color="primary">Close</Button> */}
        {/* </DialogActions>  */}
      </Dialog>
      <PopupDialog
              open={popupOpen.onSuccess}
              onClose={() => {setPopupOpen(false);navigate('/my-orders');}}
              title="Important Notice"
              message="1.contact the traveler and handover the parcel in the airport / railway station,
              2.share the details of the person who will receive the parcel in the destination
              3.make the payment to the traveler"
              onConfirm={()=>navigate('/my-orders')}
              confirmText="ok"
            />
    </Box>
  );
};

export default FlightDetailsSection;
