import React from 'react'
import { Outlet } from 'react-router-dom'
import Header from './Header'
import Footer from './Footer'
import { Box, useMediaQuery, useTheme } from '@mui/material'

export const MainLayout = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  return (
    <>
     <Box sx={{background:'#DC143C',textAlign:'center',fontWeight:'400',fontSize:isMobile ? '10px':'16px',lineHeight:isMobile ? '10px':'24px',padding:'8px',color:"white"}}>The Fastest and Greenest way to Courier</Box>
    <Header/>
    <div className='main-body' style={{minHeight: '50vh'}}>
    <Outlet />
    </div>
    <Box ><Footer/></Box>
    </>
  )
}
