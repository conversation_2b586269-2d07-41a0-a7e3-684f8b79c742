// Removed unused import
import ArrowForwardIcon from "@mui/icons-material/ArrowForward";
import EastIcon from "@mui/icons-material/East";
import { useForm } from "react-hook-form";
import KeyboardArrowDownIcon from "@mui/icons-material/KeyboardArrowDown";
import duration from "dayjs/plugin/duration";
import dayjs from "dayjs";
import * as Yup from 'yup'
import {
  Alert,
  Box,
  Button,
  Divider,
  Grid,
  MenuItem,
  Paper,
  Select,
  Snackbar,
  Stack,
  TextField,
  Typography,
  useMediaQuery,
  useTheme,
} from "@mui/material";
import React, { useEffect, useState } from "react";
import { yupResolver } from "@hookform/resolvers/yup";
// import PopupSkeleton from "./popupSkeleton"
import { useLocation, useNavigate, useSearchParams } from "react-router-dom";
import PriceSummary from "./PriceSummery";
dayjs.extend(duration);

const validationSchema = Yup.object({
  description: Yup.string()
    .min(5, "Description must be at least 5 characters")
    .required("Description is required")
    .max(100, 'Description must be under 100 characters'),
  cost: Yup.number()
    .min(1, "Cost must be greater than zero")
    .required("Cost is required"),
  category: Yup.number()
    .notOneOf([0], "Please select a valid category")
    .required("Category is required"),
  boarding_point: Yup.string().when('$trainNumber', {
    is: (val) => val !== null,
    then: (schema) =>
      schema
        .notOneOf(['select', ''], "Please select boarding point")
        .required("Boarding point is required"),
    otherwise: (schema) => schema.notRequired(),
  }),
  de_boarding_point: Yup.string().when('$trainNumber', {
    is: (val) => val !== null,
    then: (schema) =>
      schema
        .notOneOf(['select', ''], "Please select de-boarding point")
        .required("De-boarding point is required"),
    otherwise: (schema) => schema.notRequired(),
  }),

  // others: Yup.string().when("category", {
  //   is: 7,
  //   then: (schema) =>
  //     schema
  //       .min(3, "Please specify at least 3 characters for 'Others'")
  //       .max(100, "Please specify within 100 characters for Others")
  //       .required("Please specify the 'Others' category"),
  //   otherwise: (schema) => schema.notRequired(),
  // }),
});


const PopupResult = ({ journeyData, setPopupOpen }) => {
  console.log("date popup", journeyData)
  const [searchparams] = useSearchParams();
  const mode = searchparams.get("modeOfTransport");
  const navigate = useNavigate()
  const [category, setCategory] = useState([])
  // State for loading indicator
  const [loading, setLoading] = useState(false)
  const [success, setSuccess] = useState(false)
  const [message, setMessage] = useState('')
  const [boarding, setBoarding] = useState([])
  const [deBoarding, setDeBoarding] = useState([])
  // console.log("boarding",boarding,deBoarding)
  const token = localStorage.getItem("authToken");
  // Responsive design
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const location = useLocation();
  const searchParams = new URLSearchParams(location.search);

  const transportCharges = {
    flight: 500,
    'flight-inter': 1500,
    train: 200,
    bus: 150,
    car: 300,
    bike: 100,
    // add more modes as needed
  };

  // Utility function to calculate journey duration (commented out as not currently used)
  /*
  const calculateDuration = (departureTime, arrivalTime, startDate, arrivalDate) => {
    const start = dayjs(`${startDate} ${departureTime}`);
    const end = dayjs(`${arrivalDate} ${arrivalTime}`);
    const diff = end.diff(start, "minute");
    if (diff < 0) return "+1d";
    const formattedDuration = dayjs.duration(diff, "minutes").format("H[h] m[m]");
    return formattedDuration;
  };
  */
  const {
    register,
    handleSubmit,
    watch,
    formState: { errors },
  } = useForm({
    defaultValues: {
      description: "",
      cost: transportCharges[mode] ?? 0,
      category: 0,
      others: "",
      boarding_point: 'select',
      de_boarding_point: 'select'
    },
    resolver: yupResolver(validationSchema),
    context: { trainNumber: journeyData?.train_number },
  });
  const selectedCategory = watch("category");

  const handleCategory = async () => {
    setLoading(true)
    try {

      const response = await fetch(`${import.meta.env.VITE_API_URL}/api/category`);

      if (!response.ok) {
        throw new Error(`HTTP error! Status: ${response.status}`);
      }

      const result = await response.json();
      // setCategory(Array.isArray(result)?result:[result])
      if (response.ok) {
        //  console.log("category :" + result)
        setCategory(result.categories)
        setLoading(false)
      }
    } catch (error) {
      console.error("Error posting journey details:", error);
    }
  };
  const handleBoarding = async () => {
    setLoading(true)
    try {

      const source = journeyData?.source;
      const destination = journeyData?.destination;
      const mode = searchParams.get('modeOfTransport');
      const trainNumber = journeyData?.train_number;
      const startDate = searchParams.get('date');

      const queryParams = new URLSearchParams({
        source,
        destination,
        mode,
        trainNumber,
        startDate
      }).toString();

      const response = await fetch(`${import.meta.env.VITE_API_URL}/api/journeys/Boarding-point?${queryParams}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': token
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! Status: ${response.status}`);
      }

      const result = await response.json();
      // setCategory(Array.isArray(result)?result:[result])
      if (response.ok) {
        //  console.log("category :" + result)
        setBoarding(result.data ? result.data.boarding.map(item => item.boarding_point) : []);
        setDeBoarding(result.data ? result.data.deboarding.map(item => item.de_boarding_point) : []);

        setLoading(false)
      }
    } catch (error) {
      console.error("Error posting journey details:", error);
    }
  };

  const onSubmit = async (data) => {
    console.log("data", data)
    if (!data) return console.error("No data to submit");
    try {
      const authToken = localStorage.getItem("authToken");
      const user = JSON.parse(localStorage.getItem('user'))
      const userId = user ? user.userId : null;
      const response = await fetch(
        `${import.meta.env.VITE_API_URL}/api/orders`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: authToken,
          },
          body: JSON.stringify({
            source: journeyData?.source,
            destination: journeyData?.destination,
            start_date: journeyData?.start_date,
            train_number: journeyData?.train_number,
            flight_number: journeyData?.flight_number,
            user_id: userId,
            boarding_point: data.boarding_point,
            de_boarding_point: data.de_boarding_point,
            package_category: data.category,
            price: data.cost,
            others: data.others,
            order_description: data.description,
            payment_status: "paid",
            order_status: 'order confirm',
          }),
        }
      );

      if (!response.ok) {
        const responseData = await response.json();
        // console.log("Response:", responseData);
        setMessage(responseData.message)
        setSuccess(true)
      }

      if (response.ok) {
        setMessage("Order confirmed successfully")
        setSuccess(true)

        if (response.status === 401) {
          setMessage("You need to login first")
          setSuccess(true)
          navigate('/login')
        }
        setTimeout(() => {
          setPopupOpen((prev) => ({ ...prev, orderModel: false, onSuccess: true }))
        }, 1100);
      }


    } catch (error) {
      setMessage(error.message)
      setSuccess(true)
      onSuccess(true)
      console.error("Error posting journey details:", error);
      console.error("Error posting journey details:", error.message);
    } finally {
      // setSuccess(false)
    }
  };

  useEffect(() => { handleCategory(), handleBoarding() }, [journeyData])

  // if (loading) {
  //   return <PopupSkeleton />;
  // }
  return (
    <Box className='main-popup' sx={{ p: isMobile ? 2 : 4, bgcolor: "white", borderRadius: '15px' }}>
      {/* Header with journey details */}
      <form onSubmit={handleSubmit(onSubmit)}>
        <Stack
          direction={isMobile ? "column" : "row"}
          justifyContent="space-between"
          width="100%"
          spacing={isMobile ? 1 : 2}
          mb={isMobile ? 3 : 5}
        >
          <Stack
            className="body-top"
            direction="row"
            alignItems="center"
            spacing={2}
            sx={{ width: isMobile ? '100%' : 'auto' }}
          >
            <Box>
              <Typography variant="subtitle1" fontWeight={600}>
                {journeyData.source}
              </Typography>
              <Typography variant="body2">{journeyData.source || ''}</Typography>
            </Box>

            <ArrowForwardIcon />

            <Box>
              <Typography variant="subtitle1" fontWeight={600}>
                {journeyData.destination}
              </Typography>
              <Typography variant="body2">{journeyData.destination || ''}</Typography>
            </Box>
          </Stack>

          <Paper sx={{
            px: 2,
            py: 1,
            bgcolor: "#f5f5f5",
            borderRadius: 2,
            width: isMobile ? '100%' : 'auto',
            mt: isMobile ? 1 : 0,
            textAlign: isMobile ? 'center' : 'left'
          }}>
            <Typography variant="body2">Date</Typography>
            <Typography variant="h7" fontWeight={700}>
              {journeyData.start_date || ''}
            </Typography>
          </Paper>
        </Stack>

        {/* Train details */}
        <Grid container spacing={isMobile ? 1 : 2} mb={isMobile ? 3 : 5} sx={{ flexWrap: isMobile ? 'wrap' : 'nowrap' }}>
          <Grid item xs={isMobile ? 6 : 'auto'}>
            <Box>
              <Typography variant="body2">{journeyData.train_number ? 'Train No' : 'Flight No'} </Typography>
              <Typography variant="subtitle1" fontWeight={700}>
                {/* {journeyData.trainNumber ? journeyData.trainNumber : journeyData.flightNumber || ''} */}
                {journeyData.train_number ||
                  (() => {
                    try {
                      const parsedOnce = JSON.parse(journeyData.flight_number); // Removes outer string
                      const parsedTwice = JSON.parse(parsedOnce); // Parses actual JSON
                      return parsedTwice.flight_number1 || "";
                    } catch {
                      return "";
                    }
                  })()}
              </Typography>
            </Box>
          </Grid>

          {!isMobile && (
            <Grid item>
              <Divider orientation="vertical" sx={{ height: 44, mx: 2 }} />
            </Grid>
          )}

          <Grid item xs={isMobile ? 6 : 'auto'}>
            <Box>
              <Typography variant="body2">Start Time</Typography>
              <Typography variant="subtitle1" fontWeight={700}>
                {journeyData.departure_time || ''}
              </Typography>
            </Box>
          </Grid>

          <Grid item xs={12} sx={{ display: isMobile ? 'block' : 'flex', my: isMobile ? 2 : 0 }}>
            <Box sx={{ textAlign: "center", position: "relative", width: '100%' }}>
              <Typography
                variant="body2"
                sx={{
                  width: 'max-content',
                  position: "absolute",
                  top: 0,
                  left: "50%",
                  transform: "translateX(-50%)",
                  display: isMobile ? 'none' : 'block'
                }}
              >
                {/* Duration would be displayed here if available */}
              </Typography>
              <Box
                sx={{
                  mt: isMobile ? 1 : 4,
                  position: "relative",
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  width: '100%'
                }}
              >
                <Box
                  sx={{
                    height: 1,
                    bgcolor: "black",
                    width: "100%",
                    position: "absolute",
                  }}
                />

                <EastIcon
                  sx={{
                    position: "absolute",
                    right: 0,
                    left: -10,
                    bottom: 0,
                    bgcolor: "transparent",
                    zIndex: 1,
                  }}
                />
                <Typography
                  variant="body2"
                  fontWeight={700}
                  sx={{ position: "absolute", bottom: 15 }}
                >
                  {/* {journeyData.nextDay ||''}  */}
                </Typography>
              </Box>
            </Box>
          </Grid>

          <Grid ml={2} item xs={isMobile ? 12 : 'auto'}>
            <Box sx={{ textAlign: isMobile ? 'center' : 'left' }}>
              <Typography variant="body2">Arrival Time</Typography>
              <Typography variant="subtitle1" fontWeight={700}>
                {journeyData.arrival_time || ''}
              </Typography>
            </Box>
          </Grid>
        </Grid>

        {mode !== "flight" && mode !== "flight-inter" &&
          <>
            {/* Boarding and De-boarding selection */}
            <Stack direction={isMobile ? "column" : "row"} spacing={isMobile ? 2 : 3} mb={isMobile ? 3 : 5}>
              <Box sx={{ flex: 1 }}>
                <Typography variant="subtitle2" fontWeight={600} mb={1}>
                  Boarding
                </Typography>
                <Select
                  defaultValue={'select'}
                  // value={boarding_point}
                  // onChange={(e) => setBoarding_point(e.target.value)}

                  {...register("boarding_point")}
                  error={!!errors.boarding_point}
                  helperText={errors.boarding_point?.message}
                  fullWidth
                  sx={{
                    borderRadius: 2, "& .MuiOutlinedInput-input": {
                      padding: '10.5px 14px !important'
                    }
                  }}
                  IconComponent={KeyboardArrowDownIcon}
                >
                  <MenuItem value={'select'}>
                    Select Boarding..
                  </MenuItem>
                  {boarding.map((item, index) => (
                    <MenuItem key={index} value={item}>
                      {item}
                    </MenuItem>))}
                </Select>
                {errors.boarding_point &&
                  <Typography variant='error'>{errors.boarding_point?.message}</Typography>}
              </Box>

              <Box sx={{ flex: 1 }}>
                <Typography variant="subtitle2" fontWeight={600} mb={1}>
                  De Boarding
                </Typography>
                <Select
                  defaultValue={'select'}
                  // value={deBoarding_point}
                  // onChange={(e) => setDeBoarding_point(e.target.value)}
                  {...register("de_boarding_point")}
                  error={!!errors.de_boarding_point}
                  helperText={errors.de_boarding_point?.message}
                  fullWidth
                  sx={{
                    borderRadius: 2, "& .MuiOutlinedInput-input": {
                      padding: '10.5px 14px !important'
                    }
                  }}
                  IconComponent={KeyboardArrowDownIcon}
                >
                  <MenuItem value={'select'}>
                    Select De_Boarding..
                  </MenuItem>
                  {deBoarding.map((item, index) => (
                    <MenuItem key={index} value={item}>
                      {item}
                    </MenuItem>))}
                </Select>
                {errors.de_boarding_point &&
                  <Typography variant='error'>{errors.de_boarding_point?.message}</Typography>}
              </Box>
            </Stack>
          </>
        }
        <Stack direction={isMobile ? "column" : "row"} spacing={isMobile ? 2 : 3} mb={isMobile ? 3 : 3}>
          <Box sx={{ flex: 1 }}>
            <Typography variant="subtitle2" fontWeight={600} mb={1}>
              Category Type
            </Typography>
            <Select
              className="body-select"
              {...register("category")}
              name="category"
              defaultValue={0}
              error={!!errors.category}
              fullWidth
              sx={{
                borderRadius: 2,
                "& .MuiOutlinedInput-input": {
                  padding: '8.5px 14px !important'
                }
              }}
              IconComponent={KeyboardArrowDownIcon}
            >
              <MenuItem value={0}>
                Select category..
              </MenuItem>

              {Array.isArray(category) && category.length > 0 ? (
                category
                  .filter(cst => {
                    if (mode === "flight-inter") {
                      return cst.category_name.toLowerCase() === "documents"; // allow only document
                    }
                    return true; // allow all in other modes
                  })
                  .map((cst, index) => (
                    <MenuItem key={index} value={cst.id}>
                      {cst.category_name}
                    </MenuItem>
                  ))
              ) : (
                <MenuItem disabled>No categories available</MenuItem>
              )}
            </Select>

            {errors.category && (
              <Typography color="error" sx={{ fontSize: isMobile ? '12px' : '14px' }}>
                {errors.category.message}
              </Typography>
            )}
          </Box>

          <Box sx={{ flex: 1 }}>
            <Typography variant="subtitle2" fontWeight={600} mb={1}>
              Description
            </Typography>
            <TextField
              {...register("description")}
              fullWidth
              error={!!errors.description}
              helperText={errors.description?.message}
              onKeyPress={(e) => {
                const regex = /^[a-zA-Z0-9 ]+$/;
                if (!regex.test(e.key)) {
                  e.preventDefault();
                }
              }}
              size="small" variant="outlined"
              sx={{ '& .MuiFormHelperText-root': { fontSize: isMobile ? '12px' : '14px' } }}
            // onChange={(e)=>setDestination(e.target.value)}
            />
          </Box>


        </Stack>
        {/* {selectedCategory === 7 && (
          <Box mb={2} sx={{ maxWidth: '225px' }}>
            <Typography variant="subtitle2" fontWeight={600} mb={1}>
              your package category
            </Typography>
            <TextField
              {...register("others")}
              fullWidth
              error={!!errors.others}
              helperText={errors.others?.message}
              size="small" variant="outlined"
              onKeyPress={(e) => {
                const regex = /^[a-zA-Z0-9 ]+$/;
                if (!regex.test(e.key)) {
                  e.preventDefault();
                }}}
              sx={{ '& .MuiFormHelperText-root': { fontSize: isMobile ? '12px' : '14px' } }}
            // onChange={(e)=>setDestination(e.target.value)}
            />
          </Box>)} */}


        {/* Footer with price and confirm button */}
        <Stack
          spacing={isMobile ? 2 : 2.5}
          direction={"column"}
          justifyContent="space-between"
        >
          <Paper
            variant="outlined"
            sx={{
              px: 2.5,
              py: 2.5,
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              gap: 1,
              // borderRadius: 2,
              // bgcolor: "#f5f5f5",
              // borderStyle: "dashed",
              width: isMobile ? '100%' : 'auto'
            }}
          >
            <PriceSummary
              deliveryCharges={transportCharges[mode] ?? 0}
              platformCharges={45}
              discount={45}
            />

            {/* <Typography variant="body2">Estimated Price</Typography>
            <Typography variant="h6" fontWeight={700}>
              {mode === "flight" ? 'Rs.499' : 'Rs.99'}
            </Typography> */}
          </Paper>

          <Button
            variant="contained"
            color="error"
            endIcon={<ArrowForwardIcon />}
            type="submit"
            sx={{
              borderRadius: 2,
              px: 2.5,
              py: 1.25,
              width: isMobile ? '100%' : 'auto'
            }}
          >
            Confirm your order
          </Button>
        </Stack>
      </form>
      <Snackbar
        anchorOrigin={{
          horizontal: isMobile ? 'center' : 'right',
          vertical: 'top'
        }}
        open={success}
        autoHideDuration={1000}
        onClose={() => setMessage(false)}
      >
        <Alert
          icon={false}
          sx={{
            width: isMobile ? '90%' : '100%',
            borderLeft: '15px solid #DC143C',
            borderRadius: '10px',
            background: '#EFEFEF'
          }}
        >
          {message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default PopupResult;

