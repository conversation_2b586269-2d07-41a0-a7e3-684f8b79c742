import { Box, Stack, Typography, useMediaQuery, useTheme,Link as MuiLink } from '@mui/material'
import React, { useState } from 'react'
import logo from '../../public/logo-web-transparent.png'
import { Link } from 'react-router-dom'
import TermsDialog from "./TermsDialog";

const Footer = () => {
    const [termsDialogOpen, setTermsDialogOpen] = useState(false);
    const [termsCategory, setTermsCategory] = useState('Train');
    const theme = useTheme()
    const isMobile = useMediaQuery(theme.breakpoints.down('sm'))
    return (
        <Box sx={{ paddingTop: '10px', display: 'flex', justifyContent: 'space-around', background: '#D9D9D9', flexDirection: isMobile ? 'column' : 'row', alignItems: 'center' }}>
            <Box >
                <Stack direction='row' gap={5}>
                    <MuiLink
                    sx={{color:'#000 !important',cursor:'pointer'}}
                     onClick={(e) => {
                        e.preventDefault();
                        setTermsCategory('Train');
                        setTermsDialogOpen(true);
                      }}>
                    <Typography sx={{ fontWeight: '400', fontSize: '16px', lineHeight: '24px' }}>Terms & Conditions</Typography>
                    </MuiLink>
                    <Typography sx={{ fontWeight: '400', fontSize: '16px', lineHeight: '24px' }}>Cookie Settings</Typography>
                </Stack>
            </Box>
            <Box mt={isMobile ? 1:2} mb={isMobile ? 0:2}>
                <Link to={"/"}>
                <img src={logo} alt="Next.js logo" style={{ cursor: 'pointer' }} width={140} height={'auto'} />
                </Link>
            </Box>

            <TermsDialog
        open={termsDialogOpen}
        onClose={() => setTermsDialogOpen(false)}
        initialCategory={termsCategory}
      />
        </Box>
    )
}

export default Footer