/* OrderCard.css - Mobile and Tablet-friendly styles */

.text-front{
    font-size: 18px !important;
    font-weight: 400 !important;
    line-height: 26px !important;
}

.icon-mobile-view{
    display: none !important;
}

.order-card-container {
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 25px;
}

/* Mobile styles */
@media screen and (max-width: 600px) {
    .icon-web-view{
        display: none !important;
    }
    .icon-mobile-view{
        display: flex !important;
    }
    .journey-card-map-body {
        padding: 8px !important;
    }
    .train-details-section {
        width: 100% !important;
    }
}

/* Tablet-specific styles */
@media screen and (min-width: 601px) and (max-width: 960px) {
    .journey-card-map-body {
        display: flex !important;
        flex-direction: row !important;
        gap: 15px !important;
        padding: 12px !important;
    }

    .train-details-section {
        width: 100% !important;
        max-width: 300px !important;
    }

    .journey-info-section {
        flex: 1 !important;
        min-width: 300px !important;
    }

    .destination-section {
        margin-left: 20px !important;
    }

    .arrow-icon {
        margin: auto 10px !important;
    }

    .expanded-details {
        display: flex !important;
        flex-direction: row !important;
        flex-wrap: wrap !important;
        gap: 15px !important;
    }

    .user-info-box {
        flex: 1 !important;
        min-width: 250px !important;
        margin-right: 15px !important;
    }

    .progress-bar-section {
        flex: 2 !important;
        min-width: 300px !important;
    }
}

/* Animation for hover effects */
@keyframes buttonHover {
    0% {
        transform: translateY(0);
    }
    50% {
        transform: translateY(-2px);
    }
    100% {
        transform: translateY(0);
    }
}

.action-button:hover {
    animation: buttonHover 0.3s ease-in-out;
    background-color: rgba(220, 20, 60, 0.05) !important;
}