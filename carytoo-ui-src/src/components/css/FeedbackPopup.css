/* FeedbackPopup.css */

.feedback-popup-enter {
  opacity: 0;
  transform: scale(0.9);
}

.feedback-popup-enter-active {
  opacity: 1;
  transform: scale(1);
  transition: opacity 300ms, transform 300ms;
}

.feedback-popup-exit {
  opacity: 1;
  transform: scale(1);
}

.feedback-popup-exit-active {
  opacity: 0;
  transform: scale(0.9);
  transition: opacity 300ms, transform 300ms;
}

.rating-icon {
  transition: transform 0.2s ease-in-out;
}

.rating-icon:hover {
  transform: scale(1.2);
}

.feedback-form-field {
  margin-bottom: 16px;
}

.feedback-form-field label {
  font-weight: 500;
}

/* Mobile optimizations */
@media (max-width: 600px) {
  .feedback-dialog-content {
    padding: 16px !important;
  }
  
  .feedback-dialog-actions {
    padding: 8px 16px 16px !important;
  }
}

/* Animation for success message */
@keyframes successPulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

.success-message {
  animation: successPulse 0.5s ease-in-out;
}

/* Custom styling for the rating component */
.custom-rating .MuiRating-iconFilled {
  color: #DC143C;
}

.custom-rating .MuiRating-iconHover {
  color: #DC143C;
}

.feedback-submit-button {
  background-color: #DC143C !important;
  transition: background-color 0.3s ease !important;
}

.feedback-submit-button:hover {
  background-color: #b01030 !important;
}

.feedback-cancel-button {
  color: rgba(0, 0, 0, 0.6) !important;
  transition: color 0.3s ease !important;
}

.feedback-cancel-button:hover {
  color: rgba(0, 0, 0, 0.8) !important;
}
