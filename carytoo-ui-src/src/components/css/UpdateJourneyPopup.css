/* UpdateJourneyPopup.css */

.update-popup-enter {
  opacity: 0;
  transform: scale(0.9);
}

.update-popup-enter-active {
  opacity: 1;
  transform: scale(1);
  transition: opacity 300ms, transform 300ms;
}

.update-popup-exit {
  opacity: 1;
  transform: scale(1);
}

.update-popup-exit-active {
  opacity: 0;
  transform: scale(0.9);
  transition: opacity 300ms, transform 300ms;
}

.update-form-field {
  margin-bottom: 16px !important;
}

.update-form-field label {
  font-weight: 500;
}

/* Mobile optimizations */
@media (max-width: 600px) {
  .update-dialog-content {
    padding: 16px !important;
  }

  .update-dialog-actions {
    padding: 8px 16px 16px !important;
  }
}

/* Animation for success message */
@keyframes successPulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

.success-message {
  animation: successPulse 0.5s ease-in-out;
}

.update-submit-button {
  background-color: #DC143C !important;
  transition: background-color 0.3s ease !important;
}

.update-submit-button:hover {
  background-color: #b01030 !important;
}

.update-cancel-button {
  color: rgba(0, 0, 0, 0.6) !important;
  transition: color 0.3s ease !important;
}

.update-cancel-button:hover {
  color: rgba(0, 0, 0, 0.8) !important;
}

/* Input field focus styles */
.MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline {
  border-color: #DC143C !important;
}

.MuiFormLabel-root.Mui-focused {
  color: #DC143C !important;
}

/* Styles for changed fields */
.MuiFormHelperText-root:not(:empty) {
  color: #DC143C !important;
  font-weight: 500;
  transition: all 0.3s ease;
}

/* Animation for changed fields */
@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.02);
  }
  100% {
    transform: scale(1);
  }
}

.MuiTextField-root:has(.MuiFormHelperText-root:not(:empty)) .MuiOutlinedInput-notchedOutline {
  border-color: #DC143C !important;
  animation: pulse 1s ease-in-out;
}
