import React from "react";
import { Box, Typography } from "@mui/material";
import FlightTakeoffIcon from '@mui/icons-material/FlightTakeoff';
import TrainIcon from "@mui/icons-material/Train";

const SearchNoData = (Mode) => {
  return (
    <Box
      display="flex"
      flexDirection="column"
      justifyContent="center"
      alignItems="center"
      height="50vh"
    >
        {Mode === "train" ? (
        <TrainIcon sx={{ fontSize: 80, color: "#DC143C" }} />
      ) : (
        <FlightTakeoffIcon sx={{ fontSize: 80, color: "#DC143C" }} />
      )}

      <Typography sx={{display:'flex',alignItems:'center',flexDirection:'column'}} variant="h6" color="#000000" mt={2}>
     Sorry, no rides match your Destination
      {'   '}  <Typography> Try changing your location or date</Typography>
      </Typography>
    </Box>
  );
};

export default SearchNoData;
