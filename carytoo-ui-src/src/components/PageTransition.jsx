import React, { useEffect, useState } from 'react';
import { Fade } from '@mui/material';
import { useLocation } from 'react-router-dom';

/**
 * A higher-order component that adds page transition animations
 * 
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.children - The page content to be animated
 * @returns {React.ReactElement} Animated page component
 */
const PageTransition = ({ children }) => {
  const [isVisible, setIsVisible] = useState(false);
  const location = useLocation();

  useEffect(() => {
    // Reset animation on route change
    setIsVisible(false);
    
    // Start animation after a short delay
    const timer = setTimeout(() => {
      setIsVisible(true);
    }, 100);
    
    return () => clearTimeout(timer);
  }, [location.pathname]);

  return (
    <Fade in={isVisible} timeout={800}>
      <div style={{ width: '100%' }}>
        {children}
      </div>
    </Fade>
  );
};

export default PageTransition;
