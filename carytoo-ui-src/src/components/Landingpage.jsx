import { Box, Fade, Slide, Tab, Tabs, Typography, useMediaQuery, useTheme } from "@mui/material"
import { useEffect, useState } from "react";
import Trainsearch from "./Trainsearch";
import Flightsearch from "./Flightsearch";
import FlightInterSearch from './FlightInterSearch'
import FlightTakeoffIcon from '@mui/icons-material/FlightTakeoff';
import TrainOutlinedIcon from '@mui/icons-material/TrainOutlined';
// Icons are commented out in the code but imported here for potential future use
// import FlightIcon from '../assets/FlightInput.png'
// import TrainIcon from '../assets/Train_icon.png'


const LandingPage = () => {
    const [value, setValue] = useState(0);
    const [headlineVisible, setHeadlineVisible] = useState(false);
    const theme = useTheme();
    const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

    // Trigger headline animation after component mounts
    useEffect(() => {
        const timer = setTimeout(() => {
            setHeadlineVisible(true);
        }, 300);

        return () => clearTimeout(timer);
    }, []);

    const handleChange = (_, newValue) => {
      setValue(newValue);
    };

    function CustomTabPanel(props) {
        const { children, value, index, ...other } = props;

        return (
            <div
                role="tabpanel"
                hidden={value !== index}
                id={`tab-panel-${index}`}
                aria-labelledby={`tab-${index}`}
                {...other}
            >
                {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
            </div>
        );
    }

  return (
    <Box sx={{alignItems:'center',display:'flex',flexDirection:'column',justifyContent:'center'}}>
        <Box sx={{alignItems:'center',display:'flex',justifyContent:'center',padding:'8px',gap:'20px',textAlign:'center',}}>
          <Slide direction="up" in={headlineVisible} timeout={1000}>
            <Fade in={headlineVisible} timeout={1500}>
              <Typography
                sx={{
                  color:'#000000',
                  width:{sm:'fit-content',md:'fit-content',lg:'900px'},
                  fontWeight:'700',
                  fontSize:isMobile?'24px !important':'43px',
                  lineHeight:isMobile?'32px !important':'68px',
                  alignItems:'center',
                  padding:isMobile?'20px' :'30px',
                  position:'relative',
                  top:'100px',
                  textShadow: '0px 4px 8px rgba(0, 0, 0, 0.1)',
                  transition: 'all 0.5s ease-in-out'
                }}
              >
                India's First CrowdShipper Exclusively For Train and Flight
              </Typography>
            </Fade>
          </Slide>
        </Box>

    <Box className='main-tabs'
        sx={{ flexDirection: isMobile ? "column" : "row",alignItems:'center',display:'flex',justifyContent:'center',padding:'15px',gap:'20px',textAlign:'center',backgroundColor:'#FFFFFF',width:'fit-content',borderRadius:'10px',position:'relative',top:'120px'}}>

    <Tabs
        value={value}
        className="body-tabs"
        onChange={handleChange}
        centered
        sx={{
          "& .MuiTabs-indicator": { display: "none" }, // Hide default indicator
          "& .MuiTab-root": {
            backgroundColor: "#f5f5f5",
            borderRadius: "8px",
            padding: "10px 20px",
            margin: "0 10px",
            minWidth: "150px",
            color: "#131313",
            fontWeight: "bold",
            transition: "0.3s",
            display:'flex',
            flexDirection:isMobile ?'row' :'column',
            border:'2px solid #131313',
            "&:hover": { backgroundColor: "#ddd" },
            "&.Mui-selected": { backgroundColor: "#DC143C", color: "#EFEFEF", border: "none", },
          },
          "& .MuiTabs-list ":{ 
            display:'flex',
            flexDirection:isMobile ? 'column':'row' ,}
        }}
      >
        <Tab
    icon={
      <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
        {/* <img src={FlightIcon} alt="Flight Icon" style={{ color: "#EFEFEF" }} width={24} height={24} /> */}
        <FlightTakeoffIcon/>
        <Typography>Flight</Typography>
      </Box>
    }
    sx={{ textTransform: "none", flexDirection: "row" }}
  />

  <Tab
    icon={
      <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
        {/* <img src={TrainIcon} color="#131313" alt="Train Icon" width={24} height={24} /> */}
        <TrainOutlinedIcon/>
        <Typography>Train</Typography>
      </Box>
    }
    sx={{ textTransform: "none", flexDirection: "row" }}
  />

       <Tab
    icon={
      <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
        {/* <img src={FlightIcon} alt="Flight Icon" style={{ color: "#EFEFEF" }} width={24} height={24} /> */}
        <FlightTakeoffIcon/>
        <Typography>Flight-International</Typography>
      </Box>
    }
    sx={{ textTransform: "none", flexDirection: "row" }}
  />
      </Tabs>

      </Box>

        <Box sx={{alignItems:'center',display:'flex',justifyContent:'center',}}>
          <CustomTabPanel value={value} index={0}>
            <Flightsearch/>
          </CustomTabPanel>
          <CustomTabPanel value={value} index={1}>
             <Trainsearch/>
          </CustomTabPanel>
           <CustomTabPanel value={value} index={2}>
             <FlightInterSearch/>
          </CustomTabPanel>
        </Box>
    </Box>
  )
}

export default LandingPage