import EditIcon from "@mui/icons-material/Edit";
import FilterListIcon from "@mui/icons-material/FilterList";
import FlightTakeoffOutlinedIcon from '@mui/icons-material/FlightTakeoffOutlined';
import LocationOnIcon from "@mui/icons-material/LocationOn";
import TrainIcon from "@mui/icons-material/Train";
import SearchOutlinedIcon from '@mui/icons-material/SearchOutlined';
import {
  Alert,
  Box,
  Button,
  IconButton,
  InputAdornment,
  MenuItem,
  Paper,
  Select,
  Snackbar,
  Stack,
  TextField,
  Typography,
  useMediaQuery,
  useTheme,
} from "@mui/material";
import React, { useState } from "react";
import { useSearchParams } from "react-router-dom";

const SearchResultSection = ({ Data }) => {
  const [_, setSearchParams] = useSearchParams();
  const [modify, setModify] = useState(false)
  const [source, setSource] = useState(Data.source || "");
  const [destination, setDestination] = useState(Data.destination || "");
  const [date, setDate] = useState(Data.date || "");
  const [mode, setMode] = useState(Data.mode || "");
  const [open,setOpen]=useState(true)
  const [message,setMessage]=useState('')
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'))
  // const mode = Data.mode;
  function formatDate(inputDate) {
    const dateObj = new Date(inputDate);
    if (isNaN(dateObj)) return "Invalid Date";

    const day = String(dateObj.getDate()).padStart(2, '0'); // Get day with leading zero if needed
    const monthShort = dateObj.toLocaleString('en-US', { month: 'short' }); // Get short month name
    const year = dateObj.getFullYear(); // Get full year

    return `${day}, ${monthShort}    ${'      '}  ${year}`;
  }

  const handleCloseAlert = () => {
    setOpen(false);
  };
  const handleSearch = () => {
    if (!source || !destination || !date || !mode) {
      alert("Please fill all fields!");
      return;
    }
    if (source.trim().length < 3 || destination.trim().length < 3) {
      setMessage("Source and Destination must be at least 3 characters long!");
      setOpen(true)
      return;
    }
    // Set search parameters
    setSearchParams({
      source,
      destination,
      date,
      modeOfTransport: mode
    });

    setModify(false);
  }
  return (
    <Stack
      className="main-searchResult"
      direction={isMobile ? 'column' : 'row'}
      spacing={isMobile ? 3 : 2}
      justifyContent={modify ? "space-between" : "flex-start"}
      width="100%"
      marginTop={isMobile ? 3 : 5}
      sx={{
        padding: isMobile ? '10px' : '0px',
        boxShadow: isMobile ? '0px 2px 8px rgba(0, 0, 0, 0.1)' : 'none',
        borderRadius: isMobile ? '10px' : '0px',
        backgroundColor: isMobile ? '#ffffff' : 'transparent'
      }}
    >
      {/* Boarding From Field */}

      {!modify ? (
        <Stack direction={isMobile ? 'column' : 'row'} spacing={isMobile ? 2 : 3} sx={{ width: '100%' }}>
          <Stack
            className="main-two-field-modify"
            direction={isMobile ? 'column' : 'row'}
            spacing={isMobile ? 2 : 5}
            sx={{ width: isMobile ? '100%' : 'auto' }}
          >
            <Box sx={{ width: isMobile ? '100%' : 'auto' }}>
              <Typography variant="subtitle1" fontWeight={600} mb={isMobile ? 0.5 : 1} fontSize={isMobile ? '14px' : '16px'}>
                Boarding from
              </Typography>
              <TextField
                fullWidth
                disabled
                value={Data.source}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      {Data.mode === 'flight' ? <FlightTakeoffOutlinedIcon /> : <TrainIcon />}
                    </InputAdornment>
                  ),
                  readOnly: true,
                }}
                sx={{
                  width: '100%',
                  "& .MuiOutlinedInput-root": {
                    borderRadius: "10px",
                    fontSize: isMobile ? '14px' : '16px'
                  },
                  "& .MuiOutlinedInput-input": {
                    padding: isMobile ? '8px 10px !important' : '10.5px 14px !important'
                  }
                }}
              />
            </Box>

            {/* Arrival To Field */}
            <Box sx={{ width: isMobile ? '100%' : 'auto' }}>
              <Typography variant="subtitle1" fontWeight={600} mb={isMobile ? 0.5 : 1} fontSize={isMobile ? '14px' : '16px'}>
                Arrival to
              </Typography>
              <TextField
                fullWidth
                disabled
                value={Data.destination}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <LocationOnIcon />
                    </InputAdornment>
                  ),
                  readOnly: true,
                }}
                sx={{
                  width: '100%',
                  "& .MuiOutlinedInput-root": {
                    borderRadius: "10px",
                    fontSize: isMobile ? '14px' : '16px'
                  },
                  "& .MuiOutlinedInput-input": {
                    padding: isMobile ? '8px 10px !important' : '10.5px 14px !important'
                  }
                }}
              />
            </Box>
          </Stack>
          <Paper
            elevation={0}
            sx={{
              maxWidth: isMobile ? '100%' : '188px',
              width: isMobile ? '100%' : 'auto',
              alignItems: isMobile ? 'center' : 'flex-start',
              justifyContent: 'center',
              marginTop: isMobile ? '10px !important' : 'auto !important',
              display: "flex",
              flexDirection: "column",
              height: isMobile ? 'auto' : 'fit-content',
              p: 2,
              borderRadius: "10px",
              bgcolor: "#f5f5f5",
              gap: '2px',
              padding: isMobile ? '4px 12px' : '7px 18px',
            }}
          >
            <Typography
              variant="body1"
              sx={{
                lineHeight: isMobile ? '14px' : '10px',
                fontSize: isMobile ? '12px' : '10px',
                textAlign: isMobile ? 'center' : 'left'
              }}
              color="textPrimary"
            >
              Date
            </Typography>
            <Typography
              variant="h5"
              fontWeight={400}
              fontSize={isMobile ? 10 : 15}
              color="textPrimary"
              sx={{ textAlign: isMobile ? 'center' : 'left' }}
            >
              {formatDate(Data.date)}
            </Typography>
          </Paper>

          <Button
            variant="contained"
            onClick={() => setModify((prev) => !prev)}
            color="primary"
            startIcon={<EditIcon />}
            sx={{
              height: 'fit-content',
              borderRadius: "10px",
              px: isMobile ? 1.5 : 1.875,
              py: isMobile ? 1 : 0.900,
              marginTop: isMobile ? '10px !important' : 'auto !important',
              width: isMobile ? '100%' : 'auto',
              fontSize: isMobile ? '14px' : '16px'
            }}
          >
            Modify
          </Button>
        </Stack>
      ) : (
        <Stack
          className="main-two-field-modify"
          direction={isMobile ? 'column' : 'row'}
          spacing={isMobile ? 2 : 3}
          sx={{ width: '100%' }}
        >
          <Box sx={{ width: isMobile ? '100%' : '300px' }}>
            <Typography variant="subtitle1" fontWeight={600} mb={isMobile ? 0.5 : 1} fontSize={isMobile ? '14px' : '16px'}>
              Boarding from
            </Typography>
            <TextField
              fullWidth
              onChange={(e) => setSource(e.target.value)}
              value={source}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    {Data.mode === 'flight' ? <FlightTakeoffOutlinedIcon /> : <TrainIcon />}
                  </InputAdornment>
                ),
              }}
              sx={{
                width: '100%',
                "& .MuiOutlinedInput-root": {
                  borderRadius: "10px",
                  fontSize: isMobile ? '14px' : '16px'
                },
                "& .MuiOutlinedInput-input": {
                  padding: isMobile ? '8px 10px !important' : '10.5px 14px !important'
                }
              }}
            />
          </Box>

          {/* Arrival To Field */}
          <Box sx={{ width: isMobile ? '100%' : '300px' }}>
            <Typography variant="subtitle1" fontWeight={600} mb={isMobile ? 0.5 : 1} fontSize={isMobile ? '14px' : '16px'}>
              Arrival to
            </Typography>
            <TextField
              fullWidth
              onChange={(e) => setDestination(e.target.value)}
              value={destination}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <LocationOnIcon />
                  </InputAdornment>
                ),
              }}
              sx={{
                width: '100%',
                "& .MuiOutlinedInput-root": {
                  borderRadius: "10px",
                  fontSize: isMobile ? '14px' : '16px'
                },
                "& .MuiOutlinedInput-input": {
                  padding: isMobile ? '8px 10px !important' : '10.5px 14px !important'
                }
              }}
            />
          </Box>
          <Box sx={{ width: isMobile ? '100%' : '300px' }}>
            <Typography variant="subtitle1" fontWeight={600} mb={isMobile ? 0.5 : 1} fontSize={isMobile ? '14px' : '16px'}>
              Date
            </Typography>
            <TextField
              fullWidth
              onChange={(e) => setDate(e.target.value)}
              value={date}
              type="date"
              sx={{
                width: '100%',
                "& .MuiOutlinedInput-root": {
                  borderRadius: "10px",
                  fontSize: isMobile ? '14px' : '16px'
                },
                "& .MuiOutlinedInput-input": {
                  padding: isMobile ? '8px 10px !important' : '10.5px 14px !important'
                }
              }}
            />
          </Box>
          <Box sx={{ width: isMobile ? '100%' : '300px' }}>
            <Typography variant="subtitle1" fontWeight={600} mb={isMobile ? 0.5 : 1} fontSize={isMobile ? '14px' : '16px'}>
              Mode Of Transport
            </Typography>
            <TextField
              select
              fullWidth
              value={mode}
              onChange={(e) => setMode(e.target.value)}
              sx={{
                "& .MuiOutlinedInput-root": {
                  borderRadius: "10px",
                  fontSize: isMobile ? "14px" : "16px",
                },
                "& .MuiSelect-select": {
                  padding: "0px !important",
                },
                "& .MuiOutlinedInput-input": {
                  padding: "10px !important",
                },
              }}
            >
              <MenuItem value="flight">Flight</MenuItem>
              <MenuItem value="train">Train</MenuItem>
              <MenuItem value="flight-inter">Flight International</MenuItem>
            </TextField>


          </Box>
          <Button
            variant="contained"
            onClick={() => handleSearch()}
            color="primary"
            startIcon={modify ? <SearchOutlinedIcon /> : <EditIcon />}
            sx={{
              height: 'fit-content',
              borderRadius: "10px",
              px: isMobile ? 1.5 : 1.875,
              py: isMobile ? 1 : 1.25,
              marginTop: isMobile ? '10px !important' : 'auto !important',
              width: isMobile ? '100%' : 'auto',
              fontSize: isMobile ? '14px' : '16px'
            }}
          >
            Search
          </Button>
        </Stack>
      )}
      <Stack
        direction={'row'}
        marginTop={isMobile ? '10px !important' : 'auto !important'}
        spacing={2}
        sx={{
          width: isMobile ? '100%' : 'auto',
          justifyContent: isMobile ? 'flex-end' : 'flex-start'
        }}
      >
        {/* Filter button */}
        <IconButton
          sx={{
            bgcolor: "#f5f5f5",
            borderRadius: "5px",
            p: isMobile ? 1 : 1.25,
            height: 'fit-content',
            marginTop: isMobile ? '0 !important' : 'auto !important'
          }}
        >
          <FilterListIcon />
        </IconButton>
      </Stack>
        <Snackbar
                    open={open}
                    autoHideDuration={1000}
                    onClose={handleCloseAlert}
                    anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
                  >
                    <Alert
                      onClose={handleCloseAlert}
                      sx={{
                        width: '100%',
                        borderRadius: '10px',
                        border: '1px solid #DC143C',
                        background: 'white'
                      }}
                      icon={false}
                    >
                      {message}
                    </Alert>
                  </Snackbar>
    </Stack>
  );
};

export default SearchResultSection;
