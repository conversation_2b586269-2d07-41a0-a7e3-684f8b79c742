import React, { useEffect, useState } from 'react';
import './css/UpdateJourneyPopup.css';
import {
  Dialog, DialogTitle, DialogContent, DialogActions,
  Button, TextField, Typography, IconButton, Box,
  CircularProgress, Snackbar, Alert, useMediaQuery, useTheme
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import EditIcon from '@mui/icons-material/Edit';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';

const validationSchema = yup.object().shape({
  seatNumber: yup
    .string()
    .required('Seat number is required'),
    // .matches(/^\d{1,3}$/, 'Seat number must be 1 to 3 digits'),
  coachNumber: yup
    .string()
    .required('Coach number is required'),
    // .matches(/^[A-Za-z]{1,3}\d{0,2}$/, 'Coach number must be 1–3 letters followed by up to 2 digits'),
});

const UpdateJourneyPopup = ({ open, onClose, journeyId }) => {
  const [loading, setLoading] = useState(false);
  const [fetchLoading, setFetchLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState(false);
  const [message, setMessage] = useState('');
  const token = localStorage.getItem('authToken');

  const theme = useTheme();
  const fullScreen = useMediaQuery(theme.breakpoints.down('sm'));

  const {
    register,
    handleSubmit,
    reset,
    watch,
    formState: { errors, isDirty }
  } = useForm({
    resolver: yupResolver(validationSchema),
    defaultValues: {
      seatNumber: '',
      coachNumber: ''
    }
  });

  const fetchJourneyDetails = async () => {
    if (!journeyId) return;

    setFetchLoading(true);
    try {
      const response = await fetch(`${import.meta.env.VITE_API_URL}/api/journeys/edit/${journeyId}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': token
        }
      });

      if (!response.ok) throw new Error('Failed to fetch journey details');

      const data = await response.json();
      if (data && data[0]) {
        reset({
          seatNumber: data[0].seat_number || '',
          coachNumber: data[0].coach_number || ''
        });
      }
    } catch (err) {
      console.error('Error fetching journey details:', err);
      setError(true);
      setMessage('Failed to load journey details. Please try again.');
    } finally {
      setFetchLoading(false);
    }
  };

  useEffect(() => {
    if (open && journeyId) {
      fetchJourneyDetails();
    }
  }, [open, journeyId]);

  const onSubmit = async (formData) => {
    if (!isDirty) {
      setError(true);
      setMessage('No changes detected');
      return;
    }

    setLoading(true);
    try {
      const response = await fetch(`${import.meta.env.VITE_API_URL}/api/journeys/${journeyId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          "Authorization": token
        },
        body: JSON.stringify({
          seat_number: formData.seatNumber,
          coach_number: formData.coachNumber
        })
      });

      const result = await response.json();
      if (response.ok) {
        setSuccess(true);
        setMessage('Journey updated successfully!');
        setTimeout(() => handleClose(), 1500);
      } else {
        throw new Error(result.message || 'Failed to update journey');
      }
    } catch (err) {
      console.error('Error updating journey:', err);
      setError(true);
      setMessage(err.message || 'Failed to update journey. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    reset();
    setSuccess(false);
    setError(false);
    onClose();
  };

  return (
    <>
      <Dialog
        open={open}
        onClose={handleClose}
        fullScreen={fullScreen}
        PaperProps={{ sx: { borderRadius: '10px', maxWidth: '500px', width: '100%' } }}
      >
        <DialogTitle sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', borderBottom: '1px solid #e0e0e0', pb: 1 }}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <EditIcon sx={{ mr: 1, color: '#DC143C' }} />
            <Typography variant="h6" sx={{ fontWeight: 'bold', color: '#DC143C' }}>
              Update Journey Details
            </Typography>
          </Box>
          <IconButton edge="end" color="inherit" onClick={handleClose} aria-label="close">
            <CloseIcon />
          </IconButton>
        </DialogTitle>

        <DialogContent sx={{ pt: 3 }}>
          {fetchLoading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
              <CircularProgress sx={{ color: '#DC143C' }} />
            </Box>
          ) : (
            <Box component="form" onSubmit={handleSubmit(onSubmit)} sx={{ mt: 1 }}>
              <Typography variant="body2" sx={{ mb: 2, color: 'text.secondary' }}>
                You can update your seat number and coach number for this journey.
              </Typography>

              <TextField
                label="Seat Number"
                fullWidth
                margin="normal"
                placeholder="Enter seat number"
                variant="outlined"
                InputLabelProps={{ shrink: true }}
                {...register('seatNumber')}
                error={!!errors.seatNumber}
                helperText={errors.seatNumber?.message}
                inputProps={{ maxLength: 10 }}
                onKeyPress={(e) => {
                  const regex = /^[a-zA-Z0-9 ]+$/;
                  if (!regex.test(e.key)) e.preventDefault();
                }}
              />

              <TextField
                label="Coach Number"
                fullWidth
                margin="normal"
                placeholder="Enter coach number"
                variant="outlined"
                InputLabelProps={{ shrink: true }}
                {...register('coachNumber')}
                error={!!errors.coachNumber} 
                inputProps={{ maxLength: 10 }}
                helperText={errors.coachNumber?.message}
                onKeyPress={(e) => {
                  const regex = /^[a-zA-Z0-9 ]+$/;
                  if (!regex.test(e.key)) e.preventDefault();
                }}
              />

              <DialogActions sx={{ px: 0, pt: 3 }}>
                <Button
                  onClick={handleClose}
                  color="inherit"
                  sx={{ borderRadius: '8px', textTransform: 'none', fontWeight: 'medium' }}
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  variant="contained"
                  disabled={loading || fetchLoading || !isDirty}
                  sx={{
                    bgcolor: isDirty ? '#DC143C' : '#cccccc',
                    '&:hover': { bgcolor: isDirty ? '#b01030' : '#cccccc' },
                    borderRadius: '8px',
                    textTransform: 'none',
                    fontWeight: 'medium',
                    position: 'relative',
                    overflow: 'hidden'
                  }}
                >
                  {loading ? 'Updating...' : isDirty ? 'Update Journey' : 'No Changes'}
                  {!isDirty && !loading && !fetchLoading && (
                    <Box
                      sx={{
                        position: 'absolute',
                        top: 0,
                        left: 0,
                        right: 0,
                        bottom: 0,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        backgroundColor: 'rgba(0, 0, 0, 0.1)',
                        borderRadius: '8px',
                      }}
                    />
                  )}
                </Button>
              </DialogActions>
            </Box>
          )}
        </DialogContent>
      </Dialog>
      
      <Snackbar anchorOrigin={{ horizontal: 'right', vertical: 'top' }} open={success} 
          onClose={() => {setSuccess(false);setError(false);}}
                autoHideDuration={1000}>
                <Alert icon={false}  onClose={() => {setSuccess(false);setError(false);}} sx={{ background: '#EFEFEF', width: '100%', borderLeft: '15px solid #DC143C', borderRadius: '10px' }}>
                  {message}
                </Alert>
              </Snackbar>
    </>
  );
};

export default UpdateJourneyPopup;
