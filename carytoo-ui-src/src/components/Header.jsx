import { <PERSON>, <PERSON><PERSON>, <PERSON>er, <PERSON><PERSON><PERSON><PERSON>on, ListItemIcon, Menu, MenuItem, useMediaQuery, useTheme } from "@mui/material";
import LogoutOutlinedIcon from '@mui/icons-material/LogoutOutlined';
import LocationOn from "@mui/icons-material/LocationOn";
import LocalShippingOutlinedIcon from '@mui/icons-material/LocalShippingOutlined';
import AdminPanelSettingsIcon from '@mui/icons-material/AdminPanelSettings';
import { useState } from "react";
import { Person2Outlined } from "@mui/icons-material";
import UseGlobalContext from "../lib/hooks/Useglobelcontext";
import { useLocation, useNavigate, useSearchParams } from "react-router-dom";
import PermIdentityOutlinedIcon from '@mui/icons-material/PermIdentityOutlined';
import logo from '../../public/logo-web-transparent.png'
import user1 from '../assets/User.png'
import ride from '../assets/Add-icon.png'
import image from '../assets/Home-background.png'
import image2 from '../assets/Train_background.png'
import image3 from '../assets/Train_background.png'
import MenuIcon from "@mui/icons-material/Menu";
import MyDrawer from "./menu";
import { isAdmin } from "../utils/adminUtils";

// import User from 'assets/User.png'
// import Addicon from "assets/Add-icon.png"


function Header() {
  const location = useLocation();

  const getBackgroundStyle = () => {
    if (location.pathname === "/") {
      return {
        backgroundImage: `url(${image})`,
        backgroundSize: "cover",
        backgroundPosition: "center",
        minHeight: "700px",
        transition: "background 0.3s ease-in-out",
        position: 'absolute',
        // zIndex:'-1'
      };
    } else if (location.pathname === "/search") {
      return {
        backgroundImage: `url(${image2})`,
        backgroundSize: "cover",
        backgroundPosition: "center",
        minHeight: "300px",
        transition: "background 0.3s ease-in-out",
      };
    } else {
      return {
        backgroundImage: `url(${image3})`,
        backgroundSize: "cover",
        backgroundPosition: "center", // Default background for other pages
        minHeight: "300px",
        transition: "background 0.3s ease-in-out",
      };
    }
  };
  const navigate = useNavigate();
  const [anchorEl, setAnchorEl] = useState(null);
  const [menuOpen, setMenuOpen] = useState(false);
  const open = Boolean(anchorEl);
  const user = JSON.parse(localStorage.getItem("user")); // Get and parse user object
  const username = user ? user.name : null;
  // console.log("user",username)
  const { isLoggedIn } = UseGlobalContext();
  // console.log("isLoggedIn",isLoggedIn)
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  const handleClick = (event) => {
    setAnchorEl(event.currentTarget);
  };
  // console.log("getBackgroundStyle",getBackgroundStyle())

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleLogout = () => {
    localStorage.clear('authToken')
    localStorage.clear('id')
    localStorage.clear('email')
    localStorage.clear('username')
    navigate('/login')
    window.location.reload()
  }
  return (
    <Box sx={{ ...getBackgroundStyle(), backgroundRepeat: 'no-repeat', alignItems: 'center', width: '100%', }}>

      <Box  sx={{ display: 'flex', justifyContent: 'space-between', padding: '10px', textAlign: 'center', alignItems: 'center', paddingLeft: isMobile ? '5px' : '24px', paddingRight: isMobile ? '5px' : '24px', background: 'transparent', 
  // position: 'fixed',
  // top: 0,
  // left: 0,
  // right: 0,
  // zIndex: 1100
   }}>
        <img
          onClick={() => navigate('/')}
          src={logo}
          alt="Next.js logo"
          width={isMobile ? 120 : 240}
          height={'auto'}

          style={{ cursor: 'pointer' }}
        />
        {/* </Link> */}
        {isMobile ?
          <>
          <MyDrawer/>
          </> :
          <Box sx={{ display: 'flex', justifyContent: 'space-between', padding: '8px', gap: '20px' }}>
            {isLoggedIn ? (
              <>
                <Button variant="outline" sx={{ color: '#DC143C', textTransform: 'none', fontWeight: '400', fontSize: '16px', lineHeight: '24px',border:'2px solid #DC143C' }}
                  onClick={handleClick}
                  startIcon={
                    <PermIdentityOutlinedIcon sx={{color:'#DC143C'}}/>
                  // <img src={user1} alt="User Icon" style={{ color: '#000000', background: 'transparent' }} width={24} height={24} />
                }
                  >Account</Button>
                <Menu anchorEl={anchorEl} open={open} onClose={handleClose}
                  sx={{
                    "& .MuiPaper-root": {
                      backgroundColor: "#DC143C",
                      color: "#FFFFFF",
                      borderRadius: "8px",
                    },
                    marginTop: '10px'
                  }}>
                  <MenuItem
                    sx={{ "&:hover": { backgroundColor: "#DC143C" }, background: '#DC143C', color: "#FFFFFF" }}>
                    <ListItemIcon>
                      <Person2Outlined fontSize="small" sx={{ color: '#EFEFEF' }} />
                    </ListItemIcon>
                    Hi {username}
                  </MenuItem>
                  <MenuItem onClick={() =>{ navigate("/my-trips");handleClose()}}
                    sx={{ "&:hover": { backgroundColor: "#DC143C" }, background: '#DC143C', color: "#FFFFFF", margin: '5px 0px' }}>
                    <ListItemIcon>
                      <LocationOn fontSize="small" sx={{ color: '#EFEFEF' }} />
                    </ListItemIcon>
                    My Trip
                  </MenuItem>
                  <MenuItem onClick={() => {navigate("/my-orders");handleClose()}}
                    sx={{ "&:hover": { backgroundColor: "#DC143C" }, background: '#DC143C', color: "#FFFFFF", margin: '5px 0px' }}>
                    <ListItemIcon>
                      <LocalShippingOutlinedIcon fontSize="small" sx={{ color: '#EFEFEF' }} />
                    </ListItemIcon>
                    My Order
                  </MenuItem>
                  {isAdmin() &&
                    <MenuItem onClick={() => {navigate("/admin");handleClose()}}
                      sx={{ "&:hover": { backgroundColor: "#DC143C" }, background: '#DC143C', color: "#FFFFFF", margin: '5px 0px' }}>
                      <ListItemIcon>
                        <AdminPanelSettingsIcon fontSize="small" sx={{ color: '#EFEFEF' }} />
                      </ListItemIcon>
                      Admin Dashboard
                    </MenuItem>
                    }
                  <MenuItem onClick={() =>{handleLogout();handleClose()}}
                    sx={{ "&:hover": { backgroundColor: "#DC143C" }, background: '#DC143C', color: "#FFFFFF", margin: '5px 0px' }}>
                    <ListItemIcon>
                      <LogoutOutlinedIcon fontSize="small" sx={{ color: '#EFEFEF' }} />
                    </ListItemIcon>
                    Logout
                  </MenuItem>

                </Menu>

                <Button
                  onClick={() => navigate(isLoggedIn ? '/journey-reg' : '/login')}
                  variant="contained"
                  sx={{ borderRadius: '9px', background: '#DC143C', color: '#FFFFFF', textTransform: 'none', fontWeight: '400', fontSize: '16px', lineHeight: '24px' }}
                  startIcon={<img src={ride} alt="User Icon" style={{ color: '#FFFFFF', backgroundColor: '#DC143C', textColor: 'white' }} width={24} height={24} />}>Post Trip</Button>
              </>
            ) : (<> <Button
              onClick={() => navigate('/login')}
              variant="contained"
              sx={{ borderRadius: '9px', background: '#DC143C', color: '#FFFFFF', textTransform: 'none', fontWeight: '400', fontSize: '16px', lineHeight: '24px' }}
            >Login</Button></>)}
            {/* </Link> */}
          </Box>}
      </Box>
    </Box>
  );
}
export default Header;