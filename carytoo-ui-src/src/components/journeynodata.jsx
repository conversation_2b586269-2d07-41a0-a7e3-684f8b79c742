import React from "react";
import FlightTakeoffIcon from "@mui/icons-material/FlightTakeoff";
import { Box, Typography, Button } from "@mui/material";
import { useNavigate } from "react-router-dom";

const JourneyNoData = () => {
    const navigate = useNavigate()
  return (
    <Box
      display="flex"
      flexDirection="column"
      justifyContent="center"
      alignItems="center"
      height="50vh"
      textAlign="center"
      sx={{ background: "#f5f5f5, #e9ecef)", borderRadius: 2, p: 3 }}
    >
    
        <FlightTakeoffIcon sx={{ fontSize: 80, color: "#DC143C" }} />
    

      <Typography variant="h6" color="textSecondary" mt={2}>
        Your journey list is empty. Ready to start your next adventure?
      </Typography>

      <Button
        variant="contained"
        color="primary"
        sx={{ mt: 5 }}
        onClick={() => navigate('/journey-reg')}
      >
        Plan your First Journey
      </Button>
    </Box>
  );
};

export default JourneyNoData;
