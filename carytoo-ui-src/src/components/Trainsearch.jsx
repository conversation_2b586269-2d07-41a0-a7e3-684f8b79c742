import React, { useState } from 'react'
import { Alert, Box, Button, InputAdornment, Snackbar, TextField, useMediaQuery, useTheme } from '@mui/material'
import { useNavigate } from 'react-router-dom';
import image1 from '../assets/Train_icon.png'
import * as yup from 'yup';
import PopupDialog from './Popupdilog';
// import Train from 'assets/Train_icon.png'

const trainSearch = () => {
  // const router = useRoute();
  const [source, setSource] = useState("");
  const [destination, setDestination] = useState("");
  const [date, setDate] = useState("");
  const [mode] = useState("train");
  const token = localStorage.getItem('authToken')
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));  
  const navigation = useNavigate();
  const [alertOpen, setAlertOpen] = useState(false);
  const [alertMessage, setAlertMessage] = useState("");
  const [alertSeverity, setAlertSeverity] = useState("error");
  const [open,setOpen]=useState(false)

    const validationSchema = yup.object().shape({
      source: yup.string().required('Source location is required')
      .min(3,'please provide least 3 character in From'),
      destination: yup.string().required('Destination location is required')
      .min(3,'please provide least 3 character in To'),
      date: yup.string().required('Date is required')
    });

    const handleCloseAlert = () => {
      setAlertOpen(false);
    };

  const handleSearch = () => {
    if(!token){
      setOpen(true)
      // navigation(`/login`)
      return;
    }
  
     try {
            validationSchema.validateSync(
              { source, destination, date },
              { abortEarly: false }
            );
    
            // If validation passes, proceed with search
            
    // Manually construct the query string in the required format
    const queryParams = `source=${source}&destination=${destination}&date=${date}&modeOfTransport=${mode}`;
  
    // Navigate to the results page with custom query params
    navigation(`/search?${queryParams}`);
          } catch (error) {
            if (error instanceof yup.ValidationError) {
              // Get the first error message
              const errorMessage = error.errors[0];
              setAlertMessage(errorMessage);
              setAlertSeverity("error");
              setAlertOpen(true);
            }
          }
        
  };

  return (
    <Box sx={{ display: 'flex', gap: '15px', background: '#FFFFFF', alignItems: 'center', padding: '20px', borderRadius: '9px',flexDirection:isMobile ?'column' :'row',position:'relative',top:'140px' }}>
      {/* <Box > */}
      <TextField label='From' size="small" variant="outlined"
      onChange={(e)=>setSource((e.target.value).toLowerCase().trim())}
        InputProps={{
          endAdornment: (
            <InputAdornment position="end">
              <img src={image1} alt="icon" width={20} height={20} />
            </InputAdornment>
          ),
        }} />

      <TextField label='To' size="small" variant="outlined"
       onChange={(e)=>setDestination((e.target.value).toLowerCase().trim())}
        InputProps={{
          endAdornment: (
            <InputAdornment position="end">
              <img src={image1} alt="icon" width={20} height={20} />
            </InputAdornment>
          ),
        }} />

      <TextField label='Pick Your Date'
      fullWidth={isMobile ? true : false}
        InputLabelProps={{ shrink: true }} 
        inputProps={{
          min:new Date().toISOString().split('T')[0]
        }}size="small" type='date' variant="outlined" 
      onChange={(e)=>setDate(e.target.value)}
      />
       {/* <Link  href={`/search/${source}/${destination}/${date}/${mode}`}> */}
      <Button onClick={handleSearch} variant='contained' size='medium' sx={{ backgroundColor: '#DC143C', textTransform: 'none', borderRadius: '8px' }}>Search</Button>
      {/* </Link> */}
      {/* </Box> */}

      <PopupDialog
              open={open}
              onClose={() => {setOpen(false);navigation('/login');}}
              // title="Important Notice"
              message="You need to login First before Proceed to search"
              onConfirm={()=>navigation('/login')}
              confirmText="ok"
            />

       <Snackbar
              open={alertOpen}
              autoHideDuration={4000}
              onClose={handleCloseAlert}
              anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
            >
              <Alert
                onClose={handleCloseAlert}
                severity={alertSeverity}
                sx={{
                  width: '100%',
                  borderRadius: '10px',
                  border: '1px solid #DC143C',
                  background: 'white'
                }}
                icon={false}
              >
                {alertMessage}
              </Alert>
            </Snackbar>
    </Box>
  )
}

export default trainSearch