import React from 'react';
import { Paper, Typography, Box, Divider } from '@mui/material';

const PriceSummary= ({ deliveryCharges, platformCharges, discount }) => {
  const total = deliveryCharges + platformCharges - discount;

  return (
    <Box elevation={3} sx={{ borderRadius: 3,width:'100%' }}>
      <Typography fontWeight="bold" gutterBottom sx={{ fontSize: '1rem' }}>
        Price Summary
      </Typography>

      <Box display="flex" justifyContent="space-between" mb={0.5}>
        <Typography sx={{ fontSize: '1rem' }}>Delivery Charges</Typography>
        <Typography sx={{ fontSize: '1rem' }}>₹{deliveryCharges}</Typography>
      </Box>

      <Box display="flex" justifyContent="space-between" mb={0.5}>
        <Typography sx={{ fontSize: '1rem' }}>Platform Charges</Typography>
        <Typography sx={{ fontSize: '1rem' }}>₹{platformCharges}</Typography>
      </Box>

      <Box display="flex" justifyContent="space-between" mb={0.5}>
        <Typography sx={{ fontSize: '1rem' }}>Discount</Typography>
        <Typography sx={{ fontSize: '1rem' }} color="error">-₹{discount}</Typography>
      </Box>

      <Divider sx={{ my: 1 }} />

      <Box display="flex" justifyContent="space-between">
        <Typography fontWeight="bold" sx={{ fontSize: '1rem' }}>
          Total
        </Typography>
        <Typography fontWeight="bold" sx={{ fontSize: '1rem' }}>
          ₹{total}
        </Typography>
      </Box>
    </Box>
  );
};

export default PriceSummary;
