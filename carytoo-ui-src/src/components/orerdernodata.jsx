import React from "react";
import LocalShippingIcon from '@mui/icons-material/LocalShipping';
import { Box, Typography, Button } from "@mui/material";
import { useNavigate } from "react-router-dom";

const OrderNoData = () => {
    const navigate = useNavigate()
  return (
    <Box
      display="flex"
      flexDirection="column"
      justifyContent="center"
      alignItems="center"
      height="50vh"
      textAlign="center"
      sx={{ background: "#f5f5f5, #e9ecef)", borderRadius: 2, p: 3 }}
    >
    
        <LocalShippingIcon sx={{ fontSize: 80, color: "#DC143C" }} />
    

      <Typography variant="h6" color="textSecondary" mt={2}>
      No orders found. Want to place your first order?
      </Typography>
    </Box>
  );
};

export default OrderNoData;
