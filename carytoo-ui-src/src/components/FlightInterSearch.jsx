import { <PERSON>ert, <PERSON>, Button, InputAdornment, Snackbar, TextField, useMediaQuery, useTheme } from '@mui/material'
// import FromIcon from 'assets/Flight-input-icon.png'
// import ToIcon from 'assets/Flight-to-icon.png'
import React, { useState } from 'react'
import { useNavigate } from 'react-router-dom';
import * as yup from 'yup';
import image from '../assets/FlightInput.png'
import image1 from '../assets/Flight-to-icon.png'
import PopupDialog from './Popupdilog';

const flightSearch = () => {
  const [source, setSource] = useState("");
  const [destination, setDestination] = useState("");
  const [date, setDate] = useState("");
  const [mode] = useState("flight-inter");
  const [alertOpen, setAlertOpen] = useState(false);
  const [alertMessage, setAlertMessage] = useState("");
  const [alertSeverity, setAlertSeverity] = useState("error");
  const [open,setOpen] = useState(false)
  const navigate = useNavigate();
//   const token = getAuthToken();
const token = localStorage.getItem('authToken');
  const theme = useTheme()
  const isMobile =useMediaQuery(theme.breakpoints.down('sm'))
  // Validation schema using Yup
  const validationSchema = yup.object().shape({
    source: yup.string().required('Source location is required')
    .min(3,'please provide least 3 character in From'),
    destination: yup.string().required('Destination location is required')
    .min(3,'please provide least 3 character in To'),
    date: yup.string().required('Date is required')
  });
    const handleSearch = () => {
      if(!token){
        // navigate(`/login`)
        // console.log("token was expired")
        // console.log("token",token)
        // setAlertMessage("You need to login first");
        // setAlertSeverity("warning");
        // setAlertOpen(true);
        // setTimeout(()=>{
          // navigate('/login')
          setOpen(true)
        // },[1000])
        return;
      }

      // Validate using Yup schema
      try {
        validationSchema.validateSync(
          { source, destination, date },
          { abortEarly: false }
        );

        // If validation passes, proceed with search
        const queryParams = `source=${source}&destination=${destination}&date=${date}&modeOfTransport=${mode}`;
        navigate(`/search?${queryParams}`);
      } catch (error) {
        if (error instanceof yup.ValidationError) {
          // Get the first error message
          const errorMessage = error.errors[0];
          setAlertMessage(errorMessage);
          setAlertSeverity("error");
          setAlertOpen(true);
        }
      }
    };

    const handleCloseAlert = () => {
      setAlertOpen(false);
    };
  return (
    <>
      <Box sx={{ display: 'flex', gap: '15px', background: '#FFFFFF', alignItems: 'center', padding: '20px', borderRadius: '9px',flexDirection:isMobile ?'column' :'row',position:'relative',top:'140px' }}>
        {/* <Box > */}
        <TextField label='From' size="small" variant="outlined"
        onChange={(e)=>setSource((e.target.value).toLowerCase().trim())}
         InputProps={{
          endAdornment: (
            <InputAdornment position="end">
              <img src={image} alt="icon" width={20} height={20} />
            </InputAdornment>
          ),
          shrink:'true'
        }}/>
        <TextField label='To' size="small" variant="outlined"
        onChange={(e)=>setDestination((e.target.value).toLowerCase().trim())}
          InputProps={{
            endAdornment: (
              <InputAdornment position="end">
                <img src={image1} alt="icon" width={20} height={20} />
              </InputAdornment>
            ),
          }}/>

        <TextField
       fullWidth={isMobile ? true : false}
         label='Pick Your Date' InputLabelProps={{ shrink: true }}
         inputProps={{
          min:new Date().toISOString().split('T')[0]
        }}
         size="small" type='date' variant="outlined"
         value={date}
         onChange={(e)=>setDate(e.target.value)} />

        {/* <Link href='/Search?'> */}
        <Button variant='contained' onClick={handleSearch} size='medium' sx={{ backgroundColor: '#DC143C', textTransform: 'none', borderRadius: '8px' }}>Search</Button>
        {/* </Link> */}
        {/* </Box> */}
      </Box>

      {/* Alert for validation errors */}
      <Snackbar
        open={alertOpen}
        autoHideDuration={4000}
        onClose={handleCloseAlert}
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
      >
        <Alert
          onClose={handleCloseAlert}
          severity={alertSeverity}
          sx={{
            width: '100%',
            borderRadius: '10px',
            border: '1px solid #DC143C',
            background: 'white'
          }}
          icon={false}
        >
          {alertMessage}
        </Alert>
      </Snackbar>

      <PopupDialog
              open={open}
              onClose={() => {setOpen(false);navigate('/login');}}
              // title="Important Notice"
              message="You need to login First before Proceed to search"
              onConfirm={()=>navigate('/login')}
              confirmText="ok"
            />
    </>
  )
}

export default flightSearch