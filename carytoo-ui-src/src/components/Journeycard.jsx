import TimeIcon from "@mui/icons-material/AccessTime";
import ArrowForwardIcon from "@mui/icons-material/ArrowForward";
import DeleteIcon from "@mui/icons-material/Delete";
import CoachIcon from "@mui/icons-material/DirectionsRailway";
import TrainIcon from "@mui/icons-material/DirectionsTransit";
import PersonOutlineOutlinedIcon from '@mui/icons-material/PersonOutlineOutlined';
import PhoneOutlinedIcon from '@mui/icons-material/PhoneOutlined';
import EditIcon from "@mui/icons-material/Edit";
import SeatIcon from "@mui/icons-material/EventSeat";
import DoneAllIcon from '@mui/icons-material/DoneAll';
import FlightTakeoffOutlinedIcon from '@mui/icons-material/FlightTakeoffOutlined';
import { Alert, Box, Button, Divider, Snackbar, Stack, Typography, IconButton, useTheme, useMediaQuery, Checkbox, FormControlLabel } from "@mui/material";
import React, { useEffect, useState } from "react";
import WhatsAppIcon from '@mui/icons-material/WhatsApp';
import ProgressBar from "./Progressbar";
import { useNavigate } from "react-router-dom";
import EmojiFlagsIcon from '@mui/icons-material/EmojiFlags';
import JourneyNoData from "./journeynodata";
import './css/Journeycard.css'
import FeedbackPopup from "./FeedbackPopup";
import { isAdmin } from "../utils/adminUtils";
import ConfirmDeleteDialog from "./ConfirmDeleteDialog";
import UpdateJourneyPopup from "./UpdateJourneyPopup";
import ForumIcon from '@mui/icons-material/Forum';
import { AirlineStops } from "@mui/icons-material";

const JourneyCard = ({ journeyData, Mode, fetchJourney }) => {
  console.log("journeyData", journeyData)
  const [arrived, setArrived] = useState(false);
  const [expandedCards, setExpandedCards] = useState({});
  const [message, setMessage] = useState()
  const [loading, setLoading] = useState(false)
  const [open, setOpen] = useState(false);
  const [openFeedback, setOpenFeedback] = useState({});
  const [openUpdatePopup, setOpenUpdatePopup] = useState({});
  const [selectedJourneyId, setSelectedJourneyId] = useState(null);
  console.log("openFeedback", openFeedback);
  const [success, setSuccess] = useState()
  const token = localStorage.getItem('authToken')
  const [reload, setReload] = useState(false);
  const [feedbackTitle, setFeedbackTitle] = useState('');
  const [feedbackValue, setFeedbackValue] = useState('');
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));
  const navigate = useNavigate();

  function formatDate(inputDate) {
    const dateObj = new Date(inputDate);
    if (isNaN(dateObj)) return "Invalid Date";

    const yearShort = String(dateObj.getFullYear()); // Last two digits of the year
    const monthShort = dateObj.toLocaleString('en-US', { month: 'short' }); // Short month name
    const day = String(dateObj.getDate()).padStart(2, '0'); // Day with leading zero

    return `${day} ${monthShort}  ${yearShort}`;
  }
  const formateFlightNumber = (flightNumber) => {
    // console.log("flightNumbers", flightNumber)
    const flightNumbers = JSON.parse(flightNumber);
    // console.log("flightNumbers", flightNumbers)
    return flightNumbers.flight_number1;
  }

  function removeSeconds(timeString) {
    // Check if the input is in the expected format
    if (!timeString || typeof timeString !== 'string') {
      return timeString;
    }

    // Split the time string by colon
    const parts = timeString.split(':');

    // If the string has at least 2 parts (hours and minutes), return only those parts
    if (parts.length >= 2) {
      return `${parts[0]}:${parts[1]}`;
    }

    // If the format is unexpected, return the original string
    return timeString;
  }

  const handleArrived = async (id) => {
    try {
      const response = await fetch(`${import.meta.env.VITE_API_URL}/api/journeys/arrived/${id}`, {
        method: "PATCH",
        headers: {
          "Authorization": token,
          "Content-Type": "application/json",
        },
      });
      const result = await response.json()
      if (response.ok) {
        setMessage(result.message)
        setSuccess(true)
        setTimeout(() => {
          fetchJourney(true)
        }, 1000);
        // setTimeout(() => {
        //   window.location.reload();
        // }, 1000);
      }
    } catch (e) {
      console.error("Server Error: " + e);
    }
  };
  const handleDelete = async (id) => {
    // console.log("delete", id)
    try {
      const response = await fetch(`${import.meta.env.VITE_API_URL}/api/journeys/${id}`, {
        method: "DELETE",
        headers: {
          "Authorization": token,
          "Content-Type": "application/json",
        },
      });
      const result = await response.json()
      if (response.ok) {
        setMessage(result.message)
        setSuccess(true)
        setTimeout(() => {
          fetchJourney(true)
        }, 1000);
        setSelectedJourneyId(null)
        // setTimeout(() => {
        //   window.location.reload();
        // }, 1000);
      }
    } catch (e) {
      console.error("Server Error: " + e);
    }
  };


  const fetchFeedback = async (journeyId) => {
    setLoading(true);
    try {
      const response = await fetch(`${import.meta.env.VITE_API_URL}/api/feedback/by-journey-id/${journeyId}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': token,
        },
      });

      const data = await response.json();

      if (response.ok) {
        setFeedbackValue(data ? data.description : null);
        setFeedbackTitle(data ? data.feedback : null);
        console.log("data", data)
        console.log("feedbacktitle", feedbackTitle)
        setLoading(false);
      }

      console.log('Feedback List:', data);
      return data;
    } catch (error) {
      console.error('Error fetching feedback by user_type:', error.message);
      return null;
    }
  }

  useEffect(() => {
    const journeyId = Object.keys(openFeedback).find(id => openFeedback[id] === true);
    if (journeyId) {
      fetchFeedback(journeyId);
    }
  }, [openFeedback]);

  const handleClick = (id) => {
    setExpandedCards((prev) => ({
      ...prev,
      [id]: !prev[id],
    }));
  };
  const handleEdit = (id) => {
    setSelectedJourneyId(id);
    setOpenUpdatePopup((prev) => ({
      ...prev,
      [id]: true
    }));
    console.log("Opening update popup for journey ID:", id);
  };
  if (!journeyData || (Array.isArray(journeyData) && journeyData.length === 0)) {
    return <JourneyNoData />;
  }
  // const newArray = Array.isArray(journeyData) ? journeyData : [journeyData];
  return (
    <Box className='main-body-journey-card' width={'fit-content'} sx={{ display: 'flex', flexDirection: 'column', gap: '25px' }}>
      {journeyData.map((trip, index) => (

        <Box key={index} sx={{ paddingTop: '10px', display: 'flex', flexDirection: 'column', background: '#f5f5f5', border: '2px solid #E5E5E5', borderRadius: '15px', padding: isMobile ? '8px' : '16px', }}>

          <Box className='journey-card-map-body'
            gap={{ xs: '10px', sm: '10px', md: '40px', lg: '80px' }}
            sx={{ display: 'flex', flexDirection: isMobile ? 'column' : 'row', }}>
            <Stack sx={{ display: 'flex', flexDirection: 'column' }}>

              <Stack direction="row" justifyContent="space-between" sx={{ mt: 1, mr: isMobile ? 0 : 2, width: '100%' }}>
                {/* Train details section */}
                <Stack spacing={2} sx={{ width: 200 }}>

                  <Stack direction="row" spacing={1} alignItems="center">
                    {/* <Typography fontWeight={700} sx={{ fontSize: '24px' }}>
                      {formatDate(trip.start_date)}
                    </Typography> */}
                  </Stack>

                    {trip.mode_of_transport === 'flight-inter' && 
                    <Typography
                    sx={{background:"#DC143C",color:"white",borderRadius:'9px',display:'flex',alignItems:'center',justifyContent:'center'}}
                    > International Flight</Typography>}
                  <Stack direction="row" spacing={1} alignItems="center">
                    
                    {Mode === "journey" ? (trip.train_number ? <TrainIcon sx={{ color: '#DC143C' }} /> : <FlightTakeoffOutlinedIcon sx={{ color: '#DC143C' }} />)
                      : (trip.Journey.train_number ? <TrainIcon sx={{ color: '#DC143C' }} /> : <FlightTakeoffOutlinedIcon sx={{ color: '#DC143C' }} />

                      )}
                      
                      
                    <Typography variant="subtitle1" fontWeight={600}>
                      {trip.train_number ? "Train No" : "Flight No"}:
                      {/* {tripData.train_number || (tripData.flightNumbers ? JSON.parse(tripData.flightNumbers).flightNumber1 : '')} */}
                      {trip.train_number || formateFlightNumber(trip.flight_number)}
                    </Typography>
                  </Stack>

                  {(trip.train_number) ? (
                    <>
                      <Stack direction="row" spacing={1} alignItems="center">
                        <SeatIcon sx={{ color: '#DC143C' }} />
                        <Typography variant="subtitle1" fontWeight={600}>
                          Seat No: {trip.seat_number}
                        </Typography>
                      </Stack>

                      <Stack direction="row" spacing={1} alignItems="center">
                        <CoachIcon sx={{ color: '#DC143C' }} />
                        <Typography variant="subtitle1" fontWeight={600}>
                          Coach No: {trip.coach_number}
                        </Typography>
                      </Stack>
                      <Stack >
                      </Stack>
                    </>
                  ) : (<>
                    <Stack direction="row" spacing={1} alignItems="center" sx={{ width: '390px !important' }}>
                      <AirlineStops sx={{ color: '#DC143C' }} />
                      <Typography variant="subtitle1" fontWeight={600}>
                        Terminal—Dep: {trip.departure_terminal_number ? trip.departure_terminal_number : null}
                      </Typography>
                    </Stack>

                    <Stack direction="row" spacing={1} alignItems="center">
                      <AirlineStops sx={{ color: '#DC143C' }} />
                      <Typography variant="subtitle1" fontWeight={600}>
                        Terminal—Arr: {trip.arrival_terminal_number ? trip.arrival_terminal_number : null}
                      </Typography>
                    </Stack>
                    <Stack >
                    </Stack>
                  </>)}
                </Stack>

                <Stack className="icon-mobile-view" direction={'column'} spacing={1}>
                  {(isAdmin() && location.pathname === '/admin') ? (null) : (

                    <Stack direction="column" height={'fit-content'} >
                      {(trip.train_number) ? (
                        <Button
                          onClick={() => handleEdit(trip.id)}
                          startIcon={<EditIcon sx={{ color: "#DC143C !important" }} />} sx={{ color: "#DC143C !important" }}>
                          Edit
                        </Button>) : (null)}
                      <Button
                        onClick={() => { setOpen(true); setSelectedJourneyId(trip.id) }}
                        startIcon={<DeleteIcon sx={{ color: "#DC143C !important" }} />} sx={{ color: "#DC143C !important" }}>
                        Delete
                      </Button>
                      {trip.order_id !== null ? (
                        <Button
                          onClick={() => setOpenFeedback((prev) => ({ ...prev, [trip.id]: true }))}
                          startIcon={<ForumIcon sx={{ color: "#DC143C !important" }} />} sx={{ color: "#DC143C !important" }}>
                          FeedBack
                        </Button>
                      ) : (null)}

                      <FormControlLabel
                        control={
                          <Checkbox
                          size="small"
                            checked={trip.is_arrived === "true"}
                            disabled={trip.is_arrived === "true"}
                            onChange={() => handleArrived(trip.id)}
                          />
                        }
                        label="Ready to receive"
                      />

                      {/* <Typography>your journey</Typography> */}
                    </Stack>

                  )}
                  {isAdmin() && trip.ticket_image_url && (
                    <Typography ml={'15px !important'} component="a" href={trip.ticket_image_url} sx={{ cursor: 'pointer', color: '#DC143C' }}>Download Ticket</Typography>)}

                     {isAdmin() && trip.feedback_entries && trip.feedback_entries.length > 0 &&
                        <Button
                          onClick={() => setOpenFeedback((prev) => ({ ...prev, [trip.id]: true }))}
                          startIcon={<ForumIcon sx={{ color: "#DC143C !important" }} />} sx={{ color: "#DC143C !important" }}>
                          FeedBack
                        </Button>}
                    
                </Stack>
              </Stack>

            </Stack>

            <Stack direction="column" >
              <Divider orientation="vertical" sx={{ pr: 1, height: "100%", color: 'E5E5E5E' }} />
            </Stack>

            <Stack direction={"row"} spacing={{ xs: 2, sm: 4, md: 8 }} pt={1} mb={'15px'} width={{ md: 470 }}>
              <Stack direction="column" spacing={2} alignItems="center" sx={{ marginRight: '15px !important' }}>
                <Stack spacing={1}>

                  <Typography variant="h6" fontWeight={700}>
                    {trip.source}
                  </Typography>

                  {/* Source Info (Body) */}
                  <Stack  >
                    <Typography variant="body1">
                      {Mode === "journey" ? trip.boarding_point : trip.Journey.boarding_point}
                    </Typography>
                  </Stack>

                   <Stack  >
                    <Typography fontWeight={700} variant="body1">
                      {trip.start_date}
                    </Typography>
                  </Stack>

                  {/* Departure Time */}
                  <Stack direction="row" spacing={1} alignItems="center">
                    <TimeIcon sx={{ color: "#DC143C" }} />
                    <Typography variant="body1" fontWeight={700}>
                      {trip.departure_time ? removeSeconds(trip.departure_time) : ''}
                    </Typography>
                  </Stack>
                </Stack>
              </Stack>

              <ArrowForwardIcon sx={{ ml: { xs: '0 !important', sm: '0 !important', md: '0' }, my: 'auto', marginTop: '5px !important' }} />

              <Stack direction="column" spacing={2} className="destination-margin" marginLeft={{ xs: '10px !important', sm: '20px !important', md: '64px' }} alignItems="center">
                <Stack spacing={1}>

                  {/* Source Title */}
                  <Typography variant="h6" fontWeight={700}>
                    {trip.destination}
                  </Typography>

                  {/* Source Info (Body) */}
                  <Stack  >
                    <Typography variant="body1">
                      {Mode === "journey" ? trip.de_boarding_point : trip.Journey.de_boarding_point}
                    </Typography>
                  </Stack>

                   <Stack  >
                    <Typography fontWeight={700} variant="body1">
                      { trip.end_date}
                    </Typography>
                  </Stack>

                  {/* Departure Time */}
                  <Stack direction="row" spacing={1} alignItems="center">
                    <TimeIcon sx={{ color: "#DC143C" }} />
                    <Typography variant="body1" fontWeight={700}>
                      {trip.arrival_time ? removeSeconds(trip.arrival_time) : ''}
                    </Typography>
                  </Stack>

                </Stack>
              </Stack>
              {trip.orders && trip.orders[0]?.price &&
                <Stack ml={3}>
                  <Typography sx={{ fontWeight: '700', fontSize: '16px' }}>Price:</Typography>
                  <Typography fontWeight={700}>Rs.{trip.orders[0].price}</Typography>
                </Stack>
              }
            </Stack>


            <Stack className="icon-web-view" direction={'column'} spacing={5}>
              {(isAdmin() && location.pathname === '/admin') ? (null) : (
                <Stack direction={{ md: 'column', lg: "row" }} height={'fit-content'} spacing={2}>
                  {(trip.train_number) ? (
                    <Button
                      onClick={() => handleEdit(trip.id)}
                      startIcon={<EditIcon sx={{ color: "#DC143C !important" }} />} sx={{ color: "#DC143C !important" }}>
                      Edit
                    </Button>) : (null)}
                  <Button
                    onClick={() => { setOpen(true); setSelectedJourneyId(trip.id) }}
                    startIcon={<DeleteIcon sx={{ color: "#DC143C !important" }} />} sx={{ color: "#DC143C !important" }}>
                    Delete
                  </Button>
                  {trip.order_id !== null ? (
                    <Button
                      onClick={() => setOpenFeedback((prev) => ({ ...prev, [trip.id]: true }))}
                      startIcon={<ForumIcon />} sx={{ color: "#DC143C !important" }}>
                      FeedBack
                    </Button>
                  ) : (null)}

                  <FormControlLabel
                    control={
                      <Checkbox
                        size="small"
                        checked={trip.is_arrived === "true"}
                        disabled={trip.is_arrived === "true"}
                        onChange={() => handleArrived(trip.id)}
                      />
                    }
                    label="Ready to receive"
                    sx={{
                      '& .MuiFormControlLabel-label': {
                        fontSize: '0.8rem',
                        width:'101px'
                      }
                    }}
                  />

                  {/* <Typography>your journey</Typography> */}
                </Stack>)}

              {isAdmin() && location.pathname === '/admin' && (
                <Box sx={{ display: 'flex', justifyContent: 'space-evenly', gap: '15px', }}>
                  <Typography ml={'15px !important'} component="a" href={trip.image_url} sx={{ cursor: 'pointer', color: '#DC143C' }}>{trip.journey_status}</Typography>

                  {trip.feedback_entries && trip.feedback_entries.length > 0  && (
                    <Button
                      onClick={() => setOpenFeedback((prev) => ({ ...prev, [trip.id]: true }))}
                      startIcon={<ForumIcon />} sx={{ color: "#DC143C !important" }}>
                      FeedBack
                    </Button>
                  )}

                  {isAdmin() && trip.ticket_image_url && location.pathname === '/admin' &&(
                    <Typography ml={'15px !important'} component="a" href={trip.ticket_image_url} sx={{ cursor: 'pointer', color: '#DC143C' }}>Download Ticket</Typography>)}

                  {trip.is_arrived === "true" && (
                    <Typography
                      fontSize={'12px'}
                      sx={{
                        color: trip.is_arrived === "true" ? "white !important" : "#DC143C !important", bgcolor: trip.is_arrived === "true" ? "green !important" : "none", borderRadius: '10px', display: 'flex', alignItems: 'center', justifyContent: 'center', gap: '5px', padding: '5px'
                      }}>
                      {<DoneAllIcon sx={{ color: "white !important", fontSize: '12px' }} />}Arrived
                    </Typography>
                  )}
                </Box>)}


            </Stack>
          </Box>
          {/* {expandedCards[trip.id] && ( */}
          <Box className='expanded-details-main' sx={{ display: 'flex', minWidth: '100%', flexDirection: isMobile ? 'column' : 'row' }}>

            {trip.orders && trip.orders[0]?.User ? (
              <Box sx={{ backgroundColor: '#FFFFFF', width: isMobile ? '100%' : 'fit-content', padding: 2, borderRadius: '15px', gap: '10px' }}>
                <Stack direction="row" spacing={2} sx={{ alignItems: 'center' }}>
                  {/* <IconButton disabled={true}> */}
                  <PersonOutlineOutlinedIcon />
                  {/* </IconButton> */}
                  <Typography>{trip.orders[0].User.username}</Typography>

                </Stack>
                <Stack direction="row" spacing={2} sx={{ alignItems: 'center' }}>
                  <IconButton
                    sx={{ padding: '0px !important' }}
                    href={`https://wa.me/${trip.orders[0].User.phoneNumber}`}
                  >
                    <WhatsAppIcon />
                  </IconButton>

                  <a href={`tel:${trip.orders[0].User.phoneNumber}`} style={{
                    textDecoration: 'none',
                    color: '#000000 !important'
                  }}>
                    <Typography sx={{ cursor: 'pointer', color: '#000000' }}>
                      {trip.orders[0].User.phoneNumber}
                    </Typography>
                  </a>

                </Stack>
              </Box>) : (null)}

            <ProgressBar mode={Mode} status={trip.journey_status} id={trip.id} />
          </Box>
          {/* )} */}
          <ConfirmDeleteDialog
            message={"Are you sure you want to delete this journey?"}
            open={open} onClose={() => setOpen(false)} onConfirm={() => handleDelete(selectedJourneyId)} />
          {openFeedback[trip.id] && (
            <FeedbackPopup
              open={openFeedback[trip.id]}
              onClose={() => setOpenFeedback((prev) => ({ ...prev, [trip.id]: false }))}
              journeyId={trip.id}
              orderId={trip.orders[0]?.order_id}
              journeyUserId={trip.user_id}
              orderUserId={trip.orders[0]?.user_id}
              type='journey'
              feedbackValue={trip.feedback_entries? trip.feedback_entries[0].description :feedbackValue}
              FeedTitle={trip.feedback_entries?trip.feedback_entries[0].feedback:feedbackTitle}
            />
          )}
          {/* Update Journey Popup */}
          {openUpdatePopup[trip.id] && (
            <UpdateJourneyPopup
              open={openUpdatePopup[trip.id]}
              onClose={() => {
                setOpenUpdatePopup((prev) => ({ ...prev, [trip.id]: false }));
                // Refresh journey data after update
                fetchJourney(true);
                setSelectedJourneyId(null)
              }}
              journeyId={trip.id}
            />
          )}
        </Box>
      ))}


      <Snackbar anchorOrigin={{ horizontal: 'right', vertical: 'top' }} open={success} onClose={() => setSuccess(false)}
        autoHideDuration={1000}>
        <Alert icon={false} onClose={() => setSuccess(false)} sx={{ background: '#EFEFEF', width: '100%', border: '1px solid #DC143C', borderRadius: '10px', color: "#000000" }}>
          {message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default JourneyCard;
