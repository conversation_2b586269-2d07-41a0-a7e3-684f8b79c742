import React, { useState } from "react";
import { Box, Drawer, IconButton, List, ListItem, ListItemButton, ListItemIcon, ListItemText } from "@mui/material";
import MenuIcon from "@mui/icons-material/Menu";
import UseGlobalContext from "../lib/hooks/Useglobelcontext";
import LocationOn from "@mui/icons-material/LocationOn";
import LocalShippingOutlinedIcon from '@mui/icons-material/LocalShippingOutlined';
import LogoutOutlinedIcon from '@mui/icons-material/LogoutOutlined';
import AdminPanelSettingsIcon from '@mui/icons-material/AdminPanelSettings';
import ride from '../assets/Add-icon.png'
import { useNavigate } from "react-router-dom";
import { isAdmin } from "../utils/adminUtils";

const MyDrawer = () => {
  const [open, setOpen] = useState(false);
  const navigate = useNavigate();
  const { isLoggedIn } = UseGlobalContext();

  const toggleDrawer = (state) => () => {
    setOpen(state);
  };

  const handleNavigation = (path) => {
    navigate(path); // Navigate to the given path
    setOpen(false); // Close the drawer after navigation
  };
  const handleLogout = () => {
    localStorage.clear('authToken')
    localStorage.clear('id')
    localStorage.clear('email')
    localStorage.clear('username')
    navigate('/login')
    window.location.reload()
  }

  // Create the menu items list
  const menuItems = [
    {
      name:'My Trips',
      path:'/my-trips',
      icon:<LocationOn sx={{color:'white'}}/>
    },
    {
      name:'My orders',
      path:'/my-orders',
      icon:<LocalShippingOutlinedIcon sx={{color:'white'}}/>
    },
    {
      name:'publish Ride',
      path:'/journey-reg',
      icon:<img src={ride} alt="ride" style={{ width: 24, height: 24 }} />
    }
  ];

  // Add admin link if user is admin
  if (isAdmin()) {
    menuItems.push({
      name:'Admin Dashboard',
      path:'/admin',
      icon:<AdminPanelSettingsIcon sx={{color:'white'}}/>
    });
  }

  // Add logout at the end
  menuItems.push({
    name:'Logout',
    action: handleLogout,
    icon:<LogoutOutlinedIcon sx={{color:'white'}}/>
  });

  const list = menuItems;
  return (
    <Box>
      <IconButton onClick={toggleDrawer(true)}>
        <MenuIcon  sx={{color:'#FF5C7A'}}/>
      </IconButton>
      <Drawer className="drawer-main" sx={{'& .MuiDrawer-paper':{background:'#DC143C'}}} anchor="right"  open={open} onClose={toggleDrawer(false)}>
        <List sx={{background:'#DC143C'}}>
        {isLoggedIn ? (
            list.map((item, index) => (
              <ListItemButton key={index}
               onClick={() => {
                if (item.action) {
                  item.action(); // Call the logout function directly
                } else {
                  handleNavigation(item.path);
                }
                setOpen(false); // Close drawer after clicking
              }}>
                <ListItemIcon>{item.icon}</ListItemIcon>
                <ListItemText sx={{ color: "white" }} primary={item.name} />
              </ListItemButton>

            ))
          ) : (
            <ListItemButton onClick={() => handleNavigation("/login")}>
              <ListItemIcon>
                <LogoutOutlinedIcon sx={{ color: "white" }} />
              </ListItemIcon>
              <ListItemText sx={{ color: "white" }} primary="Login" />
            </ListItemButton>
          )}
        </List>
      </Drawer>
    </Box>
  );
};

export default MyDrawer;
