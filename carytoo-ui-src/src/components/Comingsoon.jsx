// ComingSoon.jsx
import React from 'react';
import { Box, Typography, Button, Container } from '@mui/material';
import AccessTimeIcon from '@mui/icons-material/AccessTime';

const ComingSoon = () => {
  return (
    <Box
    mt={8}
      sx={{
        // backgroundColor: '#f7f7f7',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        textAlign: 'center',
        padding: 4,
      }}
    >
      <Container maxWidth="sm">
        <AccessTimeIcon sx={{ fontSize: 60, color: '#DC143C', mb: 2 }} />
        <Typography variant="h3" gutterBottom>
          Coming Soon
        </Typography>
        <Typography variant="subtitle1" sx={{ mb: 3 }}>
          Carytoo is launching international flight shipments soon. Stay tuned!
        </Typography>
        {/* <Button variant="contained" size="large" color="primary">
          Notify Me
        </Button> */}
      </Container>
    </Box>
  );
};

export default ComingSoon;
