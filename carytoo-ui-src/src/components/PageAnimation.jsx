import React, { useEffect, useState } from 'react';
import { Fade, Grow, Slide, Zoom } from '@mui/material';

/**
 * A reusable component that adds animations to page content
 * 
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.children - The content to be animated
 * @param {string} props.type - Animation type: 'fade', 'slide', 'grow', or 'zoom'
 * @param {string} props.direction - Direction for slide animation: 'up', 'down', 'left', 'right'
 * @param {number} props.timeout - Animation duration in milliseconds
 * @param {number} props.delay - Delay before animation starts in milliseconds
 * @returns {React.ReactElement} Animated component
 */
const PageAnimation = ({ 
  children, 
  type = 'fade', 
  direction = 'up', 
  timeout = 1000, 
  delay = 0 
}) => {
  const [visible, setVisible] = useState(false);

  useEffect(() => {
    const timer = setTimeout(() => {
      setVisible(true);
    }, delay);

    return () => clearTimeout(timer);
  }, [delay]);

  // Select the appropriate animation component based on type
  switch (type) {
    case 'slide':
      return (
        <Slide direction={direction} in={visible} timeout={timeout}>
          {children}
        </Slide>
      );
    case 'grow':
      return (
        <Grow in={visible} timeout={timeout} style={{ transformOrigin: '0 0 0' }}>
          <div>
          {children}
          </div>
        </Grow>
      );
    case 'zoom':
      return (
        <Zoom in={visible} timeout={timeout}>
          {children}
        </Zoom>
      );
    case 'fade':
    default:
      return (
        <Fade in={visible} timeout={timeout}>
          {children}
        </Fade>
      );
  }
};

export default PageAnimation;
