import React from 'react';
import { Dialog, DialogTitle, DialogContent, DialogActions, Button, Typography } from '@mui/material';

const PopupDialog =({
  open,
  onClose,
  title = 'Notice',
  message,
  onConfirm,
  confirmText = 'Okay',
}) => {
  const handleConfirm = () => {
    if (onConfirm) onConfirm();
    onClose();
  };
  const textArray = message.split(',');

  return (
    <Dialog open={open} >
      <DialogTitle>{title}</DialogTitle>
      <DialogContent>
        <Typography>{textArray.map((part, index) => (
        <>
          {part}
          {index < textArray.length - 1 && <br />}
        </>
      ))}</Typography>
      </DialogContent>
      <DialogActions>
        <Button onClick={handleConfirm} variant="contained">
          {confirmText}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default PopupDialog;
