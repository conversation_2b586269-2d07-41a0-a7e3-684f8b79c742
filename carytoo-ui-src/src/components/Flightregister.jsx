import AirplaneTicketIcon from "@mui/icons-material/AirplaneTicket";
import ArrowForwardIcon from "@mui/icons-material/ArrowForward";
import FlightLandIcon from "@mui/icons-material/FlightLand";
import FlightTakeoffIcon from "@mui/icons-material/FlightTakeoff";
import UploadFileIcon from '@mui/icons-material/UploadFile';
import ClearIcon from '@mui/icons-material/Clear';
import {
  Alert, Box, Button, Checkbox, Chip, FormControlLabel, InputAdornment, MenuItem, Select, Snackbar, Stack, TextField, Typography,
  Link as MuiLink, useMediaQuery, useTheme,
  styled,
  FormHelperText,
  FormControl
} from "@mui/material";
import React, { useEffect, useState } from "react";
import * as yup from "yup";
import { Controller, useForm } from "react-hook-form";
import { useNavigate, useSearchParams } from "react-router-dom";
import { yupResolver } from '@hookform/resolvers/yup';
import TermsDialog from "./TermsDialog";
import { AirlineStops } from "@mui/icons-material";

const today = new Date();
const todayDateOnly = new Date(
  today.getFullYear(),
  today.getMonth(),
  today.getDate()
);

const VisuallyHiddenInput = styled('input')({
  clip: 'rect(0 0 0 0)',
  clipPath: 'inset(50%)',
  height: 1,
  overflow: 'hidden',
  position: 'absolute',
  bottom: 0,
  left: 0,
  whiteSpace: 'nowrap',
  width: 1,
});

const flightNumberRegex = /^[a-zA-Z0-9]{3,7}$/;

const schema = yup.object().shape({
  source: yup.string().required("Source location is required").min(3,'Source location lest 3 characters'),
  destination: yup.string().required("Destination location is required").min(3,'Destination location lest 3 characters'),
  start_date: yup.date()
    .typeError("Invalid date format")
    .required("Date From is required")
    .min(todayDateOnly, "Date From cannot be in the past"),
  end_date: yup.date()
    .typeError("Invalid date format")
    .required("Date To is required")
    .min(yup.ref("start_date"), "Date To must be after Date From"),
  file: yup.mixed()
    .notRequired() // Make the field explicitly not required
    .nullable() // Allow null values
    .test("fileType", "Only images (JPG, PNG, JPEG) are allowed", function (value) {
      // Skip validation if no file is provided
      if (!value) return true;

      if (value instanceof File) {
        return ["application/pdf", "image/jpeg", "image/jpg", "image/png"].includes(value.type);
      }
      return true; // Skip validation if it's a string (existing file in edit mode)
    })
    .test("fileSize", "File size must be less than 5MB", function (value) {
      // Skip validation if no file is provided
      if (!value) return true;

      if (value instanceof File) {
        return value.size <= 5 * 1024 * 1024; // 5MB limit
      }
      return true; // Skip validation if it's a string (existing file in edit mode)
    }),

  stops: yup
    .number()
    .oneOf([0, 1, 2], "Invalid stop selection")
    .required("Number of stops is required"),


  // Flight numbers validation based on stops
  // flight_number1: yup.string().when("stops", {
  //   is: (stops) => ["Non Stop", "1 Stop", "2 Stop"].includes(stops),
  //   then: (schema) => schema.required("Flight number 1 is required"),
  // }),

  // flight_number2: yup.string().when("stops", {
  //   is: (stops) => ["1 Stop", "2 Stop"].includes(stops),
  //   then: (schema) => schema.required("Flight number 2 is required"),
  // }),

  // flight_number3: yup.string().when("stops", {
  //   is: (stops) => stops === "2 Stop",
  //   then: (schema) => schema.required("Flight number 3 is required"),
  // }),


  flight_number1: yup.string()
    .required("Flight number 1 is required")
    .matches(flightNumberRegex, "Enter a valid flight number (3–7 characters, letters & numbers only)")
    .min(3, "Flight number must be at least 3 characters")
    .max(7, "Flight number must be at most 7 characters"),

  flight_number2: yup.string()
    .when("stops", {
      is: (val) => val === 1 || val === 2,
      then: (schema) => schema
        .required("Flight number 2 is required")
        .matches(flightNumberRegex, "Enter a valid flight number (3–7 characters, letters & numbers only)")
        .min(3)
        .max(7),
      otherwise: (schema) => schema.notRequired(),
    }),

  flight_number3: yup.string()
    .when("stops", {
      is: (val) => val === 2,
      then: (schema) => schema
        .required("Flight number 3 is required")
        .matches(flightNumberRegex, "Enter a valid flight number (3–7 characters, letters & numbers only)")
        .min(3)
        .max(7),
      otherwise: (schema) => schema.notRequired(),
    }),

  // flight_number: yup.array()
  // .of(
  //   yup.string("enter the valid flight number")
  //     .transform((val) => String(val))
  //     .required("Flight number is required")
  //     .matches(/^[a-zA-Z]{2,4}\d{2,6}$/, "Enter a valid flight number (e.g., JSF6598)")
  //     .min(3, "Flight number must be at least 3 digits")
  // ),
  space_availability: yup
    .string()
    .required('Space Availability is required')
    .test(
      'not-zero',
      'Space Availability is required',
      (val) => val !== '0'
    ),
  arrival_terminal_number: yup.string().required("Arrival Terminal Number time is required"),
  departure_terminal_number: yup.string().required("Departure Terminal Number time is required"),
  departure_time: yup.string().required("Departure time is required"),
  arrival_time: yup.string().required("Arrival time is required"),
  terms: yup.bool().oneOf([true], "You must agree to continue"),
});

// Note: We're using a regular file input with custom styling instead of this hidden input

const flightRegister = () => {
  const [searchParams] = useSearchParams();
  const mode = searchParams.get("mode");
  const [selectedFileName, setSelectedFileName] = useState("");
  const [termsDialogOpen, setTermsDialogOpen] = useState(false);
  const [termsCategory, setTermsCategory] = useState('Flight');
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const { control, register, handleSubmit, formState: { errors, isDirty }, watch, setValue, getValues, reset } = useForm({
    resolver: yupResolver(schema), defaultValues: {
      source: "",
      destination: "",
      start_date: "",
      end_date: "",
      departure_time: "",
      flight_number1: "",
      flight_number2: "",
      flight_number3: "",
      arrival_time: "",
      stops: 0,
      space_availability: 0,
      arrival_terminal_number: "",
      departure_terminal_number:"",
      file: null,
      terms: false,
    }
  });

  console.log("data", getValues())
  console.log("error", errors)

  const [message, setMessage] = useState("");
  const [data, setData] = useState([]);
  const [success, setSuccess] = useState(false);
  const [stops, setStops] = useState(0);
  const [spaceAvailability, setSpaceAvailability] = useState(0)
  const user = JSON.parse(localStorage.getItem("user"))
  const userId = user ? user.userId : null;
  const token = localStorage.getItem("authToken")
  const navigate = useNavigate();
  const edit = searchParams.get("mode");

  useEffect(() => {
    if (edit == 2) {
      // setTransportType(mode);
      const editJourney = async () => {
        try {
          const response = await fetch(`${import.meta.env.VITE_API_URL}/api/journeys/edit/${searchParams.get("id")}`, {
            method: "GET",
            headers: {
              "Authorization": token,
              'Content-Type': 'application/json',
            },
          });
          const data = await response.json();
          if (response.ok) {
            // console.log("data",data)
            setData(data)
          }
        } catch (e) {
          console.error("error while fetching edit data", e)
        }
      };

      editJourney();
    }
  }, [mode]);


  const handleStopsChange = (event) => {
    const value = event.target.value;
    setStops(value);
    setValue("stops", value);

    // Clear flight number fields that are no longer needed
    if (value === 0) {
      setValue("flight_number2", "");
      setValue("flight_number3", "");
    } else if (value === 1) {
      setValue("flight_number3", "");
    }
  };

  const handleSpaceChange = (event) => {
    const value = event.target.value;
    setSpaceAvailability(value);
    setValue('space_availability', value)
  };

  const handleRemoveFile = () => {
    setSelectedFileName('');
    reset((prevData) => ({ ...prevData, file: null }));
    document.getElementById('file-input').value = ''; // Reset file input
  };


  useEffect(() => {
    if (data && data.length > 0) {
      let flightNumbers = data[0].flight_number;

      if (typeof flightNumbers === "string") {
        try {
          flightNumbers = JSON.parse(flightNumbers);
          if (typeof flightNumbers === "string") {
            flightNumbers = JSON.parse(flightNumbers);
          }
        } catch (error) {
          console.error("Error parsing flight_numbers:", error);
          flightNumbers = {};
        }
      }

      let stops = flightNumbers?.flight_number3
        ? 2
        : flightNumbers?.flight_number2
          ? 1
          : 0;
      setStops(stops)

      const formatDateForInput = (dateString) => {
        if (!dateString) return '';
        const date = new Date(dateString);
        if (isNaN(date.getTime())) return ""; // Invalid date
        return date.toISOString().split('T')[0]; // Returns YYYY-MM-DD
      };

      reset({
        source: data[0].source,
        destination: data[0].destination || "",
        start_date: formatDateForInput(data[0].start_date),
        end_date: formatDateForInput(data[0].end_date),
        departure_time: data[0].departure_time || "",
        arrival_time: data[0].arrival_time || "",
        stops: stops,
        flight_number1: flightNumbers?.flight_number1 || "",
        flight_number2: flightNumbers?.flight_number2 || "",
        flight_number3: flightNumbers?.flight_number3 || "",
        terms: true,
      });
      if (data[0]?.ticket_image_url) {
        const urlParts = data[0].ticket_image_url.split("/");
        const extractedFileName = urlParts[urlParts.length - 1];
        setValue("file", decodeURIComponent(extractedFileName));
        setSelectedFileName(decodeURIComponent(extractedFileName));
      }
    }
  }, [data]);


  const handleFileChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      setSelectedFileName(file.name);
      setValue("file", file);
    }
  };


  const onSubmit = async (data) => {
    setSuccess(false);
    const File = data.file || null;
    const file = File; // Handle null file case
    const formData = new FormData();
    formData.append("file", file);
    formData.append("user_id", userId);
    formData.append("mode_of_transport", 'flight');
    formData.append("start_date", new Date(data.start_date).toLocaleDateString("en-CA"));
    formData.append("end_date", new Date(data.end_date).toLocaleDateString("en-CA"));
    formData.append("source", (data.source).toLowerCase().trim());
    formData.append("destination", (data.destination).toLowerCase().trim());
    formData.append("departure_time", data.departure_time);
    formData.append("arrival_time", data.arrival_time);
    formData.append("space_availability", data.space_availability);
    formData.append("arrival_terminal_number", data.arrival_terminal_number);
    formData.append("departure_terminal_number", data.departure_terminal_number);
    // Create a flight number object based on the stops
    const flightNumberObj = {
      flight_number1: data.flight_number1,
      flight_number2: data.flight_number2 || "",
      flight_number3: data.flight_number3 || ""
    };
    formData.append("flight_number", JSON.stringify(flightNumberObj));
    try {
      console.log("data", formData)
      // return;
      // const token = await localStorage.getItem('authToken')
      let response;
      if (mode == 2) {
        response = await fetch(`${import.meta.env.VITE_API_URL}/api/journeys/${searchParams.get("id")}`, {
          method: "PUT",
          headers: { "Authorization": token },
          body: formData,
        });
      } else {
        response = await fetch(`${import.meta.env.VITE_API_URL}/api/journeys`, {
          method: "POST",
          headers: { "Authorization": token },
          // body: JSON.stringify(formattedData),
          body: formData,
        });
      }

      const request = await response.json();
      setMessage(request.message)
      setSuccess(true);

      if (response.ok) {
        setMessage(request.message)
        setSuccess(true);
        setMessage(mode == 2 ? 'journey updated successfully' : 'journey registered successfully')
        setSuccess(true)
        setTimeout(() => {
          navigate('/my-trips')
        }, 1000)
      }
      // alert("Flight details posted successfully!");
    } catch (err) {
      setError(err.message);
      console.error("error", err);
    }
  };

  return (
    <Box
      className="flight-raid-body"
      width='100%'
      display="flex"
      flexDirection="column"
      alignItems="center"
      justifyContent="center"
      gap={5}
      //   p={10}
      // pr={10}
      // pl={10}
      bgcolor="common.white"
    >
      <Typography variant="h6" color="black" sx={{ whiteSpace: "nowrap" }}>
        Enter Flight Details:
      </Typography>
      <form onSubmit={handleSubmit(onSubmit)} className="body-form" >
        <Stack spacing={5}>
          {/* First section: Journey from / Journey to */}
          <Box display="flex" flexDirection={{ xs: "column", sm: "row" }} justifyContent="space-between" width="100%">
            <Box width={{ xs: "100%", sm: "48%" }}>
              <Stack spacing={2.5}>
                <Typography variant="body2" color="black">
                  Journey from
                </Typography>
                <TextField
                  fullWidth
                  name="source"
                  {...register("source")}
                  error={!!errors.source}
                  helperText={errors.source?.message}
                  placeholder="Departure From"
                  onKeyPress={(e) => {
                    const regex = /^[a-zA-Z0-9 ]+$/;
                    if (!regex.test(e.key)) {
                      e.preventDefault();
                    }
                  }}
                  InputProps={{
                    endAdornment: (
                      <InputAdornment position="start">
                        <FlightTakeoffIcon sx={{ color: "#DC143C" }} />
                      </InputAdornment>
                    ),
                  }}
                  sx={{ "& .MuiOutlinedInput-root": { borderRadius: "10px" } }}
                />
              </Stack>
            </Box>
            <Box width={{ xs: "100%", sm: "48%" }}>
              <Stack spacing={2.5}>
                <Typography variant="body2" color="black">
                  Journey to
                </Typography>
                <TextField
                  fullWidth
                  name="destination"
                  {...register("destination")}
                  error={!!errors.destination}
                  helperText={errors.destination?.message}
                  placeholder="Destination to"
                  onKeyPress={(e) => {
                    const regex = /^[a-zA-Z0-9 ]+$/;
                    if (!regex.test(e.key)) {
                      e.preventDefault();
                    }
                  }}
                  InputProps={{
                    endAdornment: (
                      <InputAdornment position="start">
                        <FlightLandIcon sx={{ color: "#DC143C" }} />
                      </InputAdornment>
                    ),
                  }}
                  sx={{ "& .MuiOutlinedInput-root": { borderRadius: "10px" } }}
                />
              </Stack>
            </Box>
          </Box>

          {/* Second section: Date from / Date to */}
          <Box display="flex" justifyContent="space-between" flexDirection={{ xs: "column", sm: "row" }} width="100%">
            <Box width={{ xs: "100%", sm: "48%" }}>
              <Stack spacing={2.5}>
                <Typography variant="body2" color="black">
                  Date from
                </Typography>
                <TextField
                  fullWidth
                  name="start_date"
                  type="date"
                  {...register("start_date")}
                  error={!!errors.start_date}
                  helperText={errors.start_date?.message}
                  onKeyPress={(e) => {
                    const regex = /^[a-zA-Z0-9 ]+$/;
                    if (!regex.test(e.key)) {
                      e.preventDefault();
                    }
                  }}
                  inputProps={{
                    min: new Date().toISOString().split('T')[0]
                  }}
                  placeholder="Date of journey start"
                  sx={{
                    "& .MuiOutlinedInput-root": { borderRadius: "10px" },
                    '& input[type="date"]::-webkit-calendar-picker-indicator': {
                      filter: 'invert(18%) sepia(92%) saturate(7456%) hue-rotate(341deg) brightness(92%) contrast(102%)', // red
                      cursor: 'pointer',
                    },
                  }}
                />
              </Stack>
            </Box>
            <Box width={{ xs: "100%", sm: "48%" }}>
              <Stack spacing={2.5}>
                <Typography variant="body2" color="black">
                  Date to
                </Typography>
                <TextField
                  fullWidth
                  type="date"
                  name="end_date"
                  {...register("end_date")}
                  error={!!errors.end_date}
                  onKeyPress={(e) => {
                    const regex = /^[a-zA-Z0-9 ]+$/;
                    if (!regex.test(e.key)) {
                      e.preventDefault();
                    }
                  }}
                  inputProps={{
                    min: new Date().toISOString().split('T')[0]
                  }}
                  helperText={errors.end_date?.message}
                  placeholder="Date of journey end"
                  sx={{
                    "& .MuiOutlinedInput-root": { borderRadius: "10px" },
                    '& input[type="date"]::-webkit-calendar-picker-indicator': {
                      filter: 'invert(18%) sepia(92%) saturate(7456%) hue-rotate(341deg) brightness(92%) contrast(102%)', // red
                      cursor: 'pointer',
                    },
                  }}
                />
              </Stack>
            </Box>
          </Box>

          {/* Third section: Time of departure / Time of arrival */}
          <Box display="flex" justifyContent="space-between" flexDirection={{ xs: "column", sm: "row" }} width="100%">
            <Box width={{ xs: "100%", sm: "48%" }}>
              <Stack spacing={2.5}>
                <Typography variant="body2" color="black">
                  Time of departure
                </Typography>
                <TextField
                  fullWidth
                  type="time"
                  name="departure_time"
                  {...register("departure_time")}
                  onKeyPress={(e) => {
                    const regex = /^[a-zA-Z0-9 ]+$/;
                    if (!regex.test(e.key)) {
                      e.preventDefault();
                    }
                  }}
                  error={!!errors.departure_time}
                  helperText={errors.departure_time?.message}
                  placeholder="Date of journey start"
                  sx={{
                    "& .MuiOutlinedInput-root": { borderRadius: "10px" },
                    '& input[type="time"]::-webkit-calendar-picker-indicator': {
                      filter: 'invert(18%) sepia(92%) saturate(7456%) hue-rotate(341deg) brightness(92%) contrast(102%)',
                    },
                  }}
                />
              </Stack>
            </Box>
            <Box width={{ xs: "100%", sm: "48%" }}>
              <Stack spacing={2.5}>
                <Typography variant="body2" color="black">
                  Time of arrival
                </Typography>
                <TextField
                  fullWidth
                  type="time"
                  name="arrival_time"
                  {...register("arrival_time")}
                  error={!!errors.arrival_time}
                  helperText={errors.arrival_time?.message}
                  onKeyPress={(e) => {
                    const regex = /^[a-zA-Z0-9 ]+$/;
                    if (!regex.test(e.key)) {
                      e.preventDefault();
                    }
                  }}
                  placeholder="Date of journey end"
                  sx={{
                    "& .MuiOutlinedInput-root": { borderRadius: "10px" },
                    '& input[type="time"]::-webkit-calendar-picker-indicator': {
                      filter: 'invert(18%) sepia(92%) saturate(7456%) hue-rotate(341deg) brightness(92%) contrast(102%)',
                    },
                  }}
                />
              </Stack>
            </Box>
          </Box>

          <Box display="flex" justifyContent="space-between" sx={{ alignItems: 'unset' }} flexDirection={{ xs: "column", sm: "row" }} width="100%" className="stop-and-upload-main">
            <Box width={{ xs: "100%", sm: "48%" }}>
              <Stack spacing={2.5}>
                <Typography variant="body2" color="black">
                  No. of Stops
                </Typography>
                <Select
                  fullWidth
                  value={stops}
                  aria-hidden="false"
                  name="stops"
                  {...register("stops")}
                  onChange={handleStopsChange}
                  onKeyPress={(e) => {
                    const regex = /^[a-zA-Z0-9 ]+$/;
                    if (!regex.test(e.key)) {
                      e.preventDefault();
                    }
                  }}
                  helperText={errors.stops?.message}
                  sx={{ "& .MuiOutlinedInput-root": { borderRadius: "10px" } }}
                >
                  <MenuItem value={0}>Non Stop</MenuItem>
                  <MenuItem value={1}>1 Stop</MenuItem>
                  <MenuItem value={2}>2 Stop</MenuItem>
                </Select>
              </Stack>
            </Box>
            <Box width={{ xs: "100%", sm: "48%" }}>
              {/* <Stack spacing={2.5} direction={"column"}>
                 <Typography variant="body2" color="black">Flight Numbers</Typography>
                <TextField
                  fullWidth
                  name="flightNumbers"
                  placeholder="Add Flight Numbers"
                  onKeyDown={(e) => {
                    if (e.key === "Enter") {
                      e.preventDefault();
                      const flightNumber = e.target.value.trim();

                      // Max limit based on `stop`
                      const maxAllowed = stops + 1;

                      if (flightNumbers.length >= maxAllowed) {
                        setMessage(`You can only add ${maxAllowed} flight number${maxAllowed > 1 ? "s" : ""} for stop value ${stops}`);
                        setSuccess(true);
                        return;
                      }

                      if (flightNumbers) {
                        setValue("flight_number", [...flightNumbers, flightNumber]);
                        e.target.value = ""; // Clear input after adding
                      }
                    }
                  }}
                  error={!!errors.flight_number}
                  helperText={errors.flight_number? (Array.isArray(errors.flight_number) ? errors.flight_number[0].message : errors.flight_number.message):null}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <AirplaneTicketIcon sx={{ color: "#DC143C" }} />
                      </InputAdornment>
                    ),
                  }}
                  sx={{ "& .MuiOutlinedInput-root": { borderRadius: "10px" } }}
                />
                <Box display="flex" flexWrap="wrap" gap={1}>
                  {flightNumbers.map((fn, index) => (
                    <Chip
                      key={index}
                      label={fn}
                      onDelete={() => handleRemoveFlightNumber(fn)}
                      deleteIcon={<ClearIcon sx={{ color: "white" }} />}
                      color="primary"
                    />
                  ))}
                </Box>
              </Stack> */}
              <Stack direction={'column'} alignItems="flex-start" spacing={2} >
                <Typography fontWeight={600}>Upload Flight Ticket</Typography>
                <TextField
                  fullWidth
                  // label="Upload Ticket"
                  value={selectedFileName || ''}
                  helperText={errors.file?.message}
                  error={!!errors.file}
                  placeholder="Upload Ticket..."
                  onClick={() => document.getElementById('file-input').click()}
                  InputProps={{
                    readOnly: true,
                    endAdornment: (
                      <InputAdornment position="end">
                        {selectedFileName && (
                          <Button
                            variant="outlined"
                            color="error"
                            size="small"
                            onClick={(e) => { e.stopPropagation(); handleRemoveFile() }}
                            sx={{ textTransform: 'none', border: 'none' }}
                          >
                            Remove
                          </Button>
                        )}
                        <UploadFileIcon sx={{ color: '#DC143C' }} />
                      </InputAdornment>

                    ),
                    sx: {
                      "& input:-webkit-autofill": {
                        backgroundColor: "white !important",
                        WebkitBoxShadow: "0 0 0px 1000px white inset !important",
                        WebkitTextFillColor: "black !important",
                      },
                      '& input[type="date"]::-webkit-calendar-picker-indicator': {
                        filter: '#DC143C', // red
                        cursor: 'pointer',
                      },
                      cursor: 'pointer',
                    },
                  }}
                  sx={{ width: 310, cursor: 'pointer' }}
                />
                {/* <input
              id="file-input"
              type="file"
              style={{ display: 'none' }}
              {...register("file")}
              onChange={handleFileChange}
               accept="image/png, image/jpeg, image/jpg"
            /> */}
                <VisuallyHiddenInput type="file" id="file-input" accept="application/pdf,image/*" onChange={handleFileChange} />
                <Typography variant="body1" color="#000">
                  *uploading ticket increases your chances to get an order
                </Typography>
                {/* {errors.file && (
            <Typography variant="body1" color="#DC143C">
              {errors.file.message}
            </Typography>
          )} */}
              </Stack>
            </Box>
          </Box>

          {/* Flight Number Fields */}
          <Box display="flex" justifyContent="space-between" flexDirection="column" width="100%" mt={3} mb={2}>
            <Typography variant="body2" color="black" mb={2}>
              Flight Numbers
            </Typography>

            <Box display="flex" flexDirection={{ xs: "column", sm: "row" }} width="100%" gap={2.5}>
              {/* We'll always render all three fields but hide the ones not needed */}
              {/* This ensures consistent layout and width */}
              <Box width={{ xs: "100%", sm: "33%" }} sx={{ display: 'block' }}>
                <TextField
                  fullWidth
                  name="flight_number1"
                  {...register("flight_number1")}
                  error={!!errors.flight_number1}
                  helperText={errors.flight_number1?.message}
                  placeholder="Flight Number 1"
                  onKeyPress={(e) => {
                    const regex = /^[a-zA-Z0-9 -]+$/;
                    if (!regex.test(e.key)) {
                      e.preventDefault();
                    }
                  }}
                  InputProps={{
                    endAdornment: (
                      <InputAdornment position="start">
                        <AirplaneTicketIcon sx={{ color: "#DC143C" }} />
                      </InputAdornment>
                    ),
                  }}
                  sx={{ "& .MuiOutlinedInput-root": { borderRadius: "10px" } }}
                />
              </Box>

              <Box width={{ xs: "100%", sm: "33%" }} sx={{ display: stops >= 1 ? 'block' : { xs: 'none', sm: 'block' }, opacity: stops >= 1 ? 1 : { xs: 0, sm: 0.3 } }}>
                <TextField
                  fullWidth
                  name="flight_number2"
                  {...register("flight_number2")}
                  error={!!errors.flight_number2}
                  helperText={errors.flight_number2?.message}
                  placeholder="Flight Number 2"
                  disabled={!(stops >= 1)}
                  onKeyPress={(e) => {
                    const regex = /^[a-zA-Z0-9 -]+$/;
                    if (!regex.test(e.key)) {
                      e.preventDefault();
                    }
                  }}
                  InputProps={{
                    endAdornment: (
                      <InputAdornment position="start">
                        <AirplaneTicketIcon sx={{ color: stops >= 1 ? "#DC143C" : "#ccc" }} />
                      </InputAdornment>
                    ),
                  }}
                  sx={{ "& .MuiOutlinedInput-root": { borderRadius: "10px" } }}
                />
              </Box>

              <Box width={{ xs: "100%", sm: "33%" }} sx={{ display: stops === 2 ? 'block' : { xs: 'none', sm: 'block' }, opacity: stops === 2 ? 1 : { xs: 0, sm: 0.3 } }}>
                <TextField
                  fullWidth
                  name="flight_number3"
                  {...register("flight_number3")}
                  error={!!errors.flight_number3}
                  helperText={errors.flight_number3?.message}
                  placeholder="Flight Number 3"
                  disabled={!(stops === 2)}
                  onKeyPress={(e) => {
                    const regex = /^[a-zA-Z0-9 -]+$/;
                    if (!regex.test(e.key)) {
                      e.preventDefault();
                    }
                  }}
                  InputProps={{
                    endAdornment: (
                      <InputAdornment position="start">
                        <AirplaneTicketIcon sx={{ color: stops === 2 ? "#DC143C" : "#ccc" }} />
                      </InputAdornment>
                    ),
                  }}
                  sx={{ "& .MuiOutlinedInput-root": { borderRadius: "10px" } }}
                />
              </Box>
            </Box>
          </Box>



           <Box display="flex" justifyContent="space-between" flexDirection={{ xs: "column", sm: "row" }} width="100%">
           <Box width={{ xs: "100%", sm: "48%" }}>
              <Stack spacing={2.5}>
                <Typography variant="body2" color="black">
                 Departure Terminal Number
                </Typography>

                <TextField
                  fullWidth
                  name="Departure-terminal-number"
                  {...register("departure_terminal_number")}
                  error={!!errors.departure_terminal_number}
                  helperText={errors.departure_terminal_number?.message}
                  placeholder="Terminal Number  Ex.D1"
                  onKeyPress={(e) => {
                    const regex = /^[a-zA-Z0-9 -]+$/;
                    if (!regex.test(e.key)) {
                      e.preventDefault();
                    }
                  }}
                  InputProps={{
                    endAdornment: (
                      <InputAdornment position="start">
                        <AirlineStops sx={{ color: "#DC143C" }} />
                      </InputAdornment>
                    ),
                  }}
                  sx={{ "& .MuiOutlinedInput-root": { borderRadius: "10px" } }}
                />
              </Stack>
            </Box>

            <Box width={{ xs: "100%", sm: "48%" }}>
              <Stack spacing={2.5}>
                <Typography variant="body2" color="black">
                  Arrival Terminal Number
                </Typography>

                <TextField
                  fullWidth
                  name="arrival-terminal-number"
                  {...register("arrival_terminal_number")}
                  error={!!errors.arrival_terminal_number}
                  helperText={errors.arrival_terminal_number?.message}
                  placeholder="Terminal Number  Ex.D1"
                  onKeyPress={(e) => {
                    const regex = /^[a-zA-Z0-9 -]+$/;
                    if (!regex.test(e.key)) {
                      e.preventDefault();
                    }
                  }}
                  InputProps={{
                    endAdornment: (
                      <InputAdornment position="start">
                        <AirlineStops sx={{ color: "#DC143C" }} />
                      </InputAdornment>
                    ),
                  }}
                  sx={{ "& .MuiOutlinedInput-root": { borderRadius: "10px" } }}
                />
              </Stack>
            </Box>
          </Box>
          
          <Box display="flex" justifyContent="space-between" flexDirection={{ xs: "column", sm: "row" }} width="100%">
            <Box width={{ xs: "100%", sm: "48%" }}>
              <Stack spacing={2.5}>
                <Typography variant="body2" color="black">
                  Space Availability
                </Typography>

                <FormControl fullWidth error={!!errors.space_availability} sx={{ mb: 2 }}>
                  <Controller
                    name="space_availability"
                    control={control}
                    defaultValue={0}
                    rules={{ required: 'space_availability is required' }}
                    render={({ field }) => (
                      <Select {...field} labelId="space_availability-label" >
                        <MenuItem value={0}>select Space Availability in yourback</MenuItem>
                        <MenuItem value={'hand luggage'}>hand luggage </MenuItem>
                        <MenuItem value={'checkin luggage'}>checkin luggage</MenuItem>
                        <MenuItem value={'both'}>Both</MenuItem>
                      </Select>
                    )}
                  />
                  <FormHelperText sx={{ '& .MuiFormHelperText-root': { color: '#000  !important' } }}>{errors.space_availability?.message}</FormHelperText>
                </FormControl>
              </Stack>
            </Box>
{/* 
            <Box width={{ xs: "100%", sm: "48%" }}>
              <Stack spacing={2.5}>
                <Typography variant="body2" color="black">
                  Terminal Number
                </Typography>

                <TextField
                  fullWidth
                  name="terminal-number"
                  {...register("terminal_number")}
                  error={!!errors.terminal_number}
                  helperText={errors.terminal_number?.message}
                  placeholder="Terminal Number  Ex.D1"
                  onKeyPress={(e) => {
                    const regex = /^[a-zA-Z0-9 -]+$/;
                    if (!regex.test(e.key)) {
                      e.preventDefault();
                    }
                  }}
                  InputProps={{
                    endAdornment: (
                      <InputAdornment position="start">
                        <AirlineStops sx={{ color: "#DC143C" }} />
                      </InputAdornment>
                    ),
                  }}
                  sx={{ "& .MuiOutlinedInput-root": { borderRadius: "10px" } }}
                />
              </Stack>
            </Box> */}
          </Box>


        </Stack>
        {/* <Stack direction={'column'} alignItems="flex-start" spacing={2} sx={{ marginTop: '30px' }}>
            <Typography fontWeight={600}>Upload Flight Ticket</Typography>
            <TextField
              fullWidth
              // label="Upload Ticket"
              value={selectedFileName || ''}
              placeholder="Upload Ticket..."
              onClick={() =>document.getElementById('file-input').click()}
              InputProps={{

                readOnly: true,
                endAdornment: (
                  <InputAdornment position="end">
                    {selectedFileName && (
                      <Button
                        variant="outlined"
                        color="error"
                        size="small"
                        onClick={(e)=>{e.stopPropagation();  handleRemoveFile()} }
                        sx={{ textTransform: 'none',border:'none' }}
                      >
                        Remove
                      </Button>
                    )}
                    <UploadFileIcon sx={{ color: '#DC143C' }} />
                  </InputAdornment>

                ),
                sx: {
                  "& input:-webkit-autofill": {
                    backgroundColor: "white !important",
                    WebkitBoxShadow: "0 0 0px 1000px white inset !important",
                    WebkitTextFillColor: "black !important",
                  },
                  '& input[type="date"]::-webkit-calendar-picker-indicator': {
                    filter: '#DC143C', // red
                    cursor: 'pointer',
                  },
                  cursor: 'pointer',
                },
              }}
              sx={{ width: 310, cursor: 'pointer' }}
            />
            <input
              id="file-input"
              type="file"
              style={{ display: 'none' }}
              {...register("file")}
              onChange={handleFileChange}
               accept="image/png, image/jpeg, image/jpg"
            />
          {errors.file && (
            <Typography variant="body1" color="#DC143C">
              {errors.file.message}
            </Typography>
          )}
          </Stack> */}

        <Stack direction="row" alignItems="center" justifyContent="center" mt={2} mb={2} spacing={2}>
          <FormControlLabel
            control={
              <Checkbox
                {...register("terms")}
                sx={{
                  color: "primary.main",
                  "&.Mui-checked": {
                    color: "primary.main",
                  },
                }}
              />
            }
            label={
              <Typography variant="body1" color="black" sx={{ fontSize: isMobile ? '14px' : '16px' }}>
                I Agree with <MuiLink
                  component="span"
                  onClick={(e) => {
                    e.preventDefault();
                    setTermsCategory('Flight');
                    setTermsDialogOpen(true);
                  }}
                  sx={{
                    color: '#DC143C',
                    cursor: 'pointer',
                    textDecoration: 'none',
                    '&:hover': {
                      textDecoration: 'underline'
                    }
                  }}
                >
                  Terms & Conditions
                </MuiLink>
              </Typography>
            }
          />
          {errors.terms && <Typography mb={2} color="error">{errors.terms.message}</Typography>}
        </Stack>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
          <Typography>Your Estimated Earings Rs.500.00</Typography>
        </Box>
        <Stack direction="row" alignItems="center" justifyContent="center" mt={2} mb={2}>
          <Button
            variant="contained"
            color="primary"
            type="submit"
            disabled={mode == 2 && !isDirty}
            endIcon={<ArrowForwardIcon />}
            sx={{ px: 5, py: 2.5, borderRadius: "10px" }}
          >
            {/* <Typography
              variant="body1"
              color="common.white"
              sx={{ whiteSpace: "nowrap" }}
            > */}
            {mode == 2 ? "Update Journey" : "Confirm & Post trip"}
            {/* </Typography> */}
          </Button>
        </Stack>
      </form>

      <Snackbar anchorOrigin={{ horizontal: 'center', vertical: 'top' }} open={success} onClose={() => setSuccess(false)}
        autoHideDuration={1000}>
        <Alert icon={false} onClose={() => setSuccess(false)} sx={{ background: '#EFEFEF', width: '100%', borderLeft: '15px solid #DC143C', borderRadius: '10px' }}>
          {message}
        </Alert>
      </Snackbar>

      {/* Terms and Conditions Dialog */}
      <TermsDialog
        open={termsDialogOpen}
        onClose={() => setTermsDialogOpen(false)}
        initialCategory={termsCategory}
      />
    </Box>
  );
};

export default flightRegister;