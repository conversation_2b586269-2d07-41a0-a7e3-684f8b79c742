# UpdateJourneyPopup Component Documentation

This document provides information on how to use the UpdateJourneyPopup component in your application.

## Component Overview

The UpdateJourneyPopup is a modal dialog that allows users to update specific journey details:

- Seat Number
- Coach Number

## Installation

The component is already part of your project. You can find it in:

- `src/components/UpdateJourneyPopup.jsx`
- `src/components/css/UpdateJourneyPopup.css`

## Usage

### Basic Usage

To add the UpdateJourneyPopup to your page:

```jsx
import React, { useState } from 'react';
import UpdateJourneyPopup from '../components/UpdateJourneyPopup';

function MyPage() {
  const [openUpdatePopup, setOpenUpdatePopup] = useState(false);
  const [selectedJourneyId, setSelectedJourneyId] = useState(null);
  
  const handleOpenUpdatePopup = (journeyId) => {
    setSelectedJourneyId(journeyId);
    setOpenUpdatePopup(true);
  };
  
  const handleCloseUpdatePopup = () => {
    setOpenUpdatePopup(false);
    setSelectedJourneyId(null);
  };
  
  return (
    <div>
      {/* Your page content */}
      
      <button onClick={() => handleOpenUpdatePopup('journey-id-123')}>
        Edit Journey
      </button>
      
      <UpdateJourneyPopup 
        open={openUpdatePopup} 
        onClose={handleCloseUpdatePopup} 
        journeyId={selectedJourneyId} 
      />
    </div>
  );
}
```

## Component Props

| Prop | Type | Required | Description |
|------|------|----------|-------------|
| `open` | boolean | Yes | Controls whether the popup is open |
| `onClose` | function | Yes | Function called when the popup is closed |
| `journeyId` | string | Yes | ID of the journey to be updated |

## API Integration

The component interacts with two API endpoints:

1. **GET** `api/auth/journeys/edit/${id}` - Fetches the current journey details
2. **PUT** `api/auth/journeys/${id}` - Updates the journey details

### API Request Format

```javascript
// GET request is sent with Authorization header

// PUT request body
{
  "seat_number": "12A",
  "coach_number": "C5"
}
```

### API Response Format

```javascript
// Expected GET response
{
  "seat_number": "12A",
  "coach_number": "C5",
  // other journey details may be included but are not used
}

// Expected PUT response
{
  "message": "Journey updated successfully",
  // other details may be included
}
```

## Error Handling

The component handles various error scenarios:

1. **Fetch Errors**: If the journey details cannot be fetched, an error message is displayed
2. **Update Errors**: If the update fails, an error message is displayed
3. **Validation Errors**: If required fields are empty, validation errors are shown

## Customization

### Styling

The component uses Material-UI's styling system and can be customized using the `sx` prop:

```jsx
<UpdateJourneyPopup 
  open={openUpdatePopup} 
  onClose={handleCloseUpdatePopup} 
  journeyId={selectedJourneyId}
  // Custom styling can be passed as props if you extend the component
/>
```

### CSS Customization

You can modify the `src/components/css/UpdateJourneyPopup.css` file to change the default styles.

## Example

A complete example is available at `/update-journey-example` in your application. This page demonstrates how to use the UpdateJourneyPopup component.

## Best Practices

1. **Error Handling**: Always handle potential API errors gracefully
2. **Validation**: Validate user input before submitting
3. **User Feedback**: Provide clear feedback on success or failure
4. **Accessibility**: Ensure the popup is accessible to all users
5. **Mobile Responsiveness**: Test the popup on different screen sizes

## Troubleshooting

### The popup doesn't open

Make sure you've set the `open` prop correctly and that you're passing a valid journey ID.

### The journey details don't load

Check that your API endpoint is correctly configured and that you're passing the correct journey ID.

### The update doesn't work

Ensure that your API endpoint is properly configured and that you're sending the correct data format.
