import AccessTimeOutlined from "@mui/icons-material/AccessTimeOutlined";
import AirlineSeatReclineNormalOutlined from "@mui/icons-material/AirlineSeatReclineNormalOutlined";
import ArrowForwardOutlined from "@mui/icons-material/ArrowForwardOutlined";
import CalendarTodayOutlined from "@mui/icons-material/CalendarTodayOutlined";
import DirectionsTransitOutlined from "@mui/icons-material/DirectionsTransitOutlined";
import EventSeatOutlined from "@mui/icons-material/EventSeatOutlined";
import LocationOnOutlined from "@mui/icons-material/LocationOnOutlined";
import PlaceOutlined from "@mui/icons-material/PlaceOutlined";
import UploadFileIcon from '@mui/icons-material/UploadFile';
import {
  Alert, Box, Button, Checkbox, FormControl, FormControlLabel, IconButton, InputAdornment, Radio, RadioGroup, Snackbar, Stack, styled, TextField, Typography,
  useMediaQuery, useTheme, Link as MuiLink,
  Card
} from "@mui/material";
import React, { useEffect, useState } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import * as yup from "yup";
import TermsDialog from "./TermsDialog";
import PopupDialog from "./Popupdilog";

const today = new Date();
const todayDateOnly = new Date(
  today.getFullYear(),
  today.getMonth(),
  today.getDate()
);

const validationSchema = yup.object({
  train_number: yup.string().required("Train number is required")
    .max(5, "train number cannot exceed 5"),
  seat_number: yup
    .string()
    // .typeError("Seat number must be a number")
    .required("Seat number is required")
    // .integer("Seat number must be an integer")
    .min(1, "Seat number must be at least 1")
  // .max(3, "Seat number cannot exceed 3")
  ,
  coach_number: yup.string()
    .required("Coach number is required")
    .max(10, " coach number cannot exceed 10"),
  source: yup.string().required("City from is required").min(3, `City from is Must 3 character`),
  destination: yup.string().required("City to is required").min(3, `City To is Must 3 character`),
  boarding_point: yup.string().required("boarding_point is required"),
  de_boarding_point: yup.string().required("  de_boarding_point is required"),
  file: yup.mixed()
    .notRequired() // Make the field explicitly not required
    .nullable() // Allow null values
    .test("fileType", "Only images (JPG, PNG, JPEG) are allowed", function (value) {
      // Skip validation if no file is provided
      if (!value) return true;

      if (value instanceof File) {
        return ["application/pdf", "image/jpeg", "image/jpg", "image/png"].includes(value.type);
      }
      return true; // Skip validation if it's a string (existing file in edit mode)
    })
    .test("fileSize", "File size must be less than 5MB", function (value) {
      // Skip validation if no file is provided
      if (!value) return true;

      if (value instanceof File) {
        return value.size <= 5 * 1024 * 1024; // 5MB limit
      }
      return true; // Skip validation for non-File values
    }),

  start_date: yup.date()
    .nullable()
    .transform((value, originalValue) => (originalValue === "" ? null : value))
    .required("Date From is required")
    .min(todayDateOnly, "Date From cannot be in the past"),
  end_date: yup.date()
    .nullable()
    .transform((value, originalValue) => (originalValue === "" ? null : value))
    .required("Date To is required")
    .min(yup.ref("start_date"), "Date To must be after Date From"),
  departure_time: yup.string().required("Departure time is required"),
  arrival_time: yup.string().required("Arrival time is required"),
  terms: yup.bool().oneOf([true], "You must agree to continue"),
});

const VisuallyHiddenInput = styled('input')({
  clip: 'rect(0 0 0 0)',
  clipPath: 'inset(50%)',
  height: 1,
  overflow: 'hidden',
  position: 'absolute',
  bottom: 0,
  left: 0,
  whiteSpace: 'nowrap',
  width: 1,
});

const TrainRegister = () => {
  const [searchparams] = useSearchParams()
  const mode = searchparams.get("mode");
  const edit = searchparams.get("mode");
  const [mode_of_transport, setModeOfTransport] = useState("train");
  const [ticketStatus, setTicketStatus] = useState();
  const [success, setSuccess] = useState(false)
  const [message, setMessage] = useState('')
  const [termsDialogOpen, setTermsDialogOpen] = useState(false);
  const [termsCategory, setTermsCategory] = useState('Train');
  const theme = useTheme()
  const [data, setData] = useState([])
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));
  const navigate = useNavigate()
  // const token = localStorage.getItem('authToken')
  // const user = localStorage.getItem('id')
  // const [user, setUser] = useState(null)
  const [errors, setErrors] = useState({});
  const [fileName, setFileName] = useState("");
  const [editable, setEditable] = useState(false);
  const [openPopup, setOpenPopup] = useState(false);
  const [formData, setFormData] = useState({
    train_number: "",
    seat_number: "",
    coach_number: "",
    source: "",
    destination: "",
    start_date: "",
    end_date: "",
    boarding_point: "",
    de_boarding_point: "",
    departure_time: "",
    arrival_time: "",
  });

  const user = JSON.parse(localStorage.getItem('user'))
  const userId = user ? user.userId : null;

  const token = localStorage.getItem('authToken')

  useEffect(() => {
    if (edit == 2) {
      const editJourney = async () => {
        try {
          const response = await fetch(`${import.meta.env.VITE_API_URL}/api/journeys/edit/${searchparams.get("id")}`, {
            method: "GET",
            headers: {
              "Authorization": token,
              'Content-Type': 'application/json',
            },
          });
          const data = await response.json();
          if (response.ok) {
            console.log("data", data)
            setData(data)
          }
        } catch (e) {
          console.error("error while fetching edit data", e)
        }
      };

      editJourney();
    }
  }, [mode]);


  useEffect(() => {
    if (data && data.length > 0) {
      setTicketStatus("confirmed");

      // Format dates as YYYY-MM-DD strings for date input fields
      const formatDateForInput = (dateString) => {
        if (!dateString) return "";
        const date = new Date(dateString);
        if (isNaN(date.getTime())) return ""; // Invalid date
        return date.toISOString().split('T')[0]; // Returns YYYY-MM-DD
      };

      setFormData({
        train_number: data[0]?.train_number || "",
        seat_number: data[0]?.seat_number || "",
        coach_number: data[0]?.coach_number || "",
        source: data[0]?.source || "",
        destination: data[0]?.destination || "",
        start_date: formatDateForInput(data[0]?.start_date),
        end_date: formatDateForInput(data[0]?.end_date),
        departure_time: data[0]?.departure_time || "",
        arrival_time: data[0]?.arrival_time || "",
      });


      if (data[0]?.ticket_image_url) {
        const urlParts = data[0].ticket_image_url.split("/");
        const extractedFileName = urlParts[urlParts.length - 1];
        setFormData((prevData) => ({
          ...prevData,
          file: decodeURIComponent(extractedFileName),
        }));
        setFileName(decodeURIComponent(extractedFileName));
        // console.log("file name "+decodeURIComponent(extractedFileName))
      }
      console.log("formdata in use effect", formData);
    }
  }, [data]);

  const handleFileChange = (e) => {
    ;
    const file = e.target.files[0];
    let fileSize = file.size / (1024 * 1024);
    if (fileSize > 5) {
      setMessage("File size should be less than 5MB");
      setSuccess(true)
    } else {
      setMessage('')
      setErrors((prevErrors) => ({ ...prevErrors, file: undefined }));
      setFileName(file.name);
      console.log("file.name", file.type);
      setFormData((prevData) => ({
        ...prevData,
        file: file,
      }));
      if (mode == 2) setEditable(true);
    }
  };

  const handleRemoveFile = () => {
    setFormData((prevData) => ({ ...prevData, file: null }));
    setFileName('');
    document.getElementById('file-input').value = ''; // Reset file input
  };

  const saveJourney = async () => {
    const formDataToSend = new FormData();
    formDataToSend.append("user_id", userId);
    formDataToSend.append("mode_of_transport", mode_of_transport);
    formDataToSend.append("ticketStatus", ticketStatus);

    Object.keys(formData).forEach((key) => {
      formDataToSend.append(key, formData[key]);
    });

    try {
      console.log("formdata", formDataToSend);
      let response;
      if (mode == 2) {
        response = await fetch(`${import.meta.env.VITE_API_URL}/api/journeys/${searchparams.get("id")}`, {
          method: "PUT",
          headers: { "Authorization": token },
          body: formDataToSend,
        });
      } else {
        response = await fetch(`${import.meta.env.VITE_API_URL}/api/journeys`, {
          method: "POST",
          headers: {
            "Authorization": token
          },
          body: formDataToSend,
        });
      }

      const request = await response.json();

      if (!response.ok) {
        setMessage(request.message)
        setSuccess(true)
        throw new Error("Failed to save journey");
        return;
      }

      setMessage(mode == 2 ? 'journey updated successfully' : 'journey registered successfully')
      setSuccess(true)
      setTimeout(() => {
        navigate('/my-trips')
      }, 1000)
    } catch (error) {
      setMessage('Failed to save journey. Please try again.')
      setSuccess(true)
      console.error("Error saving journey:", error);
    }
  };

  // const handleTransportChange = (type) => {
  //   setModeOfTransport(type);
  // };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    if (name === "boarding_point" || name === "de_boarding_point" || name === "source" || name === "destination") {
      setFormData((prev) => ({ ...prev, [name]: value.toLowerCase() }));
    } else {
      setFormData((prev) => ({ ...prev, [name]: value.toLowerCase().trim() }));
    } if (mode == 2) setEditable(true);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    // Make a shallow copy to avoid mutating original state
    const processedFormData = { ...formData };
    const formDataToSend = new FormData();
    // Convert relevant fields to lowercase and trim
    ["source", "destination", "boarding_point", "de_boarding_point"].forEach((field) => {
      if (processedFormData[field]) {
        processedFormData[field] = processedFormData[field].toLowerCase().trim();
      }
    });

    // Then append to FormData
    Object.keys(processedFormData).forEach((key) => {
      formDataToSend.append(key, processedFormData[key]);
    });
    console.log("formDataToSend", formDataToSend)
    // return;
    try {
      await validationSchema.validate(formData, { abortEarly: false });
      setErrors({});
      saveJourney();
    } catch (err) {
      console.log("err", err);
      const newErrors = {};
      err.inner.forEach((error) => {
        newErrors[error.path] = error.message;
      });
      setErrors(newErrors);
    }
  };
  const formFields = [
    [
      {
        id: "train_number",
        label: "Train number",
        placeholder: "Ex.04502",
        icon: <DirectionsTransitOutlined
          sx={{
            color: ticketStatus !== "confirmed" ? 'gray' : '#DC143C',
            // color: '#DC143C'
          }} />,
        // type: 'number',
        max: 5,
      },
      {
        id: "seat_number",
        label: "Seat no",
        placeholder: "02",
        icon: <EventSeatOutlined sx={{
          color: ticketStatus !== "confirmed" ? 'gray' : '#DC143C',
          // color: '#DC143C'
        }} />,
        type: 'text'
      },
      {
        id: "coach_number",
        label: "Coach no",
        placeholder: "17GWL",
        icon: <AirlineSeatReclineNormalOutlined sx={{
          color: ticketStatus !== "confirmed" ? 'gray' : '#DC143C',
          // color: '#DC143C'
        }} />,
        type: 'text'
      },
    ],
    [
      {
        id: "source",
        label: "City from",
        placeholder: "Departure From Ex.Bangalore",
        icon: <LocationOnOutlined sx={{
          color: ticketStatus !== "confirmed" ? 'gray' : '#DC143C',
          // color: '#DC143C'
        }} />,
        type: 'text'
      },
      {
        id: "destination",
        label: "City to",
        placeholder: "Destination to  Ex.Chennai",
        icon: <PlaceOutlined sx={{
          color: ticketStatus !== "confirmed" ? 'gray' : '#DC143C',
          // color: '#DC143C'
        }} />,
        type: 'text'
      },
    ],
    [
      {
        id: "boarding_point",
        label: "Boarding Station",
        placeholder: "Station From",
        icon: <DirectionsTransitOutlined sx={{
          color: ticketStatus !== "confirmed" ? 'gray' : '#DC143C',
          // color: '#DC143C'
        }} />,
      },
      {
        id: "de_boarding_point",
        label: "Arrival Station",
        placeholder: "Station to",
        icon: <LocationOnOutlined sx={{
          color: ticketStatus !== "confirmed" ? 'gray' : '#DC143C',
          // color: '#DC143C'
        }} />,
      },
    ],
    [
      {
        id: "start_date",
        label: "Date from",
        placeholder: "Date of journey start",
        // icon: <CalendarTodayOutlined />,
        type: 'date'
      },
      {
        id: "end_date",
        label: "Date to",
        placeholder: "Date of journey end",
        // icon: <CalendarTodayOutlined />,
        type: 'date'
      },
    ],
    [
      {
        id: "departure_time",
        label: "Time of departure",
        placeholder: "Date of journey start",
        // icon: <AccessTimeOutlined />,
        type: 'time'
      },
      {
        id: "arrival_time",
        label: "Time of arrival",
        placeholder: "Date of journey end",
        // icon: <AccessTimeOutlined />,
        type: 'time'
      },
    ],
  ];

  return (
    <Box
      className='main-body-train'
      display="flex"
      flexDirection="column"
      alignItems="center"
      justifyContent="center"
      width="100%"
      gap={5}
      p={isMobile ? 3 : 0}
      // pr={10}
      // pl={10}
      bgcolor="white"
    >

      <Box maxWidth={744}>
        <Typography variant="h5" fontWeight={600} textAlign="center" mb={2}>
          Has your ticket and seat been confirmed?
        </Typography>

        <FormControl sx={{ height: '50px' }} component="fieldset">
          <RadioGroup
            row
            value={ticketStatus}
            onChange={(e) => {
              const value = e.target.value;
              setTicketStatus(value);
              if (value === "pending") {
                setOpenPopup(true); // Open the modal or dialog
              }
            }}
            sx={{ justifyContent: "center", mb: 4 }}
          >
            <FormControlLabel
              value="confirmed"
              control={
                <Radio color="primary" checked={ticketStatus === "confirmed"} />
              }
              label="Yes"
            />
            <FormControlLabel
              value="pending"
              control={<Radio color="default" />}
              label="No"
              sx={{ ml: 5 }}
            />
          </RadioGroup>
          {/* {ticketStatus !== "confirmed" ? <Typography sx={{ mb: 4}} color="error">! Please conform your ticket before proceed.. </Typography> :null} */}
        </FormControl>
        <Box
          sx={{
            p: 2,
            m: 2,
            opacity: ticketStatus === "confirmed" ? 1 : 0.2,
            pointerEvents: ticketStatus === "confirmed" ? 'auto' : 'none',
            transition: "opacity 0.5s ease-in-out",
          }}
        >
          <form onSubmit={handleSubmit}>
            <Typography variant="h5" fontWeight={600} textAlign="center" mb={3}>
              Enter Train Details:
            </Typography>
            <Stack spacing={3}>
              {formFields.map((row, rowIndex) => (
                <Box sx={{ display: "flex", flexWrap: "wrap", gap: "24px", }} key={`row-${rowIndex}`}>
                  {row.map((field, index) => (
                    <Box key={`field-${index}`}
                      sx={{
                        flex: "1 1 100%", maxWidth: "100%", "@media (min-width: 600px)": {
                          flex: "1 1 16.67%",
                          maxWidth: "50.67%",
                        },
                      }}  >
                      <Stack spacing={1}  >
                        <Typography fontWeight={600}>{field.label}</Typography>
                        <TextField
                          fullWidth
                          disabled={ticketStatus !== "confirmed"}
                          name={field.id}
                          type={field.type}
                          value={formData[field.id] || ""}
                          placeholder={field.placeholder}
                          variant="outlined"
                          error={!!errors[field.id]}
                          helperText={errors[field.id]}
                          onChange={handleInputChange}
                          onKeyPress={(e) => {
                            const regex = /^[a-zA-Z0-9 ]+$/;
                            if (!regex.test(e.key)) {
                              e.preventDefault();
                            }
                            if (field.id === "train_number") {
                              // For train_number, only allow digits
                              const regex = /^[0-9]+$/;
                              if (!regex.test(e.key)) {
                                e.preventDefault();
                              }
                            }

                          }}
                          // inputProps={{
                          //   min: 0, inputMode: 'numeric',
                          //   pattern: '[0-9]*',
                          //   maxLength: 6,
                          // }}
                          inputProps={{
                            ...(field.id === "train_number" && { maxLength: field.max, inputMode: 'numeric', pattern: '[0-9]*' }),
                            ...(field.type === "date" && { min: new Date().toISOString().split("T")[0] }),

                          }}
                          InputProps={{
                            endAdornment: ticketStatus === "confirmed" && field.icon ? (
                              <InputAdornment position="start">
                                {/* {ticketStatus === "confirmed" && field.icon} */}
                                {field.icon}
                              </InputAdornment>
                            ) : null,
                          }}
                          sx={{
                            "& .MuiOutlinedInput-root": {
                              borderRadius: 2.5,
                            },
                            '& input[type="date"]::-webkit-calendar-picker-indicator': {
                              filter: 'invert(18%) sepia(92%) saturate(7456%) hue-rotate(341deg) brightness(92%) contrast(102%)',
                              cursor: 'pointer',
                            },
                            '& input[type="time"]::-webkit-calendar-picker-indicator': {
                              filter: 'invert(18%) sepia(92%) saturate(7456%) hue-rotate(341deg) brightness(92%) contrast(102%)',

                            },
                          }}
                        />
                      </Stack>
                    </Box>
                  ))}
                </Box>
              ))}
            </Stack>


            <Stack direction={'column'} alignItems="flex-start" spacing={2} sx={{ marginTop: '30px' }}>
              <Typography fontWeight={600}>Upload Train Ticket</Typography>
              <TextField
                fullWidth
                // disabled={ticketStatus !== "confirmed"}
                // label="Upload Ticket"
                value={fileName || ''}
                placeholder="Upload Ticket..."
                onClick={() => document.getElementById('file-input').click()}
                InputProps={{
                  readOnly: true,
                  endAdornment: ticketStatus === "confirmed" ? (
                    <InputAdornment position="end">
                      {fileName && (
                        <Button
                          variant="outlined"
                          color="error"
                          size="small"
                          onClick={(e) => { e.stopPropagation(); handleRemoveFile() }}
                          sx={{ textTransform: 'none', border: 'none' }}
                        >
                          Remove
                        </Button>
                      )}
                      {/* {ticketStatus === "confirmed" && <UploadFileIcon sx={{ color: ticketStatus !== "confirmed" ? 'gray' : '#DC143C'}} />} */}
                      <UploadFileIcon sx={{ color: '#DC143C' }} />
                    </InputAdornment>

                  ) : null,
                  sx: {
                    "& input:-webkit-autofill": {
                      backgroundColor: "white !important",
                      WebkitBoxShadow: "0 0 0px 1000px white inset !important",
                      WebkitTextFillColor: "black !important",
                    },
                    '& input[type="date"]::-webkit-calendar-picker-indicator': {
                      filter: '#DC143C', // red
                      cursor: 'pointer',
                    },
                    cursor: 'pointer',
                  },
                }}
                sx={{ width: 310, cursor: 'pointer' }}
              />
              {/* <input
              id="file"
              type="file"
              style={{ display: 'none' }}
              onChange={handleFileChange}
               accept="image/png, image/jpeg, image/jpg"
            /> */}
              <VisuallyHiddenInput type="file" id="file-input" accept="application/pdf,image/*" onChange={handleFileChange} />

              <Typography variant="body1" color="#000">
                *uploading ticket increases your chances to get an order and helps us maintain the website spam free
              </Typography>
              {errors.file && (
                <Typography variant="body1" color="#DC143C">
                  {errors.file}
                </Typography>
              )}
            </Stack>

            <Box display="flex" justifyContent="center" mt={5}>
              <FormControlLabel
                control={<Checkbox defaultChecked color="primary" />}
                label={
                  <Typography variant="body1" color="black" sx={{ fontSize: isMobile ? '14px' : '16px' }}>
                    I Agree with <MuiLink
                      component="span"
                      onClick={(e) => {
                        e.preventDefault();
                        setTermsCategory('Train');
                        setTermsDialogOpen(true);
                      }}
                      sx={{
                        color: '#DC143C',
                        cursor: 'pointer',
                        textDecoration: 'none',
                        '&:hover': {
                          textDecoration: 'underline'
                        }
                      }}
                    >
                      Terms & Conditions
                    </MuiLink>
                  </Typography>
                }
              />
            </Box>
            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
              <Typography>Your Estimated Earings Rs.100.00</Typography>
            </Box>
            <Box display="flex" justifyContent="center" mt={3}>
              <Button
                variant="contained"
                type="submit"
                color="primary"
                // onClick={handleSubmit}
                disabled={mode == 2 && !editable}
                onSubmit={handleSubmit}
                endIcon={<ArrowForwardOutlined />}
                sx={{ borderRadius: 2.5, px: 3, py: 1.25 }}
              >
                {mode == 2 ? "Update Journey" : "Confirm & Post trip"}
              </Button>
            </Box>
          </form>
        </Box>
      </Box>
      <Snackbar anchorOrigin={{ vertical: 'top', horizontal: 'right' }} open={success} autoHideDuration={1000} onClose={() => setSuccess(false)}>
        <Alert onClose={() => setSuccess(false)} icon={false} sx={{ background: '#EFEFEF', borderLeft: '15px solid #DC143C', borderRadius: '10px', width: '100%' }}>
          {message}
        </Alert>
      </Snackbar>

      <PopupDialog
        open={openPopup}
        onClose={() => { setOpenPopup(false); navigate('/'); }}
        message="Post your trip once your ticket is confirmed."
        onConfirm={() => navigate('/')}
      />

      {/* Terms and Conditions Dialog */}
      <TermsDialog
        open={termsDialogOpen}
        onClose={() => setTermsDialogOpen(false)}
        initialCategory={termsCategory}
      />
    </Box>
  );
};

export default TrainRegister;
