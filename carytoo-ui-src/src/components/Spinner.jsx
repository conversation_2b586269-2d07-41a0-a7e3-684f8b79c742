import React from 'react';
import { Box } from '@mui/material';
import { keyframes } from '@emotion/react';

// Rotation animation
const rotate = keyframes`
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
`;

const Spinner = ({ size = 80, borderWidth = 6, color = '#DC143C', trackColor = '#f3f3f3' }) => {
  return (
    <Box
      role="status"
      aria-busy="true"
      sx={{
        width: size,
        height: size,
        borderRadius: '50%',
        border: `${borderWidth}px solid ${trackColor}`, // Track (background circle)
        borderTop: `${borderWidth}px solid ${color}`,    // Only top is colored
        animation: `${rotate} 1s linear infinite`,       // Spin the circle
      }}
      className="spinner-circle"
    />
  );
};

export default Spinner;
