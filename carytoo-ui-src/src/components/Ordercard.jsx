import TimeIcon from "@mui/icons-material/AccessTime";
import ArrowForwardIcon from "@mui/icons-material/ArrowForward";
import DeleteIcon from "@mui/icons-material/Delete";
import CoachIcon from "@mui/icons-material/DirectionsRailway";
import TrainIcon from "@mui/icons-material/DirectionsTransit";
import PersonOutlineOutlinedIcon from '@mui/icons-material/PersonOutlineOutlined';
import WhatsAppIcon from '@mui/icons-material/WhatsApp';
import HeadsetMicOutlinedIcon from '@mui/icons-material/HeadsetMicOutlined';
import SeatIcon from "@mui/icons-material/EventSeat";
import FlightTakeoffOutlinedIcon from '@mui/icons-material/FlightTakeoffOutlined';
import ForumIcon from '@mui/icons-material/Forum';
import { Alert, Box, Button, Divider, IconButton, Snackbar, Stack, Typography, useMediaQuery, useTheme } from "@mui/material";
import React, { useEffect, useState } from "react";
import ProgressBar from "./Progressbar";
import OrderNoData from "./orerdernodata";
import "./css/ordercard.css";
import ConfirmDeleteDialog from "./ConfirmDeleteDialog";
import FeedbackPopup from "./FeedbackPopup";
import DoneAllIcon from '@mui/icons-material/DoneAll';
import { isAdmin } from "../utils/adminUtils";
import { useLocation } from "react-router-dom";
import { AirlineStops } from "@mui/icons-material";

const OrderCard = ({ orderDate, Mode, fetchOrders }) => {
  console.log("orderDate", orderDate);
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));
  // const [expandedCards, setExpandedCards] = useState({});
  const [message, setMessage] = useState()
  const [success, setSuccess] = useState()
  const [loading, setLoading] = useState(false)
  const [open, setOpen] = useState(false);
  const [openFeedback, setOpenFeedback] = useState({});
  const [feedbackTitle, setFeedbackTitle] = useState('');
  const [feedbackValue, setFeedbackValue] = useState('');
  const [selectedOrderId, setSelectedOrderId] = useState(null);
  const location = useLocation();
  const token = localStorage.getItem('authToken')
  // const [token, setToken] = useState(null)
  //   const token = getAuthToken()


  // useEffect(() => {
  //   if (typeof window !== "undefined") {
  //     // const userId = localStorage.getItem('id')
  //     const token = localStorage.getItem('authToken')
  //     setToken(token);
  //     // setUser(userId)
  //   }
  // }, []);
  function formatDate(inputDate) {
    const dateObj = new Date(inputDate);
    if (isNaN(dateObj)) return "Invalid Date";

    const yearShort = String(dateObj.getFullYear()); // Last two digits of the year
    const monthShort = dateObj.toLocaleString('en-US', { month: 'short' }); // Short month name
    const day = String(dateObj.getDate()).padStart(2, '0'); // Day with leading zero

    return `${day}  ${monthShort} ${yearShort}`;
  }

  function removeSeconds(timeString) {
    // Check if the input is in the expected format
    if (!timeString || typeof timeString !== 'string') {
      return timeString;
    }

    // Split the time string by colon
    const parts = timeString.split(':');

    // If the string has at least 2 parts (hours and minutes), return only those parts
    if (parts.length >= 2) {
      return `${parts[0]}:${parts[1]}`;
    }

    // If the format is unexpected, return the original string
    return timeString;
  }

  const handleDelete = async (order_id) => {
    console.log("id", order_id)
    // return;
    try {
      const token = localStorage.getItem("authToken");

      const response = await fetch(`${import.meta.env.VITE_API_URL}/api/orders/order/${order_id}`, {
        method: "DELETE",
        headers: {
          "Authorization": token,
          "Content-Type": "application/json",
        },
      });
      const result = await response.json()
      if (response.ok) {
        setMessage(result.message)
        setSuccess(true)
        setTimeout(() => {
          fetchOrders(true)
        }, 1000);
        setSelectedOrderId(null)
      }
    } catch (e) {
      console.error("Server Error: " + e);
    }
  };
  const formateFlightNumber = (flightNumber) => {
    const flightNumbers = JSON.parse(flightNumber);
    return flightNumbers.flight_number1;
  }

  // const handleClick = (id) => {
  //   setExpandedCards((prev) => ({
  //     ...prev,
  //     [id]: !prev[id],
  //   }));
  // };

  const newArray = Array.isArray(orderDate) ? orderDate : [orderDate]
  const fetchFeedback = async (orderId) => {
    setLoading(true);
    try {
      const response = await fetch(`${import.meta.env.VITE_API_URL}/api/feedback/by-order-id/${orderId}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': token,
        },
      });

      const data = await response.json();

      if (response.ok) {
        setFeedbackValue(data ? data.description : null);
        setFeedbackTitle(data ? data.feedback : null);
        console.log("data", data)
        console.log("feedbacktitle", feedbackTitle)
        setLoading(false);
      }

      console.log('Feedback List:', data);
      return data;
    } catch (error) {
      console.error('Error fetching feedback by user_type:', error.message);
      return null;
    }
  }

  useEffect(() => {
    const orderId = Object.keys(openFeedback).find(id => openFeedback[id] === true);
    if (orderId) {
      fetchFeedback(orderId);
    }
  }, [openFeedback]);


  if (!orderDate || (Array.isArray(orderDate) && orderDate.length === 0)) {
    return <OrderNoData />;
  }
  return (
    <Box className="order-card-container" width={'fit-content'} sx={{ display: 'flex', flexDirection: 'column', gap: '25px' }}>
      {newArray.map((trip, index) => (
        <Box key={index} sx={{ paddingTop: '10px', display: 'flex', flexDirection: 'column', background: '#f5f5f5', border: '2px solid #E5E5E5', borderRadius: '15px', padding: isMobile ? '8px' : '16px', }}>

          <Box className='journey-card-map-body'
            gap={{ xs: '10px', sm: '10px', md: '40px', lg: '80px' }}
            sx={{ display: 'flex', flexDirection: isMobile ? 'column' : 'row', }}>
            <Stack sx={{ display: 'flex', flexDirection: isMobile ? 'row' : 'column' }}>



              <Stack direction="column" className="train-details-section" sx={{ mt: 1, width: '100%' }}>
                {/* <Typography variant="h6" mb={1} fontWeight={700} sx={{ fontSize: '24px' }}>
                  {formatDate(trip.Journey.start_date)}
                </Typography> */}

                {/* Train details section */}
                <Stack spacing={2} sx={{ width: 200 }}>
                  <Stack direction="row" spacing={1} alignItems="center">
                    {trip.Journey.train_number ? <TrainIcon sx={{ color: '#DC143C' }} /> : <FlightTakeoffOutlinedIcon sx={{ color: '#DC143C' }} />}
                    <Typography variant="subtitle1" fontWeight={600}>
                      {trip.train_number ? "Train No" : "Flight No"}:
                      {/* {tripData.train_number || (tripData.flightNumbers ? JSON.parse(tripData.flightNumbers).flightNumber1 : '')} */}
                      {trip.Journey.train_number || formateFlightNumber(trip.Journey.flight_number)}
                    </Typography>
                  </Stack>

                  {(trip.Journey.train_number) ? (
                    <>
                      <Stack direction="row" spacing={1} alignItems="center">
                        <SeatIcon sx={{ color: '#DC143C' }} />
                        <Typography variant="subtitle1" fontWeight={600}>
                          Seat No: {trip.Journey.seat_number}
                        </Typography>
                      </Stack>

                      <Stack direction="row" spacing={1} alignItems="center">
                        <CoachIcon sx={{ color: '#DC143C' }} />
                        <Typography variant="subtitle1" fontWeight={600}>
                          Coach No: {trip.Journey.coach_number}
                        </Typography>
                      </Stack>
                      <Stack >
                      </Stack>
                    </>
                  ) : (<>
                    <Stack direction="row" spacing={1} alignItems="center" sx={{ width: '390px !important' }}>
                      <AirlineStops sx={{ color: '#DC143C' }} />
                      <Typography variant="subtitle1" fontWeight={600}>
                        Terminal—Dep: {trip.Journey.departure_terminal_number ? trip.Journey.departure_terminal_number : null}
                      </Typography>
                    </Stack>

                    <Stack direction="row" spacing={1} alignItems="center">
                      <AirlineStops sx={{ color: '#DC143C' }} />
                      <Typography variant="subtitle1" fontWeight={600}>
                        Terminal—Arr: {trip.Journey.arrival_terminal_number ? trip.Journey.arrival_terminal_number : null}
                      </Typography>
                    </Stack>
                    <Stack >
                    </Stack>
                  </>)}
                </Stack>
              </Stack>

              <Stack className="icon-mobile-view" direction={'column'} spacing={1}>
                {(isAdmin() && location.pathname === '/admin') ? (
                  <>
                    <Typography ml={'15px !important'} component="a" href={trip.image_url} sx={{ cursor: 'pointer', color: '#DC143C' }}>{trip.order_status}</Typography>

                    {trip.feedback_entries && trip.feedback_entries.length > 0 &&
                      <Button
                        onClick={() => setOpenFeedback((prev) => ({ ...prev, [trip.order_id]: true }))}
                        startIcon={<ForumIcon />} sx={{ color: "#DC143C !important" }}>
                        FeedBack
                      </Button>
                    }
                  </>
                ) : (
                  <Stack direction="column" height={'fit-content'}>
                    <Button
                      className="action-button"
                      onClick={() => { setOpen(true), setSelectedOrderId(trip.order_id) }}
                      startIcon={<DeleteIcon sx={{ color: "#DC143C !important" }} />} sx={{ justifyContent: 'flex-start', color: "black !important" }}>
                      Delete
                    </Button>
                    {trip.order_id !== null ? (
                      <Button
                        onClick={() => setOpenFeedback((prev) => ({ ...prev, [trip.order_id]: true }))}
                        startIcon={<ForumIcon sx={{ color: "#DC143C !important" }} />} sx={{ color: "#DC143C !important" }}>
                        FeedBack
                      </Button>
                    ) : (null)}
                    <Button
                      className="action-button"
                      onClick={() => window.location.href = "tel:+919876543210"}
                      startIcon={<HeadsetMicOutlinedIcon color={"#DC143C !important"} />}
                      sx={{ color: "#DC143C !important" }}>
                      Support
                    </Button>
                    {/* <Typography>your journey</Typography> */}
                  </Stack>)}
                {/* <Typography onClick={() => handleClick(trip.order_id)} sx={{ cursor: 'pointer' }}>{expandedCards[trip.order_id] ? "View Less" : "View More"}</Typography> */}
              </Stack>
            </Stack>

            <Stack direction="column" >
              <Divider orientation="vertical" sx={{ pr: 1, height: "100%", color: 'E5E5E5E' }} /></Stack>

            <Stack direction={"row"} className="journey-info-section" spacing={{ sm: 5, lg: 8, md: 6 }} pt={1} mb={isMobile ? 3 : 0} width={{ lg: 470 }} justifyContent={'space-between'}>
              <Stack direction="column" spacing={2} alignItems="center">
                <Stack spacing={1}>

                  {/* Source Title */}
                  <Typography variant="h6" fontWeight={700}>
                    {trip.Journey.source}
                  </Typography>

                  {/* Source Info (Body) */}
                  <Stack  >
                    <Typography variant="body1">
                      {trip.Journey.boarding_point}
                    </Typography>
                  </Stack>

                  <Stack  >
                    <Typography fontWeight={700} variant="body1">
                      {trip.Journey.start_date}
                    </Typography>
                  </Stack>
                  {/* Departure Time */}
                  <Stack direction="row" spacing={1} alignItems="center">
                    <TimeIcon sx={{ color: "#DC143C" }} />
                    <Typography variant="body1" fontWeight={700}>
                      {trip.Journey.departure_time ? removeSeconds(trip.Journey.departure_time) : ''}
                    </Typography>
                  </Stack>

                </Stack>
              </Stack>

              <ArrowForwardIcon className="arrow-icon" sx={{ ml: { xs: '20px !important', sm: 0 }, my: 'auto', }} />

              <Stack direction="column" className="destination-section" spacing={2} alignItems="center">
                <Stack spacing={1}>

                  {/* Source Title */}
                  <Typography variant="h6" fontWeight={700}>
                    {trip.Journey.destination}
                  </Typography>

                  {/* Source Info (Body) */}
                  <Stack  >
                    <Typography variant="body1">
                      {trip.Journey.de_boarding_point}
                    </Typography>
                  </Stack>

                  <Stack  >
                    <Typography fontWeight={700} variant="body1">
                      {trip.Journey.end_date}
                    </Typography>
                  </Stack>

                  {/* Departure Time */}
                  <Stack direction="row" spacing={1} alignItems="center">
                    <TimeIcon sx={{ color: "#DC143C" }} />
                    <Typography variant="body1" fontWeight={700}>
                      {trip.Journey.arrival_time ? removeSeconds(trip.Journey.arrival_time) : ''}
                    </Typography>
                  </Stack>

                </Stack>
              </Stack>
              {trip.price &&
                <Stack sx={{ ml: { sm: 2 } }}>
                  <Typography sx={{ fontWeight: '700', fontSize: '16px' }}>Price:</Typography>
                  <Typography fontWeight={700}>Rs.{trip.price}</Typography>
                </Stack>}
            </Stack>
            <Stack className="icon-web-view" direction={'column'} spacing={5}>
              {(isAdmin() && location.pathname === '/admin') ? (
                <Box>
                  <Typography ml={'15px !important'} component="a" href={trip.image_url} sx={{ cursor: 'pointer', color: '#DC143C' }}>{trip.order_status}</Typography>

                  {trip.feedback_entries && trip.feedback_entries.length > 0 &&
                    <Button
                      onClick={() => setOpenFeedback((prev) => ({ ...prev, [trip.order_id]: true }))}
                      startIcon={<ForumIcon />} sx={{ color: "#DC143C !important" }}>
                      FeedBack
                    </Button>
                  }
                </Box>
              ) : (
                <Stack direction={{ lg: "row", md: 'column' }} height={'fit-content'} spacing={2}>

                  <Button
                    className="action-button"
                    onClick={() => { setOpen(true), setSelectedOrderId(trip.order_id) }}
                    startIcon={<DeleteIcon />} sx={{ color: "black", justifyContent: 'flex-start' }}>
                    Delete
                  </Button>

                  {trip.order_id !== null ? (
                    <Button
                      onClick={() => setOpenFeedback((prev) => ({ ...prev, [trip.order_id]: true }))}
                      startIcon={<ForumIcon />} sx={{ color: "#DC143C !important" }}>
                      FeedBack
                    </Button>
                  ) : (null)}

                  <Button
                    className="action-button"
                    // onClick={() => window.location.href = "tel:+919876543210"}
                    onClick={() => window.location.href = "mailto:<EMAIL>"}
                    startIcon={<HeadsetMicOutlinedIcon color={"#DC143C !important"} />}
                    sx={{ color: "#DC143C !important" }}>
                    Support
                  </Button>
                  {/* <Typography>your journey</Typography> */}
                </Stack>)}

              {/* <Typography onClick={() => handleClick(trip.order_id)} sx={{ cursor: 'pointer' }}>{expandedCards[trip.order_id] ? "View Less" : "View More"}</Typography> */}
            </Stack>
          </Box>
          {/* {expandedCards[trip.order_id] && ( */}
          <Box className="expanded-details" sx={{ display: 'flex', flexDirection: isMobile ? 'column' : 'row' }}>

            {trip.Journey.user ? (
              <Box className="user-info-box" sx={{ backgroundColor: '#FFFFFF', width: isMobile ? '100%' : 'fit-content', padding: 2, borderRadius: '15px', gap: '10px' }}>
                <Stack direction="row" spacing={2} sx={{ alignItems: 'center' }}>
                  {/* <IconButton disabled={true}> */}
                  <PersonOutlineOutlinedIcon />
                  {/* </IconButton> */}
                  <Typography>{trip.Journey.user?.username || 'N/A'}</Typography>
                </Stack>
                <Stack direction="row" spacing={2} sx={{ alignItems: 'center' }}>
                  <IconButton
                    sx={{ padding: '0px !important' }}
                    href={`https://wa.me/${trip.Journey.user?.phoneNumber}`}
                  >
                    <WhatsAppIcon />
                  </IconButton>
                  <Typography
                    onClick={() => `tel:${trip.Journey.user?.phoneNumber}`}
                  >  {trip.Journey.user?.phoneNumber || 'N/A'}</Typography>
                  {trip.Journey.is_arrived !== 'false' && (
                    <Typography sx={{ color: 'green', display: 'flex', alignItems: 'center' }}><DoneAllIcon sx={{ color: "green" }} />Arrived</Typography>
                  )}
                </Stack>

              </Box>
            ) : (null)}
            <ProgressBar mode={Mode} status={trip.order_status} id={trip.order_id} />
          </Box>
          {/* )} */}
          {openFeedback[trip.order_id] && (
            <FeedbackPopup
              open={openFeedback[trip.order_id]}
              onClose={() => setOpenFeedback((prev) => ({ ...prev, [trip.order_id]: false }))}
              orderId={trip.order_id}
              // feedbackValue={feedbackValue}
              // FeedTitle={feedbackTitle}
              type='order'
              journeyId={trip.Journey.id}
              journeyUserId={trip.Journey.user_id}
              orderUserId={trip.user_id}
              feedbackValue={trip.feedback_entries ? trip.feedback_entries[0].description : feedbackValue}
              FeedTitle={trip.feedback_entries? trip.feedback_entries[0].feedback : feedbackTitle}
            />
          )}
          <ConfirmDeleteDialog
            message={"Are you sure you want to delete this order?"}
            open={open} onClose={() => setOpen(false)} onConfirm={() => handleDelete(selectedOrderId)} />

          <Snackbar anchorOrigin={{ horizontal: 'right', vertical: 'top' }} open={success} onClose={() => setSuccess(false)}
            autoHideDuration={1000}>
            <Alert icon={false} onClose={() => setSuccess(false)} sx={{ background: '#EFEFEF', width: '100%', border: '1px solid #DC143C', borderRadius: '10px', color: "#000000" }}>
              {message}
            </Alert>
          </Snackbar>
        </Box>

      ))}
    </Box>
  );
};

export default OrderCard;