// context.js
import React, { createContext, useState, useEffect } from "react";
// import { updateToken } from "../api/client";

// Define user shape without TypeScript
const userShape = {
  userId: "",
  name: "",
  email: "",
  role:'',
};

// Create the context
// eslint-disable-next-line react-refresh/only-export-components
export const GlobalContext = createContext();

const GlobalContextProvider = ({ children }) => {
  const [user, setUser] = useState(() => {
    // Initialize user from localStorage if available
    const storedUser = localStorage.getItem("user");
    return storedUser ? JSON.parse(storedUser) : null;
  });
  const [token, setToken] = useState(() => {
    // Initialize from localStorage if available
    return localStorage.getItem("authToken");
  });
  const isLoggedIn = !!user;

  const updateToken = (newToken) => {
    setToken(newToken);
    localStorage.setItem("authToken", newToken);
  };

  const updateUser = (newUser) => {
    // console.log("newUser",newUser);
    // Validate newUser against userShape
    if (newUser && typeof newUser === "object") {
      const isValid = Object.keys(userShape).every((key) => key in newUser);
      if (!isValid) {
        console.warn("Invalid user object structure");
        return;
      }
      setUser(newUser);
      localStorage.setItem("user", JSON.stringify(newUser));
    } else if (newUser === null) {
      // If logout, set user to null
      setUser(null);
      localStorage.removeItem("user");
    } else {
      console.warn("Invalid user data type");
    }
  };

  // Update token whenever it changes
  // useEffect(() => {
  //   updateToken(token);
  // }, [token]);

  return (
    <GlobalContext.Provider
      value={{
        user,
        isLoggedIn,
        updateUser,
        token,
        setToken,
        updateToken,
      }}
    >
      {children}
    </GlobalContext.Provider>
  );
};

export default GlobalContextProvider;