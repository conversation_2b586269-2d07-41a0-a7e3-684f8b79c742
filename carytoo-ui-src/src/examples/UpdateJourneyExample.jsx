import React, { useState } from 'react';
import { 
  Box, 
  Typography, 
  Container, 
  Paper, 
  Button, 
  Divider,
  Card,
  CardContent,
  Grid
} from '@mui/material';
import EditIcon from '@mui/icons-material/Edit';
import UpdateJourneyPopup from '../components/UpdateJourneyPopup';

const UpdateJourneyExample = () => {
  const [openUpdatePopup, setOpenUpdatePopup] = useState(false);
  const [selectedJourneyId, setSelectedJourneyId] = useState(null);
  
  // Sample journey data for demonstration
  const sampleJourneys = [
    { id: '1', from: 'New York', to: 'Boston', date: '2023-06-15', seat_number: '12A', coach_number: 'C5' },
    { id: '2', from: 'Chicago', to: 'Detroit', date: '2023-06-18', seat_number: '7B', coach_number: 'B3' },
    { id: '3', from: 'San Francisco', to: 'Los Angeles', date: '2023-06-20', seat_number: '22C', coach_number: 'D2' },
  ];
  
  const handleOpenUpdatePopup = (journeyId) => {
    setSelectedJourneyId(journeyId);
    setOpenUpdatePopup(true);
  };
  
  const handleCloseUpdatePopup = () => {
    setOpenUpdatePopup(false);
    setSelectedJourneyId(null);
  };
  
  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Typography variant="h4" component="h1" gutterBottom sx={{ fontWeight: 'bold', color: '#DC143C' }}>
        Update Journey Example
      </Typography>
      
      <Typography variant="body1" paragraph>
        This page demonstrates how to use the UpdateJourneyPopup component to edit journey details.
      </Typography>
      
      <Paper elevation={3} sx={{ p: 3, mb: 4, borderRadius: '10px' }}>
        <Typography variant="h5" gutterBottom sx={{ fontWeight: 'bold' }}>
          Your Journeys
        </Typography>
        
        <Divider sx={{ mb: 2 }} />
        
        <Grid container spacing={3}>
          {sampleJourneys.map((journey) => (
            <Grid item xs={12} md={4} key={journey.id}>
              <Card elevation={2} sx={{ height: '100%', borderRadius: '10px' }}>
                <CardContent>
                  <Typography variant="h6" gutterBottom sx={{ fontWeight: 'bold' }}>
                    {journey.from} to {journey.to}
                  </Typography>
                  
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    Date: {journey.date}
                  </Typography>
                  
                  <Box sx={{ mt: 2, display: 'flex', justifyContent: 'space-between' }}>
                    <Box>
                      <Typography variant="body2">
                        <strong>Seat:</strong> {journey.seat_number}
                      </Typography>
                      <Typography variant="body2">
                        <strong>Coach:</strong> {journey.coach_number}
                      </Typography>
                    </Box>
                    
                    <Button
                      variant="outlined"
                      startIcon={<EditIcon />}
                      onClick={() => handleOpenUpdatePopup(journey.id)}
                      sx={{
                        borderColor: '#DC143C',
                        color: '#DC143C',
                        '&:hover': {
                          borderColor: '#b01030',
                          backgroundColor: 'rgba(220, 20, 60, 0.04)'
                        }
                      }}
                    >
                      Edit
                    </Button>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      </Paper>
      
      <Paper elevation={3} sx={{ p: 3, mb: 4, borderRadius: '10px' }}>
        <Typography variant="h5" gutterBottom sx={{ fontWeight: 'bold' }}>
          Integration Guide
        </Typography>
        
        <Divider sx={{ mb: 2 }} />
        
        <Typography variant="subtitle1" sx={{ fontWeight: 'bold', mt: 2 }}>
          1. Import the Component
        </Typography>
        <Paper 
          sx={{ 
            p: 2, 
            mt: 1, 
            mb: 3,
            bgcolor: '#f5f5f5', 
            fontFamily: 'monospace',
            overflow: 'auto'
          }}
        >
          {`import UpdateJourneyPopup from './components/UpdateJourneyPopup';`}
        </Paper>
        
        <Typography variant="subtitle1" sx={{ fontWeight: 'bold' }}>
          2. Add State Variables
        </Typography>
        <Paper 
          sx={{ 
            p: 2, 
            mt: 1, 
            mb: 3,
            bgcolor: '#f5f5f5', 
            fontFamily: 'monospace',
            overflow: 'auto'
          }}
        >
          {`const [openUpdatePopup, setOpenUpdatePopup] = useState(false);
const [selectedJourneyId, setSelectedJourneyId] = useState(null);`}
        </Paper>
        
        <Typography variant="subtitle1" sx={{ fontWeight: 'bold' }}>
          3. Create Handler Functions
        </Typography>
        <Paper 
          sx={{ 
            p: 2, 
            mt: 1, 
            mb: 3,
            bgcolor: '#f5f5f5', 
            fontFamily: 'monospace',
            overflow: 'auto'
          }}
        >
          {`const handleOpenUpdatePopup = (journeyId) => {
  setSelectedJourneyId(journeyId);
  setOpenUpdatePopup(true);
};

const handleCloseUpdatePopup = () => {
  setOpenUpdatePopup(false);
  setSelectedJourneyId(null);
};`}
        </Paper>
        
        <Typography variant="subtitle1" sx={{ fontWeight: 'bold' }}>
          4. Add the Component to Your JSX
        </Typography>
        <Paper 
          sx={{ 
            p: 2, 
            mt: 1, 
            bgcolor: '#f5f5f5', 
            fontFamily: 'monospace',
            overflow: 'auto'
          }}
        >
          {`<UpdateJourneyPopup 
  open={openUpdatePopup} 
  onClose={handleCloseUpdatePopup} 
  journeyId={selectedJourneyId} 
/>`}
        </Paper>
      </Paper>
      
      {/* The actual UpdateJourneyPopup component */}
      <UpdateJourneyPopup 
        open={openUpdatePopup} 
        onClose={handleCloseUpdatePopup} 
        journeyId={selectedJourneyId} 
      />
    </Container>
  );
};

export default UpdateJourneyExample;
