import { useState } from 'react'
import reactLogo from './assets/react.svg'
import viteLogo from '/vite.svg'
// import './App.css'
import { Box, Typography } from '@mui/material'
import { BrowserRouter, Routes, Route, Outlet } from 'react-router-dom'
import Home from './pages/Home'
import GlobalContextProvider from './context'
import { MainLayout } from './components/MainLayout'
import CustomThemeProvider from './Theme/Themeprovider'
import LoginPage from './pages/Login'
import RegisterPage from './pages/Register'
import Myjourney from './pages/Myjourney'
import Journeyregister from './pages/Journeyregister'
import Searchresult from './pages/Searcresult'
import Myorders from './pages/Myorders'
import ForgotPassword from './pages/forgotpassword'
import EmailVerify from './pages/EmailVerify'
import Admin from './pages/Admin'

function App() {
  const [count, setCount] = useState(0)
  const homescreenLayout = () => {
    return (
      <><Box sx={{ background: '#DC143C', textAlign: 'center', fontWeight: '400', fontSize: '16px', lineHeight: '24px', padding: '8px' }}><Typography>Carytoo – Turning Every Trip Into a Delivery, Every Traveler Into a Connector!</Typography></Box>
        <Outlet />
      </>)
  }

  return (
    <GlobalContextProvider>
      <CustomThemeProvider>
      <BrowserRouter>
        <Routes>
          <Route path="/" element={<MainLayout />} >
            <Route index element={<Home />} />
            <Route path='/login' element={<LoginPage />} />
            <Route path='/register' element={<RegisterPage />} />
            <Route path='/my-trips' element={<Myjourney />} />
            <Route path='/journey-reg' element={<Journeyregister />} />
            <Route path='/my-orders' element={<Myorders />} />
            <Route path='/search' element={<Searchresult />} />
            <Route path='/forget-password' element={<ForgotPassword />} />
            <Route path='/verify-email' element={<EmailVerify />} />
            <Route path='/admin' element={<Admin />} />
          </Route>
        </Routes>
      </BrowserRouter>
      </CustomThemeProvider>
    </GlobalContextProvider>


  )
}

export default App
