/**
 * Utility functions for admin-related functionality
 */

/**
 * Check if the current user has admin privileges
 * This is a simple implementation that can be expanded later
 * with proper role-based access control
 * 
 * @returns {boolean} True if the user is an admin, false otherwise
 */
export const isAdmin = () => {
  try {
    const user = JSON.parse(localStorage.getItem('user'));
    
    // For now, we'll use a simple check
    // This should be replaced with proper role-based checks in a production environment
    // For example, checking if user.role === 'admin'
    
    // Currently, we'll just check if the user exists
    // In a real implementation, you would check for admin role or permissions
    const Admin = user.role === 'admin'
    return !!Admin;
  } catch (error) {
    console.error('Error checking admin status:', error);
    return false;
  }
};

/**
 * Redirect non-admin users away from admin pages
 * 
 * @param {Function} navigate - React Router's navigate function
 * @returns {boolean} True if the user is an admin, false otherwise
 */
export const checkAdminAccess = (navigate) => {
  const adminStatus = isAdmin();
  
  if (!adminStatus && navigate) {
    navigate('/login');
  }
  
  return adminStatus;
};
