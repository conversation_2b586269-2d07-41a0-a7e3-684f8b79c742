"use client"

import { createTheme } from "@mui/material";

  const appTheme = createTheme({
    palette: {
      primary: {
        main: "rgba(220, 20, 60, 1)", // primary-color
        light: "rgba(255, 92, 122, 0.9)", // lighter version for hover states
        dark: "rgba(180, 0, 40, 1)", // darker version for active states
      },
      secondary: {
        main: "rgba(33, 33, 33, 0.9)",
        light: "rgba(66, 66, 66, 0.9)",
        dark: "rgba(0, 0, 0, 0.9)",
      },
      black: {
        main: "rgba(19, 19, 19, 1)", // black
      },
      grey: {
        main: "rgba(217, 217, 217, 1)", // variable-collection-stoke-gray
        light: "rgba(239, 239, 239, 1)",
        dark: "rgba(189, 189, 189, 1)",
      },
      common: {
        white: "rgba(255, 255, 255, 1)", // white
      },
      background: {
        default: "#FFFFFF",
        paper: "#FFFFFF",
        light: "#F5F5F5",
      },
      text: {
        primary: "rgba(0, 0, 0, 0.87)",
        secondary: "rgba(0, 0, 0, 0.6)",
      },
    },
    typography: {
      fontFamily: "'Plus Jakarta Sans', sans-serif",
      fontSize: 16,
      fontWeightLight: 300,
      fontWeightRegular: 400,
      fontWeightMedium: 500,
      fontWeightBold: 700,
      body1: {
        fontSize: "1rem", // 16px, responsive
        fontWeight: 400,
        letterSpacing: "0px",
        lineHeight: 1.5, // responsive line height
        '@media (max-width:600px)': {
          fontSize: '0.875rem', // 14px on mobile
        },
      },
      body2: {
        fontSize: "1rem", // 16px, responsive
        fontWeight: 600,
        letterSpacing: "0px",
        lineHeight: 1.5, // responsive line height
        '@media (max-width:600px)': {
          fontSize: '0.875rem', // 14px on mobile
        },
      },
      subtitle1: {
        fontSize: "1.125rem", // 18px, responsive
        fontWeight: 600,
        letterSpacing: "0px",
        lineHeight: 1.5, // responsive line height
        '@media (max-width:600px)': {
          fontSize: '1rem', // 16px on mobile
        },
      },
      subtitle2: {
        fontSize: "1rem", // 16px, responsive
        fontWeight: 500,
        letterSpacing: "0.1px",
        lineHeight: 1.5, // responsive line height
        '@media (max-width:600px)': {
          fontSize: '0.875rem', // 14px on mobile
        },
      },
      h6: {
        fontSize: "1.5rem", // 24px, responsive
        fontWeight: 600,
        letterSpacing: "0px",
        lineHeight: 1.3, // responsive line height
        '@media (max-width:600px)': {
          fontSize: '1.25rem', // 20px on mobile
        },
      },
      h5: {
        fontSize: "1.75rem", // 28px, responsive
        fontWeight: 600,
        letterSpacing: "-0.5px",
        lineHeight: 1.3, // responsive line height
        '@media (max-width:600px)': {
          fontSize: '1.5rem', // 24px on mobile
        },
      },
      h4: {
        fontSize: "2rem", // 32px, responsive
        fontWeight: 700,
        letterSpacing: "-0.5px",
        lineHeight: 1.2, // responsive line height
        '@media (max-width:600px)': {
          fontSize: '1.75rem', // 28px on mobile
        },
      },
      h3: {
        fontSize: "2.25rem", // 36px, responsive
        fontWeight: 700,
        letterSpacing: "-0.5px",
        lineHeight: 1.2, // responsive line height
        '@media (max-width:600px)': {
          fontSize: '1.875rem', // 30px on mobile
        },
      },
      h2: {
        fontSize: "2.75rem", // 44px, responsive
        fontWeight: 700,
        letterSpacing: "-1px",
        lineHeight: 1.2, // responsive line height
        '@media (max-width:600px)': {
          fontSize: '2.25rem', // 36px on mobile
        },
      },
      h1: {
        fontSize: "3.5rem", // 56px, responsive
        fontWeight: 700,
        letterSpacing: "-1.5px",
        lineHeight: 1.1, // responsive line height
        '@media (max-width:600px)': {
          fontSize: '2.5rem', // 40px on mobile
        },
      },
      button: {
        textTransform: 'none',
        fontWeight: 600,
      },
    },
    components: {
      MuiCssBaseline: {
        styleOverrides: {
          html: {
            WebkitFontSmoothing: 'antialiased',
            MozOsxFontSmoothing: 'grayscale',
            boxSizing: 'border-box',
            scrollBehavior: 'smooth',
          },
          body: {
            fontFamily: "'Plus Jakarta Sans', sans-serif",
            margin: 0,
            padding: 0,
            boxSizing: 'border-box',
            overflowX: 'hidden',
          },
          '*, *::before, *::after': {
            boxSizing: 'inherit',
          },
          '@keyframes fadeIn': {
            '0%': {
              opacity: 0,
            },
            '100%': {
              opacity: 1,
            },
          },
          '@keyframes slideUp': {
            '0%': {
              transform: 'translateY(20px)',
              opacity: 0,
            },
            '100%': {
              transform: 'translateY(0)',
              opacity: 1,
            },
          },
        }
      },
      MuiTextField: {
        styleOverrides: {
          root: {
            backgroundColor: "white !important",
            WebkitBoxShadow: "0 0 0px 1000px white inset !important",
            WebkitTextFillColor: "black !important",
            '& .MuiOutlinedInput-root': {
              transition: 'all 0.2s ease-in-out',
              '&:hover': {
                borderColor: 'rgba(220, 20, 60, 0.8)',
              },
              '&.Mui-focused': {
                boxShadow: '0 0 0 2px rgba(220, 20, 60, 0.2)',
              },
            },
            '& .MuiInputLabel-root': {
              transition: 'all 0.2s ease-in-out',
            },
            '@media (max-width:600px)': {
              fontSize: '14px',
            },
          },
        },
      },
      MuiButton: {
        styleOverrides: {
          root: {
            textTransform: "none",
            borderRadius: "10px",
            boxShadow: 'none',
            transition: 'all 0.3s ease',
            fontWeight: 600,
            '&:hover': {
              transform: 'translateY(-2px)',
              boxShadow: '0 4px 8px rgba(0, 0, 0, 0.1)',
            },
            '&:active': {
              transform: 'translateY(0)',
            },
            '@media (max-width:600px)': {
              fontSize: '14px',
              padding: '8px 16px',
            },
          },
          contained: {
            '&:hover': {
              boxShadow: '0 6px 10px rgba(220, 20, 60, 0.3)',
            },
          },
          outlined: {
            borderWidth: '2px',
            '&:hover': {
              borderWidth: '2px',
            },
          },
        },
      },
      MuiOutlinedInput: {
        styleOverrides: {
          root: {
            borderRadius: "10px",
            transition: 'all 0.2s ease-in-out',
            '&:hover': {
              borderColor: 'rgba(220, 20, 60, 0.8)',
            },
            '&.Mui-focused': {
              boxShadow: '0 0 0 2px rgba(220, 20, 60, 0.2)',
            },
          },
          input: {
            '@media (max-width:600px)': {
              padding: '12px 14px',
            },
          },
        },
      },
      MuiCheckbox: {
        styleOverrides: {
          root: {
            color: "rgba(220, 20, 60, 1)",
            "&.Mui-checked": {
              color: "rgba(220, 20, 60, 1)",
            },
            transition: 'all 0.2s ease',
            '&:hover': {
              backgroundColor: 'rgba(220, 20, 60, 0.08)',
            },
          },
        },
      },
      MuiCard: {
        styleOverrides: {
          root: {
            borderRadius: '12px',
            boxShadow: '0 2px 8px rgba(0, 0, 0, 0.08)',
            transition: 'all 0.3s ease',
            overflow: 'hidden',
            '&:hover': {
              boxShadow: '0 6px 16px rgba(0, 0, 0, 0.1)',
              transform: 'translateY(-4px)',
            },
          },
        },
      },
      MuiPaper: {
        styleOverrides: {
          root: {
            borderRadius: '12px',
            transition: 'all 0.3s ease',
          },
          elevation1: {
            boxShadow: '0 2px 8px rgba(0, 0, 0, 0.08)',
          },
          elevation2: {
            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
          },
        },
      },
      MuiTableCell: {
        styleOverrides: {
          root: {
            fontSize: "1rem",
            fontWeight: 400,
            letterSpacing: "0px",
            lineHeight: 1.5,
            padding: '16px',
            '@media (max-width:600px)': {
              fontSize: '0.875rem',
              padding: '12px 8px',
            },
          },
          head: {
            fontSize: "1rem",
            fontWeight: 600,
            letterSpacing: "0px",
            lineHeight: 1.5,
            backgroundColor: 'rgba(245, 245, 245, 0.8)',
            '@media (max-width:600px)': {
              fontSize: '0.875rem',
            },
          },
        },
      },
      MuiListItemText: {
        styleOverrides: {
          primary: {
            fontSize: "1rem",
            fontWeight: 600,
            letterSpacing: "0px",
            lineHeight: 1.5,
            '@media (max-width:600px)': {
              fontSize: '0.875rem',
            },
          },
          secondary: {
            fontSize: "0.875rem",
            fontWeight: 400,
            letterSpacing: "0px",
            lineHeight: 1.5,
            '@media (max-width:600px)': {
              fontSize: '0.75rem',
            },
          },
        },
      },
      MuiListItemButton: {
        styleOverrides: {
          root: {
            transition: 'all 0.2s ease',
            '&:hover': {
              backgroundColor: 'rgba(220, 20, 60, 0.08)',
            },
          },
        },
      },
      MuiDrawer: {
        styleOverrides: {
          paper: {
            borderRadius: '0 0 12px 12px',
          },
        },
      },
      MuiAppBar: {
        styleOverrides: {
          root: {
            boxShadow: '0 2px 8px rgba(0, 0, 0, 0.08)',
          },
        },
      },
      MuiIconButton: {
        styleOverrides: {
          root: {
            transition: 'all 0.2s ease',
            '&:hover': {
              backgroundColor: 'rgba(220, 20, 60, 0.08)',
              transform: 'scale(1.1)',
            },
          },
        },
      },
      MuiChip: {
        styleOverrides: {
          root: {
            borderRadius: '8px',
            transition: 'all 0.2s ease',
            '&:hover': {
              boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
            },
          },
        },
      },
      MuiAlert: {
        styleOverrides: {
          root: {
            borderRadius: '10px',
            boxShadow: '0 2px 8px rgba(0, 0, 0, 0.08)',
          },
          standardSuccess: {
            backgroundColor: 'rgba(46, 125, 50, 0.9)',
            color: '#000',
          },
          standardError: {
            backgroundColor: 'rgba(211, 47, 47, 0.9)',
            color: '#000',
          },
          standardWarning: {
            backgroundColor: 'rgba(237, 108, 2, 0.9)',
            color: '#000',
          },
          standardInfo: {
            backgroundColor: 'rgba(2, 136, 209, 0.9)',
            color: '#000',
          },
        },
      },
      MuiSnackbar: {
        styleOverrides: {
          root: {
            '& .MuiAlert-root': {
              animation: 'slideUp 0.3s ease-out',
            },
          },
        },
      },
    },
  });

  export default appTheme
