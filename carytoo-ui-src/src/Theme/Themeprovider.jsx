import {
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    Theme<PERSON>rovider as MuiThemeProvider,
    createTheme,
  } from "@mui/material";
  import appTheme from './Theme'; 

 const CustomThemeProvider  = ({ children }) => {
    return (
      <MuiThemeProvider theme={appTheme}>
        <CssBaseline />
        {children}
      </MuiThemeProvider>
    );
  };

  export default CustomThemeProvider 