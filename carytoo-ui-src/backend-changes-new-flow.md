# Backend API Changes for New Registration Flow

To implement the new registration flow where users are only created in the database after email verification, the following backend API changes are needed:

## 1. New Endpoints

### 1.1. Pre-Registration Endpoint

Create a new endpoint that initiates the registration process without creating a user:

```javascript
// api/auth/pre-register
router.post('/pre-register', async (req, res) => {
  try {
    const { email } = req.body;
    
    // Check if user already exists
    const existingUser = await User.findOne({ email });
    if (existingUser) {
      return res.status(400).json({ message: 'User with this email already exists' });
    }
    
    // Generate OTP for email verification
    const otp = generateOTP(); // 6-digit OTP
    
    // Save OTP to database with expiry
    await OTP.create({
      email,
      otp,
      type: 'register',
      expiresAt: new Date(Date.now() + 10 * 60 * 1000) // 10 minutes
    });
    
    // Send verification email with OTP
    await sendVerificationEmail(email, otp);
    
    res.status(200).json({ 
      message: 'Verification code sent to your email' 
    });
  } catch (error) {
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});
```

### 1.2. Complete Registration Endpoint

Create a new endpoint that verifies the OTP and creates the user in one step:

```javascript
// api/auth/complete-registration
router.post('/complete-registration', async (req, res) => {
  try {
    const { email, otp, username, password, phoneNumber } = req.body;
    
    // Check if user already exists
    const existingUser = await User.findOne({ email });
    if (existingUser) {
      return res.status(400).json({ message: 'User already exists' });
    }
    
    // Find the OTP record
    const otpRecord = await OTP.findOne({ 
      email, 
      otp: parseInt(otp),
      type: 'register',
      expiresAt: { $gt: new Date() }
    });
    
    if (!otpRecord) {
      return res.status(400).json({ message: 'Invalid or expired verification code' });
    }
    
    // Hash password
    const hashedPassword = await bcrypt.hash(password, 10);
    
    // Create new user with verified: true
    const newUser = new User({
      username,
      email,
      password: hashedPassword,
      phoneNumber,
      verified: true // User is verified from the start
    });
    
    await newUser.save();
    
    // Delete the used OTP
    await OTP.deleteOne({ _id: otpRecord._id });
    
    res.status(201).json({ 
      message: 'Registration completed successfully! You can now log in.' 
    });
  } catch (error) {
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});
```

## 2. Modify Existing Endpoints

### 2.1. Update the Login Endpoint

The login endpoint should still check if the user's email is verified:

```javascript
// api/auth/login
router.post('/login', async (req, res) => {
  try {
    const { email, password } = req.body;
    
    // Find user
    const user = await User.findOne({ email });
    
    if (!user) {
      return res.status(401).json({ message: 'Invalid email or password' });
    }
    
    // Check if email is verified
    if (!user.verified) {
      return res.status(403).json({ 
        message: 'Email not verified. Please verify your email before logging in.',
        needsVerification: true,
        email: email
      });
    }
    
    // Check password
    const isPasswordValid = await bcrypt.compare(password, user.password);
    
    if (!isPasswordValid) {
      return res.status(401).json({ message: 'Invalid email or password' });
    }
    
    // Generate JWT token
    const token = jwt.sign(
      { userId: user._id, email: user.email },
      process.env.JWT_SECRET,
      { expiresIn: '24h' }
    );
    
    res.status(200).json({
      message: 'Login successful',
      token,
      user: {
        id: user._id,
        username: user.username,
        email: user.email,
        role: user.role || 'user'
      }
    });
  } catch (error) {
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});
```

### 2.2. Update the Resend Verification Email Endpoint

```javascript
// api/auth/get-mail
router.post('/get-mail', async (req, res) => {
  try {
    const { email, type = 'register' } = req.body;
    
    // Delete any existing OTPs for this email and type
    await OTP.deleteMany({ email, type });
    
    // Generate new OTP
    const otp = generateOTP();
    
    // Save OTP to database with expiry
    await OTP.create({
      email,
      otp,
      type,
      expiresAt: new Date(Date.now() + 10 * 60 * 1000) // 10 minutes
    });
    
    // Send verification email with OTP
    await sendVerificationEmail(email, otp, type);
    
    res.status(200).json({ message: 'Verification code sent to your email' });
  } catch (error) {
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});
```

## 3. Database Schema Changes

### 3.1. User Model

The User model should still have a `verified` field, but now all users will be created with `verified: true` since they're only created after verification:

```javascript
const userSchema = new mongoose.Schema({
  username: { type: String, required: true },
  email: { type: String, required: true, unique: true },
  password: { type: String, required: true },
  phoneNumber: { type: String, required: true },
  verified: { type: Boolean, default: true }, // Default is now true
  role: { type: String, enum: ['user', 'admin'], default: 'user' },
  createdAt: { type: Date, default: Date.now }
});
```

### 3.2. OTP Model

```javascript
const otpSchema = new mongoose.Schema({
  email: { type: String, required: true },
  otp: { type: Number, required: true },
  type: { type: String, enum: ['register', 'forgot password'], required: true },
  expiresAt: { type: Date, required: true },
  createdAt: { type: Date, default: Date.now }
});
```

## 4. Security Considerations

1. **Expiry Time**: OTPs should expire after a reasonable time (e.g., 10 minutes).
2. **Rate Limiting**: Implement rate limiting to prevent abuse of the pre-registration endpoint.
3. **Data Validation**: Validate all input data thoroughly.
4. **Secure Storage**: Ensure that OTPs are stored securely.
5. **Logging**: Log all registration attempts for security auditing.

## 5. Implementation Notes

1. The frontend should store registration data temporarily in localStorage.
2. After successful verification, the frontend should send the complete registration data to create the user.
3. All sensitive data in localStorage should be cleared after successful registration or if the user navigates away.
4. Consider adding a timeout for the registration process (e.g., 1 hour) to prevent stale registration data.
