#!/bin/bash

# Chess Brigade GitHub Secrets Setup Script
# This script helps you extract secrets from your current configuration
# and provides the commands to set them up in GitHub

set -e

echo "🔐 Chess Brigade GitHub Secrets Setup"
echo "======================================"
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_color() {
    printf "${1}${2}${NC}\n"
}

print_color $BLUE "This script will help you set up GitHub Secrets for Chess Brigade CI/CD"
print_color $YELLOW "⚠️  IMPORTANT: Never run this script in a public environment!"
echo ""

# Check if we're in the right directory
if [ ! -f "infra-template/chess-backend-params.sh" ]; then
    print_color $RED "❌ Error: Please run this script from the root of your infra repository"
    exit 1
fi

print_color $GREEN "✅ Found infra-template directory"
echo ""

# Extract secrets from current configuration
print_color $BLUE "📋 Extracting secrets from current configuration..."
echo ""

# Read current values
if [ -f "infra-template/chess-dev.tfvars" ]; then
    DB_PASSWORD=$(grep 'db_password' infra-template/chess-dev.tfvars | cut -d'"' -f2)
    DB_USER=$(grep 'db_username' infra-template/chess-dev.tfvars | cut -d'"' -f2)
fi

if [ -f "infra-template/chessbrigade-ui-params.sh" ]; then
    CLOUDFRONT_ID=$(grep 'CLOUDFRONT_DISTRIBUTION_ID' infra-template/chessbrigade-ui-params.sh | cut -d'"' -f2)
    S3_BUCKET=$(grep 'S3_BUCKET_NAME' infra-template/chessbrigade-ui-params.sh | cut -d'"' -f2)
fi

# Extract hardcoded secrets from deployment script (these need to be moved to secrets!)
print_color $YELLOW "⚠️  Found hardcoded secrets in deployment script that MUST be moved to GitHub Secrets:"
echo ""

print_color $RED "🚨 CRITICAL: The following secrets are currently hardcoded and exposed:"
echo "   - JWT_SECRET: d10b70c4c1c2178ab8a4479891eea051bf28f799e652fbc0246b8bf4c6714300"
echo "   - RAZORPAY_KEY_ID: ***********************"
echo "   - RAZORPAY_KEY_SECRET: Fkkx8w7TokKOPXaCBV1aI819"
echo "   - EMAIL_PASS: Chessbrigade@123"
echo "   - MSG91_AUTH_KEY: 449596AJ7D2xtob6818886dP1"
echo ""

print_color $BLUE "📝 GitHub CLI Commands to Set Up Secrets"
print_color $BLUE "========================================"
echo ""

print_color $GREEN "Copy and run these commands in your terminal (after installing GitHub CLI):"
echo ""

# AWS Secrets
print_color $YELLOW "# AWS Infrastructure Secrets"
echo "gh secret set CHESS_AWS_ACCESS_KEY_ID --body \"YOUR_AWS_ACCESS_KEY_ID\""
echo "gh secret set CHESS_AWS_SECRET_ACCESS_KEY --body \"YOUR_AWS_SECRET_ACCESS_KEY\""
echo "gh secret set CHESS_AWS_REGION --body \"ap-south-1\""
echo ""

# Database Secrets
print_color $YELLOW "# Database Secrets"
if [ -n "$DB_PASSWORD" ]; then
    echo "gh secret set CHESS_DB_PASSWORD --body \"$DB_PASSWORD\""
else
    echo "gh secret set CHESS_DB_PASSWORD --body \"YOUR_DB_PASSWORD\""
fi

if [ -n "$DB_USER" ]; then
    echo "gh secret set CHESS_DB_USER --body \"$DB_USER\""
else
    echo "gh secret set CHESS_DB_USER --body \"chessbrigade_admin\""
fi
echo ""

# Application Secrets
print_color $YELLOW "# Application Secrets"
echo "gh secret set CHESS_JWT_SECRET --body \"d10b70c4c1c2178ab8a4479891eea051bf28f799e652fbc0246b8bf4c6714300\""
echo "gh secret set CHESS_EMAIL_PASSWORD --body \"YOUR_EMAIL_PASSWORD\""
echo "gh secret set CHESS_EMAIL_USER --body \"<EMAIL>\""
echo "gh secret set CHESS_EMAIL_HOST --body \"smtpout.secureserver.net\""
echo ""

# Payment Secrets
print_color $YELLOW "# Payment Gateway Secrets (🔴 CRITICAL)"
echo "gh secret set CHESS_RAZORPAY_KEY_ID --body \"***********************\""
echo "gh secret set CHESS_RAZORPAY_KEY_SECRET --body \"Fkkx8w7TokKOPXaCBV1aI819\""
echo "gh secret set CHESS_RAZORPAY_WEBHOOK_SECRET --body \"mywebhooksecret123\""
echo "gh secret set CHESS_RAZORPAY_ACCOUNT_NUMBER --body \"****************\""
echo ""

# Communication Secrets
print_color $YELLOW "# SMS/Communication Secrets"
echo "gh secret set CHESS_MSG91_AUTH_KEY --body \"449596AJ7D2xtob6818886dP1\""
echo "gh secret set CHESS_MSG91_SENDER_ID --body \"JAINTL\""
echo "gh secret set CHESS_OTP_TEMPLATE_ID --body \"68183faad6fc05796a565072\""
echo "gh secret set CHESS_TOURNAMENT_WITHDRAWAL_TEMPLATE_ID --body \"682192a2d6fc0541c97d4642\""
echo "gh secret set CHESS_PAYMENT_CONFIRMATION_TEMPLATE_ID --body \"6821904ed6fc054190094d63\""
echo "gh secret set CHESS_TOURNAMENT_REGISTRATION_TEMPLATE_ID --body \"68219118d6fc0541b7470a22\""
echo "gh secret set CHESS_PAIRING_NOTIFICATION_TEMPLATE_ID --body \"68218fb6d6fc05331370a963\""
echo "gh secret set CHESS_REFUND_CONFIRMATION_TEMPLATE_ID --body \"682192a2d6fc0541c97d4642\""
echo ""

# Infrastructure Secrets
print_color $YELLOW "# Infrastructure Resource IDs"
if [ -n "$CLOUDFRONT_ID" ]; then
    echo "gh secret set CHESS_CLOUDFRONT_DISTRIBUTION_ID --body \"$CLOUDFRONT_ID\""
else
    echo "gh secret set CHESS_CLOUDFRONT_DISTRIBUTION_ID --body \"E2E0G9X3I7SJ86\""
fi

if [ -n "$S3_BUCKET" ]; then
    echo "gh secret set CHESS_S3_BUCKET_NAME --body \"$S3_BUCKET\""
else
    echo "gh secret set CHESS_S3_BUCKET_NAME --body \"chess-brigade\""
fi

echo "gh secret set CHESS_ECR_REPOSITORY_NAME --body \"chessbrigade-api\""
echo "gh secret set CHESS_ECS_CLUSTER_NAME --body \"chessbrigade-cluster\""
echo "gh secret set CHESS_ECS_SERVICE_NAME --body \"chessbrigade-api-service\""
echo ""

print_color $BLUE "📋 Manual Setup Instructions"
print_color $BLUE "============================"
echo ""

print_color $GREEN "1. Install GitHub CLI:"
echo "   https://cli.github.com/"
echo ""

print_color $GREEN "2. Authenticate with GitHub:"
echo "   gh auth login"
echo ""

print_color $GREEN "3. Navigate to your repository directory and run the commands above"
echo ""

print_color $GREEN "4. Verify secrets are set:"
echo "   gh secret list"
echo ""

print_color $YELLOW "⚠️  Security Reminders:"
echo "   - Change default passwords before setting secrets"
echo "   - Use strong, unique passwords for production"
echo "   - Rotate payment gateway secrets regularly"
echo "   - Never commit secrets to version control"
echo ""

print_color $GREEN "✅ After setting up secrets, you can run the GitHub Actions workflows!"

echo ""
print_color $BLUE "🚀 Next Steps:"
echo "   1. Set up all the secrets using the commands above"
echo "   2. Go to GitHub Actions tab in your repository"
echo "   3. Run 'Deploy Chess Backend' or 'Deploy Chess UI' workflows"
echo "   4. Monitor the deployment progress"
echo ""

print_color $GREEN "Done! 🎉"
