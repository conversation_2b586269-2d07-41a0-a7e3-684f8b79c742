name: Shared Setup Components

on:
  workflow_call:
    inputs:
      aws-region:
        description: 'AWS region'
        required: false
        default: 'ap-south-1'
        type: string
      terraform-version:
        description: 'Terraform version'
        required: false
        default: '1.5.0'
        type: string
      node-version:
        description: 'Node.js version'
        required: false
        default: '18'
        type: string
      setup-terraform:
        description: 'Whether to setup Terraform'
        required: false
        default: false
        type: boolean
      setup-node:
        description: 'Whether to setup Node.js'
        required: false
        default: false
        type: boolean
    secrets:
      aws-access-key-id:
        description: 'AWS Access Key ID'
        required: true
      aws-secret-access-key:
        description: 'AWS Secret Access Key'
        required: true
    outputs:
      aws-account-id:
        description: 'AWS Account ID'
        value: ${{ jobs.setup.outputs.aws-account-id }}

jobs:
  setup:
    runs-on: ubuntu-latest
    outputs:
      aws-account-id: ${{ steps.aws-info.outputs.account-id }}
    
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Setup Terraform
        if: ${{ inputs.setup-terraform }}
        uses: hashicorp/setup-terraform@v3
        with:
          terraform_version: ${{ inputs.terraform-version }}

      - name: Setup Node.js
        if: ${{ inputs.setup-node }}
        uses: actions/setup-node@v4
        with:
          node-version: ${{ inputs.node-version }}
          cache: 'npm'

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.aws-access-key-id }}
          aws-secret-access-key: ${{ secrets.aws-secret-access-key }}
          aws-region: ${{ inputs.aws-region }}

      - name: Verify AWS authentication and get account info
        id: aws-info
        run: |
          echo "Verifying AWS authentication..."
          AWS_ACCOUNT_ID=$(aws sts get-caller-identity --query Account --output text)
          echo "AWS Account ID: $AWS_ACCOUNT_ID"
          echo "AWS Region: ${{ inputs.aws-region }}"
          echo "account-id=$AWS_ACCOUNT_ID" >> $GITHUB_OUTPUT

      - name: Setup Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Cache Terraform
        if: ${{ inputs.setup-terraform }}
        uses: actions/cache@v3
        with:
          path: |
            ~/.terraform.d/
            .terraform/
          key: ${{ runner.os }}-terraform-${{ hashFiles('**/.terraform.lock.hcl') }}
          restore-keys: |
            ${{ runner.os }}-terraform-
