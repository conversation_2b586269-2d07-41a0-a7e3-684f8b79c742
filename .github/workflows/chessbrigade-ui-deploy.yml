name: Deploy Chess UI

on:
  push:
    branches: [main, release-*]  # Trigger on main and release branches
  workflow_dispatch:
    inputs:
      branch:
        description: 'Git branch to deploy'
        required: false
        default: 'release-may-jagan'
      environment:
        description: 'Deployment environment'
        required: false
        default: 'production'
        type: choice
        options:
        - production
        - staging

jobs:
  deploy:
    runs-on: ubuntu-latest

    env:
      # AWS Configuration
      AWS_REGION: ap-south-1
      S3_BUCKET_NAME: chess-brigade

      # Git Configuration
      GIT_REPO_URL: https://github.com/mathi0695/chessbrigade-ui.git
      GIT_CLONE_DIR: chessbrigade-ui-src

      # Build Configuration
      NODE_VERSION: "18"
      BUILD_COMMAND: "npm run build"
      BUILD_OUTPUT_DIR: "dist"

      # Environment Variables for React Build
      REACT_APP_API_URL: "https://chess-api.mathiarasan.com"
      REACT_APP_ENV: "production"

    steps:
      - name: Checkout infra repository
        uses: actions/checkout@v4
        with:
          ref: ${{ github.event.inputs.branch || github.ref }}

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.CHESS_AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.CHESS_AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_REGION }}

      - name: Verify AWS authentication
        run: |
          echo "Verifying AWS authentication..."
          aws sts get-caller-identity

      - name: Verify S3 bucket exists
        run: |
          echo "Verifying S3 bucket exists..."
          aws s3api head-bucket --bucket ${{ env.S3_BUCKET_NAME }}

      - name: Verify CloudFront distribution exists
        run: |
          echo "Verifying CloudFront distribution exists..."
          aws cloudfront get-distribution --id ${{ secrets.CHESS_CLOUDFRONT_DISTRIBUTION_ID }}

      - name: Create parameter file for UI deployment
        working-directory: ./infra-template
        run: |
          echo "Creating UI deployment parameter file..."
          cat > chessbrigade-ui-params-ci.sh << 'EOF'
          #!/bin/bash
          # ChessBrigade UI Deployment Parameters for CI/CD

          # AWS Configuration
          AWS_REGION="${{ env.AWS_REGION }}"
          S3_BUCKET_NAME="${{ env.S3_BUCKET_NAME }}"
          CLOUDFRONT_DISTRIBUTION_ID="${{ secrets.CHESS_CLOUDFRONT_DISTRIBUTION_ID }}"

          # Git Configuration
          GIT_REPO_URL="${{ env.GIT_REPO_URL }}"
          GIT_BRANCH="${{ github.event.inputs.branch || 'release-may-jagan' }}"
          GIT_CLONE_DIR="${{ env.GIT_CLONE_DIR }}"

          # Build Configuration
          NODE_VERSION="${{ env.NODE_VERSION }}"
          BUILD_COMMAND="${{ env.BUILD_COMMAND }}"
          BUILD_OUTPUT_DIR="${{ env.BUILD_OUTPUT_DIR }}"

          # Environment Variables for React Build
          REACT_APP_API_URL="${{ env.REACT_APP_API_URL }}"
          REACT_APP_ENV="${{ env.REACT_APP_ENV }}"
          EOF

          chmod +x chessbrigade-ui-params-ci.sh

      - name: Run Chess UI Deployment
        working-directory: ./infra-template
        run: |
          echo "Starting Chess UI deployment..."
          # Make the deployment script executable
          chmod +x chessbrigade-ui-deploy.sh

          # Run the deployment script with our CI parameter file
          ./chessbrigade-ui-deploy.sh -p chessbrigade-ui-params-ci.sh -b "${{ github.event.inputs.branch || 'release-may-jagan' }}"

      - name: Verify deployment
        run: |
          echo "Verifying UI deployment..."
          # Test the CloudFront distribution
          CLOUDFRONT_DOMAIN=$(aws cloudfront get-distribution \
            --id ${{ secrets.CHESS_CLOUDFRONT_DISTRIBUTION_ID }} \
            --query "Distribution.DomainName" \
            --output text)

          echo "CloudFront Domain: $CLOUDFRONT_DOMAIN"
          echo "UI is available at: https://$CLOUDFRONT_DOMAIN"
          echo "Custom domain: https://chess-ui.mathiarasan.com"

          # Test the UI endpoint
          echo "Testing UI endpoint..."
          curl -f "https://$CLOUDFRONT_DOMAIN" -o /dev/null -s || echo "UI check failed, but deployment may still be successful"

      - name: Cleanup
        if: always()
        working-directory: ./infra-template
        run: |
          echo "Cleaning up temporary files..."
          rm -f chessbrigade-ui-params-ci.sh
          # Clean up any cloned source code
          rm -rf chessbrigade-ui-src

      - name: Notify deployment status
        if: always()
        run: |
          if [ "${{ job.status }}" == "success" ]; then
            echo "✅ Chess UI deployment completed successfully!"
            CLOUDFRONT_DOMAIN=$(aws cloudfront get-distribution \
              --id ${{ secrets.CHESS_CLOUDFRONT_DISTRIBUTION_ID }} \
              --query "Distribution.DomainName" \
              --output text)
            echo "🚀 UI is available at: https://$CLOUDFRONT_DOMAIN"
            echo "🌐 Custom domain: https://chess-ui.mathiarasan.com"
          else
            echo "❌ Chess UI deployment failed!"
            echo "Please check the logs above for details."
          fi

      - name: Install dependencies
        run: npm ci || npm install

      - name: Build application
        run: npm run build
        env:
          REACT_APP_API_URL: ${{ env.REACT_APP_API_URL }}
          REACT_APP_ENV: ${{ env.REACT_APP_ENV }}

      - name: Deploy to S3 (static assets with cache)
        run: |
          aws s3 sync "${{ env.BUILD_OUTPUT_DIR }}" "s3://${{ env.S3_BUCKET_NAME }}" \
            --delete \
            --cache-control "max-age=31536000,public,immutable" \
            --exclude "index.html" \
            --exclude "*.json"

      - name: Deploy to S3 (dynamic assets without cache)
        run: |
          aws s3 sync "${{ env.BUILD_OUTPUT_DIR }}" "s3://${{ env.S3_BUCKET_NAME }}" \
            --cache-control "no-cache,no-store,must-revalidate" \
            --include "index.html" \
            --include "*.json"

      - name: Create CloudFront invalidation
        run: |
          INVALIDATION_ID=$(aws cloudfront create-invalidation \
            --distribution-id ${{ env.CLOUDFRONT_DISTRIBUTION_ID }} \
            --paths "/*" \
            --query "Invalidation.Id" \
            --output text)
          
          echo "Waiting for CloudFront invalidation to complete..."
          aws cloudfront wait invalidation-completed \
            --distribution-id ${{ env.CLOUDFRONT_DISTRIBUTION_ID }} \
            --id ${INVALIDATION_ID}

      - name: Get CloudFront domain
        id: get-domain
        run: |
          CLOUDFRONT_DOMAIN=$(aws cloudfront get-distribution \
            --id ${{ env.CLOUDFRONT_DISTRIBUTION_ID }} \
            --query "Distribution.DomainName" \
            --output text)
          echo "CLOUDFRONT_DOMAIN=${CLOUDFRONT_DOMAIN}" >> $GITHUB_ENV

      - name: Deployment summary
        run: |
          echo "Deployment completed successfully!"
          echo "Your application is now available at: https://${CLOUDFRONT_DOMAIN}"
          echo "You can also access it at: https://chess-ui.mathiarasan.com"
          echo "Deployment completed at $(date)"