name: Deploy ChessBrigade UI

on:
  push:
    branches: [main]  # Adjust branch as needed
  workflow_dispatch:
    inputs:
      branch:
        description: 'Git branch to deploy'
        required: false
        default: ''

jobs:
  deploy:
    runs-on: ubuntu-latest
    
    env:
      AWS_REGION: ap-south-1
      S3_BUCKET_NAME: chessbrigade-ui  # Replace with your actual bucket name
      CLOUDFRONT_DISTRIBUTION_ID: EXXXXXXXXXXXXX  # Replace with your actual distribution ID
      NODE_VERSION: 16  # Adjust as needed
      REACT_APP_API_URL: https://api.chessbrigade.com  # Replace with your actual API URL
      REACT_APP_ENV: production
      BUILD_OUTPUT_DIR: build  # Adjust based on your build output directory

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          ref: ${{ github.event.inputs.branch || 'main' }}  # Use input branch or default

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v2
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_REGION }}

      - name: Verify S3 bucket exists
        run: aws s3api head-bucket --bucket ${{ env.S3_BUCKET_NAME }}

      - name: Verify CloudFront distribution exists
        run: aws cloudfront get-distribution --id ${{ env.CLOUDFRONT_DISTRIBUTION_ID }}

      - name: Install dependencies
        run: npm ci || npm install

      - name: Build application
        run: npm run build
        env:
          REACT_APP_API_URL: ${{ env.REACT_APP_API_URL }}
          REACT_APP_ENV: ${{ env.REACT_APP_ENV }}

      - name: Deploy to S3 (static assets with cache)
        run: |
          aws s3 sync "${{ env.BUILD_OUTPUT_DIR }}" "s3://${{ env.S3_BUCKET_NAME }}" \
            --delete \
            --cache-control "max-age=31536000,public,immutable" \
            --exclude "index.html" \
            --exclude "*.json"

      - name: Deploy to S3 (dynamic assets without cache)
        run: |
          aws s3 sync "${{ env.BUILD_OUTPUT_DIR }}" "s3://${{ env.S3_BUCKET_NAME }}" \
            --cache-control "no-cache,no-store,must-revalidate" \
            --include "index.html" \
            --include "*.json"

      - name: Create CloudFront invalidation
        run: |
          INVALIDATION_ID=$(aws cloudfront create-invalidation \
            --distribution-id ${{ env.CLOUDFRONT_DISTRIBUTION_ID }} \
            --paths "/*" \
            --query "Invalidation.Id" \
            --output text)
          
          echo "Waiting for CloudFront invalidation to complete..."
          aws cloudfront wait invalidation-completed \
            --distribution-id ${{ env.CLOUDFRONT_DISTRIBUTION_ID }} \
            --id ${INVALIDATION_ID}

      - name: Get CloudFront domain
        id: get-domain
        run: |
          CLOUDFRONT_DOMAIN=$(aws cloudfront get-distribution \
            --id ${{ env.CLOUDFRONT_DISTRIBUTION_ID }} \
            --query "Distribution.DomainName" \
            --output text)
          echo "CLOUDFRONT_DOMAIN=${CLOUDFRONT_DOMAIN}" >> $GITHUB_ENV

      - name: Deployment summary
        run: |
          echo "Deployment completed successfully!"
          echo "Your application is now available at: https://${CLOUDFRONT_DOMAIN}"
          echo "You can also access it at: https://chess-ui.mathiarasan.com"
          echo "Deployment completed at $(date)"