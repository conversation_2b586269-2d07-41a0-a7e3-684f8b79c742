# Chess Brigade CI/CD Workflows

This directory contains GitHub Actions workflows for automated deployment of the Chess Brigade application stack.

## 🚀 Available Workflows

### 1. Chess Backend Deployment (`chess-backend-deploy.yml`)
Deploys the Chess Brigade API backend to AWS ECS using the existing deployment script.

**Triggers:**
- Push to `main` or `release-*` branches
- Manual trigger via GitHub Actions UI

**What it does:**
- Builds and pushes Docker image to ECR
- Updates ECS service with new task definition
- Injects all secrets securely via environment variables

### 2. Chess UI Deployment (`chessbrigade-ui-deploy.yml`)
Deploys the Chess Brigade React UI to S3 and CloudFront.

**Triggers:**
- Push to `main` or `release-*` branches  
- Manual trigger via GitHub Actions UI

**What it does:**
- Builds React application
- Syncs build output to S3
- Invalidates CloudFront cache

### 3. Shared Setup (`shared-setup.yml`)
Reusable workflow component for common setup tasks.

## 🔐 Required GitHub Secrets

Before using these workflows, you **MUST** create the following secrets in your GitHub repository:

### Go to: Repository → Settings → Secrets and variables → Actions → New repository secret

#### AWS Infrastructure Secrets
```
CHESS_AWS_ACCESS_KEY_ID          # AWS access key for deployments
CHESS_AWS_SECRET_ACCESS_KEY      # AWS secret access key
CHESS_AWS_REGION                 # AWS region (ap-south-1)
```

#### Database Secrets
```
CHESS_DB_PASSWORD                # Database password
CHESS_DB_USER                    # Database username (chessbrigade_admin)
```

#### Application Secrets
```
CHESS_JWT_SECRET                 # JWT secret for authentication
CHESS_EMAIL_PASSWORD             # Email service password
CHESS_EMAIL_USER                 # Email username
CHESS_EMAIL_HOST                 # SMTP host
```

#### Payment Gateway Secrets (🔴 CRITICAL)
```
CHESS_RAZORPAY_KEY_ID           # Razorpay key ID
CHESS_RAZORPAY_KEY_SECRET       # Razorpay secret key
CHESS_RAZORPAY_WEBHOOK_SECRET   # Razorpay webhook secret
CHESS_RAZORPAY_ACCOUNT_NUMBER   # Razorpay account number
```

#### Communication Secrets
```
CHESS_MSG91_AUTH_KEY            # MSG91 SMS service auth key
CHESS_MSG91_SENDER_ID           # MSG91 sender ID
CHESS_OTP_TEMPLATE_ID           # OTP template ID
CHESS_TOURNAMENT_WITHDRAWAL_TEMPLATE_ID
CHESS_PAYMENT_CONFIRMATION_TEMPLATE_ID
CHESS_TOURNAMENT_REGISTRATION_TEMPLATE_ID
CHESS_PAIRING_NOTIFICATION_TEMPLATE_ID
CHESS_REFUND_CONFIRMATION_TEMPLATE_ID
```

#### Infrastructure Resource IDs
```
CHESS_CLOUDFRONT_DISTRIBUTION_ID    # CloudFront distribution ID
CHESS_S3_BUCKET_NAME               # S3 bucket name (chess-brigade)
CHESS_ECR_REPOSITORY_NAME          # ECR repository name
CHESS_ECS_CLUSTER_NAME             # ECS cluster name
CHESS_ECS_SERVICE_NAME             # ECS service name
```

## 🛠️ Setup Instructions

### 1. Create AWS IAM User for GitHub Actions

Create a dedicated IAM user with the following permissions:
- ECR: Push/pull images
- ECS: Update services and task definitions
- S3: Read/write to deployment buckets
- CloudFront: Create invalidations
- RDS: Read connection info (if needed)

### 2. Configure GitHub Secrets

Use the secret values from your current deployment scripts and parameter files.

### 3. Test Deployments

1. **Manual Test**: Use the "Run workflow" button in GitHub Actions
2. **Automatic Test**: Push to a `release-*` branch

## 📋 Usage

### Manual Deployment

1. Go to GitHub Actions tab in your repository
2. Select the workflow you want to run
3. Click "Run workflow"
4. Choose the branch and environment
5. Click "Run workflow"

### Automatic Deployment

Push to `main` or any `release-*` branch to trigger automatic deployment.

## 🔍 Monitoring

### Deployment Status
- Check the Actions tab for real-time deployment status
- Each step shows detailed logs
- Failed deployments will show error details

### Application Health
- Backend: Check `https://chess-api.mathiarasan.com/health`
- UI: Check `https://chess-ui.mathiarasan.com`

## 🚨 Security Notes

1. **Never commit secrets** to the repository
2. **Rotate secrets regularly**, especially payment gateway keys
3. **Use least privilege** AWS IAM permissions
4. **Monitor deployment logs** for any exposed sensitive data

## 🐛 Troubleshooting

### Common Issues

1. **Terraform state errors**: Ensure Terraform state is properly committed
2. **AWS permission errors**: Check IAM user permissions
3. **Secret not found**: Verify all required secrets are configured
4. **Build failures**: Check Node.js version compatibility

### Getting Help

1. Check the workflow logs in GitHub Actions
2. Verify all secrets are properly configured
3. Test AWS credentials locally
4. Check Terraform state and outputs

## 📁 File Structure

```
.github/workflows/
├── chess-backend-deploy.yml     # Backend deployment workflow
├── chessbrigade-ui-deploy.yml   # UI deployment workflow
├── shared-setup.yml             # Reusable setup components
└── README.md                    # This file
```

## 🔄 Workflow Dependencies

The workflows use your existing deployment scripts:
- `infra-template/deploy-chess-backend.sh`
- `infra-template/chessbrigade-ui-deploy.sh`

No changes to your existing scripts are required - the workflows wrap them with proper secret injection.
