name: Deploy Chess Backend

on:
  push:
    branches: [main, release-*]  # Trigger on main and release branches
  workflow_dispatch:
    inputs:
      branch:
        description: 'Git branch to deploy'
        required: false
        default: 'may-release-jagan'
      environment:
        description: 'Deployment environment'
        required: false
        default: 'production'
        type: choice
        options:
        - production
        - staging

jobs:
  deploy:
    runs-on: ubuntu-latest
    
    env:
      # AWS Configuration
      AWS_REGION: ap-south-1
      ECR_REPOSITORY_NAME: chessbrigade-api
      IMAGE_TAG: latest
      ECS_CLUSTER_NAME: chessbrigade-cluster
      ECS_SERVICE_NAME: chessbrigade-api-service
      
      # Git Configuration
      GIT_REPO_URL: https://github.com/mathi0695/chessbrigade-api.git
      GIT_CLONE_DIR: chess-backend-src
      
      # Database Configuration
      DB_NAME: chessbrigade-live
      
      # Application Configuration
      CONTAINER_PORT: "3000"
      AWS_BUCKET_NAME: chess-brigade
      AWS_ACCESS_REGION: ap-south-1
      FRONTEND_URL: "https://chess-ui.mathiarasan.com,http://localhost:5173"

    steps:
      - name: Checkout infra repository
        uses: actions/checkout@v4
        with:
          ref: ${{ github.event.inputs.branch || github.ref }}

      - name: Setup Terraform
        uses: hashicorp/setup-terraform@v3
        with:
          terraform_version: "1.5.0"

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.CHESS_AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.CHESS_AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_REGION }}

      - name: Setup Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Verify AWS authentication
        run: |
          echo "Verifying AWS authentication..."
          aws sts get-caller-identity

      - name: Initialize Terraform
        working-directory: ./infra-template
        run: |
          echo "Initializing Terraform..."
          terraform init

      - name: Verify Terraform state and outputs
        working-directory: ./infra-template
        run: |
          echo "Checking Terraform state..."
          terraform show
          echo "Getting RDS endpoint..."
          terraform output rds_endpoint
          echo "Getting API URL..."
          terraform output api_url

      - name: Set environment variables from secrets
        run: |
          echo "Setting up environment variables..."
          # Export all secrets as environment variables for the deployment script
          echo "DB_PASSWORD=${{ secrets.CHESS_DB_PASSWORD }}" >> $GITHUB_ENV
          echo "DB_USER_ENV=${{ secrets.CHESS_DB_USER }}" >> $GITHUB_ENV
          echo "AWS_ACCESS_KEY_ID=${{ secrets.CHESS_AWS_ACCESS_KEY_ID }}" >> $GITHUB_ENV
          echo "AWS_SECRET_ACCESS_KEY=${{ secrets.CHESS_AWS_SECRET_ACCESS_KEY }}" >> $GITHUB_ENV
          echo "JWT_SECRET=${{ secrets.CHESS_JWT_SECRET }}" >> $GITHUB_ENV
          echo "MAIL_PASSWORD=${{ secrets.CHESS_EMAIL_PASSWORD }}" >> $GITHUB_ENV
          echo "EMAIL=${{ secrets.CHESS_EMAIL_USER }}" >> $GITHUB_ENV
          echo "SMTP_HOST=${{ secrets.CHESS_EMAIL_HOST }}" >> $GITHUB_ENV

      - name: Create parameter file for deployment
        working-directory: ./infra-template
        run: |
          echo "Creating deployment parameter file..."
          cat > chess-backend-params-ci.sh << 'EOF'
          #!/bin/bash
          # Chess Backend Deployment Parameters for CI/CD
          
          # AWS Configuration
          AWS_REGION="${{ env.AWS_REGION }}"
          ECR_REPOSITORY_NAME="${{ env.ECR_REPOSITORY_NAME }}"
          IMAGE_TAG="${{ env.IMAGE_TAG }}"
          ECS_CLUSTER_NAME="${{ env.ECS_CLUSTER_NAME }}"
          ECS_SERVICE_NAME="${{ env.ECS_SERVICE_NAME }}"
          
          # Git Configuration
          GIT_REPO_URL="${{ env.GIT_REPO_URL }}"
          GIT_BRANCH="${{ github.event.inputs.branch || 'may-release-jagan' }}"
          GIT_CLONE_DIR="${{ env.GIT_CLONE_DIR }}"
          
          # Database Configuration
          DB_NAME="${{ env.DB_NAME }}"
          DB_USER="${{ secrets.CHESS_DB_USER }}"
          
          # Application Configuration
          CONTAINER_PORT="${{ env.CONTAINER_PORT }}"
          AWS_BUCKET_NAME="${{ env.AWS_BUCKET_NAME }}"
          AWS_ACCESS_REGION="${{ env.AWS_ACCESS_REGION }}"
          FRONTEND_URL="${{ env.FRONTEND_URL }}"
          EOF
          
          chmod +x chess-backend-params-ci.sh

      - name: Run Chess Backend Deployment
        working-directory: ./infra-template
        run: |
          echo "Starting Chess Backend deployment..."
          # Make the deployment script executable
          chmod +x deploy-chess-backend.sh
          
          # Run the deployment script with our CI parameter file
          ./deploy-chess-backend.sh -p chess-backend-params-ci.sh -b "${{ github.event.inputs.branch || 'may-release-jagan' }}"

      - name: Verify deployment
        run: |
          echo "Verifying deployment..."
          # Get the API URL from Terraform output
          cd infra-template
          API_URL=$(terraform output -raw api_url)
          echo "API URL: $API_URL"
          
          # Test the API endpoint
          echo "Testing API health endpoint..."
          curl -f "$API_URL/health" || echo "Health check failed, but deployment may still be successful"

      - name: Cleanup
        if: always()
        working-directory: ./infra-template
        run: |
          echo "Cleaning up temporary files..."
          rm -f chess-backend-params-ci.sh
          # Clean up any cloned source code
          rm -rf chess-backend-src

      - name: Notify deployment status
        if: always()
        run: |
          if [ "${{ job.status }}" == "success" ]; then
            echo "✅ Chess Backend deployment completed successfully!"
            echo "🚀 API is available at: $(cd infra-template && terraform output -raw api_url)"
          else
            echo "❌ Chess Backend deployment failed!"
            echo "Please check the logs above for details."
          fi
