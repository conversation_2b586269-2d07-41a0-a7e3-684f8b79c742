# General
aws_region = "us-east-1"
app_name   = "my-app"
environment = "Production"

# VPC Configuration
vpc_cidr             = "10.0.0.0/16"
public_subnet_cidrs  = ["10.0.0.0/24", "10.0.1.0/24"]
private_subnet_cidrs = ["10.0.10.0/24", "10.0.11.0/24"]

# Database Configuration
db_name           = "myappdb"
db_username       = "myappuser"
db_password       = "CHANGE_ME_TO_SECURE_PASSWORD"
db_instance_class = "db.t3.micro"
db_allocated_storage = 20
db_engine_version = "16.8"

# ECS Configuration
container_image = "public.ecr.aws/bitnami/node:18/app"
container_port  = 3000
ecs_task_cpu    = "256"
ecs_task_memory = "512"
desired_count   = 1
health_check_path = "/"

# Domain Configuration
domain_name     = "example.com"
domain_aliases  = ["www.example.com"]
api_domain_name = "api.example.com"
bucket_name     = "my-app-website"
