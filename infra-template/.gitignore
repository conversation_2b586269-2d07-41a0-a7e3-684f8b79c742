# Terraform files
*.tfstate
*.tfstate.*
*.tfvars
.terraform/
.terraform.lock.hcl
terraform.tfplan
terraform.tfplan.*
crash.log
crash.*.log

# AWS credentials and config
.aws/
aws-credentials
*.pem
*.key

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
*.env

# Node.js dependencies and build artifacts
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
.pnpm-debug.log*

# Build outputs
dist/
build/
out/
.next/
.nuxt/
.cache/
.parcel-cache/

# Source code directories (cloned repositories)
*-src/
*-backend-src/
*-ui-src/
carytoo-backend-src/
chessbrigade-ui-src/
portfolio-ui-src/

# Docker files
Dockerfile.local
docker-compose.override.yml
.dockerignore

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~
.DS_Store
Thumbs.db

# Logs
*.log
logs/
*.log.*

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov
.nyc_output/

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional stylelint cache
.stylelintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# yarn v2
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.*

# Backup files
*.backup
*.bak
*.tmp

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Archive files
*.zip
*.tar.gz
*.rar
*.7z

# Large media files
*.mp4
*.avi
*.mov
*.wmv
*.flv
*.webm
*.mkv
*.m4v
*.3gp
*.3g2
*.mp3
*.wav
*.flac
*.aac
*.ogg
*.wma
*.m4a
*.psd
*.ai
*.sketch
*.fig

# Database files
*.db
*.sqlite
*.sqlite3

# Certificates and keys
*.crt
*.cert
*.ca-bundle
*.p7b
*.p7s
*.pfx
*.p12

# Sensitive configuration files
secrets.yml
secrets.yaml
config/secrets.yml
config/database.yml
.secrets

# Terraform backend configuration (if using remote state)
backend.tf
backend.hcl

# Local terraform modules
.terraform.d/

# Ignore specific large directories that might be created
uploads/
downloads/
temp/
tmp/
cache/
