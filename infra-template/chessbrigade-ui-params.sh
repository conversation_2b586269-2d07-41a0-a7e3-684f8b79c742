#!/bin/bash
# ChessBrigade UI Deployment Parameters

# AWS Configuration
AWS_REGION="ap-south-1"
S3_BUCKET_NAME="chess-brigade"
CLOUDFRONT_DISTRIBUTION_ID="E2E0G9X3I7SJ86" # Replace with your actual CloudFront distribution ID

# Git Configuration
GIT_REPO_URL="https://github.com/mathi0695/chessbrigade-ui.git"  # Update with your actual repository URL
GIT_BRANCH="release-may-jagan"  # Update with your desired branch
GIT_CLONE_DIR="chessbrigade-ui-src"

# Build Configuration
NODE_VERSION="18"  # Specify the Node.js version to use
BUILD_COMMAND="npm run build"  # The command to build the React app
BUILD_OUTPUT_DIR="dist"  # The directory where build output is generated (usually 'dist' or 'build')

# Environment Variables for React Build
REACT_APP_API_URL="https://chess-api.mathiarasan.com"
REACT_APP_ENV="production"
# Add any other environment variables needed for your React build
