const XLSX = require("xlsx");
const fs = require("fs");
const bcrypt = require("bcryptjs");
const { models, sequelize } = require("../config/db");
require("dotenv").config();

/**
 * Process Excel file for player import
 * @param {string} filePath - Path to the Excel file
 * @param {Object} options - Options for processing
 * @returns {Object} - Processing results
 */
async function processPlayerExcelFile(filePath, options = {}) {
  try {
    // Read the Excel file
    const workbook = XLSX.readFile(filePath);
    const sheet = workbook.Sheets[workbook.SheetNames[0]];
    const data = XLSX.utils.sheet_to_json(sheet);

    if (data.length === 0) {
      return {
        success: false,
        message: "No data found in the Excel file",
        filePath,
      };
    }

   
    let successCount = 0;
    let failedCount = 0;
    const errors = [];
    const successfulPlayers = [];

    // Get club name and UUID from club ID if provided
    let clubName = null;
    let clubUuid = null;

     // Process each row
    for (const row of data) {
      try {
        // Extract data from row with flexible field mapping
        const playerName = row["Player Name"] || row["Name"] || row["Full Name"] || row["playername"] || row["name"];
        const emailRaw = row["Email"] || row["Email Address"] || row["email"] || row["emailaddress"] || "";
        const email = emailRaw.trim().toLowerCase();
        let phoneNumber = row["Phone Number"] || row["Phone"] || row["Contact"] || row["Mobile"] || row["phone"] || row["phonenumber"] || row["contact"] || row["mobile"];
        const dateOfBirth = row["Date of Birth"] || row["DOB"] || row["Dob"] || row["dateofbirth"] || row["dob"] || null;
        const gender = row["Gender"] || row["gender"] || row["Sex"] || row["sex"] || null;

        // Clean phone number - remove country code 91 if present
        if (phoneNumber) {
          phoneNumber = phoneNumber.toString().replace(/[\s\-\(\)\+]/g, ''); // Remove spaces, dashes, parentheses, plus

          // Remove country code 91 if present at the beginning
          if (phoneNumber.startsWith('91') && phoneNumber.length > 10) {
            phoneNumber = phoneNumber.substring(2);
          }

          // Ensure phone number is exactly 10 digits for Indian numbers
          if (phoneNumber.length > 10) {
            phoneNumber = phoneNumber.substring(phoneNumber.length - 10); // Take last 10 digits
          }
        }

        // Validate required fields
        const rowNumber = data.indexOf(row) + 2; // +2 for Excel row number (header + 1-based)

        if (!playerName || !email || !phoneNumber) {
          const missingFields = [];
          if (!playerName) missingFields.push('playerName');
          if (!email) missingFields.push('email');
          if (!phoneNumber) missingFields.push('phoneNumber');

          errors.push(`Row ${rowNumber}: Missing required fields: ${missingFields.join(', ')}`);
          failedCount++;
          continue;
        }

        // Generate unique email and phone if duplicates exist
        const uniqueEmail = await generateUniqueEmail(email, playerName);
        const uniquePhone = await generateUniquePhone(phoneNumber.toString(), playerName);

        // Generate password
        const plainPassword = "password123";
        const password = await bcrypt.hash(plainPassword, 10);

        // Debug: Log the data being inserted
        console.log(`Processing row ${rowNumber}:`, {
          name: playerName.substring(0, 255),
          email: uniqueEmail,
          phone: uniquePhone,
          phoneLength: uniquePhone.length,
          originalPhone: phoneNumber
        });

        // Check for potential length issues
        if (uniquePhone.length > 20) {
          console.warn(`⚠ Phone number too long (${uniquePhone.length} chars): ${uniquePhone}`);
        }

        // Start database transaction
        const transaction = await sequelize.transaction();

        try {
          // Create user within transaction
          const user = await models.User.create({
            name: playerName.substring(0, 255), // Ensure name fits
            cbid: generateCbid(),
            email: uniqueEmail,
            phoneNumber: uniquePhone.substring(0, 20), // Limit phone number length
            password: password,
            role: 'player',
            isActive: true,
            isAccess: true
          }, { transaction });

          console.log(`✅ User created successfully: ${user.name} (ID: ${user.id})`);

          // Create player detail within transaction
          await models.PlayerDetail.create({
            userId: user.id,
            dob: dateOfBirth ? formatDate(dateOfBirth) : new Date('2000-01-01'), // Default DOB if not provided
            gender: (gender ? gender.toLowerCase() : 'not specified').substring(0, 10), // Limit to 10 chars
            country: 'India',
            state: 'TN', // Shortened to fit 10 char limit
            district: 'Chennai', // Shortened to fit 10 char limit
            city: 'Chennai', // Shortened to fit 10 char limit
            pincode: '000000', // Ensure pincode is exactly 6 digits
            fideId: null,
            fideRating: null,
            aicfId: null,
            stateId: null,
            districtId: null,
            club: clubName ? clubName.substring(0, 100) : null, // Limit club name length
            clubId: clubUuid || null, // Use UUID from club record, null if not provided
            termsAndConditions: true,
          }, { transaction });

          console.log(`✅ PlayerDetail created successfully for: ${user.name}`);

          // Commit transaction - both User and PlayerDetail created successfully
          await transaction.commit();

          successfulPlayers.push({
            name: user.name,
            email: user.email,
            cbid: user.cbid
          });
          successCount++;

        } catch (transactionError) {
          // Rollback transaction - deletes User if PlayerDetail creation fails
          await transaction.rollback();
          console.log(`🔄 Transaction rolled back for ${playerName} - User deleted due to PlayerDetail failure`);
          throw transactionError; // Re-throw to be caught by outer catch block
        }

      } catch (error) {
        const rowNumber = data.indexOf(row) + 2;
        console.error(`❌ Error processing row ${rowNumber}:`, error.message);
        console.error('Error details:', error);
        errors.push(`Row ${rowNumber}: ${error.message}`);
        failedCount++;
      }
    }

    return {
      success: true,
      data: {
        successCount,
        failedCount,
        totalProcessed: successCount + failedCount,
        successfulPlayers: successfulPlayers.slice(0, 10) // Limit for response size
      },
      errors: errors.slice(0, 20), // Limit errors for response size
      filePath,
    };

  } catch (error) {
    return {
      success: false,
      message: Error `processing file: ${error.message}`,
      filePath,
    };
  }
}

/**
 * Generate unique email if duplicate exists
 */
const generateUniqueEmail = async (originalEmail, playerName) => {
  let email = originalEmail;
  let counter = 1;

  while (true) {
    const existing = await models.User.findOne({ where: { email } });
    if (!existing) {
      return email;
    }

    // Check if existing user has same name
    if (existing.name.toLowerCase().trim() === playerName.toLowerCase().trim()) {
      throw new Error(`User with email ${originalEmail} and name "${playerName}" already exists`);
    }

    // Generate professional email variations with chess-related suffixes
    const [localPart, domain] = originalEmail.split('@');

    // Professional chess-related suffixes
    const chessSuffixes = [
      'chess', 'player', 'tournament', 'game', 'club',
      'board', 'move', 'king', 'queen', 'knight'
    ];

    if (counter <= chessSuffixes.length) {
      const suffix = chessSuffixes[counter - 1];
      email = `${localPart}.${suffix}@${domain}`;
    } else {
      const extraNumber = counter - chessSuffixes.length;
      email =`${localPart}.chess${extraNumber}@${domain}`;
    }

    counter++;

    if (counter > 100) {
      throw new Error(`Unable to generate unique email for ${originalEmail}`);
    }
  }
};

/**
 * Generate unique phone number if duplicate exists
 */
const generateUniquePhone = async (originalPhone, playerName) => {
  let phone = originalPhone;
  let counter = 1;

  while (true) {
    const existing = await models.User.findOne({ where: { phoneNumber: phone } });
    if (!existing) {
      return phone;
    }

    // Check if existing user has same name
    if (existing.name.toLowerCase().trim() === playerName.toLowerCase().trim()) {
      throw new Error(`User with phone ${originalPhone} and name "${playerName}" already exists`);
    }

    // Generate professional phone variations
    let cleanPhone = originalPhone.replace(/[-\s\(\)\+]/g, '');

    // Remove country code 91 if present
    if (cleanPhone.startsWith('91') && cleanPhone.length > 10) {
      cleanPhone = cleanPhone.substring(2);
    }

    // Ensure we have a 10-digit base number
    if (cleanPhone.length > 10) {
      cleanPhone = cleanPhone.substring(cleanPhone.length - 10);
    }

    if (counter <= 9) {
      const basePhone = cleanPhone.slice(0, -1);
      const lastDigit = parseInt(cleanPhone.slice(-1));
      const newLastDigit = (lastDigit + counter) % 10;
      phone = basePhone + newLastDigit;
    } else {
      // For subsequent attempts, add extension format but keep within length limit
      const extension = counter - 9;
      phone = `${cleanPhone}-${extension}`;
    }

    // Ensure phone number doesn't exceed 20 characters
    phone = phone.substring(0, 20);

    counter++;

    if (counter > 100) {
      throw new Error(`Unable to generate unique phone for ${originalPhone}`);
    }
  }
};

/**
 * Format date from various formats to Date object
 */
function formatDate(dateValue) {
  if (!dateValue) return null;

  if (typeof dateValue === "string") {
    // Try different date formats
    const formats = [
      /^\d{4}-\d{2}-\d{2}$/, // YYYY-MM-DD
      /^\d{2}-\d{2}-\d{4}$/, // DD-MM-YYYY
      /^\d{2}\/\d{2}\/\d{4}$/, // DD/MM/YYYY
    ];

    if (formats[0].test(dateValue)) {
      return new Date(dateValue);
    } else if (formats[1].test(dateValue)) {
      const parts = dateValue.split("-");
      return new Date(`${parts[2]}-${parts[1]}-${parts[0]}`);
    } else if (formats[2].test(dateValue)) {
      const parts = dateValue.split("/");
      return new Date(`${parts[2]}-${parts[1]}-${parts[0]}`);
    }

    return new Date(dateValue); // fallback
  }

  if (dateValue instanceof Date) {
    return dateValue;
  }

  if (typeof dateValue === "number") {
    // Excel serial date number
    const excelEpoch = new Date(1899, 11, 30);
    return new Date(excelEpoch.getTime() + dateValue * 86400000);
  }

  return null;
}

/**
 * Generate unique CBID (max 10 characters)
 */
const generateCbid = () => {
  // Generate exactly 8 character random string to fit with 'PL' prefix (total 10 chars)
  const randomPart = Math.random().toString(36).substring(2, 10).toUpperCase();
  const cbid = 'PL' + randomPart;

  // Ensure it's exactly 10 characters or less
  return cbid.substring(0, 10);
};

// Export the main function for use by controllers
module.exports = {
  processPlayerExcelFile,
  generateUniqueEmail,
  generateUniquePhone,
  formatDate,
  generateCbid
};

// Command line usage
if (require.main === module) {
  if (process.argv.length > 2) {
    const filePath = process.argv[2];
    const clubId = process.argv[3] || null;

    if (fs.existsSync(filePath)) {
      processPlayerExcelFile(filePath, { clubId })
        .then(result => {
          console.log('📊 Processing Result:', result);
        })
        .catch(error => {
          console.error('❌ Error:', error);
        });
    } else {
      console.error(`❌ File not found: ${filePath}`);
    }
  } else {
    console.log(`
📋 Usage: node scripts/importusers.js [path-to-excel-file] [club-id]
📝 Example: node scripts/importusers.js uploads/players.xlsx chennai-chess-club
    `);
  }
}