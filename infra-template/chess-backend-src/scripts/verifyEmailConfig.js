const { verifyConfiguration } = require('../utils/mailer');
const { config } = require('../config/config');

async function verifyEmailConfig() {
  try {
    const isValid = await verifyConfiguration();
    
    if (isValid) {
    } else {
      console.error('\n❌ Email configuration is invalid!');
      console.error('Please check your email settings in the .env file.');
    }
  } catch (error) {
    console.error('\n❌ Error verifying email configuration:', error);
    console.error('Please check your email settings in the .env file.');
  }
  
  process.exit(0);
}

verifyEmailConfig();
