/**
 * Excel Table Extractor for Chess Brigade API
 *
 * This script extracts tables from Excel files and inserts the data into the database.
 * It can handle various table structures and automatically detects table boundaries.
 *
 * Usage:
 *   node scripts/excel-extractor.js [file.xlsx] [tournamentId]
 *
 * If a file is provided, it will analyze and process that file.
 * If a tournamentId is provided, it will associate the extracted data with that tournament.
 */

const XLSX = require("xlsx");
const fs = require("fs");
const path = require("path");
const { models } = require("../config/db");
require("dotenv").config();

/**
 * Detects a table in a worksheet using a simpler algorithm:
 * "The earliest row with more than 3 columns will be considered the table header"
 *
 * @param {Object} worksheet - XLSX worksheet object
 * @returns {Object} - Table boundaries and headers
 */
function detectTable(worksheet) {
  const range = XLSX.utils.decode_range(worksheet["!ref"]);

  // Find the first row with more than 3 non-empty cells
  let headerRow = null;
  let headerCells = [];

  for (let r = range.s.r; r <= range.e.r; r++) {
    let cellCount = 0;
    let rowCells = [];

    for (let c = range.s.c; c <= range.e.c; c++) {
      const cellAddress = XLSX.utils.encode_cell({ r, c });
      const cell = worksheet[cellAddress];

      if (cell && cell.v !== undefined && cell.v !== null && cell.v !== "") {
        cellCount++;
        rowCells.push({ col: c, value: cell.v });
      }
    }

    // If this row has more than 3 non-empty cells, it might be a header row
    if (cellCount > 3) {
      headerRow = r;
      headerCells = rowCells;
      break;
    }
  }

  if (headerRow === null) {
    return { found: false };
  }

  // Now find the end of the table by looking for the first row after the header
  // that doesn't have data in the same columns as the headers
  let tableEnd = headerRow;
  let dataRowsCount = 0;

  for (let r = headerRow + 1; r <= range.e.r; r++) {
    let hasData = false;

    // Check if this row has data in at least some of the header columns
    for (const header of headerCells) {
      const cellAddress = XLSX.utils.encode_cell({ r, c: header.col });
      const cell = worksheet[cellAddress];

      if (cell && cell.v !== undefined && cell.v !== null && cell.v !== "") {
        hasData = true;
        break;
      }
    }

    // If the row has some data, it might be a data row
    if (hasData) {
      dataRowsCount++;
      tableEnd = r;
    } else if (dataRowsCount > 0) {
      // If we've already found data rows and now found a row without data,
      // we've reached the end of the table
      break;
    }
  }

  // If we found at least one data row, we've found a table
  if (dataRowsCount > 0) {
    return {
      found: true,
      start: headerRow,
      end: tableEnd,
      headers: headerCells,
    };
  }
  return { found: false };
}

/**
 * Extracts data from a table in a worksheet and converts it to JSON
 *
 * This function:
 * 1. Creates a map of column indices to header names
 * 2. Extracts data from each row in the table
 * 3. Converts cell values to appropriate types
 * 4. Creates JSON objects with header names as keys
 * 5. Handles blank columns by naming them "Blank_1", "Blank_2", etc.
 *
 * @param {Object} worksheet - XLSX worksheet object
 * @param {Object} tableInfo - Table boundaries and headers
 * @returns {Array} - Array of JSON objects
 */
function extractTableData(worksheet, tableInfo) {
  const result = [];
  const range = XLSX.utils.decode_range(worksheet["!ref"]);

  // Create a map of column indices to header names
  const headerMap = {};

  // Track columns with data but no header
  const columnsWithData = new Set();
  let blankColumnCount = 0;

  // Store column letters for direct access
  const columnLetters = {};

  // First, identify all columns that have data in the first few data rows
  const sampleRows = Math.min(5, tableInfo.end - tableInfo.start);
  for (
    let r = tableInfo.start + 1;
    r <= Math.min(tableInfo.start + sampleRows, tableInfo.end);
    r++
  ) {
    for (let c = range.s.c; c <= range.e.c; c++) {
      const cellAddress = XLSX.utils.encode_cell({ r, c });
      const cell = worksheet[cellAddress];

      if (cell && cell.v !== undefined && cell.v !== null && cell.v !== "") {
        columnsWithData.add(c);
      }
    }
  }

  // Add headers from tableInfo
  for (const header of tableInfo.headers) {
    // Use the header value as the key, ensuring it's a string
    let headerName = String(header.value).trim();

    // If the header is a number or empty, give it a generic name
    if (headerName === "" || !isNaN(headerName)) {
      headerName = `Column_${header.col}`;
    }

    headerMap[header.col] = headerName;
    // Store the column letter for this column
    columnLetters[headerName] = XLSX.utils.encode_col(header.col);
    columnsWithData.delete(header.col); // Remove from columnsWithData since it has a header
  }

  // Add columns with data but no header
  for (const col of columnsWithData) {
    blankColumnCount++;
    const headerName = `Blank_${blankColumnCount}`;
    headerMap[col] = headerName;
    // Store the column letter for this column
    columnLetters[headerName] = XLSX.utils.encode_col(col);
  }

  // Extract data rows
  for (let r = tableInfo.start + 1; r <= tableInfo.end; r++) {
    const rowData = {};
    let hasData = false;

    for (const col in headerMap) {
      const cellAddress = XLSX.utils.encode_cell({ r, c: parseInt(col) });
      const cell = worksheet[cellAddress];

      if (cell && cell.v !== undefined && cell.v !== null) {
        // Convert cell value to appropriate type
        let value = cell.v;

        // Format dates if the cell contains a date
        if (cell.t === "d") {
          value = cell.w || value.toISOString();
        }

        // Format numbers if needed
        if (cell.t === "n") {
          // Keep as number
          value = Number(value);
        }

        // Format booleans
        if (cell.t === "b") {
          value = Boolean(value);
        }

        rowData[headerMap[col]] = value;
        hasData = true;
      } else {
        rowData[headerMap[col]] = null;
      }
    }

    // Only add rows that have some data
    if (hasData) {
      // Add column letters to the row data for direct access
      for (const [key, colLetter] of Object.entries(columnLetters)) {
        rowData[colLetter] = rowData[key];
      }
      result.push(rowData);
    }
  }

  return result;
}

/**
 * Maps extracted data to the ReportExtract model fields
 *
 * @param {Array} data - Extracted data from Excel
 * @param {Object} options - Options for mapping
 * @returns {Array} - Array of objects mapped to the ReportExtract model
 */
function mapDataToModel(data, options = {}) {
  const { tournamentId, sourceFile, additionalFields } = options;

  // Check which model we're mapping to
  if (options.modelName === "Ranking") {
    return mapToRankingModel(data, options);
  } else if (options.modelName === "Pairing") {
    return mapToPairingModel(data, options);
  }

  // Default mapping for backward compatibility
  return data.map((row) => {
    // Create a base object with the required fields
    const mappedData = {
      tournamentId: tournamentId || null,
      sourceFile: sourceFile || null,
      additionalData: {},
    };

    // Add any additional fields passed from the API
    if (additionalFields && typeof additionalFields === "object") {
      Object.keys(additionalFields).forEach((key) => {
        if (!mappedData[key]) {
          mappedData[key] = additionalFields[key];
        }
      });
    }

    // Map common field names to model fields
    const fieldMapping = {
      // Common field names and their mappings to model fields
      "No.": "playerNumber",
      Number: "playerNumber",
      "Player No": "playerNumber",
      Name: "playerName",
      "Player Name": "playerName",
      ID: "playerId",
      "Player ID": "playerId",
      FideID: "fideId",
      "FIDE ID": "fideId",
      Rtg: "rating",
      Rating: "rating",
      sex: "gender",
      Gender: "gender",
      Gr: "category",
      Category: "category",
      Group: "category",
      Typ: "category",
      Club: "club",
      "Club/City": "city",
    };

    // Process each field in the row
    for (const [key, value] of Object.entries(row)) {
      // Check if this field has a mapping
      if (fieldMapping[key]) {
        mappedData[fieldMapping[key]] = value;
      } else {
        // If no mapping exists, add to additionalData
        mappedData.additionalData[key] = value;
      }
    }

    return mappedData;
  });
}

/**
 * Maps extracted data to the Ranking model fields
 *
 * @param {Array} data - Extracted data from Excel
 * @param {Object} options - Options for mapping
 * @returns {Array} - Array of objects mapped to the Ranking model
 */
function mapToRankingModel(data, options = {}) {
  const {
    tournament_id,
    round_id = 1,
    age_category = "Default",
    gender_category = "Default",
  } = options;
  
  return data.map((row, index) => {
    // Create a base object with the required fields
    const mappedData = {
      tournament_id,
      round_id,
      age_category,
      gender_category,
      rank: index + 1, // Default rank based on order in Excel
      total_points: 0,
      tie_breakers: {},
    };
    
    // Enhanced field mapping with more variations
    const fieldMapping = {
      // Rank/Position fields
      "Rank": "rank",
      "Pos": "rank", 
      "Position": "rank",
      "No.": "rank",
      "SNo.": "rank",
      
      // Player information
      "Name": "player_name",
      "Player": "player_name",
      "Player Name": "player_name",
      
      // FIDE information
      "FideID": "fide_id",
      "FIDE ID": "fide_id",
      "ID": "fide_id",
      "FIDE": "fide_id",
      
      // Rating
      "Rtg": "fide_rating",
      "Rating": "fide_rating",
      "FIDE Rtg": "fide_rating",
      "FIDE Rating": "fide_rating",
      
      // Federation/Association
      "Fed": "association",
      "FED": "association",
      "Federation": "association",
      "Club": "association",
      "Country": "association",
      
      // Points - Multiple possible field names
      "Pts": "total_points",
      "Points": "total_points",
      "Total": "total_points",
      "Score": "total_points",
      "Total Points": "total_points",
    };
    
    // Common tie breaker field patterns
    const tieBreakerPatterns = [
      /^BH/i,           // Buchholz variations (BH, BH:GP, etc.)
      /^SB/i,           // Sonneborn-Berger
      /^TB\d+/i,        // TB1, TB2, TB3, etc.
      /Buchholz/i,
      /Sonneborn/i,
      /Berger/i,
      /Koya/i,
      /ARO/i,
      /Direct/i,
      /Encounter/i,
      /Performance/i,
      /Progressive/i,
    ];
    
    // Process each field in the row
    for (let [key, value] of Object.entries(row)) {
      // Clean the key (remove extra spaces, handle special characters)
      const cleanKey = key.trim();
      
      // Skip empty or null values for certain fields
      if (value === null || value === undefined || value === '') {
        continue;
      }
      
      // Check if this field has a direct mapping
      if (fieldMapping[cleanKey]) {
        const mappedField = fieldMapping[cleanKey];
        
        // Special handling for numeric fields
        if (mappedField === "total_points") {
          // Handle various point formats: "1", "1.0", "1½", "0.5", etc.
          const pointValue = parsePointValue(value);
          mappedData[mappedField] = pointValue;
        } else if (mappedField === "rank" || mappedField === "fide_rating") {
          // Ensure numeric values are properly parsed
          const numValue = parseInt(value);
          if (!isNaN(numValue)) {
            mappedData[mappedField] = numValue;
          }
        } else {
          mappedData[mappedField] = value;
        }
      }
      // Check if this is a tie breaker field using patterns
      else if (tieBreakerPatterns.some(pattern => pattern.test(cleanKey))) {
        // Parse tie breaker value (could be numeric or text)
        const tbValue = parseFloat(value);
        mappedData.tie_breakers[cleanKey] = isNaN(tbValue) ? value : tbValue;
      }
      // Special handling for round results (1.Rd., 2.Rd., etc.)
      else if (/^\d+\.Rd\.?$/i.test(cleanKey)) {
        // Store round results in tie_breakers or separate field
        mappedData.tie_breakers[cleanKey] = value;
      }
    }
    
    // Ensure rank is set (use index + 1 if not found in data)
    if (!mappedData.rank || mappedData.rank === 0) {
      mappedData.rank = index + 1;
    }
    
    return mappedData;
  });
}

/**
 * Maps extracted data to the Pairing model fields
 *
 * This is a simplified version that takes advantage of the pre-processed data
 * from extractPairingTableData where duplicate columns like "Rtg" and "pts"
 * have already been renamed to "white_Rtg", "black_Rtg", "white_pts", and "black_pts"
 *
 * @param {Array} data - Extracted data from Excel
 * @param {Object} options - Options for mapping
 * @returns {Array} - Array of objects mapped to the Pairing model
 */
function mapToPairingModel(data, options = {}) {
  const {
    tournament_id,
    round_id = 1,
    age_category = "Default",
    gender_category = "Default",
  } = options;
  return data
    .map((row, index) => {
      // Create a base object with the required fields
      const mappedData = {
        tournament_id,
        round_id,
        age_category,
        gender_category,
        board_no: index + 1, // Default board number based on order in Excel
        white_player_name: "",
        black_player_name: "",
        result: "draw", // Default result
      };

      // Map common field names to model fields
      const fieldMapping = {
        // Board number fields
        "Bo.": "board_no",
        Board: "board_no",
        "Board No": "board_no",
        Table: "board_no",
        "Table No": "board_no",

        // Player names
        White: "white_player_name",
        Black: "black_player_name",

        // Pre-processed fields from extractPairingTableData
        white_Rtg: "white_player_fide_rating",
        black_Rtg: "black_player_fide_rating",
        white_pts: "white_player_points",
        black_pts: "black_player_points",

        // Legacy field mappings for backward compatibility
        "White Player": "white_player_name",
        "White Name": "white_player_name",
        "White Rating": "white_player_fide_rating",
        "White Rtg": "white_player_fide_rating",
        "White FIDE": "white_player_fide_rating",
        "White Rank": "white_player_ranking",
        "White Ranking": "white_player_ranking",
        "White Pts": "white_player_points",
        "White Points": "white_player_points",

        "Black Player": "black_player_name",
        "Black Name": "black_player_name",
        "Black Rating": "black_player_fide_rating",
        "Black Rtg": "black_player_fide_rating",
        "Black FIDE": "black_player_fide_rating",
        "Black Rank": "black_player_ranking",
        "Black Ranking": "black_player_ranking",
        "Black Pts": "black_player_points",
        "Black Points": "black_player_points",

        // Result fields
        Result: "result_raw",
        Score: "result_raw",
        result: "result_raw",
      };

      // Process each field in the row
      for (const [key, value] of Object.entries(row)) {
        // Check if this field has a mapping
        if (fieldMapping[key]) {
          if (fieldMapping[key] === "result_raw") {
            // Process the result field
            mappedData.result = parseResult(value);
          } else if (
            fieldMapping[key].includes("points") &&
            value !== null &&
            value !== undefined
          ) {
            // Convert points to float
            mappedData[fieldMapping[key]] = parseFloat(value) || 0;
          } else if (
            fieldMapping[key].includes("rating") &&
            value !== null &&
            value !== undefined
          ) {
            // Convert ratings to integer
            mappedData[fieldMapping[key]] = parseInt(value) || null;
          } else {
            mappedData[fieldMapping[key]] = value;
          }
        }
      }

      // Validate required fields
      if (!mappedData.white_player_name || !mappedData.black_player_name) {
        console.warn(`Skipping row ${index + 1} due to missing player names`);
        return null;
      }

      return mappedData;
    })
    .filter((item) => item !== null); // Remove null entries
}

/**
 * Detect if the data has the specific pairing format with repeating headers
 * Bo., White, Rtg, pts, result, pts, Black, Rtg
 *
 * @param {Array} data - Extracted data from Excel
 * @returns {boolean} - Whether the data has the specific format
 */
function detectSpecificPairingFormat(data) {
  if (!data || data.length === 0) return false;

  // Get the keys of the first row
  const keys = Object.keys(data[0]);

  // Check if we have the expected headers
  const hasBoHeader = keys.some((key) => key === "Bo.");
  const hasWhiteHeader = keys.some((key) => key === "White");
  const hasBlackHeader = keys.some((key) => key === "Black");

  // Look for result header with various possible names
  const hasResultHeader = keys.some(
    (key) =>
      key === "Result" ||
      key.includes("result") ||
      key.includes("Result ") ||
      key.includes("score")
  );

  // Count how many times 'Rtg' and 'Pts.' appear (note the period in 'Pts.')
  const rtgCount = keys.filter((key) => key === "Rtg").length;
  const ptsCount = keys.filter(
    (key) => key === "Pts." || key === "pts" || key === "Pts"
  ).length;
  // Special case for chessResultsList.xlsx format
  // Check if we have the exact headers from the chessResultsList.xlsx file
  const hasRtg = keys.includes("Rtg");
  const hasPts =
    keys.includes("Pts.") || keys.includes("pts") || keys.includes("Pts");
  const hasResult =
    keys.includes("Result ") ||
    keys.includes("Result") ||
    keys.includes("result");

  // Check if this is the chessResultsList.xlsx format
  const isChessResultsFormat =
    hasBoHeader &&
    hasWhiteHeader &&
    hasBlackHeader &&
    hasRtg &&
    hasPts &&
    hasResult;

  if (isChessResultsFormat) {
    return true;
  }

  // Additional check for the specific format mentioned by the user
  // Bo., White, Rtg, pts, result, pts, Black, Rtg
  const headerStr = keys.join(",");
  if (
    headerStr.includes("Bo.") &&
    headerStr.includes("White") &&
    headerStr.includes("Black") &&
    headerStr.includes("Rtg") &&
    (headerStr.includes("pts") || headerStr.includes("Pts.")) &&
    (headerStr.includes("result") || headerStr.includes("Result"))
  ) {
    return true;
  }

  // General case for formats with repeating headers
  return (
    hasBoHeader &&
    hasWhiteHeader &&
    hasBlackHeader &&
    hasResultHeader &&
    rtgCount >= 2 &&
    ptsCount >= 2
  );
}

/**
 * Map data with the specific pairing format to the Pairing model
 * Bo., White, Rtg, pts, result, pts, Black, Rtg
 *
 * @param {Array} data - Extracted data from Excel
 * @param {Object} options - Options for mapping
 * @returns {Array} - Array of objects mapped to the Pairing model
 */
function mapSpecificPairingFormat(data, options = {}) {
  const { tournament_id, round_id = 1,  age_category = "Default",
    gender_category = "Default",} = options;

  // First, analyze the structure to determine the exact format
  const keys = Object.keys(data[0]);
  // Check if this is the Chess Results List format
  const hasRtg = keys.includes("Rtg");
  const hasPts =
    keys.includes("Pts.") || keys.includes("pts") || keys.includes("Pts");
  const hasResult =
    keys.includes("Result ") ||
    keys.includes("Result") ||
    keys.includes("result");

  const isChessResultsFormat =
    keys.includes("Bo.") &&
    keys.includes("White") &&
    keys.includes("Black") &&
    hasRtg &&
    hasPts &&
    hasResult;

  if (isChessResultsFormat) {
    return mapChessResultsListFormat(data, options);
  }

  // Find the indices of our important headers
  const boIndex = keys.findIndex((key) => key === "Bo.");
  const whiteIndex = keys.findIndex((key) => key === "White");
  const blackIndex = keys.findIndex((key) => key === "Black");

  // Find the result header with various possible names
  const resultIndex = keys.findIndex(
    (key) =>
      key === "Result" ||
      key.includes("result") ||
      key.includes("Result ") ||
      key.includes("score")
  );

  // Find the indices of repeating headers
  const rtgIndices = keys.reduce((indices, key, index) => {
    if (key === "Rtg") indices.push(index);
    return indices;
  }, []);

  const ptsIndices = keys.reduce((indices, key, index) => {
    if (key === "Pts." || key === "pts" || key === "Pts") indices.push(index);
    return indices;
  }, []);
  // Determine which Rtg and pts belong to which player based on position relative to player names
  let whiteRtgIndex, blackRtgIndex, whitePtsIndex, blackPtsIndex;

  // For Rtg, find the closest one before and after the player names
  if (rtgIndices.length >= 2) {
    // Find Rtg indices that are closest to White and Black
    const rtgBeforeBlack = rtgIndices.filter((idx) => idx < blackIndex);
    const rtgAfterWhite = rtgIndices.filter((idx) => idx > whiteIndex);

    whiteRtgIndex = rtgAfterWhite.length > 0 ? rtgAfterWhite[0] : rtgIndices[0];
    blackRtgIndex =
      rtgBeforeBlack.length > 0
        ? rtgBeforeBlack[rtgBeforeBlack.length - 1]
        : rtgIndices[1];

    // If the above logic doesn't work, use position-based assignment
    if (whiteRtgIndex === blackRtgIndex) {
      whiteRtgIndex = rtgIndices[0];
      blackRtgIndex = rtgIndices[1];
    }
  } else if (rtgIndices.length === 1) {
    // If only one Rtg, use it for both (not ideal)
    whiteRtgIndex = blackRtgIndex = rtgIndices[0];
  }

  // For pts, find the closest one before and after the result
  if (ptsIndices.length >= 2) {
    // Find pts indices that are before and after the result
    const ptsBeforeResult = ptsIndices.filter((idx) => idx < resultIndex);
    const ptsAfterResult = ptsIndices.filter((idx) => idx > resultIndex);

    whitePtsIndex =
      ptsBeforeResult.length > 0
        ? ptsBeforeResult[ptsBeforeResult.length - 1]
        : ptsIndices[0];
    blackPtsIndex =
      ptsAfterResult.length > 0 ? ptsAfterResult[0] : ptsIndices[1];

    // If the above logic doesn't work, use position-based assignment
    if (whitePtsIndex === blackPtsIndex) {
      whitePtsIndex = ptsIndices[0];
      blackPtsIndex = ptsIndices[1];
    }
  } else if (ptsIndices.length === 1) {
    // If only one pts, use it for both (not ideal)
    whitePtsIndex = blackPtsIndex = ptsIndices[0];
  }
  // Now map the data
  return data
    .map((row, index) => {
      // Handle special case for chessResultsList.xlsx where player names might have titles
      let whitePlayerName = row[keys[whiteIndex]] || "";
      let blackPlayerName = row[keys[blackIndex]] || "";

      // Check for titles in adjacent columns (like ACM, AIM, etc.)
      const whiteNameIndex = whiteIndex;
      const blackNameIndex = blackIndex;

      // Check if there's a column before White that might contain a title
      if (whiteNameIndex > 0) {
        const prevKey = keys[whiteNameIndex - 1];
        if (
          row[prevKey] &&
          typeof row[prevKey] === "string" &&
          row[prevKey].length <= 5
        ) {
          // This might be a title, prepend it to the name
          whitePlayerName = `${row[prevKey]} ${whitePlayerName}`.trim();
        }
      }

      // Check if there's a column before Black that might contain a title
      if (blackNameIndex > 0) {
        const prevKey = keys[blackNameIndex - 1];
        if (
          row[prevKey] &&
          typeof row[prevKey] === "string" &&
          row[prevKey].length <= 5
        ) {
          // This might be a title, prepend it to the name
          blackPlayerName = `${row[prevKey]} ${blackPlayerName}`.trim();
        }
      }

      // Create the mapped data object
      const mappedData = {
        tournament_id,
        round_id,
         age_category ,
    gender_category,
        board_no: parseInt(row[keys[boIndex]]) || index + 1,
        white_player_name: whitePlayerName,
        black_player_name: blackPlayerName,
        white_player_fide_rating:
          whiteRtgIndex !== undefined
            ? parseInt(row[keys[whiteRtgIndex]]) || null
            : null,
        black_player_fide_rating:
          blackRtgIndex !== undefined
            ? parseInt(row[keys[blackRtgIndex]]) || null
            : null,
        white_player_points:
          whitePtsIndex !== undefined
            ? parseFloat(row[keys[whitePtsIndex]]) || 0
            : 0,
        black_player_points:
          blackPtsIndex !== undefined
            ? parseFloat(row[keys[blackPtsIndex]]) || 0
            : 0,
        result: row[keys[resultIndex]] || "0-0", // Store the raw result string
      };

      // Validate required fields
      if (!mappedData.white_player_name || !mappedData.black_player_name) {
        console.warn(`Skipping row ${index + 1} due to missing player names`);
        return null;
      }

      return mappedData;
    })
    .filter((item) => item !== null); // Remove null entries
}

/**
 * Parse the result string from the Excel file
 *
 * @param {string} resultStr - Result string from Excel
 * @returns {string} - Normalized result ('white', 'black', or 'draw')
 */
function parseResult(resultStr) {
  if (!resultStr) return "draw";

  const result = String(resultStr).toLowerCase().trim();

  // Common result formats
  if (
    result === "1 - 0" ||
    result === "1:0" ||
    result === "white" ||
    result === "w"
  ) {
    return "white";
  } else if (
    result === "0 - 1" ||
    result === "0:1" ||
    result === "black" ||
    result === "b"
  ) {
    return "black";
  } else if (
    result === "½ - ½" ||
    result === "1/2-1/2" ||
    result === "0.5-0.5" ||
    result === "draw" ||
    result === "d" ||
    result === "=" ||
    result === "½:½" ||
    result === "0.5:0.5"
  ) {
    return "draw";
  }

  // If we can't determine the result, default to draw
  return "draw";
}

/**
 * Map data from the Chess Results List format to the Pairing model
 *
 * This function handles the specific format found in chessResultsList.xlsx:
 * Bo., White, Rtg, Club/City, Pts., Result, Pts., Black, Rtg, Club/City
 *
 * @param {Array} data - Extracted data from Excel
 * @param {Object} options - Options for mapping
 * @returns {Array} - Array of objects mapped to the Pairing model
 */
function mapChessResultsListFormat(data, options = {}) {
  const { tournament_id, round_id = 1,  age_category = "Default",
    gender_category = "Default",} = options;

  // Get the keys to find the indices
  const keys = Object.keys(data[0]);

  // Find the indices of all relevant columns
  const boIndex = keys.findIndex((key) => key === "Bo.");
  const whiteIndex = keys.findIndex((key) => key === "White");
  const blackIndex = keys.findIndex((key) => key === "Black");
  const resultIndex = keys.findIndex(
    (key) => key === "Result " || key === "Result" || key.includes("result")
  );

  // For the chessResultsList.xlsx format, we need to handle the specific structure
  // The file has columns: Bo., Blank_1, White, Rtg, Club/City, Pts., Result, Pts., Black, Rtg, Club/City
  // We need to find the Rtg and Pts. columns for both White and Black

  // For the Chess Results List format, we know the exact structure:
  // - First Rtg is for White (after White column)
  // - Second Rtg is for Black (after Black column)
  // - First Pts. is for White (after first Club/City)
  // - Second Pts. is for Black (after Result)

  // Find all columns with Rtg header
  const rtgIndices = [];
  keys.forEach((key, index) => {
    if (key === "Rtg") {
      rtgIndices.push(index);
    }
  });

  // Find all columns with Pts. header
  const ptsIndices = [];
  keys.forEach((key, index) => {
    if (key === "Pts." || key === "pts" || key === "Pts") {
      ptsIndices.push(index);
    }
  });

  // For Chess Results List format, we need to handle the specific structure
  // Even if we don't find two Rtg columns in the headers, we know they exist in the data

  // For this specific format, we know:
  // - White player column is at index 2
  // - Black player column is at index 8
  // - First Rtg column is at index 3 (right after White)
  // - Second Rtg column is at index 10 (right after Black)
  // - First Pts. column is at index 5
  // - Second Pts. column is at index 7

  // Hardcode the indices for the Chess Results List format
  // These are the correct indices for the chessResultsList.xlsx file
  let whiteRtgIndex = 3; // Column D
  let blackRtgIndex = 10; // Column K
  let whitePtsIndex = 5; // Column F
  let blackPtsIndex = 7; // Column H
  if (rtgIndices.length >= 2) {
    // If we have at least 2 Rtg columns, use them for White and Black
    whiteRtgIndex = rtgIndices[0];
    blackRtgIndex = rtgIndices[1];
  } else if (rtgIndices.length === 1) {
    // If we only have 1 Rtg column, check its position relative to White and Black
    const rtgIndex = rtgIndices[0];
    if (rtgIndex > whiteIndex && rtgIndex < blackIndex) {
      // If Rtg is between White and Black, it's for White
      whiteRtgIndex = rtgIndex;
      // Look for a numeric value after Black for Black's rating
      for (let i = 0; i < data.length; i++) {
        const row = data[i];
        for (let j = blackIndex + 1; j < keys.length; j++) {
          const value = row[keys[j]];
          if (value && !isNaN(parseInt(value))) {
            blackRtgIndex = j;
            break;
          }
        }
        if (blackRtgIndex) break;
      }
    } else {
      // Otherwise, use it for both (not ideal)
      whiteRtgIndex = blackRtgIndex = rtgIndex;
    }
  }

  if (ptsIndices.length >= 2) {
    // If we have at least 2 Pts. columns, use them for White and Black
    whitePtsIndex = ptsIndices[0];
    blackPtsIndex = ptsIndices[1];
  } else if (ptsIndices.length === 1) {
    // If we only have 1 Pts. column, check its position relative to White and Black
    const ptsIndex = ptsIndices[0];
    if (ptsIndex > whiteIndex && ptsIndex < blackIndex) {
      // If Pts. is between White and Black, it's for White
      whitePtsIndex = ptsIndex;
      // Look for a numeric value after Black for Black's points
      for (let i = 0; i < data.length; i++) {
        const row = data[i];
        for (let j = blackIndex + 1; j < keys.length; j++) {
          const value = row[keys[j]];
          if (value && !isNaN(parseFloat(value))) {
            blackPtsIndex = j;
            break;
          }
        }
        if (blackPtsIndex) break;
      }
    } else {
      // Otherwise, use it for both (not ideal)
      whitePtsIndex = blackPtsIndex = ptsIndex;
    }
  }
  return data
    .map((row, index) => {
      // Handle special case for player names with titles
      let whitePlayerName = row[keys[whiteIndex]] || "";
      let blackPlayerName = row[keys[blackIndex]] || "";

      // Check for titles in adjacent columns (like ACM, AIM, etc.)
      if (whiteIndex > 0) {
        const prevKey = keys[whiteIndex - 1];
        if (
          row[prevKey] &&
          typeof row[prevKey] === "string" &&
          row[prevKey].length <= 5
        ) {
          // This might be a title, prepend it to the name
          whitePlayerName = `${row[prevKey]} ${whitePlayerName}`.trim();
        }
      }

      if (blackIndex > 0) {
        const prevKey = keys[blackIndex - 1];
        if (
          row[prevKey] &&
          typeof row[prevKey] === "string" &&
          row[prevKey].length <= 5
        ) {
          // This might be a title, prepend it to the name
          blackPlayerName = `${row[prevKey]} ${blackPlayerName}`.trim();
        }
      }

      // Parse the result string - keep the original format (1-0, 0-1, 0-0)
      let resultStr = row[keys[resultIndex]] || "0-0";

      // Clean up the result string (remove extra spaces)
      if (typeof resultStr === "string") {
        resultStr = resultStr.trim();
      } else {
        // If it's not a string, convert it to a string
        resultStr = String(resultStr || "0-0");
      }

      // Convert the result string to the expected enum values ('white', 'black', 'draw')
      let resultEnum;
      if (
        resultStr.includes("1 - 0") ||
        resultStr.includes("1-0") ||
        resultStr.includes("+ - -")
      ) {
        resultEnum = "white";
      } else if (
        resultStr.includes("0 - 1") ||
        resultStr.includes("0-1") ||
        resultStr.includes("- - +")
      ) {
        resultEnum = "black";
      } else if (
        resultStr.includes("½ - ½") ||
        resultStr.includes("1/2-1/2") ||
        resultStr.includes("0 - 0") ||
        resultStr.includes("0-0")
      ) {
        resultEnum = "draw";
      } else {
        // Default to draw if we can't determine the result
        resultEnum = "draw";
      }

      // For the Chess Results List format, we need to directly access the data by column index
      // The Excel file has a specific structure:
      // Column A (0): Bo. (board number)
      // Column B (1): Blank_1 (sometimes contains player titles)
      // Column C (2): White (white player name)
      // Column D (3): Rtg (white player rating)
      // Column E (4): Club/City (white player club/city)
      // Column F (5): Pts. (white player points)
      // Column G (6): Result (game result)
      // Column H (7): Pts. (black player points)
      // Column I (8): Blank_2 (sometimes contains player titles)
      // Column J (9): Black (black player name)
      // Column K (10): Rtg (black player rating)
      // Column L (11): Club/City (black player club/city)

      // For Chess Results List format, we need to directly access the data by column letter
      // Based on the Excel file structure:
      // A: Bo. (Board number)
      // B: Player title for white (ACM, AIM, etc.)
      // C: White (White player name)
      // D: Rtg (White player rating)
      // E: Club/City (White player club/city)
      // F: Pts. (White player points)
      // G: Result
      // H: Pts. (Black player points)
      // I: Player title for black (ACM, AIM, etc.) - might be missing
      // J: Black (Black player name)
      // K: Rtg (Black player rating)
      // L: Club/City (Black player club/city)

      // Get board number (column A)
      const boardNo = row["A"] || row["Bo."] || "";

      // Get white player info
      const whiteTitle = row["B"] || ""; // Column B (player title)
      const whiteName = row["C"] || row["White"] || ""; // Column C

      // Get white player rating (column D)
      const whiteRating = row["D"] || null;
      const whiteRtgIndex = 3; // Column D

      // Get white player points (column F)
      const whitePoints = row["F"] || 0;
      const whitePtsIndex = 5; // Column F

      // Get black player info
      const blackName = row["J"] || row["Black"] || ""; // Column J

      // Get black player rating (column K)
      const blackRating = row["K"] || null;
      const blackRtgIndex = 10; // Column K

      // Get black player points (column H)
      const blackPoints = row["H"] || 0;
      const blackPtsIndex = 7; // Column H

      // Get black player title (column I)
      const blackTitle = row["I"] || "";
      // Add title to player names if available
      let fullWhiteName = whiteName || "";
      if (
        whiteTitle &&
        typeof whiteTitle === "string" &&
        whiteTitle.trim() !== ""
      ) {
        fullWhiteName = `${whiteTitle} ${fullWhiteName}`.trim();
      }

      let fullBlackName = blackName || "";
      if (
        blackTitle &&
        typeof blackTitle === "string" &&
        blackTitle.trim() !== ""
      ) {
        fullBlackName = `${blackTitle} ${fullBlackName}`.trim();
      }

      // Log the indices for debugging
      // Create the mapped data object
      const mappedData = {
        tournament_id,
        round_id,
          age_category,
      gender_category,
        board_no: parseInt(boardNo) || index + 1,
        white_player_name: fullWhiteName || whitePlayerName,
        black_player_name: blackName || blackPlayerName, // Use blackName directly instead of fullBlackName
        white_player_fide_rating: whiteRating ? parseInt(whiteRating) : null,
        black_player_fide_rating: blackRating ? parseInt(blackRating) : null,
        white_player_points: whitePoints ? parseFloat(whitePoints) : 0,
        black_player_points: blackPoints ? parseFloat(blackPoints) : 0,
        result: resultEnum, // Store the enum value ('white', 'black', 'draw')
      };

      // Log the raw data for debugging
      // Validate required fields
      if (!mappedData.white_player_name || !mappedData.black_player_name) {
        console.warn(`Skipping row ${index + 1} due to missing player names`);
        return null;
      }

      return mappedData;
    })
    .filter((item) => item !== null); // Remove null entries
}

/**
 * Extracts data from a table in a worksheet specifically for pairing data
 *
 * This function is similar to extractTableData but with special handling for pairing data:
 * 1. Renames duplicate columns like "Rtg" to "white_Rtg" and "black_Rtg"
 * 2. Renames duplicate columns like "pts" to "white_pts" and "black_pts"
 * 3. Handles other pairing-specific data extraction needs
 *
 * @param {Object} worksheet - XLSX worksheet object
 * @param {Object} tableInfo - Table boundaries and headers
 * @returns {Array} - Array of JSON objects
 */
function extractPairingTableData(worksheet, tableInfo) {
  const result = [];
  const range = XLSX.utils.decode_range(worksheet["!ref"]);

  // Create a map of column indices to header names
  const headerMap = {};

  // Track columns with data but no header
  const columnsWithData = new Set();
  let blankColumnCount = 0;

  // Store column letters for direct access
  const columnLetters = {};

  // First, identify all columns that have data in the first few data rows
  const sampleRows = Math.min(5, tableInfo.end - tableInfo.start);
  for (
    let r = tableInfo.start + 1;
    r <= Math.min(tableInfo.start + sampleRows, tableInfo.end);
    r++
  ) {
    for (let c = range.s.c; c <= range.e.c; c++) {
      const cellAddress = XLSX.utils.encode_cell({ r, c });
      const cell = worksheet[cellAddress];

      if (cell && cell.v !== undefined && cell.v !== null && cell.v !== "") {
        columnsWithData.add(c);
      }
    }
  }

  // Track duplicate header names for special handling
  const headerCounts = {};

  // First pass: count occurrences of each header
  for (const header of tableInfo.headers) {
    let headerName = String(header.value).trim();

    // If the header is a number or empty, give it a generic name
    if (headerName === "" || !isNaN(headerName)) {
      headerName = `Column_${header.col}`;
    }

    // Count occurrences of each header name
    headerCounts[headerName] = (headerCounts[headerName] || 0) + 1;
  }

  // Second pass: add headers with special handling for duplicates
  for (const header of tableInfo.headers) {
    // Use the header value as the key, ensuring it's a string
    let headerName = String(header.value).trim();

    // If the header is a number or empty, give it a generic name
    if (headerName === "" || !isNaN(headerName)) {
      headerName = `Column_${header.col}`;
    }

    // Special handling for duplicate headers in pairing data
    if (headerCounts[headerName] > 1) {
      // Find the position of this header relative to other headers with the same name
      const position = tableInfo.headers
        .filter((h) => String(h.value).trim() === headerName)
        .findIndex((h) => h.col === header.col);

      // For "Rtg" columns
      if (headerName === "Rtg") {
        headerName = position === 0 ? "white_Rtg" : "black_Rtg";
      }
      // For "pts" or "Pts" or "Pts." columns
      else if (
        headerName === "pts" ||
        headerName === "Pts" ||
        headerName === "Pts."
      ) {
        headerName = position === 0 ? "white_pts" : "black_pts";
      }
      // For other duplicate columns
      else {
        headerName = `${headerName}_${position + 1}`;
      }
    }

    headerMap[header.col] = headerName;
    // Store the column letter for this column
    columnLetters[headerName] = XLSX.utils.encode_col(header.col);
    columnsWithData.delete(header.col); // Remove from columnsWithData since it has a header
  }

  // Add columns with data but no header
  for (const col of columnsWithData) {
    blankColumnCount++;
    const headerName = `Blank_${blankColumnCount}`;
    headerMap[col] = headerName;
    // Store the column letter for this column
    columnLetters[headerName] = XLSX.utils.encode_col(col);
  }

  // Extract data rows
  for (let r = tableInfo.start + 1; r <= tableInfo.end; r++) {
    const rowData = {};
    let hasData = false;

    for (const col in headerMap) {
      const cellAddress = XLSX.utils.encode_cell({ r, c: parseInt(col) });
      const cell = worksheet[cellAddress];

      if (cell && cell.v !== undefined && cell.v !== null) {
        // Convert cell value to appropriate type
        let value = cell.v;

        // Format dates if the cell contains a date
        if (cell.t === "d") {
          value = cell.w || value.toISOString();
        }

        // Format numbers if needed
        if (cell.t === "n") {
          // Keep as number
          value = Number(value);
        }

        // Format booleans
        if (cell.t === "b") {
          value = Boolean(value);
        }

        rowData[headerMap[col]] = value;
        hasData = true;
      } else {
        rowData[headerMap[col]] = null;
      }
    }

    // Only add rows that have some data
    if (hasData) {
      // Add column letters to the row data for direct access
      for (const [key, colLetter] of Object.entries(columnLetters)) {
        rowData[colLetter] = rowData[key];
      }
      result.push(rowData);
    }
  }

  return result;
}

/**
 * Processes an Excel file and extracts table data
 * @param {string} filePath - Path to the Excel file
 * @param {Object} options - Options for processing
 * @returns {Object} - Extraction results
 */
async function processExcelFile(filePath, options = {}) {
  try {
    // Read the Excel file with raw data
    const workbook = XLSX.readFile(filePath, { raw: true });
    const sheetName = workbook.SheetNames[0]; // Process the first sheet
    const worksheet = workbook.Sheets[sheetName];

    // Detect the table
    const tableInfo = detectTable(worksheet);

    if (!tableInfo.found) {
      return {
        success: false,
        message: "No table detected in the worksheet",
        filePath,
      };
    }

    // Extract the data - use the appropriate extraction function based on model type
    let data;
    if (options.modelName === "Pairing") {
      data = extractPairingTableData(worksheet, tableInfo);
    } else {
      data = extractTableData(worksheet, tableInfo);
    }

    // Map the data to the model
    const mappedData = mapDataToModel(data, {
      tournamentId: options.tournamentId,
      tournament_id: options.tournament_id,
      round_id: options.round_id,
      age_category: options.age_category,
      gender_category: options.gender_category,

      sourceFile: path.basename(filePath),
      additionalFields: options.additionalFields || {},
      modelName: options.modelName,
    });

    // Insert the data into the database
    if (options.saveToDb) {
      try {
        let result;
        if (options.modelName === "Ranking") {
          result = await models.Ranking.bulkCreate(mappedData);
        } else if (options.modelName === "Pairing") {
          result = await models.Pairing.bulkCreate(mappedData);
        } else {
          throw new Error(`Unsupported model: ${options.modelName}`);
        }
      } catch (error) {
        console.error(`Error inserting data into database: ${error.message}`);
        return {
          success: false,
          message: `Error inserting data into database: ${error.message}`,
          filePath,
        };
      }
    }

    return {
      success: true,
      data: mappedData,
      tableInfo,
      filePath,
      sheetName,
    };
  } catch (error) {
    return {
      success: false,
      message: `Error processing file: ${error.message}`,
      filePath,
    };
  }
}

/**
 * Analyzes a worksheet and prints its structure
 * @param {string} filePath - Path to the Excel file
 * @returns {boolean} - Whether the analysis was successful
 */
function analyzeWorksheet(filePath) {
  try {
    // Read the Excel file
    const workbook = XLSX.readFile(filePath);
    const sheetName = workbook.SheetNames[0]; // Analyze the first sheet
    const worksheet = workbook.Sheets[sheetName];
    const range = XLSX.utils.decode_range(worksheet["!ref"]);
    // Print the first few rows to understand the structure
    for (let r = range.s.r; r <= Math.min(range.s.r + 9, range.e.r); r++) {
      let rowData = [];
      for (let c = range.s.c; c <= range.e.c; c++) {
        const cellAddress = XLSX.utils.encode_cell({ r, c });
        const cell = worksheet[cellAddress];
        if (cell && cell.v !== undefined) {
          rowData.push(`${XLSX.utils.encode_col(c)}${r + 1}:${cell.v}`);
        }
      }
    }

    return true;
  } catch (error) {
    console.error(`Error analyzing file: ${error.message}`);
    return false;
  }
}

// Main execution
async function main() {
  try {
    // Process a single file if provided as an argument
    if (process.argv.length > 2) {
      const filePath = process.argv[2];
      const tournament_id = process.argv.length > 3 ? process.argv[3] : null;
      const round_id = process.argv.length > 4 ? parseInt(process.argv[4]) : 1;
      const age_category = process.argv.length > 5 ? process.argv[5] : null;
      const gender_category = process.argv.length > 6 ? process.argv[6] : null;
      const modelName = process.argv.length > 6 ? process.argv[6] : "Ranking";

      if (fs.existsSync(filePath)) {
        // First analyze the file
        analyzeWorksheet(filePath);

        // Then process it
        const result = await processExcelFile(filePath, {
          tournament_id,
          round_id,
            age_category,
      gender_category,
          modelName,
          saveToDb: true,
        });
      } else {
        console.error(`File not found: ${filePath}`);
      }
    } else {
    }
  } catch (error) {
    console.error(`An error occurred: ${error.message}`);
  }
}

// Run the main function
if (require.main === module) {
  main().catch(console.error);
}

// Export functions for testing and reuse
module.exports = {
  detectTable,
  extractTableData,
  extractPairingTableData,
  mapDataToModel,
  mapToRankingModel,
  mapToPairingModel,
  processExcelFile,
  analyzeWorksheet,
};


function parsePointValue(value) {
  if (typeof value === 'number') {
    return value;
  }
  
  const str = String(value).trim();
  
  // Handle empty or invalid values
  if (!str || str === '-' || str === 'bye') {
    return 0;
  }
  
  // Handle half point notation (½)
  if (str.includes('½')) {
    const parts = str.split('½');
    const wholePoints = parts[0] ? parseInt(parts[0]) : 0;
    return wholePoints + 0.5;
  }
  
  // Handle decimal notation
  const floatValue = parseFloat(str);
  if (!isNaN(floatValue)) {
    return floatValue;
  }
  
  // Handle fraction notation (1/2, 3/2, etc.)
  if (str.includes('/')) {
    const [numerator, denominator] = str.split('/').map(n => parseInt(n.trim()));
    if (!isNaN(numerator) && !isNaN(denominator) && denominator !== 0) {
      return numerator / denominator;
    }
  }
  
  // Default to 0 if unable to parse
  return 0;
}