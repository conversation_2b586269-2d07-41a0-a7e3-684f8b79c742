# Ignore node_modules and dependencies
node_modules
npm-debug.log
yarn.lock
pnpm-lock.yaml

# Ignore build and output directories
.next
out
dist
build

# Ignore environment files (sensitive data)
.env
.env.local
.env.development
.env.production

# Ignore logs and temporary files
logs
*.log
*.gz
.DS_Store
Thumbs.db

# Ignore Git and Docker files
.git
.gitignore
.dockerignore

# Ignore editor and OS files
.vscode
.idea
*.swp
