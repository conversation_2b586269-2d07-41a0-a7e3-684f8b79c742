const z = require("zod");
const bankDetailsSchema = z.object({
  bankName: z
    .string()
    .min(2, "Bank Name is required")
    .max(100, "Bank Name is too long"),
  AccountNumber: z
    .string()
    .min(9, "Account Number is required")
    .max(50, "Account Number is too long"),
  branchIFSCCode: z
    .string()
    .regex(/^[A-Z]{4}0[A-Z0-9]{6}$/, "Invalid IFSC code"),
  branchName: z
    .string()
    .min(2, "Branch Name is required")
    .max(100, "Branch Name is too long"),
  bankAccountType: z
    .string()
    .min(2, "Bank Account Type is required")
    .max(50, "Bank Account Type is too long"),
  bankAccountHolderName: z
    .string()
    .min(2, "Bank Account Holder Name is required")
    .max(100, "Bank Account Holder Name is too long"),
});

module.exports = { bankDetailsSchema };
