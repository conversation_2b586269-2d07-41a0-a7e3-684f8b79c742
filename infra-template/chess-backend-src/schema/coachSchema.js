const z = require("zod");

const coachDetailSchema = z.object({
  title: z.string().max(50).optional().default("Untitled"),
  profileUrl: z.string().url().optional(),
  dob: z.string().date("Invalid date format").or(z.date()),
  about: z.string().max(250).optional(),
  stateId: z.string().max(20).optional(),
  districtId: z.string().max(20).optional(),
  country: z.string().max(50).optional(),
  state: z.string().max(50).optional(),
  district: z.string().max(50).optional(),
  city: z.string().max(50).optional(),
  pincode: z.string().max(10).optional(),
  profileLinks: z.object({
  fide: z.string().url("Invalid FIDE URL").optional(),
  li_chess: z.string().url("Invalid Lichess URL").optional(),
  chess: z.string().url("Invalid Chess.com URL").optional(),
    })
    .optional(),
});

const coachDetailUpdateSchema = z.object({
  phoneChanged: z.coerce.boolean().optional().default(false),
  otp: z.string().length(6).optional(),
  phoneNumber: z
    .string()
    .regex(/^(0\d{9,14}|\+?[1-9]\d{9,14})$/, {
      message: "Phone number must be 10 digits",
    })
    .optional(),
  title: z.string().max(50).optional().default("Untitled"),
  profileUrl: z.string().url().optional(),
  dob: z.string().date("Invalid date format").or(z.date()).optional(),
  about: z.string().max(250).optional(),
  stateId: z.string().max(20).optional(),
  districtId: z.string().max(20).optional(),
  country: z.string().max(50).optional(),
  state: z.string().max(50).optional(),
  district: z.string().max(50).optional(),
  city: z.string().max(50).optional(),
  pincode: z.string().max(10).optional(),
  profileLinks: z.object({
  fide: z.string().url("Invalid FIDE URL").optional(),
  li_chess: z.string().url("Invalid Lichess URL").optional(),
  chess: z.string().url("Invalid Chess.com URL").optional(),
    })
    .optional(),
});


module.exports = {coachDetailSchema,coachDetailUpdateSchema };
