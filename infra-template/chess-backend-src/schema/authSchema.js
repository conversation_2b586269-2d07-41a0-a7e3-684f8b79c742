const z = require("zod");
const loginSchema = z.object({
  emailOrMobile: z.union([
    z.string().email({ message: "Invalid email format" }),
    z.string().regex(/^(0\d{9,14}|\+?[1-9]\d{9,14})$/, {
      message: "Invalid mobile number format",
    }),
  ]),
  rememberMe: z.coerce.boolean().optional(),
  password: z
    .string()
    .min(6, { message: "Password must be at least 6 characters long" }),
});

const registerSchema = z.object({
  firstName: z
    .string()
    .min(3, { message: "Name must be at least 3 characters long" }),
  lastName: z.string().optional(),
  referral: z.string().optional(),
  phoneNumber: z.string().regex(/^(0\d{9,14}|\+?[1-9]\d{9,14})$/, {
    message: "Invalid phone number format",
  }),
  otp: z
    .string()
    .min(4, { message: "OTP is required" })
    .max(6, { message: "OTP must not exceed 6 digits" }),
  email: z.string().email({ message: "Invalid email format" }),
  password: z
    .string()
    .min(6, { message: "Password must be at least 6 characters long" }),
  role: z.enum(["arbiter", "club", "player","coach"]).default("player"),
});

const resetPasswordSchema = z.object({
  email: z.string().email({ message: "Invalid email format" }),
  otp: z
    .string()
    .min(4, { message: "OTP is required" })
    .max(6, { message: "OTP must not exceed 6 digits" }),
  newPassword: z
    .string()
    .min(6, { message: "Password must be at least 6 characters long" }),
});

module.exports = {
  loginSchema,
  registerSchema,
  resetPasswordSchema,
};
