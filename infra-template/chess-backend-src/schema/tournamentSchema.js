const z = require("zod");

const phoneRegex = /^(0\d{9,14}|\+?[1-9]\d{9,14})$/;

const createTournamentSchema = z.object({
  // Tournament Details
  title: z.string().min(1, "Tournament title is required"),
  subTitle: z.string().optional(),
  presentedBy: z.string().optional(),
  fideRated: z.preprocess((val) => val === "true", z.boolean()),
  organizerName: z.string().min(1, "Organizer name is required"),
  tournamentLevel: z.enum(["state", "national", "district", "global"], {
    errorMap: () => ({ message: "Please select a tournament level" }),
  }),

  // Tournament Dates
  startDate: z
    .string()
    .regex(/^\d{4}-\d{2}-\d{2}$/, "Invalid date format (YYYY-MM-DD)"),
  endDate: z
    .string()
    .regex(/^\d{4}-\d{2}-\d{2}$/, "Invalid date format (YYYY-MM-DD)"),
  reportingTime: z
    .string()
    .regex(/^(0[1-9]|1[0-2]):[0-5][0-9]\s?(AM|PM)$/i, "pick a time")
    .nullish(), // Or .optional() if required

  // Age Categories
  tournamentCategory: z.enum(["open", "male", "female","children","children-open"]).default("open"),
  maleAgeCategory: z
    .array(z.string())
    .min(1, "At least one male age category is required")
    .optional(),
  femaleAgeCategory: z
    .array(z.string())
    .min(1, "At least one female age category is required")
    .optional(),

  // Registration
  registrationStartDate: z
    .string()
    .regex(/^\d{4}-\d{2}-\d{2}$/, "Invalid date format (YYYY-MM-DD)"),
  registrationEndDate: z
    .string()
    .regex(/^\d{4}-\d{2}-\d{2}$/, "Invalid date format (YYYY-MM-DD)"),
  registrationEndTime: z
    .string()
    .regex(/^(0[1-9]|1[0-2]):[0-5][0-9]\s?(AM|PM)$/i, "pick a time")
    .nullish(),

  tournamentDirectorName: z
    .string()
    .optional(),
  entryFeeCurrency: z.enum(["INR", "USD", "EUR"]).default("INR"),
  entryFee: z.coerce.number().min(1, "Registration fee is required"),
  timeControl: z.enum(["classical", "rapid", "bullet", "blitz"], {
    errorMap: () => ({ message: "Please select a valid time control" }),
  }),
  timeControlDuration: z.coerce.string().min(1, "Duration must be given"),
  timeControlIncrement: z.coerce.string().optional(),
  tournamentType: z
    .enum(["individual", "team"], {
      errorMap: () => ({ message: "Please select a tournament type" }),
    })
    .default("individual"),
  tournamentSystem: z
    .enum(["swiss-system", "round-robin", "knockout"])
    .default("swiss-system"),
  nationalApproval: z.string().optional(),
  stateApproval: z.string().optional(),
  districtApproval: z.string().optional(),
  fideApproval: z.string().optional(),
  contactPersonName: z.string().min(1, "Contact person name is required"),
 email: z
  .string()
  .optional()
  .refine(
    (val) => !val || val === '' || /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(val),
    {
      message: "Enter a proper email address",
    }
  ),
  contactNumber: z
    .string({ errorMap: () => ({ message: "Contact number is required" }) })
    .regex(phoneRegex, "Invalid phone number"),

  alternateContactNumber: z
    .string({
      errorMap: () => ({ message: "Alternate contact number is required" }),
    })
    .max(10, "Invalid phone number").optional(),
  // numberOfTrophiesMale: z.coerce
  //   .number({
  //     errorMap: () => ({ message: "Number of trophies is required" }),
  //   })
  //   .int()
  //   .min(1, "Number of trophies for male must be above zero")
  //   .max(1000),
  // numberOfTrophiesFemale: z.coerce
  //   .number({
  //     errorMap: () => ({ message: "Number of trophies is required" }),
  //   })
  //   .int()
  //   .min(1, "Number of trophies for female must be above zero")
  //   .max(1000),
  totalCashPrizeCurrency: z.enum(["INR", "USD", "EUR"]).default("INR"),
  totalCashPrizeAmount: z.coerce
    .number()
   // .min(1, "Total cash prize amount must be at least 1")
    .max(1000000)
    .optional(),
  country: z
    .string()
    .min(1, "Country is required")
    .regex(/^[\p{L}\s'()-]+$/u, "Country name should only contain letters"),
  state: z
    .string()
    .min(2, "State name must be at least 2 characters")
    .regex(/^[\p{L}\s'()-]+$/u, "State name should only contain letters"),
  district: z
    .string()
    .min(2, "District name must be at least 2 characters")
    .regex(/^[\p{L}\s'()-]+$/u, "District name should only contain letters"),
  city: z
    .string()
    .min(2, "City name must be at least 2 characters")
    .regex(/^[\p{L}\s'()-]+$/u, "City name should only contain letters"),
  pincode: z.string().max(6, "Pincode must be exactly 6 digits").optional(),
  venueAddress: z
    .string()
    .max(200, "Venue address cannot exceed 200 characters"),
  nearestLandmark: z
    .string()
    .max(100, "Landmark cannot exceed 100 characters")
    .optional(),
  // brochureUrl: z.string().url("Invalid brochure URL format").nullish(),
  //locationUrl: z.string().url("Invalid location URL format").nullish(),
  chatUrl: z.string().nullish(),
  chessboardProvided: z.preprocess((val) => val === "true", z.boolean()),
  timerProvided: z.preprocess((val) => val === "true", z.boolean()),
  spotEntry: z.preprocess((val) => val === "true", z.boolean()),
  parkingFacility: z.enum(["yes", "no", "limited"]).default("no"),
  foodFacility: z
    .array(
      z.enum(["breakfast", "lunch", "dinner", "snacks", "beverages", "nil"])
    )
    .default(["nil"]),
});
const updateTournamentSchema = createTournamentSchema.partial();
const getAllTournamentSchema = z.object({
  page: z.coerce.number().int().positive().default(1),
  limit: z.coerce.number().int().positive().default(10),
  title: z.string().optional(),
  city: z.string().optional(),
  age: z.string().optional(),
  tournamentType: z.enum(["rated", "unrated"]).optional(),
  tournamentCategory: z.enum(["open", "male", "female"]).optional(),
  country: z.string().optional(),
  state: z.string().optional(),
  district: z.string().optional(),
  month: z.string().optional(),
  year: z.string().optional(),
});

module.exports = {
  createTournamentSchema,
  updateTournamentSchema,
  getAllTournamentSchema,
};
