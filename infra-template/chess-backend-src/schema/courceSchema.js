const z = require("zod");

const courseSchema = z.object({
  title: z.string().min(1, "Title is required"),
  description: z.string().min(1, "Description is required"),
  startDate: z.string().min(1, "Start date is required"), // You can change to z.coerce.date() if using Date object
  endDate: z.string().min(1, "End date is required"),
  registrationStartDate: z.string().min(1, "Registration start date is required"),
  registrationEndDate: z.string().min(1, "Registration end date is required"),
  courseBanner: z.any().nullable(),
  courseDuration: z.string().min(1, "Course duration is required"),
  totalSessions: z.string().min(1, "Total sessions is required"),
  sessionsPerWeek: z.string().min(1, "Sessions per week is required"),
  courseFee: z.string().min(1, "Course fee is required"),
  paymentStructure: z.string().min(1, "Payment structure is required"),
  sessionStartTime: z.string(),
  sessionEndTime: z.string().min(1, "Session end time is required"),
  sessionDuration: z
    .string()
    .trim()
    .regex(/^\d*(\.\d{1,2})?$/, {
      message: "Duration must be a valid number with up to 2 decimal places",
    })
    .transform((val) => parseFloat(val))
    .refine((val) => !isNaN(val) && val > 0, {
      message: "Duration must be a number greater than 0",
    }),
  selectedDays:z.array(
        z.string({
          errorMap: () => ({ message: "Please select a valid Days" }),
        })
      ),
});

const EditSchema = z.object({
  title: z.string().min(1, "Title is required"),
  description: z.string().min(1, "Description is required"),
  startDate: z.string().min(1, "Start date is required"), // You can change to z.coerce.date() if using Date object
  endDate: z.string().min(1, "End date is required"),
  registrationStartDate: z.string().min(1, "Registration start date is required"),
  registrationEndDate: z.string().min(1, "Registration end date is required"),
  courseBanner: z.any().nullable(),
  courseDuration: z.string().min(1, "Course duration is required"),
  totalSessions: z.number().min(1, "Total sessions is required"),
  sessionsPerWeek: z.number().min(1, "Sessions per week is required"),
  courseFee: z.string().min(1, "Course fee is required"),
  paymentStructure: z.string().min(1, "Payment structure is required"),
  SessionStartTime: z.string().min(1, "Session start time is required"),
  SessionEndTime: z.string().min(1, "Session end time is required"),
  sessionDuration: z
    .string()
    .trim()
    .regex(/^\d*(\.\d{1,2})?$/, {
      message: "Duration must be a valid number with up to 2 decimal places",
    })
    .transform((val) => parseFloat(val))
    .refine((val) => !isNaN(val) && val > 0, {
      message: "Duration must be a number greater than 0",
    }),
      selectedDays:z.array(
        z.string({
          errorMap: () => ({ message: "Please select a valid age category" }),
        })
      ),
});
module.exports = {courseSchema,EditSchema};