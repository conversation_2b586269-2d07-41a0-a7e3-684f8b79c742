const { Banner } = require("../config/db").models;
const { sendResponse, handleError } = require("../utils/apiResponse");

// Get All Banners with Pagination
const getBannersAndVideo = async (req, res) => {
  try {
    const {type} = req.query; 

    if (!type) {
      return sendResponse(res, 400, {
        success: false,
        message: `Type is Required`,
      });
    }

    const { rows: banners, count } = await Banner.findAndCountAll({where:{type,isActive:true},order: [['rowIndex', 'ASC']]});

    
    let intervalTime = null
    if(type === 'banner'){
     intervalTime = banners.length > 0 ? banners[0].intervalTime : 3000;
    }

    return sendResponse(res, 200, {
      success: true,
      data: banners,
      count,
      intervalTime
    });
  } catch (error) {
    return handleError(res, error);
  }
};


module.exports = {
  getBannersAndVideo,
};
