const fs = require("fs");
const path = require("path");
const { processExcelFile } = require("../scripts/excel-extractor");
const { models } = require("../config/db");
const multer = require("multer");
const { v4: uuidv4 } = require("uuid");
const { Op } = require("sequelize");

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    const uploadDir = path.join(__dirname, "../uploads");
    // Create the uploads directory if it doesn't exist
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: function (req, file, cb) {
    // Generate a unique filename to prevent overwriting
    const uniqueFilename = `${Date.now()}-${uuidv4()}-${file.originalname}`;
    cb(null, uniqueFilename);
  },
});

// Create the multer upload instance
const upload = multer({
  storage: storage,
  fileFilter: function (req, file, cb) {
    // Accept only Excel files
    if (
      file.mimetype ===
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" ||
      file.mimetype === "application/vnd.ms-excel"
    ) {
      cb(null, true);
    } else {
      cb(new Error("Only Excel files are allowed"), false);
    }
  },
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB file size limit
  },
});

// Controller methods
const pairingImportController = {
  /**
   * Upload and process an Excel file
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async uploadAndProcess(req, res) {
    try {
      // The file is available at req.file due to multer middleware
      if (!req.file) {
        return res.status(400).json({
          success: false,
          message: "No file uploaded",
        });
      }

      const filePath = req.file.path;
      const tournament_id = req.body.tournament_id;
      const round_id = req.body.round_id ? parseInt(req.body.round_id) : 1;
      const age_category = req.body.age_category || "open";
      const gender_category = req.body.gender_category || "open";

      if (!tournament_id) {
        return res.status(400).json({
          success: false,
          message: "tournament_id is required",
        });
      }

      // getting tournament id from Tournament table
      const tournament = await models.Tournament.findOne({
        where: { title: tournament_id },
        attributes: ["id", "number_of_rounds"],
      });

      if (!tournament) {
        return res.status(404).json({
          success: false,
          message: "Tournament not found",
        });
      }

      const existingRound = await models.Pairing.findOne({
        where: { round_id: round_id, tournament_id: tournament.id },
      });

      if (existingRound) {
        return res.status(409).json({
          success: false,
          message: "All ready have a record for this round",
        });
      }

      // Process the Excel file
      const result = await processExcelFile(filePath, {
        tournament_id: tournament.id,
        round_id,
        age_category,
        gender_category,
        saveToDb: true,
        modelName: "Pairing",
      });

      // Delete the file after processing (optional)
      if (req.body.deleteAfterProcessing === "true") {
        fs.unlinkSync(filePath);
      }

      return res.status(200).json({
        success: true,
        message: "File processed successfully",
        data: {
          recordsProcessed: result.data ? result.data.length : 0,
          filename: req.file.originalname,
          tournament_id,
          round_id,
          age_category,
          gender_category,
        },
      });
    } catch (error) {
      console.error("Error processing file:", error);
      return res.status(500).json({
        success: false,
        message: `Error processing file: ${error.message}`,
      });
    }
  },

  /**
   * Get all pairings
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async getAll(req, res) {
    try {
      const {
        tournament_id,
        round_id,

        limit = 10,
        page = 1,
      } = req.query;


      // Build query conditions using clean parameter handling
      const where = {};

      // Only add conditions for provided parameters
      if (tournament_id) where.tournament_id = tournament_id;
      if (round_id) where.round_id = Number(round_id) || null;

      // Parse numeric parameters safely with fallbacks
      const parsedLimit = Number(limit) || 100;
      const parsedOffset = Number(offset) || 0;



      // Execute query with error handling
      const { rows, count } = await models.Pairing.findAndCountAll({
        where,
        limit: parsedLimit,
        offset: parsedOffset,
        order: [["board_no", "ASC"]],
      });

      // Return structured response
      return res.status(200).json({
        success: true,
        data: rows,
        count,
        limit: parsedLimit,
        offset: parsedOffset,
      });
    } catch (error) {
      console.error("Error fetching pairings:", error.stack || error);

      // Return appropriate error status and message
      return res.status(500).json({
        success: false,
        message: "Failed to fetch pairings",
      });
    }
  },
  /**
   * Get a single pairing by ID
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async getById(req, res) {
   
    try {
      const { id } = req.params;
      const newTitle = decodeURIComponent(id)
      const { round, page, ageCategory, genderCategory, playerName } =
        req.query;

      if (!id || !round) {
        return res.status(404).json({
          success: false,
          message: "id and round is required",
        });
      }
      const currentPage = parseInt(page) || 1;
      const limit = 5;
      const offset = (currentPage - 1) * limit;
      const tournament = await models.Tournament.findOne({
        where: { title: newTitle },
        attributes: ["id", "number_of_rounds"],
      });
      if (!tournament) {
        return res.status(404).json({
          success: false,
          message: "Tournament not found",
        });
      }

      const where = {
        tournament_id: tournament.id,
        round_id: round,
      };

      if (ageCategory) where.age_category = ageCategory;
      if (genderCategory) where.gender_category = genderCategory;
      if (playerName)
        where[Op.or] = [
          { black_player_name: { [Op.iLike]: `%${playerName}%` } },
          { white_player_name: { [Op.iLike]: `%${playerName}%` } },
        ];

      const { rows: pairing, count } = await models.Pairing.findAndCountAll({
        where,
        limit,
        offset,
      });

      const totalPages = Math.ceil(count / limit);

      if (!pairing) {
        return res.status(404).json({
          success: false,
          message: "Pairing not found",
        });
      }

      return res.status(200).json({
        success: true,

        data: pairing,
        Pagination: {
          currentPage,
          totalPages,
        },
      });
    } catch (error) {
      console.error("Error fetching pairing:", error);
      return res.status(500).json({
        success: false,
        message: `Error fetching pairing: ${error.message}`,
      });
    }
  },

  /**
   * Delete a pairing by ID
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async deleteById(req, res) {
    try {
      const { id } = req.params;

      const pairing = await models.Pairing.findByPk(id);

      if (!pairing) {
        return res.status(404).json({
          success: false,
          message: "Pairing not found",
        });
      }

      await pairing.destroy();

      return res.status(200).json({
        success: true,
        message: "Pairing deleted successfully",
      });
    } catch (error) {
      console.error("Error deleting pairing:", error);
      return res.status(500).json({
        success: false,
        message: `Error deleting pairing: ${error.message}`,
      });
    }
  },

  /**
   * Delete all pairings for a tournament
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async deleteByTournament(req, res) {
    try {
      const { tournament_id } = req.params;

      const result = await models.Pairing.destroy({
        where: { tournament_id },
      });

      return res.status(200).json({
        success: true,
        message: `${result} pairings deleted successfully`,
      });
    } catch (error) {
      console.error("Error deleting pairings:", error);
      return res.status(500).json({
        success: false,
        message: `Error deleting pairings: ${error.message}`,
      });
    }
  },

  /**
   * Delete all pairings for a tournament and round
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async deleteByTournamentAndRound(req, res) {
    try {
      const { tournament_id, round_id } = req.params;

      const result = await models.Pairing.destroy({
        where: {
          tournament_id,
          round_id: parseInt(round_id),
        },
      });

      return res.status(200).json({
        success: true,
        message: `${result} pairings deleted successfully`,
      });
    } catch (error) {
      console.error("Error deleting pairings:", error);
      return res.status(500).json({
        success: false,
        message: `Error deleting pairings: ${error.message}`,
      });
    }
  },

  async currentRound(req, res) {
    try {
      const { id } = req.query;
      const newTitle = decodeURIComponent(id);

      if (!id) {
        return res.status(404).json({
          success: false,
          message: "id is required",
        });
      }
      const tournament = await models.Tournament.findOne({
        where: { title: newTitle },
        attributes: ["id", "number_of_rounds"],
      });
      if (!tournament) {
        return res.status(404).json({
          success: false,
          message: "Tournament not found",
        });
      }

       const result = await models.Pairing.findAll({
        where: { tournament_id: tournament.id },
        attributes: ["round_id"]
      });
          if (!result || result.length === 0) {
        return res.status(200).json({
          success: true,
          currentRound: null,
          message: "No rounds conducted yet",
        });
      }

      const roundIds = result.map(r => r.round_id);
      const currentRound = roundIds.length > 0 ? Math.max(...roundIds) : 0;

      return res.status(200).json({
        success: true,
        currentRound: currentRound,
      });
      
    } catch (error) {
      console.error("Error deleting pairings:", error);
      return res.status(500).json({
        success: false,
        message: `Error deleting pairings: ${error.message}`,
      });
    }
  },

};

// Export the controller and the multer upload middleware
module.exports = {
  controller: pairingImportController,
  uploadMiddleware: upload,
};
