const { verifyWebhookSignature, processWebhookEvent } = require("../utils/razorPay");
const { sendResponse } = require("../utils/apiResponse");
const { Bankdetails, Payout } = require("../config/db").models;

/**
 * Handle RazorPay webhooks for fund account validation and payouts
 */
const handleRazorPayWebhook = async (req, res) => {
  try {
    const webhookBody = JSON.stringify(req.body);
    const signature = req.headers['x-razorpay-signature'];
    const webhookSecret = process.env.RAZORPAY_WEBHOOK_SECRET;

    

    // Verify webhook signature
    if (!signature || !webhookSecret) {
      console.error("❌ Missing webhook signature or secret");
      return sendResponse(res, 400, {
        success: false,
        error: "Missing webhook signature or secret",
      });
    }

    const isValidSignature = verifyWebhookSignature(webhookBody, signature, webhookSecret);
    
    if (!isValidSignature) {
      console.error("❌ Invalid webhook signature");
      return sendResponse(res, 400, {
        success: false,
        error: "Invalid webhook signature",
      });
    }


    // Process the webhook event
    const processedEvent = processWebhookEvent(req.body);
    
    if (!processedEvent.success) {
      console.error("❌ Failed to process webhook event:", processedEvent.error);
      return sendResponse(res, 400, {
        success: false,
        error: "Failed to process webhook event",
      });
    }

    const { event, payload } = req.body;

    // Handle fund account validation webhooks
    if (event.includes('fund_account.validation')) {
      await handleFundAccountValidationWebhook(event, payload);
    }

    // Handle payout webhooks
    if (event.includes('payout')) {
      await handlePayoutWebhook(event, payload);
    }

    return sendResponse(res, 200, {
      success: true,
      message: "Webhook processed successfully",
      event: event,
    });

  } catch (error) {
    console.error("❌ Error processing webhook:", error);
    return sendResponse(res, 500, {
      success: false,
      error: "Internal server error",
    });
  }
};

/**
 * Handle fund account validation webhook events
 */
const handleFundAccountValidationWebhook = async (event, payload) => {
  try {
    const validation = payload.fund_account_validation?.entity;
    
    if (!validation) {
      
      return;
    }

    console.log("🏦 Processing fund account validation webhook:", {
      event,
      validation_id: validation.id,
      status: validation.status,
      fund_account_id: validation.fund_account?.id
    });

    // Find bank details by fund account ID or validation ID
    const bankDetails = await Bankdetails.findOne({
      where: {
        metadata: {
          razorpay_setup: {
            fund_account_id: validation.fund_account?.id
          }
        }
      }
    });

    if (!bankDetails) {
      // Try to find by validation ID in metadata
      const bankDetailsByValidation = await Bankdetails.findOne({
        where: {
          metadata: {
            razorpay_setup: {
              validation_id: validation.id
            }
          }
        }
      });

      if (!bankDetailsByValidation) {
        console.log("⚠️ No bank details found for validation:", validation.id);
        return;
      }
      
      await updateBankDetailsFromValidation(bankDetailsByValidation, validation, event);
    } else {
      await updateBankDetailsFromValidation(bankDetails, validation, event);
    }

  } catch (error) {
    console.error("❌ Error handling fund account validation webhook:", error);
  }
};

/**
 * Update bank details based on validation webhook
 */
const updateBankDetailsFromValidation = async (bankDetails, validation, event) => {
  try {
    const isCompleted = validation.status === 'completed';
    const isFailed = validation.status === 'failed';

    console.log(`🔄 Updating bank details for validation ${validation.id}:`, {
      current_status: bankDetails.isVerified,
      new_status: validation.status,
      will_verify: isCompleted
    });

    // Update metadata with latest validation info
    const updatedMetadata = {
      ...bankDetails.metadata,
      razorpay_setup: {
        ...bankDetails.metadata?.razorpay_setup,
        verification_status: validation.status,
        validation_results: validation.results,
        amount_deposited: validation.amount_deposited || 0,
        last_webhook_update: new Date().toISOString(),
        webhook_event: event,
        payout_ready: isCompleted
      }
    };

    // Prepare update data
    const updateData = {
      metadata: updatedMetadata
    };

    // If verification completed, update verification status
    if (isCompleted && !bankDetails.isVerified) {
      updateData.isVerified = true;
      updateData.isLocked = true;
      updateData.verifiedAt = new Date();
      updateData.payoutEnabled = true;

      console.log("✅ Bank account verification completed via webhook!");
    }

    // If verification failed, update status
    if (isFailed) {
      updateData.isVerified = false;
      updateData.payoutEnabled = false;
      
      console.log("❌ Bank account verification failed via webhook");
    }

    await bankDetails.update(updateData);

    console.log("✅ Bank details updated successfully from webhook");

    // TODO: Send notification to user about verification status
    // You can implement email/SMS notification here
    if (isCompleted) {
      console.log("📧 TODO: Send verification success notification to user");
    } else if (isFailed) {
      console.log("📧 TODO: Send verification failure notification to user");
    }

  } catch (error) {
    console.error("❌ Error updating bank details from validation:", error);
  }
};

/**
 * Handle payout webhook events
 */
const handlePayoutWebhook = async (event, payload) => {
  try {
    const payout = payload.payout?.entity;
    
    if (!payout) {
      console.log("⚠️ No payout entity in webhook payload");
      return;
    }

    console.log("💸 Processing payout webhook:", {
      event,
      payout_id: payout.id,
      status: payout.status,
      amount: payout.amount / 100
    });

    // Find payout record in database
    const payoutRecord = await Payout.findOne({
      where: { payout_id: payout.id }
    });

    if (!payoutRecord) {
      console.log("⚠️ No payout record found for:", payout.id);
      return;
    }

    // Update payout status
    await payoutRecord.update({
      status: payout.status,
      processed_at: payout.processed_at ? new Date(payout.processed_at * 1000) : null,
      failure_reason: payout.failure_reason,
      metadata: {
        ...payoutRecord.metadata,
        webhook_update: new Date().toISOString(),
        razorpay_webhook_data: payout,
        webhook_event: event
      }
    });

    console.log("✅ Payout record updated successfully from webhook");

    // TODO: Send notification to club about payout status
    if (payout.status === 'processed') {
      console.log("📧 TODO: Send payout success notification to club");
    } else if (payout.status === 'failed' || payout.status === 'reversed') {
      console.log("📧 TODO: Send payout failure notification to club");
    }

  } catch (error) {
    console.error("❌ Error handling payout webhook:", error);
  }
};

/**
 * Get webhook events for debugging (admin only)
 */
const getWebhookEvents = async (req, res) => {
  try {
    // This is a simple endpoint to help debug webhook events
    // In production, you might want to store webhook events in a separate table
    
    return sendResponse(res, 200, {
      success: true,
      message: "Webhook events endpoint - implement logging if needed",
      data: {
        webhook_url: `${req.protocol}://${req.get('host')}/api/webhook/razorpay`,
        events_to_subscribe: [
          'fund_account.validation.completed',
          'fund_account.validation.failed',
         'payout.created',
          'payout.queued', 
          'payout.initiated',
          'payout.pending',
          'payout.processed',
          'payout.failed',
          'payout.reversed',
          'payout.rejected',
          'payout.updated'
        ]
      }
    });

  } catch (error) {
    console.error("❌ Error in getWebhookEvents:", error);
    return sendResponse(res, 500, {
      success: false,
      error: "Internal server error"
    });
  }
};

module.exports = {
  handleRazorPayWebhook,
  handleFundAccountValidationWebhook,
  handlePayoutWebhook,
  getWebhookEvents
};
