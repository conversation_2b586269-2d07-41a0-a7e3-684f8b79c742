const { CoachDetails, User,Otp } =require("../config/db").models;
const { Op } = require('sequelize');
const { sendResponse, handleError } = require("../utils/apiResponse");
const { coachDetailSchema, coachDetailUpdateSchema } = require('../schema/coachSchema');
const { deleteFromS3 } = require("../utils/s3");

// Create a new coach detail
const createCoachDetail = async (req, res) => {
  try {

      if (req.body.profileLinks) {
      try {
        req.body.profileLinks = JSON.parse(req.body.profileLinks);
      } catch (err) {
        return sendResponse(res, 400, {
          success: false,
          message: "Invalid JSON in profileLinks",
        });
      }
    }
     const { data, success, error } = coachDetailSchema.safeParse(req.body);
      if (!success) {
      return sendResponse(res, 422, {
        success: false,
        error: error.errors,
      });
    }

    const coachDta = data;
    const userId = req.user.userId;
    const existingCoach = await CoachDetails.findOne({ where: { userId } });
    if (existingCoach) {
      return res.status(409).json({
        success: false,
        message: 'Coach details already exist for this user'
      });
    }

    // Make sure this matches your enum in DB (coach or coach)
    const user = await User.findOne({
      where: { id: userId, role: 'coach' }
    });
    if (!user) {
      return  sendResponse(res, 404,{
        success: false,
        message: 'User not found or not a coach'
      });
    }

     const cbid = await generateCoachCbid();
     await User.update({ cbid }, { where: { id: userId } });

     const profileUrl = req?.file ? req?.file?.location : null;

     const newCoach = await CoachDetails.create(
      { userId, profileUrl, ...coachDta },
      {
        include: [
          {
            model: User,
            as: "user",
          },
        ],
      }
    );

    sendResponse(res, 201, {
      success: true,
      message: 'Coach details created successfully',
      data: newCoach,
    });

  } catch (error) {
    console.error('Error creating coach detail:', error);
    handleError(res, error, 'Error creating coach detail');
  }
};

// Get all coach details with pagination and filtering
const getAllCoachDetails = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 10,
      phone,
      email, 
      name,
      country,
      state,
      district,
      city
    } = req.query;

    const pageNum = parseInt(page);
    const limitNum = parseInt(limit);
    const offset = (pageNum - 1) * limitNum;
    const whereClause = {};
    const userWhereClause = {};


    if (country) whereClause.country = { [Op.iLike]: `%${country}%` };
    if (country) whereClause.country = { [Op.iLike]: `%${country}%` };
    if (state) whereClause.state = { [Op.iLike]: `%${state}%` };
    if (district) whereClause.district = { [Op.iLike]: `%${district}%` };
    if (city) whereClause.city = { [Op.iLike]: `%${city}%` };

    if (phone) userWhereClause.phoneNumber = phone;
    if (email) userWhereClause.email = email;
    if (name) userWhereClause.email = name;

    const { count, rows } = await CoachDetails.findAndCountAll({
      where: whereClause,
      include: [{
        where:userWhereClause,
        model: User,
        as: 'user',
        attributes: ['id', 'name', 'email', 'role']
      }],
      limit: limitNum,
      offset: offset,
    });

    const totalPages = Math.ceil(count / limitNum);

    if(!rows){
        return sendResponse(res,201,{
      success: true,
      message: 'Coach details retrieved successfully',
      data: {
        coaches: [],
        pagination: {
          currentPage: 0,
          totalPages,
          totalCount: 0,
        }
      }
    });
    }
    sendResponse(res,200,{
      success: true,
      message: 'Coach details retrieved successfully',
      data: {
        coaches: rows,
        pagination: {
          currentPage: pageNum,
          totalPages,
          totalCount: count,
        }
      }
    });

  } catch (error) {
    console.error('Error fetching coach details:', error);
    handleError(res, error, 'Error fetching coach details');
  }
};

// Get coach detail by ID
const getCoachDetailById = async (req, res) => {
  try {
    const { id } = req.params;

    const coachDetail = await CoachDetails.findByPk(id, {
      include: [{
        model: User,
        as: 'user',
        attributes: ['id', 'name', 'email', 'role']
      }]
    });

    if (!coachDetail) {
      return sendResponse(res,404,{
        success: false,
        message: 'Coach details not found'
      });
    }

    res.status(200).json({
      success: true,
      message: 'Coach details retrieved successfully',
      data: coachDetail
    });

  } catch (error) {
    console.error('Error fetching coach detail:', error);
    handleError(res, error, 'Error fetching coach detail by ID');
  }
};

// Get coach detail by user ID
const getCoachProfile = async (req, res) => {
  try {
    const userId = req.user.userId;
  if (!userId) {
    return sendResponse(res, 403, {
      success: false,
      error: "forbidden",
    });
  }
    const coachDetail = await CoachDetails.findOne({
      where: { userId },
      include: [{
        model: User,
        as: 'user',
        attributes: ['id', 'name', 'email', 'role','phoneNumber']
      }]
    });

    if (!coachDetail) {
      return sendResponse(res, 204, {
      success: false,
      error: "Coach details not found for this user",
    });
    }

   sendResponse(res, 200, {
      success: true,
      message: 'Coach details retrieved successfully',
      data: coachDetail
    });

  } catch (error) {
    console.error('Error fetching coach detail by user ID:', error);
    handleError(res, error, 'Error fetching coach detail by user ID');
  }
};

// Update coach detail
const updateCoachDetail = async (req, res) => {
  const userId = req.user.userId;
   if (!userId) {
      return sendResponse(res, 422, {
        success: false,
        error: 'userId not found',
      });
    }
  try {
     if (req.body.profileLinks && typeof req.body.profileLinks === "string") {
      try {
        req.body.profileLinks = JSON.parse(req.body.profileLinks);
      } catch (err) {
        return sendResponse(res, 400, {
          success: false,
          message: "Invalid JSON in profileLinks",
        });
      }
    }
    const { success, data, error } = coachDetailUpdateSchema.safeParse(req.body)
 console.log("date",data)
    const { otp } = req.body;
    console.log("error",error)
    if (!success) {
      return sendResponse(res, 422, {
        success: false,
        error: error,
      });
    }

    const playerData = data;
    const LinksData = data;

    const { name, phoneNumber, phoneChanged } = playerData;
    const update = {};
    if (phoneChanged) {
      const existingOtp = await Otp.findOne({
        where: { phoneNumber, type: "verification", platform: "sms" },
      });

      if (!existingOtp) {
        return sendResponse(res, 404, {
          success: false,
          error: { message: "OTP not found. Please request a new one." },
        });
      }
      const time = new Date(existingOtp.expiresAt);
      if (time < new Date()) {
        return sendResponse(res, 400, {
          success: false,
          error: { message: "OTP has expired. Please request a new one." },
        });
      }

      if (existingOtp.otp !== otp) {
        return sendResponse(res, 401, {
          success: false,
          error: { message: "Incorrect OTP." },
        });
      }
      await existingOtp.destroy();
      update.phoneNumber = phoneNumber;
    }
    if (name) {
      update.name = name;
    }

    if (name || phoneChanged) {
      await User.update(update, { where: { id: userId } });
    }

    const existingPlayer = await CoachDetails.findOne({
      where: { userId: userId },
    });
    if (!existingPlayer) {
      return sendResponse(res, 404, {
        success: false,
        error: "arbiter not found",
      });
    }

    const profileUrl = req?.file
      ? req?.file?.location
      : existingPlayer.profileUrl || null;

    const [count, rows] = await CoachDetails.update(
      { profileUrl, ...playerData},
      {
        where: { userId: userId },
        returning: true,
      }
    );
    if (existingPlayer?.profileUrl) {
      deleteFromS3(existingPlayer?.profileUrl);
    }
    if (count === 0) {
      return sendResponse(res, 404, {
        success: false,
        error: "arbiter not found",
      });
    }
    sendResponse(res, 200, {
      success: true,
      data: rows[0],
    });
  } catch (error) {
    deleteFromS3(req?.file?.location);
    handleError(res, error);
  }
};

// Delete coach detail
const deleteCoachDetail = async (req, res) => {
  try {
    const { id } = req.params;

    const coachDetail = await CoachDetails.findByPk(id);
    if (!coachDetail) {
      return res.status(404).json({
        success: false,
        message: 'Coach details not found'
      });
    }

    await coachDetail.destroy();

    res.status(200).json({
      success: true,
      message: 'Coach details deleted successfully'
    });

  } catch (error) {
    console.error('Error deleting coach detail:', error);
    handleError(res, error, 'Error deleting coach detail');
  }
};

const removeProfileImage = async (req, res) => {
  try {
    const userId = req.user.userId;
    if (!userId) {
      return sendResponse(res, 401, {
        success: false,
        error: "Unauthorized",
      });
    }
    const coachDetail = await CoachDetails.findOne({
      where: { userId: userId },
    });
    if (!coachDetail) {
      return sendResponse(res, 404, {
        success: false,
        error: "Club detail not found",
      });
    }
    if (coachDetail.profileUrl) {
      deleteFromS3(coachDetail.profileUrl);
    }
    coachDetail.profileUrl = null;
    await coachDetail.save();
    return sendResponse(res, 200, {
      success: true,
      message: "Profile image removed successfully",
    });
  } catch (error) {
    handleError(res, error);
  }
};

module.exports = {
  createCoachDetail,
  getAllCoachDetails,
  getCoachDetailById,
  getCoachProfile,
  updateCoachDetail,
  deleteCoachDetail,
  removeProfileImage,
};

// In your coach profile update controller
const generateCoachCbid = async () => {
  try {
    // Generate the formatted CBID
    const prefix = "CB";
    const currentYear = new Date().getFullYear();
    const yearCode = currentYear.toString().slice(-2);
    const coachCode = "CO"; // You can change this code if needed

    const yearPattern = `${prefix}${yearCode}${coachCode}`;
    const latestUser = await User.findOne({
      where: {
        cbid: {
          [Op.iLike]: `${yearPattern}%`,
        },
      },
      order: [["cbid", "DESC"]],
    });

    // Format sequence number with leading zeros - starts from 1 each year
    let nextSequence = 1;
    if (latestUser && latestUser.cbid) {
      const match = latestUser.cbid.match(/(\d{5})$/);
      if (match) {
        nextSequence = parseInt(match[1], 10) + 1;
      }
    }

    const sequenceNumber = nextSequence.toString().padStart(5, "0");
    return `${prefix}${yearCode}${coachCode}${sequenceNumber}`;
  } catch (error) {
    console.error("Error generating Coach CBID:", error);
    throw error;
  }
};

