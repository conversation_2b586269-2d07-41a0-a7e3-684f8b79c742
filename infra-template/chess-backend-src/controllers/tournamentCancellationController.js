const { v4: uuidv4 } = require("uuid");
const { config } = require("../config/config");
const { sendResponse, handleError } = require("../utils/apiResponse");
const {
  Tournament,
  Registration,
  Payment,
  User,
  PlayerDetail,
  ClubDetail,
  Notifications,
  BulkRegistration,
  Refund,
} = require("../config/db").models;
const emailService = require("../utils/mailer/emailService");
const { sequelize } = require("../config/db");
const { z } = require("zod");
const { Op } = require("sequelize");
const smsService = require("../utils/sms/smsService");
const { createRefund } = require("../utils/razorPay");


// Schema for tournament cancellation
const cancelTournamentSchema = z.object({
  reason: z.string().min(10, "Cancellation reason must be at least 10 characters long"),
  refundType: z.enum(["full", "partial"]).default("full"),
  refundPercentage: z.number().min(0).max(100).optional(),
});

const cancelTournament = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { id } = req.params;
    const userId = req.user.userId;
    const isAdmin = req.user.role === "admin";

    // Validate request body
    const { data, success, error } = cancelTournamentSchema.safeParse(req.body);
    if (!success) {
      await transaction.rollback();
      return sendResponse(res, 422, {
        success: false,
        error: "Validation failed",
        data: error.format(),
      });
    }

    const { reason, refundType = "full", refundPercentage = 100 } = data;

    // Find tournament with optimized query
    const tournament = await Tournament.findOne({
      where: { title: id },
      include: [{
        model: Registration,
        as: "registrations",
        include: [{
          model: Payment,
          as: "payment",
          where: { paymentStatus: ["captured"] },
          required: true,
          attributes: ['id', 'paymentAmount', 'paymentCurrency', 'razorpayPaymentId'],
        }, {
          model: User,
          as: "player",
          attributes: ['id', 'email', 'phoneNumber', 'name', 'cbid'],
        }]
      }],
      transaction,  
    });

    if (!tournament) {
      await transaction.rollback();
      return sendResponse(res, 404, {
        success: false,
        error: "Tournament not found",
      });
    }

    // Authorization check
    if (!isAdmin && tournament.clubId !== userId) {
      await transaction.rollback();
      return sendResponse(res, 403, {
        success: false,
        error: "You are not authorized to cancel this tournament",
      });
    }

    // Status validation
    const invalidStatuses = ["cancelled", "archived", "completed"];
    if (invalidStatuses.includes(tournament.tournamentStatus)) {
      await transaction.rollback();
      return sendResponse(res, 400, {
        success: false,
        error: `Cannot cancel tournament with status: ${tournament.tournamentStatus}`,
      });
    }

    // Update tournament status
    await tournament.update({
      tournamentStatus: "cancelled",
      cancellationReason: reason,
      cancellationDate: new Date(),
    }, { transaction });

    // Get all eligible payments for refund
    const eligibleRegistrations = tournament.registrations || [];
    
    if (eligibleRegistrations.length === 0) {
      await transaction.commit();
      return sendResponse(res, 200, {
        success: true,
        message: "Tournament cancelled successfully (no registrations to refund)",
        data: { tournamentId: tournament.id, refundsInitiated: 0 }
      });
    }

    // Batch create refund records
    const refundRecords = await Promise.all(
      eligibleRegistrations.map(async (registration) => {
        const payment = registration.payment;
        
        // Check for existing refund
        const existingRefund = await Refund.findOne({
          where: { paymentId: payment.id },
          transaction,
        });

        if (existingRefund) {
          return null; // Skip existing refunds
        }

        // Calculate refund amount
        const refundAmount = refundType === "partial" 
          ? (tournament.entryFee * refundPercentage / 100).toFixed(2)
          : tournament.entryFee;

        const refundReference = `RF-${uuidv4().substring(0, 8)}`;

        return Refund.create({
          paymentId: payment.id,
          tournamentId: tournament.id,
          userId: payment.userId || registration.playerId,
          razorpayPaymentId: payment.razorpayPaymentId,
          refundReference,
          refundAmount,
          refundCurrency: payment.paymentCurrency || "INR",
          refundStatus: "pending",
          refundRemarks: `Tournament cancelled: ${reason}`,
          refundType,
          refundReason: "tournament_cancelled",
          refundInitiatedBy: userId,
          refundSpeed: "normal",
        }, { transaction });
      })
    );

    // Filter out null values (existing refunds)
    const validRefunds = refundRecords.filter(refund => refund !== null);

    // Update all registrations to cancelled
    await Registration.update(
      { status: "cancelled" },
      { 
        where: { 
          tournamentId: tournament.id,
          id: { [Op.in]: eligibleRegistrations.map(r => r.id) }
        },
        transaction 
      }
    );

    // Commit transaction before external API calls
    await transaction.commit();

    // Process refunds in batches to avoid overwhelming payment gateway
    const BATCH_SIZE = 5;
    const refundBatches = [];
    
    for (let i = 0; i < validRefunds.length; i += BATCH_SIZE) {
      refundBatches.push(validRefunds.slice(i, i + BATCH_SIZE));
    }

    let successfulRefunds = 0;
    let failedRefunds = 0;

    // Process each batch with delay
    for (const batch of refundBatches) {
      const batchPromises = batch.map(async (refund) => {
        try {
          const registration = eligibleRegistrations.find(r => r.payment.id === refund.paymentId);
          const payment = registration.payment;

          if (!payment.razorpayPaymentId) {
            await refund.update({
              refundStatus: "failed",
              refundRemarks: "Missing Razorpay payment ID",
            });
            return { success: false, refund };
          }

          // Process refund with Razorpay
          const refundResponse = await createRefund(payment.razorpayPaymentId, {
            amount: refund.refundAmount * 100, // Convert to paisa
            notes: {
              tournament_id: tournament.id,
              user_id: refund.userId,
              refund_reference: refund.refundReference,
              reason: "tournament_cancelled",
            },
            receipt: refund.refundReference,
            speed: "normal",
          });

          if (refundResponse.success) {
            await refund.update({
              refundStatus: "processing",
              razorpayRefundId: refundResponse.refund.id,
              razorpayResponse: refundResponse.refund,
            });

            // Send notifications asynchronously
            setImmediate(async () => {
              try {
                const user = registration.player;
                
                await Promise.allSettled([
                  emailService.sendTournamentCancellationEmail(user, tournament, refund),
                  smsService.sendTournamentCancellationSMS({
                    mobile: user.phoneNumber,
                    tournamentTitle: tournament.title,
                    refundAmount: refund.refundAmount,
                    reason: reason.substring(0, 50) + (reason.length > 50 ? '...' : ''),
                  })
                ]);
              } catch (notificationError) {
                console.error(`Notification error for refund ${refund.id}:`, notificationError);
              }
            });

            return { success: true, refund };
          } else {
            await refund.update({
              refundStatus: "failed",
              refundRemarks: `Razorpay error: ${refundResponse.error}`,
              razorpayResponse: refundResponse.details,
            });
            return { success: false, refund, error: refundResponse.error };
          }

        } catch (error) {
          console.error(`Refund processing error for ${refund.id}:`, error);
          
          await refund.update({
            refundStatus: "failed",
            refundRemarks: `Processing error: ${error.message}`,
          });
          
          return { success: false, refund, error: error.message };
        }
      });

      // Wait for current batch to complete
      const batchResults = await Promise.allSettled(batchPromises);
      
      // Count successes and failures
      batchResults.forEach(result => {
        if (result.status === 'fulfilled' && result.value.success) {
          successfulRefunds++;
        } else {
          failedRefunds++;
        }
      });

      // Add delay between batches to avoid rate limiting
      if (refundBatches.indexOf(batch) < refundBatches.length - 1) {
        await new Promise(resolve => setTimeout(resolve, 1000)); // 1 second delay
      }
    }

    // Send admin notification
    try {
      await emailService.sendTournamentCancellationAdminNotification({
        tournament,
        reason,
        totalRegistrations: eligibleRegistrations.length,
        successfulRefunds,
        failedRefunds,
        cancelledBy: userId
      });
    } catch (adminNotificationError) {
      console.error("Admin notification error:", adminNotificationError);
    }

    return sendResponse(res, 200, {
      success: true,
      message: "Tournament cancelled successfully",
      data: {
        tournamentId: tournament.id,
        totalRegistrations: eligibleRegistrations.length,
        refundsInitiated: validRefunds.length,
        successfulRefunds,
        failedRefunds,
        refundType,
        ...(refundType === "partial" && { refundPercentage })
      },
    });

  } catch (error) {
    console.error("Error in cancelTournament:", error);
    if (transaction && !transaction.finished) {
      await transaction.rollback();
    }
    return handleError(res, error);
  }
};

// Helper function for batch processing with retry logic
const processRefundWithRetry = async (payment, refund, maxRetries = 3) => {
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      const refundResponse = await createRefund(payment.razorpayPaymentId, {
        amount: refund.refundAmount * 100,
        notes: {
          tournament_id: refund.tournamentId,
          user_id: refund.userId,
          refund_reference: refund.refundReference,
          reason: "tournament_cancelled",
          attempt: attempt.toString()
        },
        receipt: refund.refundReference,
        speed: "normal",
      });

      return refundResponse;
    } catch (error) {
      console.error(`Refund attempt ${attempt} failed for ${refund.id}:`, error);
      
      if (attempt === maxRetries) {
        throw error;
      }
      
      // Exponential backoff
      await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt) * 1000));
    }
  }
};
module.exports = {
  cancelTournament,
};