const { ClubDetail, User, PlayerDetail } = require("../config/db").models;
const { Op } = require("sequelize");
const { sendResponse, handleError } = require("../utils/apiResponse");
const { getAllPlayerSchema } = require("../schema/playerSchama");

const getClubMembers = async (req, res) => {
  const { data, success, error } = getAllPlayerSchema.safeParse(req.query);
  const clubId = req.user.userId;

  if (!success) {
    return sendResponse(res, 422, {
      success: false,
      error: "Invalid query parameters",
    });
  }
  try {
    const {
      page = 1,
      limit = 10,
      city,
      playerId,
      playerName,
      country,
      state,
      district,
      mobile,
      email,
    } = data;

    const offset = (page - 1) * limit;
    const whereClause = {};
    const filterFields = { city, country, state, district };
    Object.entries(filterFields).forEach(([key, value]) => {
      if (value) whereClause[key] = { [Op.iLike]: `%${value}%` };
    });
    if (playerId && !playerId.toLowerCase().startsWith("cb")) {
      whereClause[Op.or] = [
        { fideId: { [Op.iLike]: `%${playerId}%` } },
        { aicfId: { [Op.iLike]: `%${playerId}%` } },
        { stateId: { [Op.iLike]: `%${playerId}%` } },
        { districtId: { [Op.iLike]: `%${playerId}%` } },
      ];
    }
    let userWhereClause = {
      role: "player",
    };
    if (playerId && playerId.toLowerCase().startsWith("cb")) {
      userWhereClause = { cbid: { [Op.iLike]: `%${playerId}%` } };
    }
    if (mobile) userWhereClause.phoneNumber = { [Op.iLike]: mobile };
    if (email) userWhereClause.email = { [Op.iLike]: email };
    if (playerName) userWhereClause.name = { [Op.iLike]: `%${playerName}%` };

    const club = await ClubDetail.findOne({
      where: { userId: clubId },
      attributes: ["clubName", "id"],
    });
    whereClause.clubId = club.id;

    const { rows: players, count } = await PlayerDetail.findAndCountAll({
      where: whereClause,
      offset,
      limit,
      attributes: [
        "playerTitle",
        "fideRating",
        "fideId",
        "aicfId",
        "districtId",
        "stateId",
        "dob",
        "gender",
        "parentGuardianName",
        "emergencyContact",
        "alternateContact",
      ],
      include: [
        {
          model: User,
          attributes: ["name", "cbid", "phoneNumber", "email"],
          where: userWhereClause,
          required: true,
        },
      ],
      order: [["fideRating", "DESC"]],
    });
    const formattedPlayers = players.map((player) => {
      const playerData = player.toJSON();
      if (player.User) {
        return { ...playerData, ...player.User.toJSON() };
      }
      return playerData;
    });
    const response = {
      players: formattedPlayers,
      total: count,
      currentPage: page,
      totalPages: Math.ceil(count / limit),
    };
    sendResponse(res, 200, {
      success: true,
      data: response,
    });
  } catch (error) {
    handleError(res, error);
  }
};

module.exports = { getClubMembers };
