const { CourseDetails,User } = require("../config/db").models;
const { courseSchema } = require("../schema/courceSchema");
const { sendResponse, handleError } = require("../utils/apiResponse");
const { Op, literal, fn, col, where, cast } = require("sequelize");
const emailService = require("../utils/mailer/emailService");

// Helper function to parse time strings like "10:30 AM" to minutes since midnight
const parseTimeToMinutes = (timeString) => {
  const [time, period] = timeString.split(' ');
  let [hours, minutes] = time.split(':').map(Number);

  if (period === 'PM' && hours !== 12) {
    hours += 12;
  } else if (period === 'AM' && hours === 12) {
    hours = 0;
  }

  return hours * 60 + minutes;
};


// Helper function to check if two time ranges overlap
const timeRangesOverlap = (start1, end1, start2, end2) => {
  return start1 < end2 && end1 > start2;
};

const createCourse = async (req, res) => {
  try {
    const result = courseSchema.safeParse(req.body);
    if (!result.success) {
      return sendResponse(res, 422, {
        success: false,
        error: "Validation failed",
        data: result.error.format(),
      });
    }

    const {
      title,
      startDate,
      endDate,
      sessionStartTime,
      sessionEndTime,
      selectedDays, // e.g., ["wednesday", "friday"]
      ...data
    } = result.data;

    const { userId } = req.query;

    // Basic checks
    if (!title || !startDate || !endDate || !sessionStartTime || !sessionEndTime || !selectedDays ) {
      return sendResponse(res, 400, {
        success: false,
        message: 'All fields are required, including selectedDays array.',
      });
    }

    const formattedTitle = title.toLowerCase().replace(/\s+/g, "-");

    // Check for duplicate title
    const existingTitle = await CourseDetails.findOne({
      where: { title: formattedTitle, courseStatus: 'active' }
    });

    if (existingTitle) {
      return sendResponse(res, 409, {
        success: false,
        message: 'Title already exists. Choose a different one.',
      });
    }

    // 1. Get all potentially conflicting courses (based on date range and status)
    const potentialConflicts = await CourseDetails.findAll({
      where: {
        courseStatus: 'active',
        [Op.and]: [
          { startDate: { [Op.lte]: endDate } },
          { endDate: { [Op.gte]: startDate } }
        ]
      }
    });

    // 2. Check for overlap in both day and time
    const newStartMinutes = parseTimeToMinutes(sessionStartTime);
    const newEndMinutes = parseTimeToMinutes(sessionEndTime);

    const hasConflict = potentialConflicts.some(course => {
      const courseDays = course.selectedDays || []; // e.g., ['monday', 'wednesday']
      const hasSameDay = courseDays.some(day => selectedDays.includes(day.toLowerCase()));
      
      if (!hasSameDay) return false;

      const existingStartMinutes = parseTimeToMinutes(course.sessionStartTime);
      const existingEndMinutes = parseTimeToMinutes(course.sessionEndTime);
      
      return timeRangesOverlap(
        newStartMinutes, 
        newEndMinutes, 
        existingStartMinutes, 
        existingEndMinutes
      );
    });

    if (hasConflict) {
      return sendResponse(res, 409, {
        success: false,
        message: 'Time slot overlaps with another course on the same weekday.',
      });
    }

    // 3. Create new course
    const newCourse = await CourseDetails.create({
      userId,
      title: formattedTitle,
      startDate,
      endDate,
      sessionStartTime,
      sessionEndTime,
      selectedDays, // store array like ['wednesday', 'friday']
      courseStatus: 'active',
      ...data
    });

    return sendResponse(res, 201, {
      success: true,
      message: "Course created successfully",
      data: newCourse,
    });

  } catch (error) {
    return handleError(res, error);
  }
};

// Get all courses
const getAllCourses = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 10,
      title,
      duration,
      month,
      year,
      fee,
    } = req.query;

    const offset = (page - 1) * limit;

    // Build where clause
    const whereClause = {
      courseStatus:'active'
    };

    if (title) {
      whereClause.title = { [Op.iLike]: `%${title}%` };
    }

     if (fee) {
      whereClause.courseFee = fee;
    }

    if (duration) {
      whereClause.courseDuration = duration;
    }

    if (month) {
      whereClause[Op.and] = whereClause[Op.and] || [];
      whereClause[Op.and].push(literal(`EXTRACT(MONTH FROM "start_date") = ${month}`));
    }

    if (year) {
      whereClause[Op.and] = whereClause[Op.and] || [];
      whereClause[Op.and].push(literal(`EXTRACT(YEAR FROM "start_date") = ${year}`));
    }

    const { rows: courses, count } = await CourseDetails.findAndCountAll({
      where: whereClause,
      limit: parseInt(limit),
      offset: parseInt(offset),
      order: [["createdAt", "DESC"]],
    });

    return sendResponse(res, 200, {
      success: true,
      message: "Courses fetched successfully",
      data: courses,
      pagination: {
        totalItems: count,
        totalPages: Math.ceil(count / limit),
        currentPage: Number(page),
      },
    });
  } catch (error) {
    return handleError(res, error);
  }
};


const getAllUpcomingCourses = async (req, res) => {
  try {
    const { userId } = req.user;

    if (!userId) {
      return sendResponse(res, 400, {
        success: false,
        message: "userId is required",
      });
    }

    const whereClause = {
      [Op.or]: [
        { startDate: { [Op.gte]: new Date() } },
        { endDate: { [Op.gte]: new Date() } },
        { registrationEndDate: { [Op.gte]: new Date() } },
        { registrationStartDate: { [Op.gte]: new Date() } },
      ],
      courseStatus:'active'
    };
    whereClause.userId = userId;

    const { page, limit } = req.query;
    const newPage = parseInt(page) || 1;
    const newLimit = parseInt(limit) || 3;
    const offset = (newPage - 1) * newLimit;

    const { rows: courses, count } = await CourseDetails.findAndCountAll({
      where: whereClause,
      offset,
      newLimit,
      order: [['createdAt', 'DESC']], // Optional: sort newest first
    });


    if (count === 0) {
      return sendResponse(res, 201, {
        success: true,
        message: "Courses fetched successfully",
        data: [],
        pagination: {
          totalItems: count,
          totalPages: Math.ceil(count / limit),
          currentPage: page,
          pageSize: limit,
        },
      });
    }


    return sendResponse(res, 200, {
      success: true,
      message: "Courses fetched successfully",
      data: courses,
      pagination: {
        totalItems: count,
        totalPages: Math.ceil(count / limit),
        currentPage: page,
        pageSize: limit,
      },
    });
  } catch (error) {
    return handleError(res, error);
  }
};



// Get single course by ID
const getCourseById = async (req, res) => {
  try {
    const { title } = req.params;
    const course = await CourseDetails.findOne({ where: { title } });

    if (!course) {
      return sendResponse(res, 201, {
        success: false,
        message: "Course not found",
        data:[]
      });
    }

    return sendResponse(res, 200, {
      success: true,
      message: "Course fetched successfully",
      data: course,
    });
  } catch (error) {
    return handleError(res, error);
  }
};

// Get courses by coach ID with pagination
const getCourseByCoach = async (req, res) => {
  try {
    const userId  = req.user.userId;

    const { title,page, limit } = req.query;
    const newPage = parseInt(page) || 1;
    const newLimit = parseInt(limit) || 10;
    const offset = (newPage - 1) * newLimit;

    let whereClause ={
      userId,
      courseStatus:'active'
    }

    if(title){
      whereClause.title={[Op.iLike]: `%${title}%`}
    }

    const course = await CourseDetails.findAndCountAll({
      where:whereClause ,
      limit,
      offset, 
    });

    // If no records found
    if (course.count === 0) {
      return sendResponse(res, 201, {
        success: true,
        message: "No courses found",
        data: [],
        pagination: {
          totalItems: 0,
          totalPages: 0,
          currentPage: page
        }
      });
    }

    // Send paginated data
    return sendResponse(res, 200, {
      success: true,
      message: "Courses fetched successfully",
      data: course.rows,
      pagination: {
        totalItems: course.count,
        totalPages: Math.ceil(course.count / limit),
        currentPage: page
      }
    });
  } catch (error) {
    return handleError(res, error);
  }
};


// Update course
const updateCourse = async (req, res) => {
  try {
      const {id:courseId} = req.query;
      const result = courseSchema.safeParse(req.body);

    if (!result.success) {
      sendResponse(res, 422, {
        success: false,
        error: "Validation failed",
        data: result.error.format(),
      });
      return;
    }

    
    const { title, ...updateData } = result.data;
    const formattedTitle = title?.toLowerCase().replace(/\s+/g, "-");

    const course = await CourseDetails.findOne({ where: { id:courseId } });

    if (!course) {
      return sendResponse(res, 404, {
        success: false,
        error: "Course not found",
      });
    }

     // Determine the brochure URL to use
    let brochureUrl = course?.brochureUrl; // Keep existing by default
    let shouldDeleteOldBrochure = false;

    // Only update brochure URL if a new file was uploaded
    if (req?.file?.location) {
      brochureUrl = req.file.location;
      // Only delete old brochure if we're replacing it with a new one
      shouldDeleteOldBrochure =
        course?.brochureUrl && course.brochureUrl !== brochureUrl;
    }
    // Prepare update data - only include brochureUrl if it's being changed
    const updatePayload = {
      title: formattedTitle || course.title,
      ...updateData,
    };
    // Only include brochureUrl in update if a new file was uploaded
    if (req?.file?.location) {
      updatePayload.brochureUrl = brochureUrl;
    }

    const [updatedCount, updatedCourse] = await CourseDetails.update(
      updatePayload,
      {
        where: { id: courseId},
        returning: true,
      }
    );

    if (shouldDeleteOldBrochure) {
      try {
        await deleteFromS3(course.brochureUrl);
      } catch (error) {
        console.error("Failed to delete old brochure from S3:", error);
        // Don't fail the entire operation if S3 deletion fails
      }
    }

      if (updatedCount === 0) {
      sendResponse(res, 404, {
        success: false,
        error: { message: "Course not updated" },
      });
      return;
    }

    sendResponse(res, 200, {
      success: true,
      error: { message: "Course updated successfully" },
      data: updatedCourse,
    });
  } catch (error) {
    return handleError(res, error);
  }
};

// Delete course
const deleteCourse = async (req, res) => {
  try {
    const { id } = req.params;
    const course = await CourseDetails.findOne({ where: { id } });;

    if (!course) {
      return sendResponse(res, 404, {
        success: false,
        error: "Course not found",
      });
    }

    await course.destroy();

    return sendResponse(res, 200, {
      success: true,
      message: "Course deleted successfully",
    });
  } catch (error) {
    return handleError(res, error);
  }
};

const deactivateCourseByTitle = async (req, res) => {
  try {
    const { title } = req.params;

    if (!title) {
      return res.status(400).json({
        success: false,
        message: "Title parameter is required.",
      });
    }

    // Update status to 'inactive'
    const [updatedCount] = await CourseDetails.update(
      { courseStatus: 'inactive' },
      { where: { title } }
    );

    if (updatedCount === 0) {
      return res.status(404).json({
        success: false,
        message: "No course found with the provided title.",
      });
    }

    emailService.sendCourseRegister
    return res.status(200).json({
      success: true,
      message: `Course "${title}" has been marked as inactive.`,
    });

  } catch (error) {
    console.error("Error updating course status:", error);
    return res.status(500).json({
      success: false,
      message: "An error occurred while updating the course status.",
      error: error.message,
    });
  }
};


const getCourseAndUserDetails = async (req, res) => {
  const { title } = req.params;
  const userId = req.user.userId;

  try {
    // Fetch course details
    const course = await CourseDetails.findOne({where:{title}});
    if (!course) {
      return res.status(404).json({ success: false, message: "Course not found" });
    }

    // Fetch user details
    const user = await User.findOne({ where: { id: userId } });
    if (!user) {
      return res.status(404).json({ success: false, message: "User not found" });
    }

    // Prepare email data
    const emailData = {
      email: process.env.EMAIL_REGISTER,
      name: user.name,
      playerId: user.cbid,
      title: course.title,
      startDate: course.startDate,
      endDate: course.endDate,
      fee: course.courseFee,
      paymentMethod: course.paymentStructure, // or replace with real value
      // adminLink: `https://yourdomain.com/admin/course/${course.id}`, // if needed
    };

    // Send Email
    await emailService.sendCourseRegister(emailData);

    return res.status(200).json({
      success: true,
      message: "Course and user details fetched and email sent",
      data: {
        course,
        user,
      },
    });
  } catch (error) {
    console.error("Error fetching course or user details:", error);
    return res.status(500).json({ success: false, message: "Server error" });
  }
};



module.exports = {
  createCourse,
  getAllCourses,
  getCourseById,
  updateCourse,
  deleteCourse,
  getAllUpcomingCourses,
  getCourseByCoach,
  deactivateCourseByTitle,
  getCourseAndUserDetails,
};
