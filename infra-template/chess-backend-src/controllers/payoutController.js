const {
  createTournamentPayout,
  checkPayoutStatus,
  calculateTournamentPayout,
} = require("../utils/razorPay");
const { sendResponse, handleError } = require("../utils/apiResponse");
const {
  Bankdetails,
  Tournament,
  Payout,
  Payment,
  Registration,
  User,
  PlatformFee,
} = require("../config/db").models;

const { Op } = require("sequelize");

/**
 * Create tournament payout
 */
const createPayout = async (req, res) => {
  try {
    const userId = req.user?.userId;
    const { tournament_id, urgent = false } = req.body;

    if (!userId) {
      return sendResponse(res, 403, {
        success: false,
        error: "Forbidden",
      });
    }

    if (!tournament_id) {
      return sendResponse(res, 400, {
        success: false,
        error: "Tournament ID is required",
      });
    }

    // Get tournament details
    const tournament = await Tournament.findOne({
      where: {
        id: tournament_id,
        registrationEndDate: { [Op.lt]: new Date() },
        tournamentStatus:{[Op.not]:["archived","cancelled"]}
      },
    });

    if (!tournament) {
      return sendResponse(res, 404, {
        success: false,
        error: "Tournament not found or not completed",
      });
    }

    // Check if payout already exists for this tournament
    const existingPayout = await Payout.findOne({
      where: {
        tournament_id: tournament_id,
        status: { [Op.not]: "failed" },
      },
    });

    if (existingPayout) {
      return sendResponse(res, 400, {
        success: false,
        error: "Payout already exists for this tournament",
        data: {
          payout_id: existingPayout.payout_id,
          status: existingPayout.status,
          amount: existingPayout.amount,
        },
      });
    }

    // Get club's verified bank details with Razorpay setup
    const bankDetails = await Bankdetails.findOne({
      where: {
        clubId: tournament.clubId,
        // isVerified: true,
        // payoutEnabled: true,
      },
    });

    if (!bankDetails) {
      return sendResponse(res, 400, {
        success: false,
        error: "Verified bank details not found",
        message: "Please complete bank verification and enable payouts first",
      });
    }

    const payment = await Payment.findAll({
      where: {
        tournamentId: tournament.id,
        paymentStatus: "captured",
      },
    });

    if (!payment || payment.length === 0) {
      return sendResponse(res, 404, {
        success: false,
        error: "No payments found for this tournament",
      });
    }

    const { rows: registeration, count: totalRegisteration } =
      await Registration.findAndCountAll({
        where: {
          tournamentId: tournament.id,
          status: "active",
        },
      });

    if (!registeration || totalRegisteration === 0) {
      return sendResponse(res, 404, {
        success: false,
        error: "No active registrations found for this tournament",
      });
    }

    // Get current platform fee from database
    const currentPlatformFee = await PlatformFee.getCurrentFeePercentage();
    if (!currentPlatformFee) {
      return sendResponse(res, 400, {
        success: false,
        error: "Platform fee not configured",
      });
    }

    const totalAmountCollected = payment
      .map((p) => p.paymentAmount)
      .reduce((sum, amount) => Number(sum) + Number(amount), 0);

     

    // Extract Razorpay setup data from bank details metadata
    const razorpaySetup = bankDetails.metadata?.razorpay_setup;

    if (
      !razorpaySetup ||
      !razorpaySetup.contact_id ||
      !razorpaySetup.fund_account_id
    ) {
      return sendResponse(res, 400, {
        success: false,
        error: "Razorpay setup incomplete",
        message:
          "Bank verification did not complete properly. Please re-verify your bank details",
      });
    }

    // Calculate payout amount
    // Payment structure: Tournament Entry Fee + Platform Fee
    // Payout = Total Collected - Platform Fee

    const tournamentEntryFeeTotal =
      Number(totalRegisteration) * Number(tournament.entryFee);
    const platformFeeTotal =
      (tournamentEntryFeeTotal * 3.75) / 100;

     


    const clubPayout = tournamentEntryFeeTotal;


    // Create tournament payout using fund_account_id from bank details
    const payoutResult = await createTournamentPayout({
      tournament_id: tournament.id,
      club_id: tournament.clubId,
      fund_account_id: razorpaySetup.fund_account_id,
      amount: clubPayout,
      tournament_name: tournament.title,
      urgent,
    });

    if (!payoutResult.success) {
      return sendResponse(res, 400, {
        success: false,
        error: "Payout creation failed",
        details: payoutResult.error,
      });
    }

    // Save payout record
    const payoutRecord = await Payout.create({
      clubId: tournament.clubId,
      tournament_id: tournament.id,
      payout_id: payoutResult.payout_id,
      amount: clubPayout,
      total_collected: totalAmountCollected,
      platform_fee: platformFeeTotal,
      status: payoutResult.status,
      mode: payoutResult.mode,
      reference_id: payoutResult.reference_id,
      fund_account_id: razorpaySetup.fund_account_id,
      contact_id: razorpaySetup.contact_id,
      urgent_transfer: urgent,
      metadata: {
        calculation: {
          totalAmountCollected,
          tournamentEntryFeeTotal,
          platformFeeTotal,
          clubPayout,
          clubPayoutPercentage: (
            (clubPayout / totalAmountCollected) *
            100
          ).toFixed(2),
          platformFeePerUnit: currentPlatformFee,
          participants: totalRegisteration,
        },
        tournament: {
          id: tournament.id,
          name: tournament.name,
          participants: totalRegisteration,
          entryFee: tournament.entryFee,
        },
        payout_details: payoutResult,
      },
    });

    return sendResponse(res, 201, {
      success: true,
      message: "Tournament payout created successfully",
      data: {
        payout_id: payoutResult.payout_id,
        amount: clubPayout,
        status: payoutResult.status,
        mode: payoutResult.mode,
        estimated_processing_time: payoutResult.estimated_processing_time,
        reference_id: payoutResult.reference_id,
        calculation: {
          total_collected: totalAmountCollected,
          tournament_entry_fee_total: tournamentEntryFeeTotal,
          platform_fee_total: platformFeeTotal,
          club_payout: clubPayout,
          payout_percentage: (
            (clubPayout / totalAmountCollected) *
            100
          ).toFixed(2),
        },
        tournament: {
          id: tournament.id,
          name: tournament.name,
          participants: totalRegisteration,
          entry_fee: tournament.entryFee,
        },
      },
    });
  } catch (error) {
    console.error("❌ Error in createPayout:", error);
    handleError(res, error);
  }
};

/**
 * Get payout status
 */
const getPayoutStatus = async (req, res) => {
  try {
    const userId = req.user?.userId;
    const { tournament_id } = req.params;

    if (!userId) {
      return sendResponse(res, 403, {
        success: false,
        error: "Forbidden",
      });
    }

    if (!tournament_id) {
      return sendResponse(res, 400, {
        success: false,
        error: "Tournament ID is required",
      });
    }

    const where = { tournament_id: tournament_id };

    // Get payout record from database
    const payoutRecord = await Payout.findOne({
      where: where,
      include: [
        {
          model: Tournament,
          as: "tournament",
          attributes: ["id", "title", "registrationEndDate"],
        },
      ],
    });

    if (!payoutRecord) {
      return sendResponse(res, 404, {
        success: false,
        error: "Payout not found",
      });
    }

    const tournamentStatus =
      new Date(payoutRecord.tournament.registrationEndDate) < new Date()
        ? "completed"
        : "active";

 

    // Get latest status from Razorpay
    const statusResult = await checkPayoutStatus(payoutRecord.payout_id);

    if (statusResult.success) {
      // Update local database with latest status
      await payoutRecord.update({
        status: statusResult.payout.status,
        processed_at: statusResult.payout.processed_at,
        failure_reason: statusResult.payout.failure_reason,
        metadata: {
          ...payoutRecord.metadata,
          last_status_check: new Date().toISOString(),
          razorpay_status: statusResult.payout,
        },
      });
    }

    return sendResponse(res, 200, {
      success: true,
      data: {
        payout_id: payoutRecord.payout_id,
        status: statusResult.success
          ? statusResult.payout.status
          : payoutRecord.status,
        status_description: statusResult.success
          ? statusResult.payout.status_description
          : "Status check failed",
        amount: payoutRecord.amount,
        mode: payoutRecord.mode,
        reference_id: payoutRecord.reference_id,
        created_at: payoutRecord.createdAt,
        updated_at: payoutRecord.updatedAt,
        processed_at: statusResult.success
          ? statusResult.payout.processed_at
          : payoutRecord.processed_at,
        failure_reason: statusResult.success
          ? statusResult.payout.failure_reason
          : payoutRecord.failure_reason,
        utr: statusResult.success ? statusResult.payout.utr : null,
        fees: statusResult.success ? statusResult.payout.fees : null,
        tax: statusResult.success ? statusResult.payout.tax : null,
        bank_reference: statusResult.success ? statusResult.payout.utr : null,
        urgent: payoutRecord.urgent_transfer,
        notes: payoutRecord.metadata?.notes || null,
        tournament: payoutRecord.tournament
          ? {
              id: payoutRecord.tournament.id,
              title: payoutRecord.tournament.title,
              status: tournamentStatus,
            }
          : null,
      },
    });
  } catch (error) {
    console.error("❌ Error in getPayoutStatus:", error);
    handleError(res, error);
  }
};

/**
 * Get all payouts for a club
 */
const getClubPayouts = async (req, res) => {
  try {
    const userId = req.user?.userId;
    const { clubId } = req.params;
    const { page = 1, limit = 10, status = null } = req.query;

    if (!userId) {
      return sendResponse(res, 403, {
        success: false,
        error: "Forbidden",
      });
    }

    const offset = (page - 1) * limit;
    const whereClause = { clubId: clubId };

    if (status) {
      whereClause.status = status;
    }

    const { rows: payouts, count: total } = await Payout.findAndCountAll({
      where: whereClause,
      include: [
        {
          model: Tournament,
          as: "tournament",
          attributes: ["id", "title", "registrationEndDate"],
        },
      ],
      limit: parseInt(limit),
      offset: parseInt(offset),
      order: [["createdAt", "DESC"]],
    });

    const formattedPayouts = payouts.map((payout) => ({
      id: payout.id,
      payout_id: payout.payout_id,
      amount: payout.amount,
      status: payout.status,
      mode: payout.mode,
      reference_id: payout.reference_id,
      urgent_transfer: payout.urgent_transfer,
      created_at: payout.createdAt,
      processed_at: payout.processed_at,
      tournament: payout.tournament
        ? {
            id: payout.tournament.id,
            title: payout.tournament.title,
            status:
              new Date(payout.tournament.registrationEndDate) < new Date()
                ? "completed"
                : "active",
          }
        : null,
    }));

    return sendResponse(res, 200, {
      success: true,
      data: {
        payouts: formattedPayouts,
        pagination: {
          current_page: parseInt(page),
          per_page: parseInt(limit),
          total: total,
          total_pages: Math.ceil(total / limit),
        },
      },
    });
  } catch (error) {
    console.error("❌ Error in getClubPayouts:", error);
    handleError(res, error);
  }
};

/**
 * Calculate payout for a tournament (preview)
 */
const calculatePayoutPreview = async (req, res) => {
  try {
    const userId = req.user?.userId;
    const { tournament_id } = req.params;

    if (!userId) {
      return sendResponse(res, 403, {
        success: false,
        error: "Forbidden",
      });
    }

    if (!tournament_id) {
      return sendResponse(res, 400, {
        success: false,
        error: "Tournament ID is required",
      });
    }

    // Get tournament details
    const tournament = await Tournament.findOne({
      where: {
        title: tournament_id,
      },
    });

    if (!tournament) {
      return sendResponse(res, 404, {
        success: false,
        error: "Tournament not found",
      });
    }

    const payment = await Payment.findAll({
      where: {
        tournamentId: tournament.id,
        paymentStatus: "captured",
      },
    });

    if (!payment || payment.length === 0) {
      return sendResponse(res, 404, {
        success: false,
        error: "No payments found for this tournament",
      });
    }

    const { rows: registeration, count: totalRegisteration } =
      await Registration.findAndCountAll({
        where: {
          tournamentId: tournament.id,
          status: "active",
        },
      });

    if (!registeration || totalRegisteration === 0) {
      return sendResponse(res, 404, {
        success: false,
        error: "No active registrations found for this tournament",
      });
    }

    // Get current platform fee from database
    const currentPlatformFee = await PlatformFee.getCurrentFeePercentage();
    if (!currentPlatformFee) {
      return sendResponse(res, 400, {
        success: false,
        error: "Platform fee not configured",
      });
    }

    // Ensure all calculations are done with proper numbers
    const totalAmountCollected = payment
      .map((p) => parseFloat(p.paymentAmount) || 0)
      .reduce((sum, amount) => Number(sum) + Number(amount), 0);

    // Calculate payout with correct fee structure
    const tournamentEntryFeeTotal =
      parseFloat(totalRegisteration) * parseFloat(tournament.entryFee);

    // Club payout = Total collected amount (no deductions based on your requirement)
    const clubPayout = tournamentEntryFeeTotal;
    const clubPayoutPercentage =
      totalAmountCollected > 0
        ? ((clubPayout / totalAmountCollected) * 100).toFixed(2)
        : "0.00";

    const calculation = {
      totalAmountCollected: parseFloat(totalAmountCollected.toFixed(2)),
      tournamentEntryFeeTotal: parseFloat(tournamentEntryFeeTotal.toFixed(2)),

      clubPayout: parseFloat(clubPayout.toFixed(2)),
      clubPayoutPercentage: parseFloat(clubPayoutPercentage),
      breakdown: {
        "Total Amount Collected": `₹${totalAmountCollected.toFixed(2)}`,
        "Tournament Entry Fee Total": `₹${tournamentEntryFeeTotal.toFixed(2)}`,

        "Club Payout": `₹${clubPayout.toFixed(2)}`,
      },
    };

    return sendResponse(res, 200, {
      success: true,
      data: {
        tournament: {
          id: tournament.id,
          name: tournament.name,
          participants: parseInt(totalRegisteration),
          entry_fee: parseFloat(tournament.entryFee),
        },
        calculation: {
          total_collected: calculation.totalAmountCollected,
          tournament_entry_fee_total: calculation.tournamentEntryFeeTotal,

          club_payout: calculation.clubPayout,
          payout_percentage: calculation.clubPayoutPercentage,
        },
        breakdown: calculation.breakdown,
      },
    });
  } catch (error) {
    console.error("❌ Error in calculatePayoutPreview:", error);
    handleError(res, error);
  }
};
module.exports = {
  createPayout,
  getPayoutStatus,
  getClubPayouts,
  calculatePayoutPreview,
};
