const bcrypt = require("bcryptjs");
const jwt = require("jsonwebtoken");
const crypto = require("crypto");
const { User, Otp } = require("../config/db").models;
const {
  loginSchema,
  registerSchema,
  resetPasswordSchema,
} = require("../schema/authSchema");
const { config } = require("../config/config");
const { sendResponse, handleError } = require("../utils/apiResponse");
const { Op } = require("sequelize");
const emailService = require("../utils/mailer/emailService");
const smsService = require("../utils/sms/smsService");
const { sequelize } = require("../config/db");

const login = async (req, res) => {
  const result = await loginSchema.safeParse(req.body);
  if (!result.success) {
    return sendResponse(res, 422, { success: false, error: result.error });
  }
  try {
    const { emailOrMobile, password, rememberMe } = result.data;

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    const mobileRegex = /^(0\d{9,14}|\+?[1-9]\d{9,14})$/; // Allowing for international format

    let query = {};
    if (emailRegex.test(emailOrMobile)) {
      query = { email: emailOrMobile };
    } else if (mobileRegex.test(emailOrMobile)) {
      query = { phoneNumber: emailOrMobile };
    } else {
      return sendResponse(res, 422, {
        success: false,
        error: "Invalid format",
      });
    }

    const user = await User.scope('adminAccess').findOne({ where: query });
    if (!user) {
      return sendResponse(res, 401, {
        success: false,
        error: "Invalid credentials",
      });
    }

    if (!user.isActive || !user.isAccess) {
      return sendResponse(res, 403, {
        success: false,
        error: "Your account is inactive. Please contact support.",
      });
    }


    const isPasswordValid = await bcrypt.compare(password, user.password);
    if (!isPasswordValid) {
      return sendResponse(res, 401, {
        success: false,
        error: "Invalid credentials",
      });
    }
    const tokenPayload = {
      userId: user.id,
      email: user.email,
      role: user.role,
    };
    const accessToken = jwt.sign(tokenPayload, config.jwt_secret, {
      expiresIn: "1h",
    });

    const refreshToken = jwt.sign(tokenPayload, config.jwt_secret, {
      expiresIn: "12h",
    });

    if (rememberMe) {
      res.cookie("refreshToken", refreshToken, {
        httpOnly: true,
        maxAge: 1 * 12 * 60 * 60 * 1000, //12 hours
      });
    }

    sendResponse(res, 200, {
      success: true,
      data: {
        accessToken,
        user: {
          userId: user.id,
          email: user.email,
          role: user.role,
          name: user.name,
        },
      },
    });
  } catch (error) {
    handleError(res, error);
  }
};

const register = async (req, res) => {
  try {
    const { success, error, data } = await registerSchema.safeParse(req.body);
    if (!success) {
      const formattedErrors = error.errors?.map((err) => err.message) || [
        "Invalid input",
      ];
      return sendResponse(res, 422, { success: false, error: formattedErrors });
    }
    const { firstName, lastName, phoneNumber, email, password, role, otp, referral } =
      data;

    const existingOtp = await Otp.findOne({
      where: { phoneNumber },
    });

    if (!existingOtp) {
      return sendResponse(res, 404, {
        success: false,
        error: { message: "OTP not found. Please request a new one." },
      });
    }

    // Check if OTP is expired
    if (new Date(existingOtp.expiresAt) < new Date()) {
      return sendResponse(res, 400, {
        success: false,
        error: { message: "OTP has expired. Please request a new one." },
      });
    }

    // Validate OTP value
    if (existingOtp.otp !== otp) {
      return sendResponse(res, 401, {
        success: false,
        error: { message: "Incorrect OTP." },
      });
    }
    if (existingOtp && existingOtp.otp === otp) {
      await existingOtp.destroy();
    }

    const fullName = `${firstName} ${lastName}`;

    // Check all three fields in one query using OR
    const existingUser = await User.findOne({
      where: {
        [Op.or]: [{ email }, { phoneNumber }],
      },
    });

    if (existingUser) {
      // Determine which field caused the conflict
      if (existingUser.email === email) {
        return sendResponse(res, 409, {
          success: false,
          error: "This email is already registered",
        });
      } else if (existingUser.phoneNumber === phoneNumber) {
        return sendResponse(res, 409, {
          success: false,
          error: "This phone number is already registered",
        });
      }
    }

    const hashedPassword = await bcrypt.hash(password, 10);
    const user = await User.create({
      name: fullName,
      phoneNumber,
      email,
      password: hashedPassword,
      role,
      cbid: phoneNumber,
      referralId: referral == "" ? null : referral,
    });
    if (!user) {
      return sendResponse(res, 400, {
        success: false,
        error: "User not created",
      });
    }
    
    // Send welcome email
    try {
      emailService.sendWelcomeEmail({
        name: user.name,
        email: user.email,
        role: user.role,
      });
    } catch (emailError) {
      console.error("Error sending welcome email:", emailError);
      // Continue with the response even if email fails
    }

    sendResponse(res, 201, {
      success: true,
      message: "User registered",
      data: { id: user.id, email: user.email, role: user.role, referral: user.referralId },
    });
  } catch (error) {
    handleError(res, error);
  }
};


const logout = async (req, res) => {
  try {
    res.clearCookie("refreshToken", {
      httpOnly: true,
    });
    return sendResponse(res, 200, {
      success: true,
      message: "Logged out successfully",
    });
  } catch (error) {
    handleError(res, error);
  }
};

const resetPassword = async (req, res) => {
  const result = resetPasswordSchema.safeParse(req.body);
  if (!result.success) {
    return sendResponse(res, 422, { success: false, error: result.error });
  }

  try {
    const { email, otp, newPassword } = result.data;

    const user = await User.findOne({ where: { email } });
    if (!user) {
      return sendResponse(res, 404, {
        success: false,
        error: "User not found",
      });
    }

    const existingOtp = await Otp.findOne({
      where: {
        email,
        type: "password-reset", // ✅ make sure this matches your OTP type
        platform: "email",
      },
    });

    if (!existingOtp) {
      return sendResponse(res, 404, {
        success: false,
        error: "OTP not found. Please request a new one.",
      });
    }

    // Check OTP expiry
    if (new Date(existingOtp.expiresAt) < new Date()) {
      return sendResponse(res, 400, {
        success: false,
        error: "OTP has expired. Please request a new one.",
      });
    }
    if (existingOtp.otp !== otp) {
      return sendResponse(res, 401, {
        success: false,
        error: "Incorrect OTP.",
      });
    }

    // Hash and update password
    const hashedPassword = await bcrypt.hash(newPassword, 10);
    await user.update({ password: hashedPassword });

    // ✅ Optionally delete used OTP
    await existingOtp.destroy();

    return sendResponse(res, 200, {
      success: true,
      message: "Password has been reset successfully",
    });
  } catch (error) {
    console.error("Error in resetPassword:", error);
    return handleError(res, error);
  }
};

const refreshToken = async (req, res) => {
  const refreshToken = req.cookies.refreshToken;
  if (!refreshToken) {
    return sendResponse(res, 403, {
      success: false,
      error: "No refresh token provided",
    });
  }
  try {
    const decoded = jwt.verify(refreshToken, config.jwt_secret);
    if (!decoded) {
      return sendResponse(res, 401, { success: false, error: "Token expired" });
    }
    // Check if the user still exists and has necessary permissions
    const user = await User.findOne({ where: { email: decoded.email } });
    if (!user) {
      return sendResponse(res, 403, {
        success: false,
        error: "User not found",
      });
    }

    const tokenPayload = {
      userId: user.id,
      email: user.email,
      role: user.role,
    };
    const accessToken = jwt.sign(tokenPayload, config.jwt_secret, {
      expiresIn: "1h",
    });
    sendResponse(res, 200, { success: true, data: { accessToken } });
  } catch (error) {
    if (error instanceof jwt.TokenExpiredError) {
      return sendResponse(res, 403, { success: false, error: "Token expired" });
    }
    if (error instanceof jwt.JsonWebTokenError) {
      return sendResponse(res, 403, { success: false, error: "Invalid token" });
    }
    handleError(res, error);
  }
};

const ContactUS = async (req, res) => {
  try {
    const { email, message, phone, name, subject } = req.body;

    await emailService.sendContactEnquiryEmail({
      email,
      message,
      phone,
      name,
      subject,
    });
    return sendResponse(res, 200, {
      success: true,
      data: {
        message: "Enquiry sent successfully",
      },
    });
  } catch (error) {
    handleError(res, error);
  }
};

/**
 * Controller function to send OTP via SMS with fixed 1-minute cooldown
 */
/**
 * Controller function to send OTP via SMS with improved handling
 */
const sendOtp = async (req, res) => {
  try {
    const { phoneNumber: mobileNumber, type = "registration", userId, email, referral } = req.body;

    // Validate mobile number
    if (!mobileNumber) {
      return sendResponse(res, 400, {
        success: false,
        error: "Phone number is required",
      });
    }

    if (mobileNumber.length !== 10) {
      return sendResponse(res, 400, {
        success: false,
        error: "Phone number must be exactly 10 digits",
      });
    }

    if (type === "registration") {
      const phoneNumber = mobileNumber.slice(-10);
      const user = await User.findOne({
        where: {
          [Op.or]: [{ email: email }, { phoneNumber: phoneNumber }],
        },
      });
      if (user) {
        if (user.email === email && user.phoneNumber === phoneNumber) {
          return sendResponse(res, 409, {
            success: false,
            error: "This email & password is already registered",
          });
        } else if (user.email === email) {
          return sendResponse(res, 409, {
            success: false,
            error: "This email is already registered",
          });
        } else if (user.phoneNumber === phoneNumber) {
          return sendResponse(res, 409, {
            success: false,
            error: "This phone number is already registered",
          });
        }
      }
    }

    if (type === "registration" && referral) {
      const user = await User.findOne({
        where: { cbid: referral }
      });
      if (!user) {
        return sendResponse(res, 409, {
          success: false,
          error: "This CBID is not found",
        });
      }
    }
    // Validate userId for non-registration types
    if (type !== "registration" && !userId) {
      return sendResponse(res, 400, {
        success: false,
        error: { message: "userId is required for non-registration OTPs" },
      });
    }

    const now = new Date();
    const otpTTL = 10 * 60 * 1000; // 10 minutes

    // Using a transaction to prevent race conditions
    const result = await sequelize.transaction(async (transaction) => {
      // Construct where clause based on OTP type
      const whereClause = {
        type,
        platform: "sms",
      };

      // For registration, search by phone number
      // For verification or other types, search by userId
      if (type === "registration") {
        whereClause.phoneNumber = mobileNumber;
      } else {
        whereClause.userId = userId;
      }

      // Lock the row to prevent concurrent modifications
      let existingOtp = await Otp.findOne({
        where: whereClause,
        lock: transaction.LOCK.UPDATE,
        transaction,
      });

      let otpValue = Math.floor(100000 + Math.random() * 900000);

      if (existingOtp) {
        // Check if 1 minute has passed since last update
        const lastUpdatedTime = new Date(existingOtp.updatedAt);
        const oneMinuteAgo = new Date(now.getTime() - 60 * 1000); // 1 minute in milliseconds

        if (lastUpdatedTime > oneMinuteAgo) {
          const timeElapsedMs = now - lastUpdatedTime;
          const secondsRemaining = Math.ceil(
            (60 * 1000 - timeElapsedMs) / 1000
          );

          return {
            status: 429,
            response: {
              success: false,
              error: {
                message: ` Please wait ${secondsRemaining} seconds before requesting another OTP.`,
              },
            },
          };
        }

        // Update existing OTP
        await existingOtp.update(
          {
            otp: otpValue,
            phoneNumber: mobileNumber, // Ensure phone number is updated for non-registration types
            expiresAt: new Date(now.getTime() + otpTTL),
          },
          { transaction }
        );
      } else {
        // Create new OTP record
        await Otp.create(
          {
            phoneNumber: mobileNumber,
            otp: otpValue,
            type,
            userId: type !== "registration" ? userId : null,
            platform: "sms",
            expiresAt: new Date(now.getTime() + otpTTL),
          },
          { transaction }
        );
      }

      return {
        status: 200,
        response: {
          success: true,
          data: {
            message: "OTP sent successfully",
          },
        },
        otp: otpValue,
      };
    });

    // If rate limited, return early with error response
    if (result.status === 429) {
      return sendResponse(res, result.status, result.response);
    }

    // Send the actual SMS
    await smsService.sendOtpSMS({
      mobile: mobileNumber,
      otp: result.otp,
      expiryMinutes: Math.floor(otpTTL / (60 * 1000)),
    });

    return sendResponse(res, result.status, {
      success: true,
      message: "OTP sent successfully",
    });
  } catch (error) {
    console.error("Error in sendOtp:", error);
    return handleError(res, error);
  }
};
const sendEmailOtpForResetPassword = async (req, res) => {
  try {
    const { email } = req.body;
    const type = "password-reset";

    if (!email) {
      return sendResponse(res, 400, {
        success: false,
        error: "Email is required",
      });
    }

    // Check if user exists
    const user = await User.findOne({ where: { email } });

    if (!user) {
      return sendResponse(res, 404, {
        success: false,
        error: "No account found with this email",
      });
    }

    const userId = user.id;
    const now = new Date();
    const otpTTL = 10 * 60 * 1000; // 10 minutes

    const result = await sequelize.transaction(async (transaction) => {
      const whereClause = {
        type,
        platform: "email",
        userId,
      };

      let existingOtp = await Otp.findOne({
        where: whereClause,
        lock: transaction.LOCK.UPDATE,
        transaction,
      });

      let otpValue = Math.floor(100000 + Math.random() * 900000);

      if (existingOtp) {
        const lastUpdatedTime = new Date(existingOtp.updatedAt);
        const oneMinuteAgo = new Date(now.getTime() - 60 * 1000);

        if (lastUpdatedTime > oneMinuteAgo) {
          const timeElapsedMs = now - lastUpdatedTime;
          const secondsRemaining = Math.ceil(
            (60 * 1000 - timeElapsedMs) / 1000
          );

          return {
            status: 429,
            response: {
              success: false,
              error: {
                message: `Please wait ${secondsRemaining} seconds before requesting another OTP.`,
              },
            },
          };
        }

        await existingOtp.update(
          {
            otp: otpValue,
            email,
            expiresAt: new Date(now.getTime() + otpTTL),
          },
          { transaction }
        );
      } else {
        await Otp.create(
          {
            email,
            otp: otpValue,
            type,
            userId,
            platform: "email",
            expiresAt: new Date(now.getTime() + otpTTL),
          },
          { transaction }
        );
      }

      return {
        status: 200,
        response: {
          success: true,
          data: {
            message: "Reset password OTP sent to email",
          },
        },
        otp: otpValue,
      };
    });

    if (result.status === 429) {
      return sendResponse(res, result.status, result.response);
    }

    await emailService.sendPasswordResetEmail(
      user,
      result.otp,
      Math.floor(otpTTL / (60 * 1000))
    );

    return sendResponse(res, result.status, result.response);
  } catch (error) {
    console.error("Error in sendEmailOtpForResetPassword:", error);
    handleError(res, error);
  }
};

const authMe = async (req, res) => {
  try {
    const userId = req.user.userId;
    if (!userId) {
      return sendResponse(res, 401, {
        success: false,
        error: "Unauthorized",
      });
    }
    const user = await User.findOne({
      where: { id: userId },
      attributes: ["email", "name", "role", "id"],
    });
    if (!user) {
      return sendResponse(res, 404, {
        success: false,
        error: "User not found",
      });
    }
    sendResponse(res, 200, {
      success: true,
      data: user,
    });
  } catch (error) {
    handleError(res, error);
  }
};

module.exports = {
  login,
  register,
  logout,
  resetPassword,
  refreshToken,
  ContactUS,
  sendOtp,
  authMe,
  sendEmailOtpForResetPassword,
};
