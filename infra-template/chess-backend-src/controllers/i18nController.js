const { sequelize } = require("../config/db");

exports.getLabelsByLanguage = async (req, res) => {
  const lang = req.params.lang || "en";

  const column = lang === "ta" ? "label_ta" : "label_en";
  try {
    const [results] = await sequelize.query(`
      SELECT field_name, ${column} as label FROM field_translations
    `);

    const response = {};
    results.forEach(({ field_name, label }) => {
      response[field_name] = label;
    });

    return res.status(200).json(response);
  } catch (error) {
    return res.status(500).json({ error: "Failed to load translations" });
  }
};
