const { processPlayerExcelFile } = require("../../scripts/importusers");
const { sendResponse, handleError } = require("../../utils/apiResponse");
const multer = require("multer");
const path = require("path");
const fs = require("fs");

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    const uploadDir = "uploads/player-onboarding";
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: function (req, file, cb) {
    const uniqueSuffix = Date.now() + "-" + Math.round(Math.random() * 1e9);
    cb(null, "players-" + uniqueSuffix + path.extname(file.originalname));
  },
});

const upload = multer({
  storage: storage,
  fileFilter: function (req, file, cb) {
    const allowedTypes = [
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", // .xlsx
      "application/vnd.ms-excel", // .xls
      "text/csv", // .csv
    ];
    if (allowedTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error("Only Excel (.xlsx, .xls) and CSV files are allowed"));
    }
  },
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB limit
  },
});

// Required fields for player onboarding (only essential fields)
const requiredFields = [
  'playerName',      // Player full name
  'email',          // Player email
  'phoneNumber'     // Player phone
];

// Optional fields (will use defaults if not provided)
const optionalFields = [
  'dateOfBirth',    // Date of birth
  'gender',         // Gender
  'address',        // Address
  'city',           // City
  'state',          // State
  'district',       // District
  'country',        // Country
  'pincode',        // Postal code
  'parentGuardianName',    // Parent/Guardian name
  'emergencyContact',      // Emergency contact number
  'fideId',         // FIDE ID
  'aicfId',         // AICF ID
  'stateId',        // State ID
  'districtId',     // District ID
  'fideRating'      // FIDE rating
];

/**
 * Main controller function for player onboarding upload
 */
const onboardPlayersUpload = async (req, res) => {
  try {

    if (!req.file) {
      return sendResponse(res, 400, {
        success: false,
        error: "No file uploaded"
      });
    }

    // Process the uploaded file using the script
    const result = await processPlayerExcelFile(req.file.path);

    // Clean up uploaded file
    try {
      fs.unlinkSync(req.file.path);
    } catch (cleanupError) {
      console.error("Failed to cleanup uploaded file:", cleanupError);
    }

    // Return response based on script result
    if (result.success) {
      return sendResponse(res, 200, {
        success: true,
        message: `Players onboarding completed. ${result.data.successCount} successful, ${result.data.failedCount} failed.`,
        data: result.data,
        errors: result.errors || []
      });
    } else {
      return sendResponse(res, 400, {
        success: false,
        error: result.message,
        filePath: result.filePath
      });
    }

  } catch (error) {
    console.error("Error in onboardPlayersUpload:", error);

    // Clean up uploaded file in case of error
    if (req.file && req.file.path) {
      try {
        fs.unlinkSync(req.file.path);
      } catch (cleanupError) {
        console.error("Failed to cleanup uploaded file:", cleanupError);
      }
    }

    return handleError(res, error);
  }
};

module.exports = {
  upload,
  onboardPlayersUpload,
  requiredFields,
  optionalFields
};
