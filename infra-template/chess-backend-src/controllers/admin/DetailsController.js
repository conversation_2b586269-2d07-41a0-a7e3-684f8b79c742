const {
  getAllArbiterSchema,
  getAllPlayerSchema,
} = require("../../schema/playerSchama");
const { sendResponse, handleError } = require("../../utils/apiResponse");
const { Op, Sequelize, where } = require("sequelize");
const { v4: UUIDV4 } = require("uuid");
const { z } = require("zod");
const {
  PlayerDetail,
  ArbiterDetails,
  ClubDetail,
  User,
  Tournament,
  Payment,
  BulkRegistration,
  Refund,
  PlatformFee,
  Registration,
  CoachDetails,
} = require("../../config/db").models;
const {
  arbiterDetailSchema,
  updateDetailsSchema,
} = require("../../schema/arbiterSchema");

const { sequelize } = require("../../config/db");
const { deleteFromS3 } = require("../../utils/s3");
const { config } = require("../../config/config");
const notificationService = require("../../services/notificationService");
const cronService = require("../../services/cronService");
const { exportToExcel } = require("../../utils/report-generation");
const { getAllClubSchema } = require("../../schema/clubSchema");

const getSinglePlayer = async (req, res) => {
  try {
    const { id } = req.params;
    if (!id || typeof id !== "string") {
      return sendResponse(res, 400, {
        success: false,
        error: "Invalid player ID",
      });
    }
    const player = await User.scope("adminAccess").findOne({
      where: { cbid: id },
      attributes: [
        "name",
        "cbid",
        "id",
        "email",
        "phoneNumber",
        "isActive",
        "isAccess",
        "referral_id"
      ],
      include: [
        {
          model: PlayerDetail,
          attributes: [
            "dob",
            "gender",
            "alternateContact",
            "parentGuardianName",
            "emergencyContact",
            "playerTitle",
            "clubId",
            "fideRating",
            "fideId",
            "aicfId",
            "stateId",
            "districtId",
            "association",
            "club",
            "country",
            "state",
            "profileUrl",
            "district",
            "city",
            "address",
            "pincode",
            "id",
            "createdAt"
          ],
        },
        // {
        //     model: User,
        //     as:'referrer',
        //     attributes:["name"]
        // }
      ],
    });

    if (!player) {
      return sendResponse(res, 404, {
        success: false,
        error: "Player not found",
      });
    }
    sendResponse(res, 200, {
      success: true,
      data: player,
    });
  } catch (error) {
    handleError(res, error);
  }
};

const getClubDetailById = async (req, res) => {
  try {
    const AdminScopedUser = User.scope("adminAccess");
    const { id } = req.params;
    if (!id || typeof id !== "string") {
      return sendResponse(res, 400, {
        success: false,
        error: "Invalid club ID",
      });
    }
    const clubDetail = await ClubDetail.findOne({
      where: { clubId: id },
      attributes: {
        exclude: ["userId", "createdAt", "updatedAt"],
      },
      include: [
        {
          model: AdminScopedUser,
          as: "user",
          attributes: [
            "name",
            "cbid",
            "id",
            "email",
            "phoneNumber",
            "isActive",
            "isAccess",
          ],
        },
      ],
    });

    if (clubDetail) {
      const { user, ...clubData } = clubDetail.toJSON();
      const filteredClubDetail = {
        ...clubData,
        userId: user?.id,
        name: user?.name,
        email: user?.email,
        cbid: user?.cbid,
        phoneNumber: user?.phoneNumber,
        IsActive: user?.isActive,
        isAccess: user?.isAccess,
      };
      clubDetail.dataValues = filteredClubDetail;
    }

    if (!clubDetail) {
      return sendResponse(res, 404, {
        success: false,
        error: "Club detail not found",
      });
    }

    // Add additional information or related data if needed
    // For example, you could include the number of members or recent tournaments

    return sendResponse(res, 200, {
      success: true,
      data: clubDetail,
    });
  } catch (error) {
    console.error("Error in getClubDetailById:", error);
    handleError(res, error);
  }
};

const getSingleArbiter = async (req, res) => {
  try {
    const { id } = req.params;
    if (!id || typeof id !== "string") {
      return sendResponse(res, 400, {
        success: false,
        error: "Invalid arbiter ID",
      });
    }

    const arbiter = await User.scope("adminAccess").findOne({
      where: { cbid: id },
      attributes: [
        "name",
        "cbid",
        "id",
        "email",
        "phoneNumber",
        "isActive",
        "isAccess",
      ],
      include: [
        {
          model: ArbiterDetails,
          attributes: [
            "title",
            "fideId",
            "aicfId",
            "districtId",
            "profileUrl",
            "alternateContact",
            "officialId",

            "stateId",
            "country",
            "state",
            "district",
            "city",
            "pincode",
          ],
          required: true,
        },
      ],
    });
    if (!arbiter) {
      return sendResponse(res, 404, {
        success: false,
        error: "Arbiter not found",
      });
    }

    sendResponse(res, 200, {
      success: true,
      data: arbiter,
    });
  } catch (error) {
    handleError(res, error);
  }
};

const getPayment = async (req, res) => {
  try {
    // Step 1: Fetch tournaments
    let tournament, count;
    try {
      const result = await Tournament.findAndCountAll();
      tournament = result.rows;
      count = result.count;
    } catch (err) {
      console.error("Error fetching tournaments:", err);
      return sendResponse(res, 500, {
        success: false,
        message: "Failed to fetch tournament data.",
      });
    }

    // Step 2: Fetch payments
    let payments;
    try {
      payments = await Payment.findAll({
        where: { paymentStatus: { [Op.in]: ["captured", "refunded"] } },
        attributes: ["id", "tournamentId", "paymentAmount", "paymentStatus"],
      });
    } catch (err) {
      console.error("Error fetching payments:", err);
      return sendResponse(res, 500, {
        success: false,
        message: "Failed to fetch payment data.",
      });
    }

    if (!payments || payments.length === 0) {
      return sendResponse(res, 200, {
        success: true,
        data: {
          tournamentCount: count,
          totalAmount: 0,
          totalRefundAmount: 0,
          totalPlatformFeeEarnings: 0,
          totalTransactions: 0,
        },
      });
    }

    // Step 3: Fetch refunds
    let refunds = [];
    try {
      refunds = await Refund.findAll({
        attributes: ["id", "paymentId", "refundAmount", "refundStatus"],
      });
    } catch (error) {
      console.error("Error fetching refunds:", error);
      // Continue without refunds data - we'll calculate based on payment status
    }

    // Step 4: Calculate financial metrics
    let totalAmount = 0;
    let totalRefundAmount = 0;
    let totalPlatformFeeEarnings = 0;
    let totalTransactions = 0;

    try {
      // Platform fee percentage - 5%
      const PLATFORM_FEE = await PlatformFee.getCurrentFeePercentage();
      if (!PLATFORM_FEE) {
        throw new Error("Platform fee not found");
      }

      payments.forEach((payment) => {
        const paymentAmount = Number(payment.paymentAmount);

        if (isNaN(paymentAmount)) {
          throw new Error(`Invalid paymentAmount: ${payment.paymentAmount}`);
        }

        // Count all payment attempts
        totalTransactions++;

        // Add to total amount (entry fee + platform fee)
        totalAmount += paymentAmount;

        // Calculate platform fee earnings for successful payments
        if (
          payment.paymentStatus === "captured" ||
          payment.paymentStatus === "refunded"
        ) {
          // Calculate platform fee from the total payment amount
          // Since payment amount = entry fee + platform fee
          // Platform fee = (payment amount * platform fee %) / (100 + platform fee %)
          const platformFee = (paymentAmount * PLATFORM_FEE) / 100;
          totalPlatformFeeEarnings += platformFee;
        }

        // Calculate refund amount
        if (payment.paymentStatus === "refunded") {
          // For refunds, we refund the full payment amount
          totalRefundAmount += paymentAmount;
        }
      });

      // Alternative calculation using refunds table if available
      if (refunds && refunds.length > 0) {
        let calculatedRefundAmount = 0;
        refunds.forEach((refund) => {
          const refundAmount = Number(refund.refundAmount);
          if (!isNaN(refundAmount) && refund.refundStatus === "processed") {
            calculatedRefundAmount += refundAmount;
          }
        });

        // Use refunds table data if it exists and is reasonable
        if (calculatedRefundAmount > 0) {
          totalRefundAmount = calculatedRefundAmount;
        }
      }
    } catch (err) {
      console.error("Error calculating financial metrics:", err);
      return sendResponse(res, 500, {
        success: false,
        message: "Failed to calculate payment metrics.",
      });
    }

    // Step 5: Calculate additional metrics
    // Net revenue = Total collected - Total refunded
    const netRevenue = totalAmount - totalRefundAmount;

    // Actual earnings = Platform fee earnings from successful payments
    const actualEarnings = totalPlatformFeeEarnings;

    const refundRate =
      totalTransactions > 0
        ? ((totalRefundAmount / totalAmount) * 100).toFixed(2)
        : 0;

    // Step 6: Send successful response
    sendResponse(res, 200, {
      success: true,
      data: {
        tournamentCount: count,
        totalAmount, // Total amount collected (entry fee + platform fee)
        totalRefundAmount, // Total amount refunded
        totalPlatformFeeEarnings: totalPlatformFeeEarnings.toFixed(2), // Platform fee earnings (3.75%)
        netRevenue, // Total collected - Total refunded
        actualEarnings, // Platform fee earnings from successful payments
        totalTransactions, // Total number of payment attempts
        refundRate, // Percentage of refunds
        summary: {
          totalCollected: `₹${totalAmount.toFixed(2)}`,
          totalRefunded: `₹${totalRefundAmount.toFixed(2)}`,
          platformEarnings: `₹${totalPlatformFeeEarnings.toFixed(2)}`,
          netRevenue: `₹${netRevenue.toFixed(2)}`,
          actualEarnings: `₹${actualEarnings.toFixed(2)}`,
          breakdown: {
            description:
              "Payment amount includes: Entry fee + Platform fee (3.75%)",
            platformFeeRate: "3.75%",
            note: "Platform fee is calculated as part of the total payment amount",
          },
        },
      },
    });
  } catch (error) {
    console.error("Unexpected error in getPayment:", error);
    handleError(res, error);
  }
};

const getAllTournament = async (req, res) => {
  try {
    // Get pagination params from query string, not params
    let { page, limit = 10, tournamentName, date } = req.query;

    // Convert to numbers and validate
    page = parseInt(page);
    limit = parseInt(limit, 10);

    if (isNaN(page) || isNaN(limit) || page < 1 || limit < 1) {
      return sendResponse(res, 400, {
        success: false,
        message:
          "Invalid 'page' or 'limit' parameters. Both must be positive integers.",
      });
    }

    const whereClause = {};

    if (tournamentName) {
      whereClause.title = { [Op.iLike]: `%${tournamentName}%` };
    }

    const dateRange = getDateRange(date);
    if (dateRange) {
      whereClause.startDate = { [Op.between]: dateRange };
    }

    // Optional: Set maximum limit to prevent abuse
    if (limit > 100) {
      limit = 100;
    }

    const offset = (page - 1) * limit;

    const { rows: tournamentData, count } = await Tournament.findAndCountAll({
      where: whereClause,
      attributes: [
        "id",
        "title",
        // Add virtual field to calculate total payment amount
        [
          sequelize.literal(`(
            SELECT COALESCE(SUM(CAST("payments"."payment_amount" AS DECIMAL)), 0)
            FROM "payments" AS "payments"
            WHERE "payments"."tournament_id" = "Tournament"."id"
            AND "payments"."payment_status" = 'captured'
          )`),
          "totalPaymentAmount",
        ],
        // [
        //   sequelize.literal(`(
        //     SELECT COALESCE(SUM(CAST("payments"."payment_amount" AS DECIMAL)), 0)
        //     FROM "payments" AS "payments"
        //     WHERE "payments"."tournament_id" = "Tournament"."id"
        //     AND "payments"."payment_status" = 'refunded'
        //   )`),
        //   'totalRefundedAmount'
        // ],
      ],
      include: {
        model: Payment,
        as: "payments",
        where: { paymentStatus: "captured" },
        attributes: ["id", "tournamentId", "paymentAmount", "paymentStatus"],
      },
      limit,
      distinct: true,
      offset,
    });

    // Alternative approach: Calculate total payment amount after fetching data
    const tournamentDataWithTotals = tournamentData.map((tournament) => {
      const tournamentObj = tournament.toJSON();

      // Calculate total payment amount from included payments
      const totalPaymentAmount = tournamentObj.payments.reduce(
        (sum, payment) => {
          return sum + (parseFloat(payment.paymentAmount) || 0);
        },
        0
      );

      // const totalRefundedAmount = tournamentObj.payments
      //   .filter(p => p.paymentStatus === 'refunded')
      //   .reduce((sum, p) => sum + (parseFloat(p.paymentAmount) || 0), 0);

      return {
        ...tournamentObj,
        totalPaymentAmount: totalPaymentAmount,
        // totalRefundedAmount
      };
    });

    // Handle case where no tournaments exist
    if (count === 0) {
      return sendResponse(res, 200, {
        success: true,
        data: [],
        count: 0,
        page,
        totalPages: 0,
        message: "No tournaments found.",
      });
    }

    const totalPages = Math.ceil(count / limit);

    // Check if requested page exceeds available pages
    if (page > totalPages) {
      return sendResponse(res, 400, {
        success: false,
        message: `Page ${page} does not exist. Total pages: ${totalPages}`,
      });
    }

    // Success response
    sendResponse(res, 200, {
      success: true,
      data: tournamentDataWithTotals,
      pagination: {
        currentPage: page,
        totalPages,
        totalRecords: tournamentData.length,
        recordsPerPage: limit,
        hasNextPage: page < totalPages,
        hasPrevPage: page > 1,
      },
    });
  } catch (error) {
    console.error("Unexpected error in getAllTournament:", error);
    return sendResponse(res, 500, {
      success: false,
      message: "An unexpected error occurred while fetching tournaments.",
    });
  }
};

const clubPaymentSchema = z.object({
  page: z.coerce.number().int().positive().default(1),
  limit: z.coerce.number().int().positive().default(10),
  tournamentTitle: z.string().optional(),
  transactionId: z.string().optional(),
  paymentType: z.enum(["player", "club", "all"]).optional().default("all"),
});

const getClubEarnings = async (req, res) => {
  try {
    const userId = req.user.userId;
    const { data, success, errors } = clubPaymentSchema.safeParse(req.query);

    if (!userId) {
      return sendResponse(res, 403, {
        success: false,
        error: "Forbidden",
      });
    }

    if (!success) {
      return sendResponse(res, 422, {
        success: false,
        error: `Invalid query parameters: ${errors}`,
      });
    }

    const { tournamentTitle, transactionId, paymentType } = data;
    const whereQuery = { paymentStatus: ["captured", "refunded"] };

    if (transactionId)
      whereQuery.paymentTransactionId = { [Op.iLike]: `%${transactionId}%` };

    // Handle different payment types if specified
    if (paymentType === "player") {
      whereQuery.paymentType = "player";
    } else if (paymentType === "club") {
      whereQuery.paymentType = "club";
    }
    // If no paymentType specified, get both

    // Create a more efficient query by joining first
    const { rows: payments, count: total } = await Payment.findAndCountAll({
      where: whereQuery,
      attributes: [
        "id",
        "paymentTransactionId",
        "paymentAmount",
        "paymentCurrency",
        "paymentStatus",
        "paymentRemarks",
        "userId",
        "paymentType",
        "paymentDate",
        "razorpayResponse", // Added to access payment details
      ],
      include: [
        {
          model: Tournament,
          as: "tournament",
          attributes: ["title", "clubId"],
          where: tournamentTitle ? { title: tournamentTitle } : {},
          required: tournamentTitle ? true : false,
        },
        {
          model: User,
          as: "user",
          attributes: ["cbid"],
          include: [
            {
              model: ClubDetail,
              attributes: ["clubId", "clubName"],
              required: false,
            },
          ],
        },
        {
          model: BulkRegistration,
          as: "bulkRegistration",
          attributes: ["playersCount", "totalAmount"],
          required: false,
        },
      ],
      order: [["paymentDate", "DESC"]],
    });

    if (total === 0) {
      return sendResponse(res, 204, {
        success: true,
        data: {
          payments: [],

        },
      });
    }
    // Filter payments for the club
    const filteredPayments = payments.filter(
      (payment) => payment.tournament?.title === tournamentTitle
    );

    // Transform the data to include clear payment type info
    const formattedPayments = filteredPayments.map((payment) => {
      const basePayment = {
        id: payment.id,
        paymentTransactionId: payment.paymentTransactionId,
        paymentAmount: payment.paymentAmount,
        refoundAmount:
          payment.paymentStatus === "refunded" ? payment.paymentAmount : 0,
        paymentCurrency: payment.paymentCurrency,
        paymentStatus: payment.paymentStatus,
        paymentDate: payment.paymentDate,
        paymentRemarks: payment.paymentRemarks,
        tournamentTitle: payment.tournament?.title || "",
        paymentType: payment.paymentType || "player",
        cbid: payment.user?.cbid || null,
      };

      // Extract player information from razorpayResponse if available
      let playerName = null;
      let playerEmail = null;
      let playerPhone = null;

      if (payment.razorpayResponse && payment.razorpayResponse.notes) {
        const notes = payment.razorpayResponse.notes;
        playerName = notes.playerName || notes.clubName || null;
        playerEmail =
          notes.playerEmail ||
          notes.clubEmail ||
          payment.razorpayResponse.email ||
          null;
        playerPhone = notes.playerPhone || notes.clubPhone || null;
      }

      // Add specific details based on payment type
      if (payment.paymentType === "club") {
        return {
          ...basePayment,
          playersCount:
            payment.bulkRegistration?.playersCount ||
            (payment.razorpayResponse?.notes?.playerCount
              ? parseInt(payment.razorpayResponse.notes.playerCount)
              : 0),
          totalAmount:
            payment.bulkRegistration?.totalAmount || payment.paymentAmount,
          registrationType: "Bulk Registration",
          clubId: payment.user?.ClubDetail?.clubId || null,
          clubName:
            payment.user?.ClubDetail?.clubName || playerName || "Unknown Club",
          clubEmail: playerEmail,
          clubPhone: playerPhone,
        };
      } else {
        return {
          ...basePayment,
          playerName: playerName,
          playerEmail: playerEmail,
          playerPhone: playerPhone,
          registrationType: "Player Registration",
        };
      }
    });

    return sendResponse(res, 200, {
      success: true,
      data: {
        payments: formattedPayments,
        // total: filteredPayments.length,
        // currentPage: page,
        // totalPages: Math.ceil(filteredPayments.length / limit),
      },
    });
  } catch (error) {
    console.error("Error in getClubEarnings:", error);
    handleError(res, error);
  }
};

const updatePlayerProfile = async (req, res) => {
  try {
    const { id } = req.params;
    const data = req.body;
    if (!id) {
      return sendResponse(res, 400, {
        success: false,
        error: "Player id is required",
      });
    }
    if (!data) {
      return sendResponse(res, 400, {
        success: false,
        error: "Data is required",
      });
    }

    const user = await User.findOne({
      where: { id },

      include: [PlayerDetail],
    });

    if (!user) {
      return sendResponse(res, 404, {
        success: false,
        error: "User not found",
      });
    }

    if (!user.PlayerDetail) {
      return sendResponse(res, 404, {
        success: false,
        error: "Player detail not found",
      });
    }
    const { name, phoneNumber, email } = data;
    if (name) {
      await User.update({ name }, { where: { id } });
    }
    if (phoneNumber) {
      await User.update({ phoneNumber }, { where: { id } });
    }
    if (email) {
      await User.update({ email }, { where: { id } });
    }
    const playerData = {
      ...data,
      profileUrl: req?.file
        ? req?.file?.location
        : user.PlayerDetail?.profileUrl,
    };
    delete playerData.name;
    delete playerData.phoneNumber;
    delete playerData.email;

    const [count, rows] = await PlayerDetail.update(
      { ...playerData },
      {
        where: { userId: id },
        returning: true,
      }
    );
    if (req?.file && user.PlayerDetail?.profileUrl) {
      deleteFromS3(user.PlayerDetail?.profileUrl);
    }
    if (count === 0) {
      return sendResponse(res, 404, {
        success: false,
        error: "Player not found",
      });
    }
    sendResponse(res, 200, {
      success: true,
      data: rows[0],
    });
  } catch (error) {
    deleteFromS3(req?.file?.location);
    handleError(res, error);
  }
};
const updateClubProfile = async (req, res) => {
  try {
    const { id } = req.params;
    const data = req.body;
    if (!id) {
      return sendResponse(res, 400, {
        success: false,
        error: "Club id is required",
      });
    }
    if (!data) {
      return sendResponse(res, 400, {
        success: false,
        error: "Data is required",
      });
    }
    const clubDetail = await ClubDetail.findOne({
      where: { id: id },
    });
    if (!clubDetail) {
      return sendResponse(res, 404, {
        success: false,
        error: "Club detail not found",
      });
    }
    const clubData = {
      ...data,
      profileUrl: req?.file ? req?.file?.location : clubDetail?.profileUrl,
    };
    const [count, rows] = await ClubDetail.update(
      { ...clubData },
      {
        where: { id: id },
        returning: true,
      }
    );
    if (req?.file && clubDetail?.profileUrl) {
      deleteFromS3(clubDetail?.profileUrl);
    }
    if (count === 0) {
      return sendResponse(res, 404, {
        success: false,
        error: "Club not found",
      });
    }
    sendResponse(res, 200, {
      success: true,
      data: rows[0],
    });
  } catch (error) {
    deleteFromS3(req?.file?.location);

    handleError(res, error);
  }
};
const updateArbiterProfile = async (req, res) => {
  try {
    const { id } = req.params;
    const data = req.body;
    if (!id) {
      return sendResponse(res, 400, {
        success: false,
        error: "Arbiter id is required",
      });
    }
    if (!data) {
      return sendResponse(res, 400, {
        success: false,
        error: "Data is required",
      });
    }
    const { email, phoneNumber, name } = data;
    if (email) {
      await User.update({ email }, { where: { id } });
    }
    if (phoneNumber) {
      await User.update({ phoneNumber }, { where: { id } });
    }
    if (name) {
      await User.update({ name }, { where: { id } });
    }
    delete data.email;
    delete data.phoneNumber;
    delete data.name;

    const arbiterDetail = await ArbiterDetails.findOne({
      where: { userId: id },
    });
    if (!arbiterDetail) {
      return sendResponse(res, 404, {
        success: false,
        error: "Arbiter detail not found",
      });
    }
    const arbiterData = {
      ...data,
      profileUrl: req?.file ? req?.file?.location : arbiterDetail?.profileUrl,
    };
    const [count, rows] = await ArbiterDetails.update(
      { ...arbiterData },
      {
        where: { userId: id },
        returning: true,
      }
    );
    if (req?.file && arbiterDetail?.profileUrl) {
      deleteFromS3(arbiterDetail?.profileUrl);
    }
    if (count === 0) {
      return sendResponse(res, 404, {
        success: false,
        error: "Arbiter not found",
      });
    }
    sendResponse(res, 200, {
      success: true,
      data: rows[0],
    });
  } catch (error) {
    deleteFromS3(req?.file?.location);
    handleError(res, error);
  }
};
const getRegisteredPlayers = async (req, res) => {
  try {
    const { title } = req.params;

    if (!title) {
      return sendResponse(res, 422, {
        success: false,
        error: "Invalid tournament title",
      });
    }
    const newTitle = decodeURIComponent(title);

    const {
      tournamentId,
    } = req.query;







    // Tournament where clause
    const tournamentWhereClause = {
      title: newTitle,
    };


    // Execute query with proper includes
    const { rows: registrations, count: total } =
      await Registration.findAndCountAll({
        where: { tournamentTitle: newTitle },
        include: [
          {
            model: User,
            as: "player",

            attributes: ["cbid", "name", 'email', 'phoneNumber'],
            include: [
              {
                model: PlayerDetail,
                attributes: [
                  "playerTitle",
                  "fideRating",
                  "fideId",
                  "aicfId",
                  "districtId",
                  "stateId",
                  "club",
                ],
                required: true,
              },
            ],
            required: true,
          },
          {
            model: Tournament,
            as: "tournament",
            where: tournamentWhereClause,
            attributes: ["title", "startDate", "endDate"],
            required: true,
          },
        ],
      });

    // Handle empty results
    if (total === 0 || registrations.length === 0) {
      return sendResponse(res, 200, {
        // Changed from 204 to 200 for consistency
        success: true,
        data: {
          players: [],
        },
      });
    }

    // Format registrations
    const formattedRegistrations = registrations.map((reg) => ({
      cbid: reg.player?.cbid,
      playerName: reg.player?.name,
      name: reg.player?.name,
      phone: reg.player?.phoneNumber,
      email: reg.player?.email,
      fideRating: reg.player?.PlayerDetail?.fideRating,
      fideId: reg.player?.PlayerDetail?.fideId,
      aicfId: reg.player?.PlayerDetail?.aicfId,
      stateId: reg.player?.PlayerDetail?.stateId,
      club: reg.player?.PlayerDetail?.club,
      tournamentTitle: reg.tournament?.title,
      attendanceMark: reg.attendanceMark,
      districtId: reg.player?.PlayerDetail?.districtId,
      playerTitle: reg.player?.PlayerDetail?.playerTitle,
      registrationId: reg.id,
      startDate: reg.tournament?.startDate,
      endDate: reg.tournament?.endDate,
      status: reg.status,
    }));

    // Return success response
    return sendResponse(res, 200, {
      success: true,
      data: {
        players: formattedRegistrations,
      },
    });
  } catch (error) {
    console.error("Error getting registered players:", error);
    return handleError(res, error);
  }
};


const TournamentReferralPlayer = async (req, res) => {
  try {
    const { tournamentId, referral } = req.query;

    if (!tournamentId) {
      return sendResponse(res, 404, {
        success: false,
        error: "tournament Id is required",
      });
    }

    const registrations = await Registration.findAll({
      where:{tournamentId},
      attributes:['id'],
      include: {
        model: User,
        as: 'referredBy',
        where: referral ? { cbid: referral } : undefined,
        attributes: ['name', 'cbid', 'email','phoneNumber'],
        required: true, // ensures only those with referral
      },
    });

    if (!registrations.length) {
      return sendResponse(res, 200, { success: true, data: [] });
    }

    // Group by cbid
    const grouped = {};

    registrations.forEach((reg) => {
      const ref = reg.referredBy;

      if (!ref?.cbid) return;

      if (!grouped[ref.cbid]) {
        grouped[ref.cbid] = {
          cbid: ref.cbid,
          name: ref.name,
          email: ref.email,
          phone: ref.phoneNumber,
          referralCount: 0,
          // players: []
        };
      }

      grouped[ref.cbid].referralCount += 1;
      // grouped[ref.cbid].players.push({
      //   playerId: reg.playerId,
      //   regId: reg.regId,
      //   genderCategory: reg.genderCategory,
      //   ageCategory: reg.ageCategory,
      //   status: reg.status
      // });
    });

    // Convert grouped object to array
    const referralData = Object.values(grouped);

    return sendResponse(res, 200, {
      success: true,
      data: referralData
    });
  } catch (e) {
    console.error("Error getting Tournament Referral list:", e);
    return handleError(res, e);
  }
};

const PlayerReferralPlayer = async (req, res) => {
  try {
    const { cbid, name, email, page = 1, limit = 10 } = req.query;

    // Convert pagination params to numbers
    const pageNum = parseInt(page, 10);
    const limitNum = parseInt(limit, 10);
    const offset = (pageNum - 1) * limitNum;

    let whereClause = { role: 'player' };
    if (cbid) whereClause.cbid = cbid;
    if (name) whereClause.name = { [Op.iLike]: `%${name}%` };
    if (email) whereClause.email = { [Op.iLike]: `%${email}%` };

    // Find users with pagination
    const { count, rows: users } = await User.findAndCountAll({ 
      where: whereClause,
      limit: limitNum,
      offset: offset,
      order: [['createdAt', 'DESC']] // Optional: order by creation date
    });

    // Check if any users were found
    if (!users || users.length === 0) {
      return res.status(201).json({
        success: false,
         data: [],
        pagination: {
          currentPage: pageNum,
          totalPages: 0,
          totalRecords: 0,
          limit: limitNum
        }
      });
    }

    const results = [];

    // Loop through each found user
    for (const user of users) {
      const referredUsers = await User.findAll({
        where: { referralId: user.cbid }
      });

      results.push({
        user: user.toJSON(),
        referralCount: referredUsers.length,
        referrals: referredUsers.map(ref => ref.toJSON()) // Convert to JSON for consistency
      });
    }

    // Calculate pagination info
    const totalPages = Math.ceil(count / limitNum);

    return res.status(200).json({
      success: true,
      data: results,
      pagination: {
        currentPage: pageNum,
        totalPages: totalPages,
        totalRecords: count,
        limit: limitNum,
        hasNextPage: pageNum < totalPages,
        hasPreviousPage: pageNum > 1
      }
    });

  } catch (error) {
    console.error("Error fetching user and referrals:", error);
    return res.status(500).json({
      success: false,
      message: "Internal server error",
    });
  }
};


const getAllTournamentsForReferral = async (req, res) => {
  try {

    const { page, limit = 3, title, status, } = req.query;
    const offset = (page - 1) * limit;
    const whareClause = {
      tournamentStatus: { [Op.ne]: "archived" },
    };

    const searchTitle = title?.trim().toLowerCase().replace(/\s+/g, "-");
    if (title) whareClause.title = { [Op.iLike]: `%${searchTitle}%` };
    if (status) {
      if (status === "upcoming") {
        whareClause.startDate = { [Op.gt]: new Date() };
      } else if (status === "completed") {
        whareClause.endDate = { [Op.lt]: new Date() };
      } else if (status === "in-progress") {
        whareClause.startDate = { [Op.lte]: new Date() };
        whareClause.endDate = { [Op.gte]: new Date() };
      } else {
        whareClause.tournamentStatus = status;
      }
    }

    const { rows: tournaments, count: total } =
      await Tournament.findAndCountAll({
        where: whareClause,
        attributes: [
          "id",
          "title",
          "startDate",
          "endDate",
          "registrationEndDate",
          "city",
          "tournamentStatus",
        ],
        offset,
        limit,
      });
    sendResponse(res, 200, {
      success: true,
      data: {
        tournaments,
        total,
        currentPage: page,
        totalPages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    handleError(res, error);
  }
};

const getSingleCoach = async (req, res) => {
  try {
    const { id } = req.params;
    if (!id || typeof id !== "string") {
      return sendResponse(res, 400, {
        success: false,
        error: "Invalid arbiter ID",
      });
    }

    const arbiter = await User.scope("adminAccess").findOne({
      where: { cbid: id },
      attributes: [
        "name",
        "cbid",
        "id",
        "email",
        "phoneNumber",
        "isActive",
        "isAccess",
      ],
      include: [
        {
          model: CoachDetails,
          attributes: [
            "title",
            "dob",
            "about",
            "profileUrl",
            "stateId",
            "country",
            "state",
            "district",
            "city",
            "pincode",
            "profileLinks",
          ],
          required: true,
        },
      ],
    });
    if (!arbiter) {
      return sendResponse(res, 404, {
        success: false,
        error: "Arbiter not found",
      });
    }

    sendResponse(res, 200, {
      success: true,
      data: arbiter,
    });
  } catch (error) {
    handleError(res, error);
  }
};

const updateCoachProfile = async (req, res) => {
  try {
    const { id } = req.params;
    const data = req.body;
    if (!id) {
      return sendResponse(res, 400, {
        success: false,
        error: "Coach id is required",
      });
    }
    if (!data) {
      return sendResponse(res, 400, {
        success: false,
        error: "Data is required",
      });
    }
    const { email, phoneNumber, name } = data;
    if (email) {
      await User.update({ email }, { where: { id } });
    }
    if (phoneNumber) {
      await User.update({ phoneNumber }, { where: { id } });
    }
    if (name) {
      await User.update({ name }, { where: { id } });
    }
    delete data.email;
    delete data.phoneNumber;
    delete data.name;

    const coachDetail = await CoachDetails.findOne({
      where: { userId: id },
    });
    if (!coachDetail) {
      return sendResponse(res, 404, {
        success: false,
        error: "Arbiter detail not found",
      });
    }
    const coachData = {
      ...data,
      profileUrl: req?.file ? req?.file?.location : coachDetail?.profileUrl,
    };
    const [count, rows] = await ArbiterDetails.update(
      { ...coachData },
      {
        where: { userId: id },
        returning: true,
      }
    );
    if (req?.file && coachDetail?.profileUrl) {
      deleteFromS3(coachDetail?.profileUrl);
    }
    if (count === 0) {
      return sendResponse(res, 404, {
        success: false,
        error: "Arbiter not found",
      });
    }
    sendResponse(res, 200, {
      success: true,
      data: rows[0],
    });
  } catch (error) {
    deleteFromS3(req?.file?.location);
    handleError(res, error);
  }
};


module.exports = {
  getSinglePlayer,
  getClubDetailById,
  getSingleArbiter,
  getPayment,
  getAllTournament,
  getClubEarnings,
  updatePlayerProfile,
  updateClubProfile,
  updateArbiterProfile,
  getRegisteredPlayers,
  TournamentReferralPlayer,
  PlayerReferralPlayer,
  getAllTournamentsForReferral,
  getSingleCoach,
  updateCoachProfile,
};

const getDateRange = (dateFilter) => {
  const today = new Date();

  switch (dateFilter) {
    case "7":
      const past7Days = new Date();
      past7Days.setDate(today.getDate() - 7);
      return [past7Days, today];

    case "30":
      const past30Days = new Date();
      past30Days.setDate(today.getDate() - 30);
      return [past30Days, today];

    case "all":
    default:
      return null; // No date filter
  }
};
