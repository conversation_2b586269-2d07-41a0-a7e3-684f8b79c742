const bcrypt = require("bcryptjs");
const jwt = require("jsonwebtoken");
const crypto = require("crypto");
const { User, Otp } = require("../../config/db").models;
const {
    loginSchema,
    registerSchema,
    resetPasswordSchema,
} = require("../../schema/authSchema");
const { config } = require("../../config/config");
const { sendResponse, handleError } = require("../../utils/apiResponse");
const { Op } = require("sequelize");
const emailService = require("../../utils/mailer/emailService");
const smsService = require("../../utils/sms/smsService");
const { sequelize } = require("../../config/db");


const sendOtp = async (req, res) => {
    try {
        const { type = "verify-admin", userId, email } = req.body;


        const user = await User.scope('adminAccess').findOne({
            where: { id: userId },
        });
        const phoneNumber = user.phoneNumber

        // Validate mobile number
        if (!phoneNumber) {
            return sendResponse(res, 400, {
                success: false,
                error: "Phone number is required",
            });
        }

        if (phoneNumber.length !== 10) {
            return sendResponse(res, 400, {
                success: false,
                error: "Phone number must be exactly 10 digits",
            });
        }

        if (type === "registration") {
            // Build where clauses separately to handle null/undefined safely
            const whereConditions = [];

            if (email) {
                whereConditions.push({ email: email });
            }

            if (phoneNumber) {
                whereConditions.push({ phoneNumber: phoneNumber });
            }

            // Only proceed with query if we have conditions to check
            if (whereConditions.length > 0) {
                const user = await User.scope('adminAccess').findOne({
                    where: {
                        [Op.or]: whereConditions,
                    },
                });

                if (user) {
                    // Safely check values with proper type handling
                    const hasMatchingEmail = email && user.email === email;
                    const hasMatchingPhone =
                        phoneNumber && String(user.phoneNumber) === String(phoneNumber);

                    let errorMessage = "This account is already registered";

                    if (hasMatchingEmail && hasMatchingPhone) {
                        errorMessage = "This email & phone number are already registered";
                    } else if (hasMatchingEmail) {
                        errorMessage = "This email is already registered";
                    } else if (hasMatchingPhone) {
                        errorMessage = "This phone number is already registered";
                    }

                    return sendResponse(res, 409, {
                        success: false,
                        error: { message: errorMessage },
                    });
                }
            }
        }

        // Validate userId for non-registration types
        if (type !== "registration" && !userId) {
            return sendResponse(res, 400, {
                success: false,
                error: { message: "userId is required for non-registration OTPs" },
            });
        }

        const now = new Date();
        const otpTTL = 10 * 60 * 1000; // 10 minutes

        // Using a transaction to prevent race conditions
        const result = await sequelize.transaction(async (transaction) => {
            // Construct where clause based on OTP type
            const whereClause = {
                type,
                platform: "sms",
            };

            // For registration, search by phone number
            // For verification or other types, search by userId
            if (type === "registration") {
                whereClause.phoneNumber = phoneNumber;
            } else {
                whereClause.userId = userId;
            }

            // Lock the row to prevent concurrent modifications
            let existingOtp = await Otp.findOne({
                where: whereClause,
                lock: transaction.LOCK.UPDATE,
                transaction,
            });

            let otpValue = Math.floor(100000 + Math.random() * 900000);

            if (existingOtp) {
                // Check if 1 minute has passed since last update
                const lastUpdatedTime = new Date(existingOtp.updatedAt);
                const oneMinuteAgo = new Date(now.getTime() - 60 * 1000); // 1 minute in milliseconds

                if (lastUpdatedTime > oneMinuteAgo) {
                    const timeElapsedMs = now - lastUpdatedTime;
                    const secondsRemaining = Math.ceil(
                        (60 * 1000 - timeElapsedMs) / 1000
                    );

                    return {
                        status: 429,
                        response: {
                            success: false,
                            error: {
                                message: `Please wait ${secondsRemaining} seconds before requesting another OTP.`,
                            },
                        },
                    };
                }

                // Update existing OTP
                await existingOtp.update(
                    {
                        otp: otpValue,
                        phoneNumber: phoneNumber, // Ensure phone number is updated for non-registration types
                        expiresAt: new Date(now.getTime() + otpTTL),
                    },
                    { transaction }
                );
            } else {
                // Create new OTP record
                await Otp.create(
                    {
                        phoneNumber: phoneNumber,
                        otp: otpValue,
                        type,
                        userId: type !== "registration" ? userId : null,
                        platform: "sms",
                        expiresAt: new Date(now.getTime() + otpTTL),
                    },
                    { transaction }
                );
            }

            return {
                status: 200,
                response: {
                    success: true,
                    data: {
                        message: "OTP sent successfully",
                    },
                },
                otp: otpValue,
            };
        });

        // If rate limited, return early with error response
        if (result.status === 429) {
            return sendResponse(res, result.status, result.response);
        }

        // Send the actual SMS
        await smsService.sendOtpSMS({
            mobile: phoneNumber,
            otp: result.otp,
            expiryMinutes: Math.floor(otpTTL / (60 * 1000)),
        });

        return sendResponse(res, result.status, {
            success: true,
            message: "OTP sent successfully",
        });
    } catch (error) {
        console.error("Error in sendOtp:", error);
        return handleError(res, error);
    }
};

const userBlack = async (req, res) => {
    const { value, userId } = req.body;
    
    if (!userId ) {
        return sendResponse(res, 400, {
            success: false,
            error: "User details are required",
        });
    }

    // Convert value to boolean if needed
    const isActive = String(value).toLowerCase();

    try {

        const [updatedRowsCount] = await User.scope('adminAccess').update(
            { isActive },
            { where: { id: userId } }
        );

        if (updatedRowsCount === 0) {
            return sendResponse(res, 404, {
                success: false,
                error: "User not found or no changes applied",
            });
        }

        return sendResponse(res, 200, {
            success: true,
            message: `User ${isActive ? "unbanned" : "banned"} successfully`,
            Count:updatedRowsCount,
        });
    } catch (error) {
        console.error(error);
        return sendResponse(res, 500, {
            success: false,
            error: "Server error while updating user",
        });
    }
};

const verifyOtp = async (req, res) => {
    try {
        const { userId, otp } = req.body;

        const user = await User.scope('adminAccess').findOne({
            where: { id: userId },
        });
        const phoneNumber = user.phoneNumber


        if (!phoneNumber || !otp) {
            return sendResponse(res, 400, {
                success: false,
                error: "Phone number and OTP are required",
            });
        }

        const existingOtp = await Otp.findOne({
            where: { phoneNumber },
        });

        if (!existingOtp) {
            return sendResponse(res, 404, {
                success: false,
                error: "OTP not found. Please request a new one.",
            });
        }

        if (new Date(existingOtp.expiresAt) < new Date()) {
            return sendResponse(res, 400, {
                success: false,
                error: "OTP has expired. Please request a new one.",
            });
        }

        if (existingOtp.otp !== otp) {
            return sendResponse(res, 400, {
                success: false,
                error: "Incorrect OTP.",
            });
        }

        await existingOtp.destroy(); // OTP is used, so delete it

        return sendResponse(res, 200, {
            success: true,
            message: "OTP verified successfully",
        });
    } catch (error) {
        handleError(res, error);
    }
};


const userArchive = async (req, res) => {
    const { value, userId } = req.body;
    
    if (!userId ) {
        return sendResponse(res, 400, {
            success: false,
            error: "User details are required",
        });
    }

    // Convert value to boolean if needed
    const isAccess = String(value).toLowerCase();

    try {

        const [updatedRowsCount] = await User.scope('adminAccess').update(
            { isAccess },
            { where: { id: userId } }
        );

        if (updatedRowsCount === 0) {
            return sendResponse(res, 404, {
                success: false,
                error: "User not found or no changes applied",
            });
        }

        return sendResponse(res, 200, {
            success: true,
            message: `User ${isAccess ? "unArchived" : "Archived"} successfully`,
            Count:updatedRowsCount,
        });
    } catch (error) {
        console.error(error);
        return sendResponse(res, 500, {
            success: false,
            error: "Server error while updating user",
        });
    }
};

module.exports = {
    sendOtp,
    userBlack,
    verifyOtp,
    userArchive
};