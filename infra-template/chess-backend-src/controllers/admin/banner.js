const { Banner } = require("../../config/db").models;
const { sendResponse, handleError } = require("../../utils/apiResponse");
const { deleteFromS3 } = require("../../utils/s3");

// Create Banner
const createBanner = async (req, res) => {
  try {
    const { Url, type, format, country, city, state, name, bannerUrl, videoDuration } = req.body;

    if (!type || !format) {
      return sendResponse(res, 400, {
        success: false,
        message: "Missing required fields: type or format",
      });
    }

    if (!name) {
      return sendResponse(res, 400, {
        success: false,
        message: "Missing required field: name",
      });
    }

    if (!req.file && !Url) {
      return sendResponse(res, 400, {
        success: false,
        message: "Missing file or URL",
      });
    }

    if (Url && name === 'youtube-video') {
      const youtube = await Banner.findOne({ where: { url: Url } });
      if (youtube) {
        return sendResponse(res, 409, {
          success: false,
          message: "This youtube Url Already Exist",
        });
      }
    }

    const limit = format === 'image' ? 8 : 5;
    const { rows: banners, count } = await Banner.findAndCountAll({ where: { type, format, isActive: true } });
    // Determine the next rowIndex
    let nextRowIndex = null;
    if (banners.length < limit) {
      nextRowIndex = banners.length + 1;
    }

    const isActive = banners.length + 1 <= limit;
    const url = req.file?.location || Url;

    const newBanner = await Banner.create({
      url,
      isActive,
      type,
      format,
      country,
      city,
      state,
      name,
      rowIndex: nextRowIndex,
      bannerUrl,
      duration: videoDuration,
    });

    return sendResponse(res, 201, {
      success: true,
      data: newBanner,
      message: "Banner created successfully",
    });
  } catch (error) {
    return handleError(res, error);
  }
};

// Get All Banners with Pagination
const getAllBanners = async (req, res) => {
  try {
    const { type } = req.query
    let page = parseInt(req.query.page, 10) || 1;
    let limit = parseInt(req.query.limit, 10) || 10;
    if (page < 1) page = 1;
    const offset = (page - 1) * limit;

    const { rows: banners, count } = await Banner.findAndCountAll({
      where: { type }, offset, limit,
      order: [['rowIndex', 'ASC']]
    });
    const totalPages = Math.ceil(count / limit);

    let intervalTime = null
    if (type === 'banner') {
      intervalTime = banners.length > 0 ? banners[0].intervalTime : 3000;
    }

    return sendResponse(res, 200, {
      success: true,
      data: banners,
      intervalTime,
      pagination: { count, totalPages },
    });
  } catch (error) {
    return handleError(res, error);
  }
};

// Get Single Banner
const getBannerById = async (req, res) => {
  try {
    const { id } = req.params;
    const banner = await Banner.findOne({ where: { id } });

    if (!banner) {
      return sendResponse(res, 404, {
        success: false,
        error: "Banner not found",
      });
    }

    return sendResponse(res, 200, {
      success: true,
      data: banner,
    });
  } catch (error) {
    return handleError(res, error);
  }
};

// Update Banner
const updateBanner = async (req, res) => {
  try {
    const { id } = req.params;
    const { name, country, state, city, type, format, url } = req.body;

    const [updatedCount] = await Banner.update(
      { name, country, state, city, type, format, url },
      { where: { id } }
    );

    if (updatedCount === 0) {
      return sendResponse(res, 404, {
        success: false,
        error: "Banner not found or no changes made",
      });
    }

    return sendResponse(res, 200, {
      success: true,
      message: "Banner updated successfully",
    });
  } catch (error) {
    console.error("error while updating Banner", error)
    return handleError(res, error);
  }
};

// Delete Banner
const deleteBanner = async (req, res) => {
  try {
    const { id } = req.params;

    const banner = await Banner.findOne({ where: { id } });
    if (!banner) {
      return sendResponse(res, 404, {
        success: false,
        error: "Banner not found",
      });
    }

    if (banner.isActive) {
      return sendResponse(res, 409, {
        success: false,
        error: "Deactivate banner before deletion.",
      });
    }

    if (banner.format !== 'video') {
      deleteFromS3(banner.url);
    }

    await Banner.destroy({ where: { id } });

    return sendResponse(res, 200, {
      success: true,
      message: "Banner deleted successfully",
    });
  } catch (error) {
    return handleError(res, error);
  }
};

// Update isActive status
const updateBannerStatus = async (req, res) => {
  try {
    const { id, isActive, type, format, index } = req.body;

    if (!id || typeof isActive !== "boolean" || !type || !format |!index) {
      return sendResponse(res, 400, {
        success: false,
        error: "Missing or invalid parameters",
      });
    }

    const limit = format === 'image' ? 8 : 5;
    if (isActive === true) {
      const activeBanners = await Banner.findAll({
        where: { type, isActive: true },
      });

      if (activeBanners.length >= limit) {
        return sendResponse(res, 401, {
          success: false,
          error: `An active banner already exists for type '${type}'. Deactivate it first.`,
        });
      }
    }

    if (isActive === true){
    const existingIndex = await Banner.findOne({ where: { type, rowIndex: index, isActive :'true' } })
    if (existingIndex) {
      return sendResponse(res, 409, {
        success: false,
        error: `Already have this index so change the in dex.`,
      });
    }
  }

    const [updatedCount] = await Banner.update({ isActive }, { where: { id } });

    if (updatedCount === 0) {
      return sendResponse(res, 404, {
        success: false,
        error: "Banner not found",
      });
    }

    return sendResponse(res, 200, {
      success: true,
      message: `${type} has been ${isActive ? 'activated' : 'deactivated'} successfully.`,
    });
  } catch (error) {
    console.error("error while updating isActive", error)
    return handleError(res, error);
  }
};

// Update Banner
const updateBannerIndex = async (req, res) => {
  try {
    const { id, index, format } = req.body;

    // Basic input validation
    if (!id || index === undefined || index === null) {
      return sendResponse(res, 400, {
        success: false,
        error: "Both 'id' and 'index' are required.",
      });
    }

    // Optional: If index must be a number
    if (typeof index !== "number" || index < 0) {
      return sendResponse(res, 400, {
        success: false,
        error: "'index' must be a non-negative number.",
      });
    }

    const existingIndex = await Banner.findOne({
      where: { rowIndex: index, isActive: true, format },
    });

    if (existingIndex) {
      return sendResponse(res, 400, {
        success: false,
        error: "You have Already Active for this Row please InActive First ",
      });
    }

    const [updatedCount] = await Banner.update({ rowIndex: index }, { where: { id } });

    if (updatedCount === 0) {
      return sendResponse(res, 404, {
        success: false,
        error: "Banner not found or no changes made",
      });
    }

    return sendResponse(res, 200, {
      success: true,
      message: "Banner updated successfully",
    });
  } catch (error) {
    console.error("Update Banner Error:", error);
    return handleError(res, error);
  }
};

// Update Banner
const updateBannerInterval = async (req, res) => {
  try {
    const { interval, type } = req.body;
    //  console.log("detail",interval,type)
    // Basic input validation
    if (!interval || !type) {
      return sendResponse(res, 400, {
        success: false,
        error: "Both 'interval' and 'type' are required.",
      });
    }

    const [updatedCount] = await Banner.update({ intervalTime: interval }, { where: { type } });

    if (updatedCount === 0) {
      return sendResponse(res, 404, {
        success: false,
        error: "Banner not found or no changes made",
      });
    }

    return sendResponse(res, 200, {
      success: true,
      message: "Banner updated successfully",
    });
  } catch (error) {
    console.error("Update Banner Error:", error);
    return handleError(res, error);
  }
};

module.exports = {
  createBanner,
  getAllBanners,
  getBannerById,
  updateBanner,
  deleteBanner,
  updateBannerStatus,
  updateBannerIndex,
  updateBannerInterval
};
