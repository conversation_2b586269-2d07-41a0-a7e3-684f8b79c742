const express = require('express');
const router = express.Router();
const { controller, uploadMiddleware } = require('../controllers/pairingImportController');
const verifyJwt = require('../middlewares/verifyJwt');

/**
 * @route POST /api/pairing-import/upload
 * @desc Upload and process an Excel file to import pairings
 * @access Private
 */
router.post(
  '/upload',
  // verifyJwt,
  uploadMiddleware.single('file'),
  controller.uploadAndProcess
);



/**
 * @route GET /api/pairing-import
 * @desc Get all pairings
 * @access Private
 */
router.get(
  '/',
  verifyJwt,
  controller.getAll
);

/**
 * @route GET /api/pairing-import/:id
 * @desc Get a single pairing by ID
 * @access Private
 */
router.get(
  '/:id',
  controller.getById
);

/**
 * @route DELETE /api/pairing-import/:id
 * @desc Delete a pairing by ID
 * @access Private
 */
router.delete(
  '/:id',
  verifyJwt,
  controller.deleteById
);

/**
 * @route DELETE /api/pairing-import/tournament/:tournament_id
 * @desc Delete all pairings for a tournament
 * @access Private
 */
router.delete(
  '/tournament/:tournament_id',
  verifyJwt,
  controller.deleteByTournament
);

/**
 * @route DELETE /api/pairing-import/tournament/:tournament_id/round/:round_id
 * @desc Delete all pairings for a tournament and round
 * @access Private
 */
router.delete(
  '/tournament/:tournament_id/round/:round_id',
  verifyJwt,
  controller.deleteByTournamentAndRound
);

router.get(
  '/tournament/pairing/round',

  controller.currentRound
);

module.exports = router;
