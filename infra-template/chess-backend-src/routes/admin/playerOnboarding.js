const express = require('express');
const router = express.Router();
const { 
  onboardPlayersUpload, 
  upload,
  requiredFields,
  optionalFields 
} = require('../../controllers/admin/playerOnboardingController');
const verifyJwt = require('../../middlewares/verifyJwt');

/**
 * @route POST /api/v1/admin/clubs/onboard-players/upload
 * @desc Upload Excel/CSV file to onboard multiple players for a club
 * @access Private (Admin only)
 * @body {FormData} file - Excel/CSV file containing player data
 * @body {string} clubId - ID of the club to associate players with
 */
router.post(
  '/upload',
  verifyJwt,
  upload.single('file'),
  onboardPlayersUpload
);

/**
 * @route GET /api/v1/admin/clubs/onboard-players/template
 * @desc Get the required fields template for player onboarding
 * @access Private (Admin only)
 */
router.get('/template', verifyJwt, (req, res) => {
  res.json({
    success: true,
    data: {
      requiredFields: requiredFields,
      optionalFields: optionalFields,
      exampleMapping: {
        'Player Name': '<PERSON>',
        'Email': '<EMAIL>',
        'Phone Number': '+91-9876543210',
        'Date of Birth': '1995-05-15 or 15-05-1995 or 15/05/1995',
        'Address': '123 Main Street',
        'City': 'Chennai',
        'State': 'Tamil Nadu',
        'District': 'Chennai',
        'Country': 'India',
        'Pincode': '600001',
        'Parent/Guardian Name': 'Jane Doe',
        'Emergency Contact': '+91-9876543211',
        'FIDE ID': '12345678 (optional)',
        'AICF ID': 'AICF123 (optional)',
        'FIDE Rating': '1500 (optional)',
        'Gender': 'male/female/other (optional)'
      },
      instructions: [
        'Upload Excel (.xlsx, .xls) or CSV files only',
        'Maximum file size: 10MB',
        'First row should contain column headers',
        'Date format: YYYY-MM-DD, DD-MM-YYYY, or DD/MM/YYYY',
        'Phone numbers should include country code',
        'Email addresses must be valid and unique',
        'All required fields must be filled',
        'Players will be automatically associated with the specified club'
      ]
    }
  });
});

/**
 * @route GET /api/v1/admin/clubs/onboard-players/sample
 * @desc Download a sample Excel template for player onboarding
 * @access Private (Admin only)
 */
router.get('/sample', verifyJwt, (req, res) => {
  const XLSX = require('xlsx');
  
  // Create sample data
  const sampleData = [
    {
      'Player Name': 'John Doe',
      'Email': '<EMAIL>',
      'Phone Number': '+91-9876543210',
      'Date of Birth': '1995-05-15',
      'Address': '123 Main Street, Sector 1',
      'City': 'Chennai',
      'State': 'Tamil Nadu',
      'District': 'Chennai',
      'Country': 'India',
      'Pincode': '600001',
      'Parent/Guardian Name': 'Jane Doe',
      'Emergency Contact': '+91-9876543211',
      'FIDE ID': '12345678',
      'AICF ID': 'AICF123',
      'FIDE Rating': '1500',
      'Gender': 'male'
    },
    {
      'Player Name': 'Alice Smith',
      'Email': '<EMAIL>',
      'Phone Number': '+91-9876543212',
      'Date of Birth': '1998-08-20',
      'Address': '456 Oak Avenue, Block B',
      'City': 'Mumbai',
      'State': 'Maharashtra',
      'District': 'Mumbai',
      'Country': 'India',
      'Pincode': '400001',
      'Parent/Guardian Name': 'Bob Smith',
      'Emergency Contact': '+91-9876543213',
      'FIDE ID': '87654321',
      'AICF ID': 'AICF456',
      'FIDE Rating': '1650',
      'Gender': 'female'
    }
  ];
  
  // Create workbook and worksheet
  const workbook = XLSX.utils.book_new();
  const worksheet = XLSX.utils.json_to_sheet(sampleData);
  
  // Add worksheet to workbook
  XLSX.utils.book_append_sheet(workbook, worksheet, 'Players');
  
  // Generate buffer
  const buffer = XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });
  
  // Set headers for file download
  res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
  res.setHeader('Content-Disposition', 'attachment; filename=player_onboarding_template.xlsx');
  
  // Send file
  res.send(buffer);
});

module.exports = router;
