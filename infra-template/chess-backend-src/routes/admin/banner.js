const express = require('express');
const router = express.Router();


const { uploadFactory, handleUploadError } = require('../../utils/s3');
const { createBanner, getAllBanners, getBannerById, updateBanner, updateBannerStatus, deleteBanner, updateBannerIndex, updateBannerInterval 

} = require('../../controllers/admin/banner');

// Update Banner interval
router.put('/interval',updateBannerInterval );

// Update Banner rowIndex
router.put('/index', updateBannerIndex);

// Update Banner status
router.put('/status', updateBannerStatus);

// Create Banner
router.post('/', uploadFactory.advertisement.image(),handleUploadError,createBanner);

// Create Banner
router.post('/videos', uploadFactory.advertisement.video(),handleUploadError,createBanner);

// Get All Banners
router.get('/', getAllBanners);

// Get Banner by ID
router.get('/:id', getBannerById);

// Update Banner
router.put('/:id', updateBanner);

// Delete Banner
router.delete('/:id', deleteBanner);

module.exports = router;
