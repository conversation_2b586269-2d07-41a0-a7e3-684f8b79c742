const express = require("express");
const router = express.Router({ mergeParams: true });

const {
  
  checkRegistration,
  attendanceMark,
  getPendingBulkRegisters,
  bulk_Player_Register,
  updateBulkRegister,
  bulk_Player_get
} = require("../controllers/registerController");
const verifyJwt = require("../middlewares/verifyJwt");
router.get("/status", verifyJwt, checkRegistration);
router.patch("/attendance", verifyJwt,attendanceMark );
router.post("/bulk-register",verifyJwt, bulk_Player_Register);
router.get("/pending-bulk-registers", verifyJwt, getPendingBulkRegisters);
router.put("/bulk-register", verifyJwt, updateBulkRegister);
router.get("/bulk-register/:id", verifyJwt, bulk_Player_get);



module.exports = router;
