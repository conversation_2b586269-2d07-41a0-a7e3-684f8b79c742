const express = require('express');
const { createCoachDetail,
        getAllCoachDetails,
        getCoachDetailById,
        updateCoachDetail,
        deleteCoachDetail,
        getCoachProfile,
        removeProfileImage
        } = require('../controllers/CoachController');
const { uploadFactory, handleUploadError } = require('../utils/s3');
const verifyJwt = require('../middlewares/verifyJwt');
const router = express.Router();
const profileRouter = express.Router();

router.use("/profile", profileRouter);
// Routes

// Create coach detail
profileRouter.post(
  "/",
  verifyJwt,
  uploadFactory.coach.profileImage(),
  handleUploadError,
  createCoachDetail
);

// Get all coach details with pagination and filtering
router.get('/', getAllCoachDetails);

// Get coach detail by ID
router.get('/:id',  getCoachDetailById);

// Get coach detail by user ID
profileRouter.get('/', verifyJwt,getCoachProfile);

// Update coach detail
profileRouter.put('/update',verifyJwt,uploadFactory.coach.profileImage(),handleUploadError,updateCoachDetail);

// Partial update coach detail
profileRouter.patch('/update', verifyJwt,uploadFactory.coach.profileImage(),handleUploadError, updateCoachDetail);

profileRouter.delete("/remove-profile-image", verifyJwt, removeProfileImage);

// Delete coach detail
router.delete('/:id',  deleteCoachDetail);

module.exports = router;