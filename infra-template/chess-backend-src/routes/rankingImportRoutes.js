const express = require('express');
const router = express.Router();
const { controller, uploadMiddleware } = require('../controllers/rankingImportController');
const verifyJwt = require('../middlewares/verifyJwt');

/**
 * @route POST /api/ranking-import/upload
 * @desc Upload and process an Excel file to import rankings
 * @access Private
 */
router.post(
  '/upload',
  uploadMiddleware.single('file'),
  controller.uploadAndProcess
);

/**
 * @route GET /api/ranking-import
 * @desc Get all rankings
 * @access Private
 */
router.get(
  '/',
  verifyJwt,
  controller.getAll
);

/**
 * @route GET /api/ranking-import/:name
 * @desc Get a single ranking by name
 * @access Private
 */
router.get(
  '/search',
  verifyJwt,
  controller.getByName
);


/**
 * @route GET /api/ranking-import/:id
 * @desc Get a single ranking by ID
 * @access Private
 */
router.get(
  '/:id',
  verifyJwt,
  controller.getById
);

/**
 * @route DELETE /api/ranking-import/:id
 * @desc Delete a ranking by ID
 * @access Private
 */
router.delete(
  '/:id',
  verifyJwt,
  controller.deleteById
);

/**
 * @route DELETE /api/ranking-import/tournament/:tournament_id
 * @desc Delete all rankings for a tournament
 * @access Private
 */
router.delete(
  '/tournament/:tournament_id',
  verifyJwt,
  controller.deleteByTournament
);

/**
 * @route DELETE /api/ranking-import/tournament/:tournament_id/round/:round_id
 * @desc Delete all rankings for a tournament and round
 * @access Private
 */
router.delete(
  '/tournament/:tournament_id/round/:round_id',
  verifyJwt,
  controller.deleteByTournamentAndRound
);


router.get(
  '/tournament/round',
  verifyJwt,
  controller.getCurrentRound
);

module.exports = router;
