const express = require('express');
const router = express.Router();
const { createCourse, getAllCourses, getCourseById, updateCourse, deleteCourse, getAllUpcomingCourses, getCourseByCoach, deactivateCourseByTitle, getCourse, getCourseAndUserDetails } = require('../controllers/courseDetails');
const verifyJwt = require('../middlewares/verifyJwt');
const { uploadFactory, handleUploadError } = require('../utils/s3');

router.get('/upcoming', verifyJwt,getAllUpcomingCourses);
router.post('/create', verifyJwt,uploadFactory.coach.profileImage(),handleUploadError,createCourse);
router.get('/', getAllCourses);
router.get('/coach',verifyJwt,getCourseByCoach);
router.get('/:title', getCourseById);
router.put('/update', verifyJwt,uploadFactory.coach.profileImage(),handleUploadError, updateCourse);
router.delete('/:id', deleteCourse);
router.post('/delete/:title',verifyJwt,deactivateCourseByTitle)
router.post('/register/:title',verifyJwt,getCourseAndUserDetails)

module.exports = router;
