const router = require("express").Router();
const authRoutes = require("./auth");
const userRoutes = require("./user");
const tournamentRoutes = require("./tournament");
const presignRoutes = require("./presign");
const playerRoutes = require("./player");
const clubRoutes = require("./club");
const paymentRoutes = require("./payment");
const locationRoutes = require("./location");
const rankingImportRoutes = require("./rankingImportRoutes");
const pairingImportRoutes = require("./pairingImportRoutes");
const arbiterRouter = require("./arbiter");
const notificationRoutes = require("./notification");
const reportRoutes = require("./reportRoutes");
const payoutRouter = require("./admin/payout");
const i18nRoutes = require("./i18n");

// const adminRoutes = require("./admin");
const verifyJwt = require("../middlewares/verifyJwt");
const verifyRole = require("../middlewares/verifyRole");
const adminRoutes = require("./admin/index");
const publicRoutes = require("./public");
const openRoutes = require("./open");
const coachRouter =require("./coach")
const courseRouter =require("./course")


router.use("/auth", authRoutes);
router.use("/user", userRoutes);
router.use("/tournament", tournamentRoutes);
router.use("/arbiter", arbiterRouter);
router.use("/player", playerRoutes);
router.use("/club", clubRoutes);
router.use("/presign", presignRoutes);
router.use("/payment", paymentRoutes);
router.use("/location", locationRoutes);
router.use("/ranking-import", rankingImportRoutes);
router.use("/pairing-import", pairingImportRoutes);
router.use("/notification", notificationRoutes);
router.use("/report", reportRoutes);
router.use('/admin-payout',payoutRouter)
router.use("/admin", verifyJwt, verifyRole(["admin"]), adminRoutes);
router.use("/public", publicRoutes);
router.use("/i18n", i18nRoutes);
router.use("/open", openRoutes);
router.use("/coach", coachRouter);
router.use("/course", courseRouter);

module.exports = router;
