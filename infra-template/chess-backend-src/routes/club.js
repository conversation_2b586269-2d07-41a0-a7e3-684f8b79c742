const express = require("express");
const {
  createClubProfile,
  editClubProfile,
  getClubProfile,
  getAllClubDetails,
  getClubDetailById,
  getRegisteredPlayers,
  clubEnquiry,
  clubInvite,
  getClubJoinRequest,
  updateClubJoinRequest,
  getAllClubPlayersForTournament,
  removePlayer,
  reportGenerate,
  removeProfileImage,
  getAllClubs,
} = require("../controllers/clubController");
const {
  createBankingDetails,
  getBankingDetails,
  checkVerificationStatus,
  togglePayoutStatus,
} = require("../controllers/bankVerificationController");
const { getClubMembers } = require("../controllers/membersController.js");
const verifyJwt = require("../middlewares/verifyJwt");
const { uploadFactory, handleUploadError } = require("../utils/s3");
const router = express.Router();
const profileRouter = express.Router();

const bankVerificationRouter = express.Router();
const membersRouter = express.Router();

// Profile Routes
profileRouter.post(
  "/",
  verifyJwt,
  uploadFactory.club.profileImage(),
  handleUploadError,
  createClubProfile
);
profileRouter.put(
  "/",
  verifyJwt,
  uploadFactory.club.profileImage(),
  handleUploadError,
  editClubProfile
);
profileRouter.get("/", verifyJwt, getClubProfile);
profileRouter.get("/bankdetails", verifyJwt, getBankingDetails);
profileRouter.post("/bankdetails", verifyJwt, createBankingDetails);
profileRouter.delete("/remove-profile-image", verifyJwt, removeProfileImage);

membersRouter.get("/", verifyJwt, getClubMembers);
membersRouter.get("/players", verifyJwt, getAllClubPlayersForTournament);
membersRouter.get("/join-request", verifyJwt, getClubJoinRequest);
membersRouter.post("/join-request", verifyJwt, updateClubJoinRequest);

bankVerificationRouter.post("/create", verifyJwt, createBankingDetails);
bankVerificationRouter.get("/details", verifyJwt, getBankingDetails);
bankVerificationRouter.get("/verify-status", verifyJwt, checkVerificationStatus);
bankVerificationRouter.post("/toggle-payout", verifyJwt, togglePayoutStatus);



router.get("/tournament/players", getRegisteredPlayers);

// Use Profile Routes
router.use("/profile", profileRouter);
router.use("/members", membersRouter);
router.use("/bank", bankVerificationRouter);
// General Club Routes
router.get("/", getAllClubDetails);
router.get("/single/:id", getClubDetailById);
router.post("/enquiry", verifyJwt, clubEnquiry);
router.post("/invite", verifyJwt, clubInvite);
router.post("/remove-player", verifyJwt, removePlayer);
router.get("/report", verifyJwt, reportGenerate);
router.get("/get/:id", verifyJwt, getAllClubs);

module.exports = router;
