const express = require("express");
const router = express.Router();
const authController = require("../controllers/authController");
const verifyJwt = require("../middlewares/verifyJwt");
// const verifyToken = require("../middleware/verifyToken");

router.post("/login", authController.login);
router.post("/send-otp", authController.sendOtp);
router.post("/register", authController.register);
router.get("/logout", verifyJwt, authController.logout);
router.get("/refresh", authController.refreshToken);
router.put("/forgotpassword", authController.resetPassword);
router.post("/contact-us", authController.ContactUS);
router.get("/me", verifyJwt, authController.authMe);
router.post("/send-email-otp", authController.sendEmailOtpForResetPassword);

module.exports = router;
