# Bank Verification and Payout API Documentation

## Overview
This API provides comprehensive bank verification and tournament payout functionality using RazorPay integration. Clubs can add their bank details, verify them through RazorPay's Fund Account Validation, and receive automated payouts for completed tournaments.

## Authentication
All endpoints require JWT authentication via the `Authorization` header:
```
Authorization: Bearer <jwt_token>
```

## Bank Verification Endpoints

### 1. Create Banking Details
**POST** `/api/club/bank/create`

Creates bank details and sets up complete RazorPay payout infrastructure.

**Request Body:**
```json
{
  "bankName": "State Bank of India",
  "AccountNumber": "**************",
  "branchIFSCCode": "SBIN0001234",
  "branchName": "Main Branch",
  "bankAccountType": "current",
  "bankAccountHolderName": "CHESS CLUB NAME"
}
```

**Response (201):**
```json
{
  "success": true,
  "data": {
    "message": "Banking details verified and payout setup completed",
    "id": "uuid",
    "verification_status": "completed",
    "payout_ready": true,
    "setup_details": {
      "contact_id": "cont_xxxxx",
      "fund_account_id": "fa_xxxxx",
      "validation_id": "fav_xxxxx",
      "amount_deposited": "₹1.00",
      "verified_at": "2024-01-01T10:00:00.000Z",
      "bank_name": "State Bank of India",
      "branch_name": "Main Branch"
    }
  }
}
```

### 2. Get Banking Details
**GET** `/api/club/bank/details`

Retrieves club's banking details with verification status.

**Response (200):**
```json
{
  "success": true,
  "data": {
    "id": "uuid",
    "bankName": "State Bank of India",
    "AccountNumber": "1234****1234",
    "branchIFSCCode": "SBIN0001234",
    "branchName": "Main Branch",
    "bankAccountType": "current",
    "bankAccountHolderName": "CHESS CLUB NAME",
    "isVerified": true,
    "isLocked": true,
    "payoutEnabled": true,
    "verification_details": {
      "status": "completed",
      "contact_id": "cont_xxxxx",
      "fund_account_id": "fa_xxxxx",
      "validation_id": "fav_xxxxx",
      "amount_deposited": "₹1.00",
      "verified_at": "2024-01-01T10:00:00.000Z",
      "payout_ready": true
    },
    "bank_info": {
      "bank_name": "State Bank of India",
      "branch": "Main Branch",
      "city": "Mumbai",
      "state": "Maharashtra",
      "rtgs": true,
      "neft": true,
      "imps": true
    }
  }
}
```

### 3. Check Verification Status
**GET** `/api/club/bank/verify-status`

Checks current verification status from RazorPay.

**Response (200):**
```json
{
  "success": true,
  "data": {
    "verification_status": "completed",
    "is_verified": true,
    "payout_ready": true,
    "amount_deposited": "₹1.00",
    "validation_details": {
      "id": "fav_xxxxx",
      "fund_account_id": "fa_xxxxx",
      "results": {
        "account_status": "active",
        "registered_name": "CHESS CLUB NAME"
      },
      "created_at": **********,
      "updated_at": **********
    }
  }
}
```

### 4. Toggle Payout Status
**POST** `/api/club/bank/toggle-payout`

Enable or disable payouts for verified accounts.

**Request Body:**
```json
{
  "enable": true
}
```

**Response (200):**
```json
{
  "success": true,
  "data": {
    "message": "Payouts enabled successfully",
    "payout_enabled": true
  }
}
```

## Payout Management Endpoints

### 5. Setup Club for Payouts
**POST** `/api/club/payout/setup`

One-time setup for club payout infrastructure (usually done automatically during bank creation).

**Response (201):**
```json
{
  "success": true,
  "message": "Payout setup completed successfully",
  "data": {
    "contact_id": "cont_xxxxx",
    "fund_account_id": "fa_xxxxx",
    "verification_status": "verified",
    "setup_date": "2024-01-01T10:00:00.000Z"
  }
}
```

### 6. Calculate Payout Preview
**POST** `/api/club/payout/calculate/:tournament_id`

Calculate payout amount for a tournament before creating actual payout.

**Request Body:**
```json
{
  "platformFeePercentage": 5,
  "processingFeePercentage": 2.5,
  "gstPercentage": 18,
  "customDeductions": 0
}
```

**Response (200):**
```json
{
  "success": true,
  "data": {
    "tournament": {
      "id": "tournament_uuid",
      "name": "Chess Championship 2024",
      "participants": 50,
      "entry_fee": 500
    },
    "calculation": {
      "total_collected": 25000,
      "platform_fee": 1250,
      "processing_fee": 625,
      "custom_deductions": 0,
      "total_deductions": 1987.5,
      "club_payout": 23012.5,
      "payout_percentage": "92.05"
    },
    "breakdown": {
      "Total Collected": "₹25000.00",
      "Platform Fee (5%)": "₹1250.00",
      "Processing Fee (2.5%)": "₹625.00",
      "GST on Processing Fee (18%)": "₹112.50",
      "Custom Deductions": "₹0.00",
      "Total Deductions": "₹1987.50",
      "Club Payout": "₹23012.50"
    }
  }
}
```

### 7. Create Tournament Payout
**POST** `/api/club/payout/create`

Create actual payout for a completed tournament.

**Request Body:**
```json
{
  "tournament_id": "tournament_uuid",
  "urgent": false,
  "custom_amount": null
}
```

**Response (201):**
```json
{
  "success": true,
  "message": "Tournament payout created successfully",
  "data": {
    "payout_id": "pout_xxxxx",
    "amount": 23012.5,
    "status": "created",
    "mode": "IMPS",
    "estimated_processing_time": "Instant (within 5 minutes)",
    "reference_id": "tournament_xxxxx_**********",
    "calculation": {
      "total_collected": 25000,
      "platform_fee": 1250,
      "processing_fee": 737.5,
      "club_payout": 23012.5,
      "payout_percentage": "92.05"
    },
    "tournament": {
      "id": "tournament_uuid",
      "name": "Chess Championship 2024",
      "participants": 50
    }
  }
}
```

### 8. Get Payout Status
**GET** `/api/club/payout/status/:payout_id`

Check current status of a specific payout.

**Response (200):**
```json
{
  "success": true,
  "data": {
    "payout_id": "pout_xxxxx",
    "status": "processed",
    "status_description": "Payout has been successfully completed",
    "amount": 23012.5,
    "mode": "IMPS",
    "reference_id": "tournament_xxxxx_**********",
    "created_at": "2024-01-01T10:00:00.000Z",
    "processed_at": "2024-01-01T10:05:00.000Z",
    "failure_reason": null,
    "utr": "123456789012",
    "fees": 5.9,
    "tax": 1.06,
    "tournament": {
      "id": "tournament_uuid",
      "name": "Chess Championship 2024",
      "status": "completed"
    }
  }
}
```

### 9. Get All Club Payouts
**GET** `/api/club/payout/list?page=1&limit=10&status=processed`

List all payouts for the club with pagination.

**Query Parameters:**
- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 10)
- `status` (optional): Filter by status

**Response (200):**
```json
{
  "success": true,
  "data": {
    "payouts": [
      {
        "id": "uuid",
        "payout_id": "pout_xxxxx",
        "amount": 23012.5,
        "status": "processed",
        "mode": "IMPS",
        "reference_id": "tournament_xxxxx_**********",
        "urgent_transfer": false,
        "created_at": "2024-01-01T10:00:00.000Z",
        "processed_at": "2024-01-01T10:05:00.000Z",
        "tournament": {
          "id": "tournament_uuid",
          "name": "Chess Championship 2024",
          "status": "completed",
          "participants": 50
        }
      }
    ],
    "pagination": {
      "current_page": 1,
      "per_page": 10,
      "total": 5,
      "total_pages": 1
    }
  }
}
```

## Error Responses

### Common Error Codes
- `400` - Bad Request (validation errors, business logic violations)
- `401` - Unauthorized (missing or invalid token)
- `403` - Forbidden (insufficient permissions)
- `404` - Not Found (resource doesn't exist)
- `422` - Unprocessable Entity (validation failed)
- `500` - Internal Server Error

### Example Error Response
```json
{
  "success": false,
  "error": "Banking details already exist and are verified",
  "message": "You can only have one verified bank account per club"
}
```

## Status Codes

### Bank Verification Status
- `pending` - Verification in progress
- `completed` - Successfully verified
- `failed` - Verification failed

### Payout Status
- `setup_completed` - Payout infrastructure ready
- `created` - Payout created and being processed
- `queued` - Queued due to insufficient balance
- `processing` - Being processed by bank
- `processed` - Successfully completed
- `failed` - Failed (amount reversed)
- `reversed` - Failed and amount reversed

## Transfer Modes
- `IMPS` - Instant (up to ₹2L, 24/7)
- `NEFT` - Next working day (up to ₹10L)
- `RTGS` - Same day during business hours (above ₹10L)

## Fee Structure
- **Platform Fee**: 5% of total collected
- **Processing Fee**: 2.5% of total collected
- **GST**: 18% on processing fee
- **Club Payout**: ~92% of total collected amount
