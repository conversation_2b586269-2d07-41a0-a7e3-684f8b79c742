# Report Extract API Documentation

This document describes the API endpoints for the Report Extract feature, which allows you to upload Excel files and extract data into the database.

## Base URL

All endpoints are prefixed with `/api/report-extract`.

## Authentication

All endpoints require authentication. Include the JWT token in the Authorization header:

```
Authorization: Bearer <your_token>
```

## Endpoints

### Upload and Process Excel File

**Endpoint:** `POST /upload`

**Description:** Upload an Excel file and process it to extract data into the database.

**Request:**
- Content-Type: `multipart/form-data`

**Form Fields:**
- `file` (required): The Excel file to upload
- `tournamentId` (optional): UUID of the tournament to associate the data with
- `additionalFields` (optional): JSON string of additional fields to add to each record
- `deleteAfterProcessing` (optional): Set to 'true' to delete the file after processing

**Example Request:**
```bash
curl -X POST \
  -H "Authorization: Bearer <your_token>" \
  -F "file=@path/to/your/file.xlsx" \
  -F "tournamentId=123e4567-e89b-12d3-a456-************" \
  -F "additionalFields={\"category\":\"U14\",\"event\":\"Chess Championship\"}" \
  -F "deleteAfterProcessing=true" \
  http://localhost:3000/api/report-extract/upload
```

**Response:**
```json
{
  "success": true,
  "message": "File processed successfully",
  "data": {
    "recordsProcessed": 50,
    "filename": "chess_results.xlsx",
    "tournamentId": "123e4567-e89b-12d3-a456-************"
  }
}
```

### Get All Report Extracts

**Endpoint:** `GET /`

**Description:** Get all report extracts, with optional filtering by tournament ID.

**Query Parameters:**
- `tournamentId` (optional): Filter by tournament ID
- `limit` (optional): Number of records to return (default: 100)
- `offset` (optional): Number of records to skip (default: 0)

**Example Request:**
```bash
curl -X GET \
  -H "Authorization: Bearer <your_token>" \
  "http://localhost:3000/api/report-extract?tournamentId=123e4567-e89b-12d3-a456-************&limit=10&offset=0"
```

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "123e4567-e89b-12d3-a456-************",
      "tournamentId": "123e4567-e89b-12d3-a456-************",
      "playerNumber": 1,
      "": "John Doe",
      "playerId": "12345",
      "fideId": "67890",
      "rating": 1800,
      "gender": "m",
      "category": "U14",
      "club": "Chess Club",
      "city": "New York",
      "sourceFile": "chess_results.xlsx",
      "importDate": "2023-05-01T12:00:00.000Z",
      "additionalData": {},
      "createdAt": "2023-05-01T12:00:00.000Z",
      "updatedAt": "2023-05-01T12:00:00.000Z"
    },
    // More records...
  ],
  "count": 50,
  "limit": 10,
  "offset": 0
}
```

### Get Report Extract by ID

**Endpoint:** `GET /:id`

**Description:** Get a single report extract by ID.

**URL Parameters:**
- `id` (required): UUID of the report extract

**Example Request:**
```bash
curl -X GET \
  -H "Authorization: Bearer <your_token>" \
  "http://localhost:3000/api/report-extract/123e4567-e89b-12d3-a456-************"
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "123e4567-e89b-12d3-a456-************",
    "tournamentId": "123e4567-e89b-12d3-a456-************",
    "playerNumber": 1,
    "playerName": "John Doe",
    "playerId": "12345",
    "fideId": "67890",
    "rating": 1800,
    "gender": "m",
    "category": "U14",
    "club": "Chess Club",
    "city": "New York",
    "sourceFile": "chess_results.xlsx",
    "importDate": "2023-05-01T12:00:00.000Z",
    "additionalData": {},
    "createdAt": "2023-05-01T12:00:00.000Z",
    "updatedAt": "2023-05-01T12:00:00.000Z"
  }
}
```

### Delete Report Extract by ID

**Endpoint:** `DELETE /:id`

**Description:** Delete a single report extract by ID.

**URL Parameters:**
- `id` (required): UUID of the report extract

**Example Request:**
```bash
curl -X DELETE \
  -H "Authorization: Bearer <your_token>" \
  "http://localhost:3000/api/report-extract/123e4567-e89b-12d3-a456-************"
```

**Response:**
```json
{
  "success": true,
  "message": "Report extract deleted successfully"
}
```

### Delete All Report Extracts for a Tournament

**Endpoint:** `DELETE /tournament/:tournamentId`

**Description:** Delete all report extracts for a specific tournament.

**URL Parameters:**
- `tournamentId` (required): UUID of the tournament

**Example Request:**
```bash
curl -X DELETE \
  -H "Authorization: Bearer <your_token>" \
  "http://localhost:3000/api/report-extract/tournament/123e4567-e89b-12d3-a456-************"
```

**Response:**
```json
{
  "success": true,
  "message": "50 report extracts deleted successfully"
}
```

## Error Responses

All endpoints return a standard error format:

```json
{
  "success": false,
  "message": "Error message describing what went wrong"
}
```

Common error status codes:
- `400`: Bad Request (e.g., missing required fields)
- `401`: Unauthorized (missing or invalid token)
- `404`: Not Found (resource not found)
- `500`: Internal Server Error (server-side error)
