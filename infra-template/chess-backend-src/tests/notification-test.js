/**
 * Test script for notification service and cron jobs
 * 
 * This script tests the notification service and cron jobs by:
 * 1. Creating pairing notifications for a tournament
 * 2. Processing the notifications using the cron job
 * 3. Checking the status of the notifications
 * 
 * Usage:
 * node tests/notification-test.js
 */

require('dotenv').config();
const { models, connectDb } = require('../config/db');
const notificationService = require('../services/notificationService');
const cronService = require('../services/cronService');

// Test configuration
const TEST_TOURNAMENT_ID = process.env.TEST_TOURNAMENT_ID || ''; // Set this to a valid tournament ID
const TEST_ROUND = process.env.TEST_ROUND ? parseInt(process.env.TEST_ROUND) : 1;
const TEST_CREATOR_ID = process.env.TEST_CREATOR_ID || ''; // Set this to a valid user ID

async function runTest() {
  try {
    console.log('Connecting to database...');
    await connectDb();
    
    console.log('Starting notification test...');
    
    // Validate test configuration
    if (!TEST_TOURNAMENT_ID) {
      console.error('Error: TEST_TOURNAMENT_ID is required. Set it in .env or pass as environment variable.');
      process.exit(1);
    }
    
    if (!TEST_CREATOR_ID) {
      console.error('Error: TEST_CREATOR_ID is required. Set it in .env or pass as environment variable.');
      process.exit(1);
    }
    
    // Step 1: Create pairing notifications
    console.log(`Creating pairing notifications for tournament ${TEST_TOURNAMENT_ID}, round ${TEST_ROUND}...`);
    const notifications = await notificationService.createPairingNotifications(
      TEST_TOURNAMENT_ID,
      TEST_ROUND,
      TEST_CREATOR_ID
    );
    
    console.log(`Created ${notifications.length} notifications`);
    
    // Step 2: Process the notifications
    console.log('Processing notifications...');
    const result = await notificationService.processPendingSmsNotifications();
    
    console.log(`Processed ${result.total} notifications: ${result.success} succeeded, ${result.failed} failed`);
    
    // Step 3: Check notification status
    console.log('Checking notification status...');
    const { Notifications } = models;
    
    const pendingCount = await Notifications.count({
      where: {
        type: 'tournament-pairing',
        status: 'pending',
        metadata: {
          tournament_id: TEST_TOURNAMENT_ID,
          round_id: TEST_ROUND
        }
      }
    });
    
    const deliveredCount = await Notifications.count({
      where: {
        type: 'tournament-pairing',
        status: 'delivered',
        metadata: {
          tournament_id: TEST_TOURNAMENT_ID,
          round_id: TEST_ROUND
        }
      }
    });
    
    const failedCount = await Notifications.count({
      where: {
        type: 'tournament-pairing',
        status: 'failed',
        metadata: {
          tournament_id: TEST_TOURNAMENT_ID,
          round_id: TEST_ROUND
        }
      }
    });
    
    const retryCount = await Notifications.count({
      where: {
        type: 'tournament-pairing',
        status: 'retry',
        metadata: {
          tournament_id: TEST_TOURNAMENT_ID,
          round_id: TEST_ROUND
        }
      }
    });
    
    console.log('\nNotification Status Summary:');
    console.log('---------------------------');
    console.log(`Total Created: ${notifications.length}`);
    console.log(`Pending: ${pendingCount}`);
    console.log(`Delivered: ${deliveredCount}`);
    console.log(`Failed: ${failedCount}`);
    console.log(`Retry: ${retryCount}`);
    
    console.log('\nTest completed successfully!');
    process.exit(0);
  } catch (error) {
    console.error('Error running test:', error);
    process.exit(1);
  }
}

// Run the test
runTest();
