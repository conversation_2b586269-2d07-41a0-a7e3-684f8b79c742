'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('arbiter_details', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        allowNull: false,
        primaryKey: true,
      },
      profile_url: {
        type: Sequelize.STRING(2083),
        allowNull: true,
      },
      official_id: {
        type: Sequelize.STRING(50),
        allowNull: true,
      },
      title: {
        type: Sequelize.STRING(50),
        allowNull: true,
      },
      user_id: {
        type: Sequelize.UUID,
        allowNull: false,
        unique: true,
        references: {
          model: 'users',
          key: 'id',
        },
        onDelete: 'CASCADE',
      },
      fide_id: {
        type: Sequelize.STRING(20),
        allowNull: true,
        defaultValue: '',
      },
      aicf_id: {
        type: Sequelize.STRING(20),
        allowNull: true,
        defaultValue: '',
      },
      state_id: {
        type: Sequelize.STRING(20),
        allowNull: true,
        defaultValue: '',
      },
      district_id: {
        type: Sequelize.STRING(20),
        allowNull: true,
        defaultValue: '',
      },
      alternate_contact: {
        type: Sequelize.STRING(15),
        allowNull: true,
      },
      country: {
        type: Sequelize.STRING(50),
        allowNull: false,
      },
      state: {
        type: Sequelize.STRING(50),
        allowNull: false,
      },
      district: {
        type: Sequelize.STRING(50),
        allowNull: false,
      },
      city: {
        type: Sequelize.STRING(50),
        allowNull: false,
      },
      pincode: {
        type: Sequelize.STRING(10),
        allowNull: false,
      },
      created_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.fn('NOW'),
      },
      updated_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.fn('NOW'),
      }
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('arbiter_details');
  }
};
