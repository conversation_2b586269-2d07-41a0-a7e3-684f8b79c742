'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await Promise.all([
      queryInterface.changeColumn('arbiter_details', 'country', {
        type: Sequelize.STRING(50),
        allowNull: true,
      }),
      queryInterface.changeColumn('arbiter_details', 'state', {
        type: Sequelize.STRING(50),
        allowNull: true,
      }),
      queryInterface.changeColumn('arbiter_details', 'district', {
        type: Sequelize.STRING(50),
        allowNull: true,
      }),
      queryInterface.changeColumn('arbiter_details', 'city', {
        type: Sequelize.STRING(50),
        allowNull: true,
      }),
    ]);
  },

  down: async (queryInterface, Sequelize) => {
    await Promise.all([
      queryInterface.changeColumn('arbiter_details', 'country', {
        type: Sequelize.STRING(50),
        allowNull: false,
      }),
      queryInterface.changeColumn('arbiter_details', 'state', {
        type: Sequelize.STRING(50),
        allowNull: false,
      }),
      queryInterface.changeColumn('arbiter_details', 'district', {
        type: Sequelize.STRING(50),
        allowNull: false,
      }),
      queryInterface.changeColumn('arbiter_details', 'city', {
        type: Sequelize.STRING(50),
        allowNull: false,
      }),
    ]);
  },
};
