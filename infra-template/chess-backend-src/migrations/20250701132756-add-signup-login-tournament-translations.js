"use strict";

module.exports = {
    async up(queryInterface, Sequelize) {
        await queryInterface.bulkInsert("field_translations", [
            { field_name: "signUp", label_en: "Sign Up", label_ta: "பதிவுசெய்" },
            { field_name: "mobileNumber", label_en: "Mobile Number", label_ta: "மொபைல் எண்" },
            { field_name: "password", label_en: "Password", label_ta: "கடவுச்சொல்" },
            { field_name: "confirmPassword", label_en: "Confirm Password", label_ta: "கடவுச்சொல்லை உறுதிப்படுத்துக" },
            { field_name: "enterOTP", label_en: "Enter OTP", label_ta: "ஒரு முறை கடவுச்சொல்லை உள்ளிடுக" },
            { field_name: "referralId", label_en: "Referral ID", label_ta: "பரிந்துரை ஐடி" },
            { field_name: "sendOTP", label_en: "Send OTP", label_ta: "OTP அனுப்பு" },
            { field_name: "verifyAndSignUp", label_en: "Verify and Sign Up", label_ta: "உறுதிப்படுத்தி பதிவு செய்" },
            { field_name: "player", label_en: "Player", label_ta: "விளையாட்டாளர்" },
            { field_name: "club", label_en: "Club", label_ta: "சங்கம்" },
            { field_name: "arbiter", label_en: "Arbiter", label_ta: "தீர்வையாளர்" },
            { field_name: "selectUserType", label_en: "Select User Type", label_ta: "பயனர் வகையைத் தேர்ந்தெடுக்கவும்" },
            { field_name: "passwordStrength", label_en: "Password Strength", label_ta: "கடவுச்சொல் வலிமை" },
            { field_name: "passwordRequirements", label_en: "Password Requirements", label_ta: "கடவுச்சொல் தேவைகள்" },
            { field_name: "passwordMismatch", label_en: "Password Mismatch", label_ta: "கடவுச்சொல் பொருந்தவில்லை" },
            { field_name: "bySigningUpYouAgreeToOur", label_en: "By signing up you agree to our", label_ta: "பதிவுசெய்வதன் மூலம், நீங்கள் எங்கள்" },
            { field_name: "terms", label_en: "Terms", label_ta: "விதிமுறைகள்" },
            { field_name: "privacyPolicy", label_en: "Privacy Policy", label_ta: "தனியுரிமைக் கொள்கை" },
            { field_name: "verifyYourMobileNumber", label_en: "Verify your mobile number", label_ta: "உங்கள் மொபைல் எண்ணை உறுதிப்படுத்துக" },
            { field_name: "weSentVerificationCodeTo", label_en: "We sent verification code to", label_ta: "உறுதிப்படுத்தல் குறியீடு அனுப்பப்பட்டது" },
            { field_name: "didntReceiveCode", label_en: "Didn't receive code?", label_ta: "குறியீடு பெறவில்லை?" },
            { field_name: "resendOTPIn", label_en: "Resend OTP in", label_ta: "OTP-ஐ மீண்டும் அனுப்புக" },
            { field_name: "resendOTP", label_en: "Resend OTP", label_ta: "OTP-ஐ மீண்டும் அனுப்புக" },
            { field_name: "editInformation", label_en: "Edit Information", label_ta: "தகவலைத் திருத்துக" },
            { field_name: "login", label_en: "Login", label_ta: "உள்நுழைக" },
            { field_name: "mobileNumberOrEmail", label_en: "Mobile Number or Email", label_ta: "மொபைல் எண் அல்லது மின்னஞ்சல்" },
            { field_name: "forgotPassword", label_en: "Forgot Password", label_ta: "கடவுச்சொல்லை மறந்துவிட்டீர்களா" },
            { field_name: "ifYouDoNotHaveAccount", label_en: "If you do not have an account", label_ta: "உங்களிடம் கணக்கு இல்லையெனில்" },
            { field_name: "rememberMe", label_en: "Remember Me", label_ta: "என்னை நினைவில் வை" },
            { field_name: "sNo", label_en: "S.No", label_ta: "வரிசை எண்" },
            { field_name: "registrationEnd", label_en: "Registration End", label_ta: "பதிவுக்கு இறுதி தேதி" },
            { field_name: "location", label_en: "Location", label_ta: "இடம்" },
            { field_name: "searchTournaments", label_en: "Search Tournaments", label_ta: "போட்டிகளை தேடு" },
            { field_name: "genderCategory", label_en: "Gender Category", label_ta: "பாலினம் பிரிவு" },
            { field_name: "age", label_en: "Age", label_ta: "வயது" },
            { field_name: "all", label_en: "All", label_ta: "அனைத்தும்" },
            { field_name: "rated", label_en: "Rated", label_ta: "மதிப்பீடு செய்யப்பட்ட" },
            { field_name: "unrated", label_en: "Unrated", label_ta: "மதிப்பீடு செய்யாத" },
            { field_name: "search", label_en: "Search", label_ta: "தேடு" },
            { field_name: "fullName", label_en: "Full Name", label_ta: "முழுப் பெயர்" },
            { field_name: "emergencyContact", label_en: "Emergency Contact", label_ta: "அவசர தொடர்பு" }
        ]);
    },

    async down(queryInterface, Sequelize) {
        await queryInterface.bulkDelete("field_translations", null, {});
    }
};
