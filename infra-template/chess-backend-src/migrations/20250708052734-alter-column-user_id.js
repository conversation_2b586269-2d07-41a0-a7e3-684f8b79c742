'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    // Drop the existing unique constraint
    await queryInterface.removeConstraint('course_details', 'course_details_user_id_key');

    // Just in case, update column definition to ensure it's non-unique
    await queryInterface.changeColumn('course_details', 'user_id', {
      type: Sequelize.UUID,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id',
      },
      onDelete: 'CASCADE',
    });
  },

  async down(queryInterface, Sequelize) {
    // Re-add the unique constraint if you ever rollback
    await queryInterface.changeColumn('course_details', 'user_id', {
      type: Sequelize.UUID,
      allowNull: false,
      unique: true,
      references: {
        model: 'users',
        key: 'id',
      },
      onDelete: 'CASCADE',
    });
  }
};
