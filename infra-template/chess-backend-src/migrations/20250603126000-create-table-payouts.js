'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('payouts', {
      id: {
        allowNull: false,
        primaryKey: true,
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4
      },
      club_id: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      tournament_id: {
        type: Sequelize.UUID,
        allowNull: true,
        references: {
          model: 'tournament',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL'
      },
      payout_id: {
        type: Sequelize.STRING,
        allowNull: true
      },
      contact_id: {
        type: Sequelize.STRING,
        allowNull: true
      },
      fund_account_id: {
        type: Sequelize.STRING,
        allowNull: true
      },
      bank_details_id: {
        type: Sequelize.UUID,
        allowNull: true,
        references: {
          model: 'bank_details',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL'
      },
      amount: {
        type: Sequelize.DECIMAL(10, 2),
        allowNull: true
      },
      total_collected: {
        type: Sequelize.DECIMAL(10, 2),
        allowNull: true
      },
      platform_fee: {
        type: Sequelize.DECIMAL(10, 2),
        allowNull: true
      },
      processing_fee: {
        type: Sequelize.DECIMAL(10, 2),
        allowNull: true
      },
      status: {
        type: Sequelize.ENUM(
          "setup_completed",
          "created",
          "queued",
          "initiated",
          "processed",
          "failed",
          "reversed",
          "pending",
          "updated",
          "rejected",
          "processing"
        ),
        allowNull: false,
        defaultValue: 'created'
      },
      mode: {
        type: Sequelize.ENUM('IMPS', 'NEFT', 'RTGS'),
        allowNull: true
      },
      reference_id: {
        type: Sequelize.STRING,
        allowNull: true
      },
      urgent_transfer: {
        type: Sequelize.BOOLEAN,
        allowNull: true,
        defaultValue: false
      },
      processed_at: {
        type: Sequelize.DATE,
        allowNull: true
      },
      failure_reason: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      verification_status: {
        type: Sequelize.ENUM('pending', 'verified', 'failed'),
        allowNull: true,
        defaultValue: 'pending'
      },
      metadata: {
        type: Sequelize.JSON,
        allowNull: true
      },
      created_at: {
        allowNull: false,
        type: Sequelize.DATE
      },
      updated_at: {
        allowNull: false,
        type: Sequelize.DATE
      }
    });

    // Add indexes for better performance
    await queryInterface.addIndex('payouts', ['club_id']);
    await queryInterface.addIndex('payouts', ['tournament_id']);
    await queryInterface.addIndex('payouts', ['bank_details_id']);
    await queryInterface.addIndex('payouts', ['status']);
    await queryInterface.addIndex('payouts', ['verification_status']);
    await queryInterface.addIndex('payouts', ['payout_id']);
    await queryInterface.addIndex('payouts', ['reference_id']);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('payouts');
  }
};