'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    /**
     * Add altering commands here.
     *
     * Example:
     * await queryInterface.createTable('users', { id: Sequelize.INTEGER });
     */
     await queryInterface.sequelize.query(`
      DO $$ BEGIN
        IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'enum_tournament_tournament_category') THEN
          CREATE TYPE "enum_tournament_tournament_category" AS ENUM ('open', 'male', 'female');
        END IF;
      END $$;

      ALTER TYPE "enum_tournament_tournament_category" ADD VALUE IF NOT EXISTS 'children';
      ALTER TYPE "enum_tournament_tournament_category" ADD VALUE IF NOT EXISTS 'children-open';
    `);
  },

  async down (queryInterface, Sequelize) {
    /**
     * Add reverting commands here.
     *
     * Example:
     * await queryInterface.dropTable('users');
     */
    console.warn("Down migration not supported for ENUM value removal.");
  }
};
