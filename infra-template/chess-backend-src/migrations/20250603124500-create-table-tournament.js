"use strict";

module.exports = {
  async up(queryInterface, Sequelize) {
    // Create ENUM types first
    await queryInterface.sequelize.query(
      `CREATE TYPE "enum_tournament_tournament_level" AS ENUM ('national', 'state', 'district', 'global');`
    );
    await queryInterface.sequelize.query(
      `CREATE TYPE "enum_tournament_entry_fee_currency" AS ENUM ('INR', 'USD', 'EUR');`
    );
    await queryInterface.sequelize.query(
      `CREATE TYPE "enum_tournament_tournament_type" AS ENUM ('individual', 'team');`
    );
    await queryInterface.sequelize.query(
      `CREATE TYPE "enum_tournament_tournament_system" AS ENUM ('swiss-system', 'round-robin', 'knockout');`
    );
    await queryInterface.sequelize.query(
      `CREATE TYPE "enum_tournament_total_cash_prize_currency" AS ENUM ('INR', 'USD', 'EUR');`
    );
    await queryInterface.sequelize.query(
      `CREATE TYPE "enum_tournament_parking_facility" AS ENUM ('yes', 'no', 'limited');`
    );
    await queryInterface.sequelize.query(
      `CREATE TYPE "enum_tournament_tournament_status" AS ENUM ('active', 'inactive', 'completed', 'archived', 'cancelled');`
    );
    await queryInterface.sequelize.query(
      `CREATE TYPE "enum_tournament_tournament_category" AS ENUM ('open', 'male', 'female');`
    );

    await queryInterface.createTable("tournament", {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        allowNull: false,
        primaryKey: true,
      },
      club_id: {
        type: Sequelize.UUID,
        allowNull: true,
        references: {
          model: "users",
          key: "id",
        },
        onUpdate: "CASCADE",
        onDelete: "SET NULL",
      },
      title: {
        type: Sequelize.STRING,
        allowNull: false,
        unique: true,
      },
      sub_title: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      presented_by: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      fide_rated: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: false,
      },
      organizer_name: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      tournament_level: {
        type: "enum_tournament_tournament_level",
        allowNull: false,
        defaultValue: "national",
      },
      start_date: {
        type: Sequelize.DATEONLY,
        allowNull: false,
        defaultValue: Sequelize.fn('NOW'),
      },
      end_date: {
        type: Sequelize.DATEONLY,
        allowNull: false,
      },
      reporting_time: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      registration_start_date: {
        type: Sequelize.DATEONLY,
        allowNull: false,
      },
      registration_end_date: {
        type: Sequelize.DATEONLY,
        allowNull: false,
      },
      registration_end_time: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      arbiter_id: {
        type: Sequelize.UUID,
        allowNull: true,
        references: {
          model: "users",
          key: "id",
        },
        onUpdate: "CASCADE",
        onDelete: "SET NULL",
      },
      tournament_director_name: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      entry_fee_currency: {
        type: "enum_tournament_entry_fee_currency",
        allowNull: false,
        defaultValue: "INR",
      },
      entry_fee: {
        type: Sequelize.DECIMAL(10, 2),
        allowNull: false,
      },
      number_of_rounds: {
        type: Sequelize.JSONB, // Changed from SMALLINT to JSONB
        allowNull: true,
        defaultValue: JSON.stringify({ maleAgeCategory: {}, femaleAgeCategory: {} }),
      },
      time_control: {
        type: Sequelize.STRING(20),
        allowNull: false,
      },
      time_control_duration: {
        type: Sequelize.STRING(20),
        allowNull: false,
      },
      time_control_increment: {
        type: Sequelize.STRING(20),
        allowNull: false,
      },
      tournament_type: {
        type: "enum_tournament_tournament_type",
        allowNull: false,
        defaultValue: "individual",
      },
      tournament_system: {
        type: "enum_tournament_tournament_system",
        allowNull: false,
        defaultValue: "swiss-system",
      },
      national_approval: {
        type: Sequelize.STRING,
        allowNull: true,
        defaultValue: "false", // Changed to string "false" to match model
      },
      state_approval: {
        type: Sequelize.STRING,
        allowNull: true,
        defaultValue: "false", // Changed to string "false" to match model
      },
      district_approval: {
        type: Sequelize.STRING,
        allowNull: true,
        defaultValue: "false", // Changed to string "false" to match model
      },
      fide_approval: {
        type: Sequelize.STRING,
        allowNull: true,
        defaultValue: "false", // Changed to string "false" to match model
      },
      contact_person_name: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      email: {
        type: Sequelize.STRING(100),
        allowNull: false,
      },
      contact_number: {
        type: Sequelize.STRING(20),
        allowNull: false,
      },
      alternate_contact_number: {
        type: Sequelize.STRING(20),
        allowNull: true,
      },
      number_of_trophies_male: {
        type: Sequelize.SMALLINT,
        allowNull: false,
        defaultValue: 0,
      },
      number_of_trophies_female: {
        type: Sequelize.SMALLINT,
        allowNull: false,
        defaultValue: 0,
      },
      total_cash_prize_currency: {
        type: "enum_tournament_total_cash_prize_currency",
        allowNull: false,
        defaultValue: "INR",
      },
      total_cash_prize_amount: {
        type: Sequelize.DECIMAL(10, 2),
        allowNull: false,
        defaultValue: 0,
      },
      country: {
        type: Sequelize.STRING(60),
        allowNull: false,
      },
      state: {
        type: Sequelize.STRING(60),
        allowNull: false,
      },
      district: {
        type: Sequelize.STRING(60),
        allowNull: false,
      },
      city: {
        type: Sequelize.STRING(60),
        allowNull: false,
      },
      pincode: {
        type: Sequelize.STRING(10),
        allowNull: false,
      },
      venue_address: {
        type: Sequelize.TEXT("tiny"),
        allowNull: false,
      },
      nearest_landmark: {
        type: Sequelize.STRING(100),
        allowNull: false,
      },
      brochure_url: {
        type: Sequelize.STRING(2083),
        allowNull: true,
      },
      location_url: {
        type: Sequelize.STRING(2083),
        allowNull: true,
      },
      chat_url: {
        type: Sequelize.STRING(2083),
        allowNull: true,
      },
      chessboard_provided: {
        type: Sequelize.BOOLEAN,
        allowNull: true,
        defaultValue: false,
      },
      timer_provided: {
        type: Sequelize.BOOLEAN,
        allowNull: true,
        defaultValue: false,
      },
      parking_facility: {
        type: "enum_tournament_parking_facility",
        allowNull: true,
        defaultValue: "no",
      },
      spot_entry: {
        type: Sequelize.BOOLEAN,
        allowNull: true,
        defaultValue: false,
      },
      tournament_status: {
        type: "enum_tournament_tournament_status",
        allowNull: false,
        defaultValue: "inactive",
      },
      cancellation_reason: {
        type: Sequelize.TEXT,
        allowNull: true,
      },
      cancellation_date: {
        type: Sequelize.DATE,
        allowNull: true,
      },
      food_facility: {
        type: Sequelize.STRING,
        allowNull: true,
        defaultValue: "nil",
      },
      male_age_category: {
        type: Sequelize.ARRAY(Sequelize.STRING),
        allowNull: true,
      },
      female_age_category: {
        type: Sequelize.ARRAY(Sequelize.STRING),
        allowNull: true,
      },
      tournament_category: {
        type: "enum_tournament_tournament_category",
        allowNull: true,
        defaultValue: "open",
      },
      certificate_data: {
        type: Sequelize.JSONB,
        allowNull: true,
      },
      created_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.fn('NOW'),
      },
      updated_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.fn('NOW'),
      },
    });

    // Create indexes as defined in the model
    await queryInterface.addIndex("tournament", ["title"]);
    await queryInterface.addIndex("tournament", ["start_date"]);
    await queryInterface.addIndex("tournament", ["tournament_level"]);
    await queryInterface.addIndex("tournament", ["tournament_category"]);
    await queryInterface.addIndex("tournament", ["tournament_status"]);
  },

  async down(queryInterface) {
    await queryInterface.dropTable("tournament");
    
    // Drop ENUM types
    await queryInterface.sequelize.query(
      'DROP TYPE IF EXISTS "enum_tournament_tournament_level";'
    );
    await queryInterface.sequelize.query(
      'DROP TYPE IF EXISTS "enum_tournament_entry_fee_currency";'
    );
    await queryInterface.sequelize.query(
      'DROP TYPE IF EXISTS "enum_tournament_tournament_type";'
    );
    await queryInterface.sequelize.query(
      'DROP TYPE IF EXISTS "enum_tournament_tournament_system";'
    );
    await queryInterface.sequelize.query(
      'DROP TYPE IF EXISTS "enum_tournament_total_cash_prize_currency";'
    );
    await queryInterface.sequelize.query(
      'DROP TYPE IF EXISTS "enum_tournament_parking_facility";'
    );
    await queryInterface.sequelize.query(
      'DROP TYPE IF EXISTS "enum_tournament_tournament_status";'
    );
    await queryInterface.sequelize.query(
      'DROP TYPE IF EXISTS "enum_tournament_tournament_category";'
    );
  },
};