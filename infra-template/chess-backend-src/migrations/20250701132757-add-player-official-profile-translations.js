"use strict";

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.bulkInsert("field_translations", [
    
      { field_name: "playerTitle", label_en: "Player Title", label_ta: "பிளேயர் பட்டம்" },
      { field_name: "selectPlayerTitle", label_en: "Select Player Title", label_ta: "பட்டத்தை தேர்ந்தெடுக்கவும்" },
      { field_name: "playerTitleRequired", label_en: "Player Title Required", label_ta: "பட்டம் தேவையானது" },
      { field_name: "firstName", label_en: "First Name", label_ta: "முதல் பெயர்" },
      { field_name: "enterFirstName", label_en: "Enter First Name", label_ta: "முதல் பெயரை உள்ளிடவும்" },
      { field_name: "lastName", label_en: "Last Name", label_ta: "கடைசி பெயர்" },
      { field_name: "enterLastName", label_en: "Enter Last Name", label_ta: "கடைசி பெயரை உள்ளிடவும்" },
      { field_name: "gender", label_en: "Gender", label_ta: "பாலினம்" },
      { field_name: "selectGender", label_en: "Select Gender", label_ta: "பாலினத்தை தேர்ந்தெடுக்கவும்" },
      { field_name: "dateOfBirth", label_en: "Date of Birth", label_ta: "பிறந்த தேதி" },
      { field_name: "dateFormat", label_en: "Date Format", label_ta: "தேதி வடிவம்" },
      { field_name: "phoneNumber", label_en: "Phone Number", label_ta: "தொலைபேசி எண்" },
      { field_name: "enterMobileNumber", label_en: "Enter Mobile Number", label_ta: "மொபைல் எண்ணை உள்ளிடவும்" },
      { field_name: "emailId", label_en: "Email ID", label_ta: "மின்னஞ்சல் ஐடி" },
      { field_name: "enterOtp", label_en: "Enter OTP", label_ta: "OTP ஐ உள்ளிடவும்" },
      { field_name: "enterOtpPlaceholder", label_en: "Enter OTP Placeholder", label_ta: "OTP இடதட்டையை உள்ளிடவும்" },
      { field_name: "fideId", label_en: "FIDE ID", label_ta: "FIDE ஐடி" },
      { field_name: "enterFideId", label_en: "Enter FIDE ID", label_ta: "FIDE ஐடியை உள்ளிடவும்" },
      { field_name: "fideRating", label_en: "FIDE Rating", label_ta: "FIDE மதிப்பீடு" },
      { field_name: "enterFideRating", label_en: "Enter FIDE Rating", label_ta: "FIDE மதிப்பீட்டை உள்ளிடவும்" },
      { field_name: "aicfId", label_en: "AICF ID", label_ta: "AICF ஐடி" },
      { field_name: "enterAicfId", label_en: "Enter AICF ID", label_ta: "AICF ஐடியை உள்ளிடவும்" },
      { field_name: "stateId", label_en: "State ID", label_ta: "மாநில ஐடி" },
      { field_name: "enterStateId", label_en: "Enter State ID", label_ta: "மாநில ஐடியை உள்ளிடவும்" },
      { field_name: "districtId", label_en: "District ID", label_ta: "மாவட்ட ஐடி" },
      { field_name: "enterDistrictId", label_en: "Enter District ID", label_ta: "மாவட்ட ஐடியை உள்ளிடவும்" },
      { field_name: "selectClub", label_en: "Select Club", label_ta: "கிளப்பை தேர்ந்தெடுக்கவும்" },
      { field_name: "searchForClubs", label_en: "Search for Clubs", label_ta: "கிளப்புகளை தேடவும்" },
      { field_name: "alternateContact", label_en: "Alternate Contact", label_ta: "மாற்று தொடர்பு" },
      { field_name: "enterAlternateContact", label_en: "Enter Alternate Contact", label_ta: "மாற்று தொடர்பை உள்ளிடவும்" },
      { field_name: "parentGuardianName", label_en: "Parent/Guardian Name", label_ta: "பெற்றோர்/காவலர் பெயர்" },
      { field_name: "enterParentGuardianName", label_en: "Enter Parent/Guardian Name", label_ta: "பெற்றோர்/காவலர் பெயரை உள்ளிடவும்" },
      { field_name: "parentGuardianContact", label_en: "Parent/Guardian Contact", label_ta: "பெற்றோர்/காவலர் தொடர்பு" },
      { field_name: "enterParentGuardianContact", label_en: "Enter Parent/Guardian Contact", label_ta: "பெற்றோர்/காவலர் தொடர்பை உள்ளிடவும்" },
      { field_name: "association", label_en: "Association", label_ta: "சங்கம்" },
      { field_name: "enterAssociation", label_en: "Enter Association", label_ta: "சங்கத்தை உள்ளிடவும்" },
        { field_name: "enterDistrict", label_en: "Enter District", label_ta: "மாவட்டத்தை உள்ளிடவும்" },
      
      { field_name: "enterAddress", label_en: "Enter Address", label_ta: "முகவரியை உள்ளிடவும்" },
      { field_name: "uploadDocuments", label_en: "Upload Documents", label_ta: "ஆவணங்களை பதிவேற்றவும்" },
      { field_name: "declarationStatement", label_en: "Declaration Statement", label_ta: "அறிக்கை" },
      { field_name: "declarationText", label_en: "Declaration Text", label_ta: "அறிக்கை உரை" },
      { field_name: "agreeToTerms", label_en: "Agree to Terms", label_ta: "விதிமுறைகளுக்கு ஒப்புக்கொள்கிறேன்" },
      { field_name: "pleaseAgreeToTerms", label_en: "Please Agree to Terms", label_ta: "விதிமுறைகளுக்கு ஒப்புக்கொள்க" },
      { field_name: "update", label_en: "Update", label_ta: "புதுப்பிக்க" },
      { field_name: "create", label_en: "Create", label_ta: "உருவாக்கு" },
      { field_name: "officialId", label_en: "Official ID", label_ta: "அதிகாரப்பூர்வ ஐடி" },
      { field_name: "enterOfficialId", label_en: "Enter Official ID", label_ta: "அதிகாரப்பூர்வ ஐடியை உள்ளிடவும்" },
      { field_name: "selectTitle", label_en: "Select Title", label_ta: "பட்டத்தை தேர்ந்தெடுக்கவும்" },
      { field_name: "enterOtpRequired", label_en: "Enter OTP Required", label_ta: "OTP தேவையானது" }
    ]);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.bulkDelete("field_translations", null, {});
  }
};
