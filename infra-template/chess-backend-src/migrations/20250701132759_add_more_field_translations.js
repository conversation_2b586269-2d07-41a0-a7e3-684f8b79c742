"use strict";

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.bulkInsert("field_translations", [
      // Existing entries...
      { field_name: "enterTournamentTitle", label_en: "Enter Tournament Title", label_ta: "போட்டி தலைப்பை உள்ளிடவும்" },
      { field_name: "enterSubTitle", label_en: "Enter Sub Title", label_ta: "துணை தலைப்பை உள்ளிடவும்" },
      { field_name: "enterOrganizerName", label_en: "Enter Organizer Name", label_ta: "நிர்வாகியின் பெயரை உள்ளிடவும்" },
      { field_name: "selectFideRated", label_en: "Select FIDE Rated", label_ta: "FIDE மதிப்பீட்டை தேர்ந்தெடுக்கவும்" },
      { field_name: "selectTournamentLevel", label_en: "Select Tournament Level", label_ta: "போட்டி நிலையை தேர்ந்தெடுக்கவும்" },
      { field_name: "selectStartDate", label_en: "Select Start Date", label_ta: "தொடக்க தேதியை தேர்ந்தெடுக்கவும்" },
      { field_name: "selectEndDate", label_en: "Select End Date", label_ta: "முடிவு தேதியை தேர்ந்தெடுக்கவும்" },
      { field_name: "selectReportingTime", label_en: "Select Reporting Time", label_ta: "புகாரளிக்கும் நேரத்தை தேர்ந்தெடுக்கவும்" },
      { field_name: "selectMaleAgeCategory", label_en: "Select Male Age Category", label_ta: "ஆண் வயது பிரிவை தேர்ந்தெடுக்கவும்" },
      { field_name: "selectFemaleAgeCategory", label_en: "Select Female Age Category", label_ta: "பெண் வயது பிரிவை தேர்ந்தெடுக்கவும்" },
      { field_name: "selectTournamentCategory", label_en: "Select Tournament Category", label_ta: "போட்டி வகையை தேர்ந்தெடுக்கவும்" },
      { field_name: "selectRegistrationStartDate", label_en: "Select Registration Start Date", label_ta: "பதிவு தொடக்க தேதியை தேர்ந்தெடுக்கவும்" },
      { field_name: "selectRegistrationEndDate", label_en: "Select Registration End Date", label_ta: "பதிவு முடிவு தேதியை தேர்ந்தெடுக்கவும்" },
      { field_name: "selectRegistrationEndTime", label_en: "Select Registration End Time", label_ta: "பதிவு முடிவு நேரத்தை தேர்ந்தெடுக்கவும்" },
      { field_name: "enterTournamentDirectorName", label_en: "Enter Tournament Director Name", label_ta: "போட்டி இயக்குநரின் பெயரை உள்ளிடவும்" },
      { field_name: "selectEntryFeeCurrency", label_en: "Select Entry Fee Currency", label_ta: "பதிவு கட்டண நாணயத்தை தேர்ந்தெடுக்கவும்" },
      { field_name: "enterEntryFee", label_en: "Enter Entry Fee", label_ta: "பதிவு கட்டணத்தை உள்ளிடவும்" },
      { field_name: "enterChatUrl", label_en: "Enter Chat URL", label_ta: "அரட்டை இணைப்பை உள்ளிடவும்" },
      { field_name: "selectTimeControl", label_en: "Select Time Control", label_ta: "நேர கட்டுப்பாட்டை தேர்ந்தெடுக்கவும்" },
      { field_name: "enterTimeControlDuration", label_en: "Enter Time Control Duration", label_ta: "நேர கட்டுப்பாட்டு காலத்தைக் உள்ளிடவும்" },
      { field_name: "enterTimeControlIncrement", label_en: "Enter Time Control Increment", label_ta: "நேர கட்டுப்பாட்டு அதிகரிப்பை உள்ளிடவும்" },
      { field_name: "selectTournamentType", label_en: "Select Tournament Type", label_ta: "போட்டி வகையை தேர்ந்தெடுக்கவும்" },
      { field_name: "selectTournamentSystem", label_en: "Select Tournament System", label_ta: "போட்டி முறையை தேர்ந்தெடுக்கவும்" },
      { field_name: "enterNationalApproval", label_en: "Enter National Approval", label_ta: "தேசிய அங்கீகார எண்ணை உள்ளிடவும்" },
      { field_name: "enterStateApproval", label_en: "Enter State Approval", label_ta: "மாநில அங்கீகார எண்ணை உள்ளிடவும்" },
      { field_name: "enterDistrictApproval", label_en: "Enter District Approval", label_ta: "மாவட்ட அங்கீகார எண்ணை உள்ளிடவும்" },
      { field_name: "enterContactPersonName", label_en: "Enter Contact Person Name", label_ta: "தொடர்பு நபரின் பெயரை உள்ளிடவும்" },
      { field_name: "enterEmail", label_en: "Enter Email", label_ta: "மின்னஞ்சலை உள்ளிடவும்" },
      { field_name: "enterContactNumber", label_en: "Enter Contact Number", label_ta: "தொடர்பு எண்ணை உள்ளிடவும்" },
      { field_name: "enterAlternateContactNumber", label_en: "Enter Alternate Contact Number", label_ta: "மாற்று தொடர்பு எண்ணை உள்ளிடவும்" },
      { field_name: "enterNumberOfTrophiesFemale", label_en: "Enter Number of Trophies (Female)", label_ta: "வாழ்க்கையின் வெற்றி (பெண்) எண்ணிக்கையை உள்ளிடவும்" },
      { field_name: "enterNumberOfTrophiesMale", label_en: "Enter Number of Trophies (Male)", label_ta: "வாழ்க்கையின் வெற்றி (ஆண்) எண்ணிக்கையை உள்ளிடவும்" },
      { field_name: "selectTotalCashPrizeCurrency", label_en: "Select Cash Prize Currency", label_ta: "பண பரிசு நாணயத்தை தேர்ந்தெடுக்கவும்" },
      { field_name: "enterTotalCashPrizeAmount", label_en: "Enter Total Cash Prize Amount", label_ta: "மொத்த பண பரிசு தொகையை உள்ளிடவும்" },
      { field_name: "selectCountry", label_en: "Select Country", label_ta: "நாட்டை தேர்ந்தெடுக்கவும்" },
      { field_name: "selectState", label_en: "Select State", label_ta: "மாநிலத்தை தேர்ந்தெடுக்கவும்" },
      { field_name: "selectDistrict", label_en: "Select District", label_ta: "மாவட்டத்தை தேர்ந்தெடுக்கவும்" },
      { field_name: "selectCity", label_en: "Select City", label_ta: "நகரத்தை தேர்ந்தெடுக்கவும்" },
      { field_name: "enterPincode", label_en: "Enter Pincode", label_ta: "அஞ்சல் குறியீட்டை உள்ளிடவும்" },
      { field_name: "enterVenueAddress", label_en: "Enter Venue Address", label_ta: "நிகழ்வு முகவரியை உள்ளிடவும்" },
      { field_name: "enterNearestLandmark", label_en: "Enter Nearest Landmark", label_ta: "அருகிலுள்ள முக்கிய இடத்தை உள்ளிடவும்" },
      { field_name: "enterLocationUrl", label_en: "Enter Location URL", label_ta: "இருப்பிடம் இணைப்பை உள்ளிடவும்" },
      { field_name: "selectChessboardProvided", label_en: "Select Chessboard Provided", label_ta: "சதுரங்க பலகை வழங்கப்பட்டதா என்பதை தேர்ந்தெடுக்கவும்" },
      { field_name: "selectSpotEntry", label_en: "Select Spot Entry", label_ta: "நிகழ்விட புகுபதிகையை தேர்ந்தெடுக்கவும்" },
      { field_name: "selectTimerProvided", label_en: "Select Timer Provided", label_ta: "கால அளவீட்டு கருவி வழங்கப்பட்டதா என்பதை தேர்ந்தெடுக்கவும்" },
      { field_name: "selectParkingFacility", label_en: "Select Parking Facility", label_ta: "நிறுத்துமிடம் வசதியை தேர்ந்தெடுக்கவும்" },
      { field_name: "selectFoodFacility", label_en: "Select Food Facility", label_ta: "உணவு வசதியை தேர்ந்தெடுக்கவும்" },
      { field_name: "enterPresentedBy", label_en: "Enter Presented By", label_ta: "வழங்குபவரை உள்ளிடவும்" },
    ]);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.bulkDelete("field_translations", null, {});
  }
};
