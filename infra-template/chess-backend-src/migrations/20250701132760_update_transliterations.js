"use strict";

module.exports = {
  async up(queryInterface, <PERSON>quel<PERSON>) {
    // Update Tamil labels for player, club, and arbiter
    await queryInterface.bulkUpdate(
      "field_translations",
      { label_ta: "ப்ளேயர்" },
      { field_name: "player" }
    );

    await queryInterface.bulkUpdate(
      "field_translations",
      { label_ta: "க்ளப்" },
      { field_name: "club" }
    );

    await queryInterface.bulkUpdate(
      "field_translations",
      { label_ta: "ஆர்பிட்டர்" },
      { field_name: "arbiter" }
    );
  },

  async down(queryInterface, Sequelize) {
    // Revert to previous Tamil labels
    await queryInterface.bulkUpdate(
      "field_translations",
      { label_ta: "விளையாட்டாளர்" },
      { field_name: "player" }
    );

    await queryInterface.bulkUpdate(
      "field_translations",
      { label_ta: "சங்கம்" },
      { field_name: "club" }
    );

    await queryInterface.bulkUpdate(
      "field_translations",
      { label_ta: "தீர்வையாளர்" },
      { field_name: "arbiter" }
    );
  },
};
