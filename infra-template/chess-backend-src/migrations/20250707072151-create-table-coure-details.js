'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
     await queryInterface.sequelize.query(
      `CREATE TYPE "enum_course_course_status" AS ENUM ('active', 'inactive', 'completed', 'archived', 'cancelled');`
    );
    
    await queryInterface.createTable('course_details', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true,
        allowNull: false,
      },
       user_id: {
        type: Sequelize.UUID,
        allowNull: false,
        unique: true,
        references: {
          model: 'users',
          key: 'id',
        },
        onDelete: 'CASCADE',
      },
        registration_start_date: {
        type: Sequelize.DATEONLY,
        allowNull: false,
      },
      registration_end_date: {
        type: Sequelize.DATEONLY,
        allowNull: false,
      },
        start_date: {
        type: Sequelize.DATEONLY,
        allowNull: false,
      },
      end_date: {
        type: Sequelize.DATEONLY,
        allowNull: false,
      },
      title: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      course_duration: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      total_sessions: {
        type: Sequelize.INTEGER,
        allowNull: false,
      },
      sessions_per_week: {
        type: Sequelize.INTEGER,
        allowNull: false,
      },
      sessions_duration: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      course_fee: {
        type: Sequelize.FLOAT,
        allowNull: false,
      },
      payment_structure: {
        type: Sequelize.ENUM('hourly', 'monthly', 'weekly', 'one-time'),
        allowNull: false,
      },
        course_status: {
        type: "enum_course_course_status",
        allowNull: false,
        defaultValue: "inactive",
      },
       brochure_url: {
        type: Sequelize.STRING(2083),
        allowNull: true,
      },
      created_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('NOW()'),
      },
      updated_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('NOW()'),
      },
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('course_details');
     await queryInterface.sequelize.query(
    `DROP TYPE IF EXISTS "enum_course_course_status";`
  );
  }
};
