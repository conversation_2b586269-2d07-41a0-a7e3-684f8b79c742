"use strict";

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.bulkInsert("field_translations", [
      { field_name: "tournamentDetails", label_en: "Tournament Details", label_ta: "போட்டி விவரங்கள்" },
      { field_name: "contactDetails", label_en: "Contact Details", label_ta: "தொடர்பு விவரங்கள்" },
      { field_name: "prizeDetails", label_en: "Prize Details", label_ta: "வெகுமதி விவரங்கள்" },
      { field_name: "venueDetails", label_en: "Venue Details", label_ta: "நிகழ்வு இட விவரங்கள்" },
      { field_name: "otherDetails", label_en: "Other Details", label_ta: "பிற விவரங்கள்" },
      { field_name: "subTitle", label_en: "Sub Title", label_ta: "துணை தலைப்பு" },
      { field_name: "organizerName", label_en: "Organizer Name", label_ta: "நிர்வாகியின் பெயர்" },
      { field_name: "fideRated", label_en: "FIDE Rated", label_ta: "FIDE மதிப்பீடு" },
      { field_name: "tournamentLevel", label_en: "Tournament Level", label_ta: "போட்டி நிலை" },
      { field_name: "startDate", label_en: "Start Date", label_ta: "தொடக்க தேதி" },
      { field_name: "endDate", label_en: "End Date", label_ta: "முடிவு தேதி" },
      { field_name: "maleAgeCategory", label_en: "Male Age Category", label_ta: "ஆண் வயது பிரிவு" },
      { field_name: "femaleAgeCategory", label_en: "Female Age Category", label_ta: "பெண் வயது பிரிவு" },
      { field_name: "tournamentCategory", label_en: "Tournament Category", label_ta: "போட்டி வகை" },
      { field_name: "registrationStartDate", label_en: "Registration Start Date", label_ta: "பதிவு தொடக்க தேதி" },
      { field_name: "registrationEndDate", label_en: "Registration End Date", label_ta: "பதிவு முடிவு தேதி" },
      { field_name: "registrationEndTime", label_en: "Registration End Time", label_ta: "பதிவு முடிவு நேரம்" },
      { field_name: "tournamentDirectorName", label_en: "Tournament Director Name", label_ta: "போட்டி இயக்குநர் பெயர்" },
      { field_name: "entryFeeCurrency", label_en: "Entry Fee Currency", label_ta: "பதிவு கட்டண நாணயம்" },
      { field_name: "entryFee", label_en: "Entry Fee", label_ta: "பதிவு கட்டணம்" },
      { field_name: "chatUrl", label_en: "Chat URL", label_ta: "அரட்டை இணைப்பு" },
      { field_name: "timeControlDuration", label_en: "Time Control Duration", label_ta: "நேர கட்டுப்பாட்டு காலம்" },
      { field_name: "timeControlIncrement", label_en: "Time Control Increment", label_ta: "நேர கட்டுப்பாட்டு அதிகரிப்பு" },
      { field_name: "tournamentSystem", label_en: "Tournament System", label_ta: "போட்டி முறை" },
      { field_name: "nationalApproval", label_en: "National Approval", label_ta: "தேசிய அங்கீகாரம்" },
      { field_name: "stateApproval", label_en: "State Approval", label_ta: "மாநில அங்கீகாரம்" },
      { field_name: "districtApproval", label_en: "District Approval", label_ta: "மாவட்ட அங்கீகாரம்" },
      { field_name: "email", label_en: "Email", label_ta: "மின்னஞ்சல்" },
      { field_name: "contactNumber", label_en: "Contact Number", label_ta: "தொடர்பு எண்" },
      { field_name: "alternateContactNumber", label_en: "Alternate Contact Number", label_ta: "மாற்று தொடர்பு எண்" },
      { field_name: "numberOfTrophiesFemale", label_en: "Number of Trophies (Female)", label_ta: "விழா வெற்றி எண்ணிக்கை (பெண்)" },
      { field_name: "numberOfTrophiesMale", label_en: "Number of Trophies (Male)", label_ta: "விழா வெற்றி எண்ணிக்கை (ஆண்)" },
      { field_name: "totalCashPrizeCurrency", label_en: "Total Cash Prize Currency", label_ta: "மொத்த பண பரிசு நாணயம்" },
      { field_name: "totalCashPrizeAmount", label_en: "Total Cash Prize Amount", label_ta: "மொத்த பண பரிசு தொகை" },
      { field_name: "venueAddress", label_en: "Venue Address", label_ta: "நிகழ்வு முகவரி" },
      { field_name: "nearestLandmark", label_en: "Nearest Landmark", label_ta: "அருகிலுள்ள முக்கிய இடம்" },
      { field_name: "chessboardProvided", label_en: "Chessboard Provided", label_ta: "சதுரங்க பலகை வழங்கப்பட்டுள்ளது" },
      { field_name: "spotEntry", label_en: "Spot Entry", label_ta: "நிகழ்விட புகுபதிகை" },
      { field_name: "timerProvided", label_en: "Timer Provided", label_ta: "கால அளவீட்டு கருவி வழங்கப்பட்டது" },
      { field_name: "parkingFacility", label_en: "Parking Facility", label_ta: "நிறுத்துமிடம் வசதி" },
      { field_name: "foodFacility", label_en: "Food Facility", label_ta: "உணவு வசதி" },
      { field_name: "presentedBy", label_en: "Presented By", label_ta: "வழங்குபவர்" },
      {
        field_name: "passwordRequirement",
        label_en: "Password must include 8+ chars, uppercase, lowercase, number & special character.",
        label_ta: "கடவுச்சொல்லில் குறைந்தது 8 எழுத்துகள், பெரிய எழுத்து, சிறிய எழுத்து, எண் மற்றும் சிறப்பு எழுத்து இருக்க வேண்டும்.",
      },
      { field_name: "contactPerson", label_en: "Contact Person", label_ta: "தொடர்பாளர்" },
      { field_name: "contactPersonPhoneNumber", label_en: "Contact Person Phone Number", label_ta: "தொடர்பாளர் தொலைபேசி எண்" },
      { field_name: "country", label_en: "Country", label_ta: "நாடு" },
      { field_name: "state", label_en: "State", label_ta: "மாநிலம்" },
      { field_name: "district", label_en: "District", label_ta: "மாவட்டம்" },
      { field_name: "city", label_en: "City", label_ta: "நகரம்" },
      { field_name: "address", label_en: "Address", label_ta: "முகவரி" },
      { field_name: "arbiterTitle", label_en: "Arbiter Title", label_ta: "ஆர்பிட்டர் டைட்டில்" },
      { field_name: "clubName", label_en: "Club Name", label_ta: "க்ளப் பெயர்" },
      { field_name: "tournamentTitle", label_en: "Tournament Title", label_ta: "போட்டி தலைப்பு" },
      { field_name: "tournamentDates", label_en: "Tournament Dates", label_ta: "போட்டி தேதிகள்" },
      { field_name: "reportingTime", label_en: "Reporting Time", label_ta: "பதிவு நேரம்" },
      { field_name: "registrationDates", label_en: "Registration Dates", label_ta: "பதிவிற்கான தேதிகள்" },
      { field_name: "registrationFees", label_en: "Registration Fees", label_ta: "பதிவு கட்டணம்" },
      { field_name: "whatsappGroupUrl", label_en: "WhatsApp Group URL", label_ta: "வாட்ஸ்அப் குழு இணைப்பு" },
      { field_name: "fideApproval", label_en: "FIDE Approval", label_ta: "FIDE ஒப்புதல்" },
      { field_name: "prizeAmount", label_en: "Prize Amount", label_ta: "வெகுமதி தொகை" },
      { field_name: "addLocation", label_en: "Add Location", label_ta: "இடத்தை சேர்க்கவும்" },
      { field_name: "uploadBrochure", label_en: "Upload Brochure", label_ta: "தொகுப்பை பதிவேற்றுக" },
      { field_name: "parkingInfo", label_en: "Parking Info", label_ta: "பார்க்கிங் இல்லை - இரு சக்கர வாகனங்களுக்கு மட்டும்" },
      { field_name: "foodOptions", label_en: "Food Options", label_ta: "காலை உணவு, மதிய உணவு, இரவு உணவு, இடையுணவு, பானங்கள்" },
      { field_name: "saveDraft", label_en: "Save Draft", label_ta: "வரைபை சேமிக்கவும்" },
      { field_name: "submit", label_en: "Submit", label_ta: "சமர்ப்பிக்கவும்" },
      { field_name: "open", label_en: "Open", label_ta: "ஓப்பன்" },
      { field_name: "male", label_en: "Male", label_ta: "ஆண்" },
      { field_name: "female", label_en: "Female", label_ta: "பெண்" },
      { field_name: "children", label_en: "Children", label_ta: "குழந்தைகள்" },
      { field_name: "childrenAndOpen", label_en: "Children & Open", label_ta: "குழந்தைகள் & ஓப்பன்" },
      { field_name: "timeControl", label_en: "Time Control", label_ta: "நேர கட்டுப்பாடு" },
      { field_name: "classical", label_en: "Classical", label_ta: "சாதாரண" },
      { field_name: "rapid", label_en: "Rapid", label_ta: "வேகமான" },
      { field_name: "bullet", label_en: "Bullet", label_ta: "புல்லெட்" },
      { field_name: "blitz", label_en: "Blitz", label_ta: "ப்ளிட்ஸ்" },
      { field_name: "duration", label_en: "Duration", label_ta: "நேர அளவு" },
      { field_name: "enterDuration", label_en: "Enter Duration", label_ta: "நேரத்தை உள்ளிடுக" },
      { field_name: "mins", label_en: "Mins", label_ta: "நிமிடங்கள்" },
      { field_name: "secs", label_en: "Secs", label_ta: "விநாடிகள்" },
      { field_name: "increment", label_en: "Increment", label_ta: "இன்கிரிமென்ட்" },
      { field_name: "enterIncrement", label_en: "Enter Increment", label_ta: "இன்கிரிமென்ட் உள்ளிடவும்" },
      { field_name: "swissSystem", label_en: "Swiss System", label_ta: "சுவிஸ் முறை" },
      { field_name: "roundRobin", label_en: "Round Robin", label_ta: "ரவுண்ட் ராபின்" },
      { field_name: "knockout", label_en: "Knockout", label_ta: "நாக் அவுட்" },
      { field_name: "tournamentType", label_en: "Tournament Type", label_ta: "போட்டி வகை" },
      { field_name: "individual", label_en: "Individual", label_ta: "தனிப்பட்டவர்" },
      { field_name: "team", label_en: "Team", label_ta: "அணி" },
      { field_name: "enterFideApprovalNo", label_en: "Enter FIDE Approval No", label_ta: "FIDE ஒப்புதல் எண்ணை உள்ளிடவும்" },
      { field_name: "pincode", label_en: "Pincode", label_ta: "அஞ்சல் குறியீடு" },
      { field_name: "coach", label_en: "Coach", label_ta: "பயிற்சியாளர்" },
      { field_name: "myProfile", label_en: "My Profile", label_ta: "என் சுயவிவரம்" },
      { field_name: "arbiterTitleOptions", label_en: "Arbiter Title", label_ta: "அர்பிட்டர் பட்டங்கள்" },
      { field_name: "Untitled", label_en: "Untitled", label_ta: "பெயரிடப்படாதது" },
      { field_name: "SA", label_en: "State Arbiter", label_ta: "மாநில அர்பிட்டர்" },
      { field_name: "SNA", label_en: "Senior National Arbiter", label_ta: "மூத்த தேசிய அர்பிட்டர்" },
      { field_name: "FA", label_en: "Fide Arbiter", label_ta: "ஃபைடே அர்பிட்டர்" },
      { field_name: "IA", label_en: "International Arbiter", label_ta: "சர்வதேச அர்பிட்டர்" },
      { field_name: "emailID", label_en: "Email ID", label_ta: "மின்னஞ்சல் ஐடி" },
      { field_name: "parkingNotAvailable", label_en: "Parking Not Available", label_ta: "நிறுத்தும் வசதி இல்லை" },
      { field_name: "twoWheelerOnly", label_en: "2 Wheeler Only", label_ta: "இருசக்கர வாகனம் மட்டும்" },
      { field_name: "twoAndFourWheeler", label_en: "2 & 4 Wheeler", label_ta: "இரு மற்றும் நான்கு சக்கர வாகனங்கள்" },
      { field_name: "breakfast", label_en: "Breakfast", label_ta: "காலை உணவு" },
      { field_name: "lunch", label_en: "Lunch", label_ta: "மதிய உணவு" },
      { field_name: "dinner", label_en: "Dinner", label_ta: "இரவு உணவு" },
      { field_name: "snacks", label_en: "Snacks", label_ta: "தின்பண்டங்கள்" },
      { field_name: "beverages", label_en: "Beverages", label_ta: "பானங்கள்" },

    ]);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.bulkDelete("field_translations", null, {});
  }
};
