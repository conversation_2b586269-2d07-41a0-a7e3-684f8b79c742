'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn('course_details', 'description', {
      type: Sequelize.STRING,
      allowNull: true,
    });

    await queryInterface.addColumn('course_details', 'session_start_time', {
      type: Sequelize.STRING,
      allowNull: true,
    });

    await queryInterface.addColumn('course_details', 'session_end_time', {
      type: Sequelize.STRING,
      allowNull: true,
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeColumn('course_details', 'description');
    await queryInterface.removeColumn('course_details', 'course_start_time');
    await queryInterface.removeColumn('course_details', 'course_end_time');
  },
};
