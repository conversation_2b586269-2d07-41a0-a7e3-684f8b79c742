'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('notifications', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true,
      },
      user_id: {
        type: Sequelize.UUID,
        allowNull: true,
      },
      creator_id: {
        type: Sequelize.UUID,
        allowNull: true,
      },
      email: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      phone_number: {
        type: Sequelize.STRING(15),
        allowNull: true,
      },
      type: {
        type: Sequelize.ENUM(
          'tournament-registration',
          'tournament-withdraw',
          'tournament-pairing',
          'tournament-results',
          'promotional',
          'payment-confirmation',
          'tournament-reminder'
        ),
        allowNull: true,
      },
      platform: {
        type: Sequelize.ENUM('sms', 'email', 'whatsapp'),
        allowNull: false,
      },
      template_id: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      content: {
        type: Sequelize.JSONB,
        allowNull: true,
      },
      status: {
        type: Sequelize.ENUM('pending', 'retry', 'delivered', 'failed'),
        allowNull: false,
        defaultValue: 'pending',
      },
      priority: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 1,
      },
      expires_at: {
        type: Sequelize.DATE,
        allowNull: true,
      },
      delivery_attempts: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 0,
      },
      max_attempts: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 3,
      },
      sent_at: {
        type: Sequelize.DATE,
        allowNull: true,
      },
      last_attempt_at: {
        type: Sequelize.DATE,
        allowNull: true,
      },
      next_attempt_at: {
        type: Sequelize.DATE,
        allowNull: true,
      },
      failure_reason: {
        type: Sequelize.STRING(255),
        allowNull: true,
      },
      metadata: {
        type: Sequelize.JSON,
        allowNull: true,
      },
      batch_id: {
        type: Sequelize.UUID,
        allowNull: true,
      },
      external_id: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      created_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('NOW()'),
      },
      updated_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('NOW()'),
      },
      deleted_at: {
        type: Sequelize.DATE,
        allowNull: true,
      },
    });

    // Indexes
    await queryInterface.addIndex('notifications', ['status', 'platform'], {
      name: 'idx_notifications_status_platform',
    });

    await queryInterface.addIndex('notifications', ['user_id'], {
      name: 'idx_notifications_user_id',
    });

    await queryInterface.addIndex('notifications', ['creator_id'], {
      name: 'idx_notifications_creator_id',
    });

    await queryInterface.addIndex('notifications', ['type'], {
      name: 'idx_notifications_type',
    });

    await queryInterface.addIndex('notifications', ['expires_at'], {
      name: 'idx_notifications_expires_at',
    });

    await queryInterface.addIndex(
      'notifications',
      ['status', 'next_attempt_at', 'delivery_attempts', 'max_attempts'],
      { name: 'idx_notifications_next_attempt' }
    );

    await queryInterface.addIndex('notifications', ['batch_id'], {
      name: 'idx_notifications_batch_id',
    });
  },

  down: async (queryInterface) => {
    await queryInterface.dropTable('notifications');
  },
};
