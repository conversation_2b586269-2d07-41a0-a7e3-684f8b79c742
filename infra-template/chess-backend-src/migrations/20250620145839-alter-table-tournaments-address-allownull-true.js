'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await Promise.all([
      queryInterface.changeColumn('tournament', 'country', {
        type: Sequelize.STRING(60),
        allowNull: true,
      }),
      queryInterface.changeColumn('tournament', 'state', {
        type: Sequelize.STRING(60),
        allowNull: true,
      }),
      queryInterface.changeColumn('tournament', 'district', {
        type: Sequelize.STRING(60),
        allowNull: true,
      }),
      queryInterface.changeColumn('tournament', 'city', {
        type: Sequelize.STRING(60),
        allowNull: true,
      }),
      queryInterface.changeColumn('tournament', 'pincode', {
        type: Sequelize.STRING(10),
        allowNull: true,
      }),
      queryInterface.changeColumn('tournament', 'venue_address', {
        type: Sequelize.TEXT('tiny'),
        allowNull: true,
      }),
      queryInterface.changeColumn('tournament', 'nearest_landmark', {
        type: Sequelize.STRING(100),
        allowNull: true,
      }),
    ]);
  },

  down: async (queryInterface, Sequelize) => {
    await Promise.all([
      queryInterface.changeColumn('tournament', 'country', {
        type: Sequelize.STRING(60),
        allowNull: false,
      }),
      queryInterface.changeColumn('tournament', 'state', {
        type: Sequelize.STRING(60),
        allowNull: false,
      }),
      queryInterface.changeColumn('tournament', 'district', {
        type: Sequelize.STRING(60),
        allowNull: false,
      }),
      queryInterface.changeColumn('tournament', 'city', {
        type: Sequelize.STRING(60),
        allowNull: false,
      }),
      queryInterface.changeColumn('tournament', 'pincode', {
        type: Sequelize.STRING(10),
        allowNull: false,
      }),
      queryInterface.changeColumn('tournament', 'venue_address', {
        type: Sequelize.TEXT('tiny'),
        allowNull: false,
      }),
      queryInterface.changeColumn('tournament', 'nearest_landmark', {
        type: Sequelize.STRING(100),
        allowNull: false,
      }),
    ]);
  }
};
