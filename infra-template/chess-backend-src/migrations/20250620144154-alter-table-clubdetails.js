"use strict";

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await Promise.all([
      queryInterface.changeColumn("club_details", "country", {
        type: Sequelize.STRING(50),
        allowNull: true,
      }),
      queryInterface.changeColumn("club_details", "state", {
        type: Sequelize.STRING(50),
        allowNull: true,
      }),
      queryInterface.changeColumn("club_details", "district", {
        type: Sequelize.STRING(50),
        allowNull: true,
      }),
      queryInterface.changeColumn("club_details", "city", {
        type: Sequelize.STRING(50),
        allowNull: true,
      }),
      queryInterface.changeColumn("club_details", "pincode", {
        type: Sequelize.STRING(10),
        allowNull: true,
      }),
      queryInterface.changeColumn("club_details", "address", {
        type: Sequelize.TEXT,
        allowNull: true,
      }),
    ]);
  },

  down: async (queryInterface, Sequelize) => {
    await Promise.all([
      queryInterface.changeColumn("club_details", "country", {
        type: Sequelize.STRING(50),
        allowNull: false,
      }),
      queryInterface.changeColumn("club_details", "state", {
        type: Sequelize.STRING(50),
        allowNull: false,
      }),
      queryInterface.changeColumn("club_details", "district", {
        type: Sequelize.STRING(50),
        allowNull: false,
      }),
      queryInterface.changeColumn("club_details", "city", {
        type: Sequelize.STRING(50),
        allowNull: false,
      }),
      queryInterface.changeColumn("club_details", "pincode", {
        type: Sequelize.STRING(10),
        allowNull: false,
      }),
      queryInterface.changeColumn("club_details", "address", {
        type: Sequelize.TEXT,
        allowNull: false,
      }),
    ]);
  },
};
