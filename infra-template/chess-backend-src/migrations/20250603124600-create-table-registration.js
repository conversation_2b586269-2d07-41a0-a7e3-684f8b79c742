'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    // Create ENUM type first
    await queryInterface.sequelize.query(
      `CREATE TYPE "enum_registrations_status" AS ENUM ('active', 'cancelled', 'refunded');`
    );

    await queryInterface.createTable('registrations', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        allowNull: false,
        primaryKey: true,
      },
      tournament_id: {
        type: Sequelize.UUID,
        allowNull: false,
      },
      reg_id: {
        type: Sequelize.STRING,
        unique: true,
        allowNull: true,
      },
      player_id: {
        type: Sequelize.UUID,
        allowNull: false,
      },
      registered_date: {
        type: Sequelize.DATEONLY,
        allowNull: false,
        defaultValue: Sequelize.fn('NOW'),
      },
      age_category: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      gender_category: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      tournament_title: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      registration_remarks: {
        type: Sequelize.TEXT,
        allowNull: true,
      },
      payment_id: {
        type: Sequelize.UUID,
        allowNull: true,
      },
      attendance_mark: {
        type: Sequelize.JSONB,
        allowNull: true,
      },
      payment_amount: {
        type: Sequelize.DECIMAL(10, 2),
        allowNull: true,
      },
      payment_date: {
        type: Sequelize.DATE,
        allowNull: true,
      },
      status: {
        type: "enum_registrations_status",
        allowNull: false,
        defaultValue: "active",
      },
      created_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.fn('NOW'),
      },
      updated_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.fn('NOW'),
      },
    });

    // Create the unique index
     await queryInterface.addIndex('registrations', {
      fields: ['tournament_id', 'player_id'],
      unique: true,
      where: {
        status: 'active'
      },
      name: 'unique_active_registration'
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('registrations');
    await queryInterface.sequelize.query(
      `DROP TYPE IF EXISTS "enum_registrations_status";`
    );
  }
};