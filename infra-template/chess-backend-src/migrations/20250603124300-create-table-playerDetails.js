"use strict";

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable("players", {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4, 
        allowNull: false,
        primaryKey: true,
      },
      player_title: {
        type: Sequelize.STRING(50),
        allowNull: true,
        defaultValue: "Untitled",
      },
      profile_url: {
        type: Sequelize.STRING(2083),
        allowNull: true,
      },
      user_id: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: "users",
          key: "id",
        },
        onDelete: "CASCADE",
      },
      dob: {
        type: Sequelize.DATEONLY,
        allowNull: false,
      },
      gender: {
        type: Sequelize.STRING(10),
        allowNull: false,
      },
      parent_guardian_name: {
        type: Sequelize.STRING(100),
        allowNull: true,
        defaultValue: "",
      },
      emergency_contact: {
        type: Sequelize.STRING(15),
        allowNull: true,
        defaultValue: "",
      },
      alternate_contact: {
        type: Sequelize.STRING(15),
        allowNull: true,
        defaultValue: "",
      },
      fide_rating: {
        type: Sequelize.STRING,
        allowNull: true,
        defaultValue: "Unrated",
      },
      fide_id: {
        type: Sequelize.STRING(20),
        allowNull: true,
        defaultValue: "",
      },
      aicf_id: {
        type: Sequelize.STRING(20),
        allowNull: true,
        defaultValue: "",
      },
      state_id: {
        type: Sequelize.STRING(20),
        allowNull: true,
        defaultValue: "",
      },
      district_id: {
        type: Sequelize.STRING(20),
        allowNull: true,
        defaultValue: "",
      },
      association: {
        type: Sequelize.STRING(100),
        allowNull: true,
      },
      club: {
        type: Sequelize.STRING(100),
        allowNull: true,
        defaultValue: "",
      },
      club_id: {
        type: Sequelize.UUID,
        allowNull: true,
        references: {
          model: "club_details",
          key: "id",
        },
        onDelete: "SET NULL",
      },
      country: {
        type: Sequelize.STRING(50),
        allowNull: false,
        defaultValue: "India",
      },
      state: {
        type: Sequelize.STRING(50),
        allowNull: false,
        defaultValue: "Tamilnadu",
      },
      district: {
        type: Sequelize.STRING(50),
        allowNull: false,
        defaultValue: "Chennai",
      },
      city: {
        type: Sequelize.STRING(50),
        allowNull: false,
        defaultValue: "Chennai",
      },
      pincode: {
        type: Sequelize.STRING(10),
        allowNull: false,
      },
      address: {
        type: Sequelize.TEXT,
        allowNull: true,
      },
      my_files: {
        type: Sequelize.JSONB,
        allowNull: true,
        defaultValue: [],
      },
      terms_and_conditions: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
      },
      friend_ids: {
        type: Sequelize.ARRAY(Sequelize.UUID),
        allowNull: false,
        defaultValue: [],
      },
      created_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal("NOW()"),
      },
      updated_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal("NOW()"),
      },
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable("players");
  },
};
