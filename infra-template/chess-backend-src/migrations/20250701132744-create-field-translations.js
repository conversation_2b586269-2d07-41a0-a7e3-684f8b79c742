"use strict";

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable("field_translations", {
      id: {
        type: Sequelize.INTEGER,
        autoIncrement: true,
        primaryKey: true,
      },
      field_name: {
        type: Sequelize.STRING,
        allowNull: false,
        unique: true,
      },
      label_en: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      label_ta: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      createdAt: {
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal("NOW()"),
      },
      updatedAt: {
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal("NOW()"),
      },
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable("field_translations");
  },
};
