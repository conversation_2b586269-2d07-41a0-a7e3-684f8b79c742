const { Model, DataTypes } = require("sequelize");

module.exports = (sequelize) => {
  class CoachDetails extends Model {
    static associate(models) {
      CoachDetails.belongsTo(models.User, {
        foreignKey: "userId",
        onDelete: "CASCADE",
        as: "user",
        scope: {
          role: "coach",
        },
      });
    }
  }
 
  CoachDetails.init(
    {
      id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true,
      },
      profileUrl: {
        type: DataTypes.STRING(2083),
        allowNull: true,
        validate: {
          isUrl: true,
        },
        field: "profile_url",
      },
      title: {
        type: DataTypes.STRING(50),
        allowNull: true,
        field: "title",
      },
      userId: {
        type: DataTypes.UUID,
        allowNull: false,
        unique: true,
        references: {
          model: "users",
          key: "id",
        },
        field: "user_id",
      },
      dob: {
        type: DataTypes.DATEONLY,
        allowNull: false,
      },
       about: {
        type: DataTypes.STRING(250),
        allowNull: true,
      },
      stateId: {
        type: DataTypes.STRING(20),
        allowNull: true,
        defaultValue: "",
        field: "state_id",
      },
      districtId: {
        type: DataTypes.STRING(20),
        allowNull: true,
        defaultValue: "",
        field: "district_id",
      },
      country: {
        type: DataTypes.STRING(50),
        allowNull: true,
      },
      state: {
        type: DataTypes.STRING(50),
        allowNull: true,
      },
      district: {
        type: DataTypes.STRING(50),
        allowNull: true,
      },
      city: {
        type: DataTypes.STRING(50),
        allowNull: true,
      },
      pincode: {
        type: DataTypes.STRING(10),
        allowNull: true,
      },
      profileLinks: {
        type: DataTypes.JSON,
        allowNull: true,
        field: "profile_links",
      },  
      profileUrl: {
        type: DataTypes.STRING(2083),
        allowNull: true,
        validate: {
          isUrl: true,
        },
        field: "profile_url",
      },
    },
    {
      sequelize,
      modelName: "CoachDetails",
      tableName: "coach_details",
      timestamps: true,
    }
  );

  return CoachDetails;
};
