// models/notification.js

const { Model, DataTypes } = require("sequelize");

module.exports = (sequelize) => {
  class InviteRequest extends Model {}

  InviteRequest.init(
    {
      id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true,
      },
      userId: {
        type: DataTypes.UUID,
        allowNull: false,
        field: "user_id",
      },
      type: {
        type: DataTypes.ENUM(
          "club-invite",
          "friend-request",
          "join-request",
          "arbiter-request"
        ),
        allowNull: false,
      },
      title: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      message: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      metadata: {
        type: DataTypes.JSONB,
        allowNull: true,
      },
      status: {
        type: DataTypes.ENUM(["pending", "read", "accept", "reject"]),
        allowNull: true,
        defaultValue: "pending",
      },
      createdAt: {
        type: DataTypes.DATE,
        defaultValue: DataTypes.NOW,
        field: "created_at",
      },
    },
    {
      sequelize,
      modelName: "InviteRequest",
      tableName: "invite_requests",
      timestamps: false,
    }
  );

  return InviteRequest;
};
