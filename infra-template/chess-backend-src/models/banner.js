const { Model, DataTypes } = require("sequelize");

module.exports = (sequelize) => {
  class Banner extends Model {
    static associate(models) {
      // Optional: If Advertisement belongs to a user or admin, you can define it here.
      // Advertisement.belongsTo(models.User, {
      //   foreignKey: "userId",
      //   onDelete: "CASCADE",
      //   as: "user"
      // });
    }
  }

  Banner.init(
    {
      id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true,
      },
      addId: {
        type: DataTypes.INTEGER,
        allowNull: false,
        autoIncrement: true,
        unique: true,
        field: "add_id",
      },
      type: {
        type: DataTypes.ENUM("banner", "video"),
        allowNull: false,
      },
      format: {
        type: DataTypes.ENUM("image", "video","youtube"),
        allowNull: false,
      },
      url: {
        type: DataTypes.STRING(2083),
        allowNull: true,
        validate: {
          isUrl: true,
        },
      },
       bannerUrl: {
        type: DataTypes.STRING(2083),
        allowNull: true,
        field: "banner_url",
      },   
      rowIndex: {
        type: DataTypes.INTEGER(),
        allowNull: true,
        field: "row_index",
      },
      name: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      intervalTime:{
        type: DataTypes.INTEGER,
        allowNull: true,
        field: "interval_time",
      },
      isActive: {
        type: DataTypes.BOOLEAN,
        allowNull: false,
        field: "is_active",
      },
      country: {
        type: DataTypes.STRING(50),
        allowNull: true,
      },
      state: {
        type: DataTypes.STRING(50),
        allowNull: true,
      },
      city: {
        type: DataTypes.STRING(50),
        allowNull: true,
      },
      duration: {
        type: DataTypes.INTEGER,
        allowNull: true,
      },
    },
    {
      sequelize,
      modelName: "Banner",
      tableName: "banner",
      timestamps: true,
    }
  );

  return Banner;
};
