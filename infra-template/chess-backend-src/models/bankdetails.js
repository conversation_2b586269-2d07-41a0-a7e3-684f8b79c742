const { Model, DataTypes } = require("sequelize");

module.exports = (sequelize) => {
  class BankDetails extends Model {
    static associate(models) {
      BankDetails.belongsTo(models.User, {
        foreignKey: "clubId",
        onDelete: "CASCADE",
        as: "user",
        scope: {
          role: "club",
        },
      });
    }

    // Instance method to check if account is verified
    isAccountVerified() {
      return (
        this.isVerified &&
        this.metadata?.razorpay_verification?.status === "completed"
      );
    }

    // Instance method to get masked account number
    getMaskedAccountNumber() {
      if (!this.AccountNumber) return null;
      return this.AccountNumber.replace(/(?<=.{4})./g, "*");
    }

    // Enhanced verification summary with more details
    getVerificationSummary() {
      const razorpayVerification = this.metadata?.razorpay_verification;

      if (!razorpayVerification) {
        return {
          status: "not_verified",
          message: "Account not verified",
          canRetry: this.verificationAttempts < 3,
          attemptsRemaining: Math.max(0, 3 - this.verificationAttempts),
        };
      }

      return {
        status: razorpayVerification.status,
        validation_id: razorpayVerification.validation_id,
        fund_account_id: razorpayVerification.fund_account_id,
        contact_id: razorpayVerification.contact_id,
        amount_deposited: razorpayVerification.amount_deposited
          ? `₹${(razorpayVerification.amount_deposited / 100).toFixed(2)}`
          : "₹0.00",
        verified_at: razorpayVerification.verification_timestamp,
        error: razorpayVerification.error || null,
        error_code: razorpayVerification.error_code || null,
        message:
          razorpayVerification.status === "completed"
            ? "Account successfully verified"
            : razorpayVerification.error || "Verification pending or failed",
        canRetry: this.verificationAttempts < 3 && razorpayVerification.status === "failed",
        attemptsRemaining: Math.max(0, 3 - this.verificationAttempts),
      };
    }

    // Method to check if verification can be attempted
    canAttemptVerification() {
      if (this.isVerified) return false;
      if (this.isLocked) return false;
      if (this.verificationAttempts >= 3) return false;
      
      // Check if 24 hours have passed since last attempt for failed verifications
      if (this.lastVerificationAttempt) {
        const hoursSinceLastAttempt = (Date.now() - this.lastVerificationAttempt.getTime()) / (1000 * 60 * 60);
        if (this.verificationAttempts > 0 && hoursSinceLastAttempt < 24) {
          return false;
        }
      }
      
      return true;
    }

    // Method to update verification metadata
    async updateVerificationResult(verificationResult) {
      const updateData = {
        verificationAttempts: this.verificationAttempts + 1,
        lastVerificationAttempt: new Date(),
        metadata: {
          ...this.metadata,
          razorpay_verification: verificationResult,
        },
      };

      if (verificationResult.success && verificationResult.status === "completed") {
        updateData.isVerified = true;
        updateData.verifiedAt = new Date();
        updateData.isLocked = true;
      }

      return await this.update(updateData);
    }

    // Get complete bank details for API response
    toSafeJSON() {
      const data = this.get({ plain: true });
      return {
        id: data.id,
        clubId: data.clubId,
        bankName: data.bankName,
        maskedAccountNumber: this.getMaskedAccountNumber(),
        branchIFSCCode: data.branchIFSCCode,
        branchName: data.branchName,
        bankAccountType: data.bankAccountType,
        bankAccountHolderName: data.bankAccountHolderName,
        isVerified: data.isVerified,
        isLocked: data.isLocked,
        verificationSummary: this.getVerificationSummary(),
        createdAt: data.createdAt,
        updatedAt: data.updatedAt,
      };
    }
  }

  BankDetails.init(
    {
      id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true,
      },
      clubId: {
        type: DataTypes.UUID,
        allowNull: false,
        references: {
          model: "users",
          key: "id",
        },
        field: "club_id",
      },
      bankName: {
        type: DataTypes.STRING(100),
        allowNull: false,
        field: "bank_name",
      },
      AccountNumber: {
        type: DataTypes.STRING(50),
        allowNull: false,
        field: "account_number",
      },
      branchIFSCCode: {
        type: DataTypes.STRING(20),
        allowNull: false,
        field: "branch_ifsc_code",
      },
      branchName: {
        type: DataTypes.STRING(100),
        allowNull: false,
        field: "branch_name",
      },
      bankAccountType: {
        type: DataTypes.ENUM(
          "savings",
          "current",
          "salary",
          "fixed_deposit",
          "recurring_deposit"
        ),
        allowNull: false,
        field: "bank_account_type",
      },
      bankAccountHolderName: {
        type: DataTypes.STRING(100),
        allowNull: false,
        field: "bank_account_holder_name",
      },
      metadata: {
        type: DataTypes.JSONB,
        allowNull: true,
        defaultValue: {},
      },
      isLocked: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
        allowNull: false,
        field: "is_locked",
      },
      isVerified: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
        allowNull: false,
        field: "is_verified",
      },
      verificationAttempts: {
        type: DataTypes.INTEGER,
        defaultValue: 0,
        allowNull: false,
        field: "verification_attempts",
      },
      lastVerificationAttempt: {
        type: DataTypes.DATE,
        allowNull: true,
        field: "last_verification_attempt",
      },
      verifiedAt: {
        type: DataTypes.DATE,
        allowNull: true,
        field: "verified_at",
      },
      payoutEnabled: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
        allowNull: false,
        field: "payout_enabled",
      },
    },
    {
      sequelize,
      modelName: "BankDetails",
      tableName: "bank_details",
      timestamps: true,
      underscored: true,
      paranoid: true, // Enables soft deletes
      indexes: [
        {
          unique: true,
          fields: ["club_id"], // One bank account per club
        },
        {
          fields: ["is_verified"],
        },
        {
          fields: ["is_locked"],
        },
        {
          fields: ["branch_ifsc_code"],
        },
        {
          fields: ["verification_attempts"],
        },
        {
          fields: ["last_verification_attempt"],
        },
      ],
      hooks: {
        // Before validation hook to normalize data
        beforeValidate: (bankDetails) => {
          if (bankDetails.branchIFSCCode) {
            bankDetails.branchIFSCCode = bankDetails.branchIFSCCode
              .toUpperCase()
              .trim();
          }
          if (bankDetails.AccountNumber) {
            bankDetails.AccountNumber = bankDetails.AccountNumber
              .replace(/\s/g, '') // Remove spaces
              .trim();
          }
          if (bankDetails.bankAccountHolderName) {
            bankDetails.bankAccountHolderName = bankDetails.bankAccountHolderName
              .trim()
              .toUpperCase()
              .replace(/\s+/g, ' '); // Replace multiple spaces with single space
          }
          if (bankDetails.bankName) {
            bankDetails.bankName = bankDetails.bankName.trim();
          }
          if (bankDetails.branchName) {
            bankDetails.branchName = bankDetails.branchName.trim();
          }
        },

        // Before creating
        beforeCreate: (bankDetails) => {
          bankDetails.verificationAttempts = 0;
          if (!bankDetails.lastVerificationAttempt) {
            bankDetails.lastVerificationAttempt = new Date();
          }
        },

        // Before updating - handle verification status changes
        beforeUpdate: (bankDetails) => {
          // If verification status changed to true, set verifiedAt and lock account
          if (bankDetails.isVerified && !bankDetails._previousDataValues.isVerified) {
            bankDetails.verifiedAt = new Date();
            bankDetails.isLocked = true;
          }
          
          // If verification attempts increased, update lastVerificationAttempt
          if (bankDetails.verificationAttempts > bankDetails._previousDataValues.verificationAttempts) {
            bankDetails.lastVerificationAttempt = new Date();
          }
        },
      },
      scopes: {
        // Only verified accounts
        verified: {
          where: {
            isVerified: true,
          },
        },

        // Only unverified accounts
        unverified: {
          where: {
            isVerified: false,
          },
        },

        // Only locked accounts
        locked: {
          where: {
            isLocked: true,
          },
        },

        // Accounts that can attempt verification
        canVerify: {
          where: {
            isVerified: false,
            isLocked: false,
            verificationAttempts: {
              [sequelize.Sequelize.Op.lt]: 3,
            },
          },
        },

        // Exclude sensitive data for public views
        publicView: {
          attributes: {
            exclude: ["AccountNumber", "metadata"],
          },
        },

        // Include masked account number
        maskedView: {
          attributes: {
            include: [
              [
                sequelize.fn(
                  "CONCAT",
                  sequelize.fn("LEFT", sequelize.col("account_number"), 4),
                  sequelize.fn(
                    "REPEAT",
                    "*",
                    sequelize.fn(
                      "GREATEST",
                      0,
                      sequelize.fn("LENGTH", sequelize.col("account_number")) - 8
                    )
                  ),
                  sequelize.fn("RIGHT", sequelize.col("account_number"), 4)
                ),
                "maskedAccountNumber",
              ],
            ],
          },
        },
      },
    }
  );

  return BankDetails;
};