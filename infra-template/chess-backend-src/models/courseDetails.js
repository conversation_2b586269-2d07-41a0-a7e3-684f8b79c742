const { Model, DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  class CourseDetails extends Model {
    static associate(models) {
      // Define associations here if needed
    }
  }

  CourseDetails.init(
    {
      id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true,
      },
      userId: {
        type: DataTypes.UUID,
        allowNull: false,
        unique: true,
        references: {
          model: "users",
          key: "id",
        },
        field: "user_id",
      },
      registrationStartDate: {
        type: DataTypes.DATEONLY,
        allowNull: false,
        field: "registration_start_date"
      },
      registrationEndDate: {
        type: DataTypes.DATEONLY,
        allowNull: false,
        field: "registration_end_date"
      },
      startDate: {
        type: DataTypes.DATEONLY,
        allowNull: false,
        field: "start_date"
      },
      endDate: {
        type: DataTypes.DATEONLY,
        allowNull: false,
        field: "end_date"
      },
      description: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      sessionStartTime: {
        type: DataTypes.STRING,
        allowNull: true,
        field: "session_start_time",
      },
      sessionEndTime: {
        type: DataTypes.STRING,
        allowNull: true,
        field: "session_end_time",
      },
      title: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      courseDuration: {
        type: DataTypes.STRING,
        allowNull: false,
        field: "course_duration"
      },
      sessionDuration: {
        type: DataTypes.STRING,
        allowNull: false,
        field: "session_duration"
      },
      totalSessions: {
        type: DataTypes.INTEGER,
        allowNull: false,
        field: "total_sessions"
      },
      sessionsPerWeek: {
        type: DataTypes.INTEGER,
        allowNull: false,
        field: "sessions_per_week"
      },
      sessionDuration: {
        type: DataTypes.STRING,
        allowNull: false,
        field: "sessions_duration"
      },
      courseFee: {
        type: DataTypes.FLOAT,
        allowNull: false,
        field: "course_fee"
      },
      paymentStructure: {
        type: DataTypes.ENUM('hourly', 'monthly', 'weekly', 'one-time'),
        allowNull: false,
        field: "payment_structure"
      },
      courseStatus: {
        type: DataTypes.ENUM('active', 'inactive', 'completed', 'archived', 'cancelled'),
        allowNull: false,
        defaultValue: 'inactive',
        field: "course_status"
      },
      brochureUrl: {
        type: DataTypes.STRING(2083),
        allowNull: true,
        field: "brochure_url"
      },
      selectedDays: {
        type: DataTypes.ARRAY(DataTypes.STRING),
        allowNull: true,
        field: "selected_days",
      },
    },
    {
      sequelize,
      modelName: 'CourseDetails',
      tableName: 'course_details',
      timestamps: true,
    }
  );

  return CourseDetails;
};
