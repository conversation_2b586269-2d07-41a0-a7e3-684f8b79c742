// models/Payout.js
const { DataTypes, Model } = require("sequelize");

module.exports = (sequelize) => {
  class Payout extends Model {
    static associate(models) {
      Payout.belongsTo(models.User, {
        foreignKey: "clubId",
        onDelete: "CASCADE",
        as: "club",
        scope: {
          role: "club",
        },
      });
      Payout.belongsTo(models.Tournament, {
        foreignKey: "tournamentId",
        as: "tournament",
      });
      Payout.belongsTo(models.Bankdetails, {
        foreignKey: "bank_details_id",
        as: "bankDetails",
      });
    }
  }

  Payout.init(
    {
      id: {
        type: DataTypes.UUID,
        primaryKey: true,
        defaultValue: DataTypes.UUIDV4,
      },
      clubId: {
        type: DataTypes.UUID,
        allowNull: false,
        references: {
          model: "users",
          key: "id",
        },
        field: "club_id",
      },
      tournament_id: {
        type: DataTypes.UUID,
        allowNull: true,
        references: {
          model: "tournament",
          key: "id",
        },
      },
      payout_id: {
        type: DataTypes.STRING,
        allowNull: true, // null for setup records
      },
      contact_id: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      fund_account_id: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      bank_details_id: {
        type: DataTypes.UUID,
        references: {
          model: "bank_details",
          key: "id",
        },
      },
      amount: {
        type: DataTypes.DECIMAL(10, 2),
        allowNull: true,
      },
      total_collected: {
        type: DataTypes.DECIMAL(10, 2),
        allowNull: true,
      },
      platform_fee: {
        type: DataTypes.DECIMAL(10, 2),
        allowNull: true,
      },
      processing_fee: {
        type: DataTypes.DECIMAL(10, 2),
        allowNull: true,
      },
      status: {
        type: DataTypes.ENUM(
          "setup_completed",
          "created",
          "queued",
          "initiated",
          "processed",
          "failed",
          "reversed",
          "pending",
          "updated",
          "rejected",
          "processing"
        ),
        allowNull: false,
        defaultValue: "created",
      },
      mode: {
        type: DataTypes.ENUM("IMPS", "NEFT", "RTGS"),
        allowNull: true,
      },
      reference_id: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      urgent_transfer: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
      },
      processed_at: {
        type: DataTypes.DATE,
        allowNull: true,
      },
      failure_reason: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      verification_status: {
        type: DataTypes.ENUM("pending", "verified", "failed"),
        defaultValue: "pending",
      },
      metadata: {
        type: DataTypes.JSON,
        allowNull: true,
      },
      createdAt: {
        type: DataTypes.DATE,
        defaultValue: DataTypes.NOW,
        field: "created_at",
      },
      updatedAt: {
        type: DataTypes.DATE,
        defaultValue: DataTypes.NOW,
        field: "updated_at",
      },
    },
    {

      sequelize,
      modelName: "Payout",
      tableName: "payouts",
      timestamps: true,
      indexes: [
        {
          fields: ["clubId"],
        },
        {
          fields: ["tournament_id"],
        },
        {
          fields: ["bank_details_id"],
        },
        {
          fields: ["status"],
        },
        {
          fields: ["verification_status"],
        },
        {
          fields: ["payout_id"],
        },
        {
          fields: ["reference_id"],
        },
      ],
    }
  );

  return Payout;
};
