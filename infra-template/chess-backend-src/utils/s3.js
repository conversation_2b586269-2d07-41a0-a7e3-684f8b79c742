const {
  S3Client,
  DeleteObjectCommand,
  PutObjectCommand,
  GetObjectCommand,
} = require("@aws-sdk/client-s3");

const { getSignedUrl } = require("@aws-sdk/s3-request-presigner");
const { config: environment, config } = require("../config/config");
const multer = require("multer");
const multerS3 = require("multer-s3");
const path = require("path");
const { v4: uuidv4 } = require("uuid");

// Initialize S3 Client
const s3 = new S3Client({
  region: environment.aws.region,
  credentials: {
    accessKeyId: environment.aws.access_key_id,
    secretAccessKey: environment.aws.secret_access_key,
  },
});

/**
 * File type configurations
 */
const fileTypes = {
  // Image files
  images: {
    mimeTypes: ["image/jpeg", "image/png", "image/gif", "image/webp"],
    maxSize: 3 * 1024 * 1024, // 3MB
    folder: "images",
  },
  // Document files
  documents: {
    mimeTypes: [
      "application/pdf",
      "image/jpeg",
      "image/png",
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", // xlsx
      "application/vnd.ms-excel", // xls
      "application/msword", // doc
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document", // docx
      "text/plain", // txt
      "application/vnd.oasis.opendocument.text", // odt
    ],
    maxSize: 10 * 1024 * 1024, // 10MB
    folder: "documents",
  },
  // Certificate files
  certificates: {
    mimeTypes: ["image/jpeg", "image/png", "application/pdf"],
    maxSize: 5 * 1024 * 1024, // 5MB
    folder: "certificates",
  },
  // Tournament brochures
  brochures: {
    mimeTypes: ["image/jpeg", "image/png", "application/pdf"],
    maxSize: 8 * 1024 * 1024, // 8MB
    folder: "brochures",
  },
  // Advertisement images
  ads: {
    mimeTypes: ["image/jpeg", "image/png", "image/gif", "image/webp"],
    maxSize: 5 * 1024 * 1024, // 5MB
    folder: "advertisements",
  },
  signatures: {
    mimeTypes: ["image/jpeg", "image/png", "image/webp", "image/svg+xml"],
    maxSize: 2 * 1024 * 1024, // 2MB
    folder: "signatures",
  },
   videos: {
    mimeTypes: ["video/mp4", "video/webm", "video/ogg"],
    maxSize: 50 * 1024 * 1024, // 50MB
    folder: "advertisements/videos",
  },
};

/**
 * Entity configurations for organized storage
 */
const entityTypes = {
  player: {
    profileImage: { ...fileTypes.images, folder: "players/profile-images" },
    documents: { ...fileTypes.documents, folder: "players/documents" },
    certificates: { ...fileTypes.certificates, folder: "players/certificates" },
  },
  club: {
    profileImage: { ...fileTypes.images, folder: "clubs/profile-images" },
    documents: { ...fileTypes.documents, folder: "clubs/documents" },
  },
  arbiter: {
    profileImage: { ...fileTypes.images, folder: "arbiters/profile-images" },
    certificates: {
      ...fileTypes.certificates,
      folder: "arbiters/certificates",
    },
  },
   coach: {
    profileImage: { ...fileTypes.images, folder: "coach/profile-images" },
    certificates: {
      ...fileTypes.certificates,
      folder: "coach/certificates",
    },
  },
  tournament: {
    brochure: { ...fileTypes.brochures, folder: "tournaments/brochures" },
    certificates: {
      ...fileTypes.certificates,
      folder: "tournaments/certificates",
    },
    signatures: {
      ...fileTypes.signatures,
      folder: "tournaments/signatures",
      mimeTypes: ["image/jpeg", "image/png", "image/webp", "image/svg+xml"],
      maxSize: 2 * 1024 * 1024, // 2MB for signatures
    },
  },
  advertisement: {
    images: { ...fileTypes.ads, folder: "advertisements" },
    videos: {...fileTypes.videos,folder: "advertisement/videos"},
  },
};

/**
 * Delete file from S3
 * @param {string} fileUrl - Full URL of the file to delete
 * @returns {Promise<object>} - Result of delete operation
 */
const deleteFromS3 = async (fileUrl) => {
  if (!fileUrl) return { success: false, message: "No file URL provided" };

  try {
    // Extract S3 key from URL
    const fileKey = fileUrl.includes(".com/")
      ? fileUrl.split(".com/")[1]
      : fileUrl;

    if (!fileKey) {
      console.warn("Invalid S3 file URL format:", fileUrl);
      return { success: false, message: "Invalid file URL format" };
    }

    const params = {
      Bucket: environment.aws.bucket_name,
      Key: fileKey,
    };

    await s3.send(new DeleteObjectCommand(params));

    return { success: true, key: fileKey };
  } catch (error) {
    console.error("Error deleting file from S3:", error);
    return { success: false, error: error.message };
  }
};

/**
 * Create multer upload configuration for a specific entity and file type
 * @param {string} entity - Entity type (player, club, etc.)
 * @param {string} fileType - File type (profileImage, documents, etc.)
 * @returns {multer.StorageEngine} - Configured multer storage engine
 */
const createUploadConfig = (entity, fileType) => {
  // Get configuration for this entity and file type
  const config = entityTypes[entity]?.[fileType];

  if (!config) {
    throw new Error(`Invalid entity type or file type: ${entity}.${fileType}`);
  }

  return multer({
    storage: multerS3({
      s3: s3,
      bucket: environment.aws.bucket_name,

      contentType: multerS3.AUTO_CONTENT_TYPE,
      key: function (req, file, cb) {
        // Create unique file path with original extension
        const fileExtension = path.extname(file.originalname);

        // For signatures, include tournament ID and signature type in the path
        let fileName;
        if (entity === "tournament" && fileType === "signatures") {
          
          const signatureType = req.body.type || "signature";
          fileName = `${
            config.folder
          }/${signatureType}-${uuidv4()}${fileExtension}`;
        } else {
          fileName = `${config.folder}/${uuidv4()}${fileExtension}`;
        }

        cb(null, fileName);
      },
      metadata: function (req, file, cb) {
        const metadata = {
          fieldName: file.fieldname,
          entityId:
            req.params.id ||
            req.body.entityId ||
            req.body.tournamentId ||
            "unknown",
          originalName: file.originalname,
        };

        // Add signature-specific metadata
        if (entity === "tournament" && fileType === "signatures") {
          metadata.signatureType = req.body.type || "signature";
          metadata.tournamentId = req.body.tournamentId || "unknown";
        }

        cb(null, metadata);
      },
    }),
    limits: {
      fileSize: config.maxSize,
    },
    fileFilter: function (req, file, cb) {
      if (config.mimeTypes.includes(file.mimetype)) {
        cb(null, true);
      } else {
        cb(
          new Error(
            `Unsupported file type. Allowed types: ${config.mimeTypes.join(
              ", "
            )}`
          ),
          false
        );
      }
    },
  });
};

/**
 * Factory function to create upload middleware for each entity and file type
 */
const uploadFactory = {
  // Player uploads
  player: {
    profileImage: () =>
      createUploadConfig("player", "profileImage").single("profileImage"),
    document: () =>
      createUploadConfig("player", "documents").single("document"),
    documents: (maxCount = 5) =>
      createUploadConfig("player", "documents").array("documents", maxCount),
    certificate: () =>
      createUploadConfig("player", "certificates").single("certificate"),
    certificates: (maxCount = 5) =>
      createUploadConfig("player", "certificates").array(
        "certificates",
        maxCount
      ),
    // Fix the profileAndDocuments method in uploadFactory.player
    profileAndDocuments: () => {
      // Create a multer instance that can handle multiple file types with different configurations
      return multer({
        storage: multerS3({
          s3: s3,
          bucket: environment.aws.bucket_name,
          contentType: multerS3.AUTO_CONTENT_TYPE,
          key: function (req, file, cb) {
            const fileExtension = path.extname(file.originalname);
            let folder;

            // Determine folder based on field name
            if (file.fieldname === "profileImage") {
              folder = "players/profile-images";
            } else if (file.fieldname === "myfile") {
              folder = "players/documents";
            } else {
              return cb(new Error("Invalid field name"));
            }

            const fileName = `${folder}/${uuidv4()}${fileExtension}`;
            cb(null, fileName);
          },
          metadata: function (req, file, cb) {
            cb(null, {
              fieldName: file.fieldname,
              entityId: req.params.id || req.body.entityId || "unknown",
              originalName: file.originalname,
            });
          },
        }),
        limits: {
          fileSize: 10 * 1024 * 1024, // Use the larger limit (10MB for documents)
        },
        fileFilter: function (req, file, cb) {
          let allowedMimeTypes;

          // Set allowed mime types based on field name
          if (file.fieldname === "profileImage") {
            allowedMimeTypes = [
              "image/jpeg",
              "image/png",
              "image/gif",
              "image/webp",
            ];
          } else if (file.fieldname === "myfile") {
            allowedMimeTypes = [
              "application/pdf",
              "image/jpeg",
              "image/png",
              "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", // xlsx
              "application/vnd.ms-excel", // xls
              "application/msword", // doc
              "application/vnd.openxmlformats-officedocument.wordprocessingml.document", // docx
              "text/plain", // txt
              "application/vnd.oasis.opendocument.text", // odt
            ];
          } else {
            return cb(new Error("Invalid field name"), false);
          }

          if (allowedMimeTypes.includes(file.mimetype)) {
            cb(null, true);
          } else {
            cb(
              new Error(
                `Unsupported file type for ${
                  file.fieldname
                }. Allowed types: ${allowedMimeTypes.join(", ")}`
              ),
              false
            );
          }
        },
      }).fields([
        { name: "profileImage", maxCount: 1 },
        { name: "myfile", maxCount: 1 },
      ]);
    },
  },

  // Club uploads
  club: {
    profileImage: () =>
      createUploadConfig("club", "profileImage").single("profileImage"),
    document: () => createUploadConfig("club", "documents").single("document"),
    documents: (maxCount = 5) =>
      createUploadConfig("club", "documents").array("documents", maxCount),
  },

  // Arbiter uploads
  arbiter: {
    profileImage: () =>
      createUploadConfig("arbiter", "profileImage").single("profileImage"),
    certificate: () =>
      createUploadConfig("arbiter", "certificates").single("certificate"),
    certificates: (maxCount = 3) =>
      createUploadConfig("arbiter", "certificates").array(
        "certificates",
        maxCount
      ),
  },
  coach: {
    profileImage: () =>
      createUploadConfig("coach", "profileImage").single("profileImage"),
    certificate: () =>
      createUploadConfig("coach", "certificates").single("certificate"),
    certificates: (maxCount = 3) =>
      createUploadConfig("coach", "certificates").array(
        "certificates",
        maxCount
      ),
  },

  // Tournament uploads
  tournament: {
    brochure: () =>
      createUploadConfig("tournament", "brochure").single("brochure"),
    certificate: () =>
      createUploadConfig("tournament", "certificates").single("certificate"),
    certificates: (maxCount = 10) =>
      createUploadConfig("tournament", "certificates").array(
        "certificates",
        maxCount
      ),
    signature: () =>
      createUploadConfig("tournament", "signatures").single("signature"),
    signatures: (maxCount = 10) =>
      createUploadConfig("tournament", "signatures").array(
        "signatures",
        maxCount
      ),
  },

  // Advertisement uploads
  advertisement: {
    image: () => createUploadConfig("advertisement", "images").single("image"),
    images: (maxCount = 5) =>createUploadConfig("advertisement", "images").array("images", maxCount),
    video: () =>createUploadConfig("advertisement", "videos").single("video"),},
};

/**
 * Direct upload function for programmatic file uploads
 * @param {Buffer} fileBuffer - File data buffer
 * @param {string} fileName - Original file name
 * @param {string} contentType - MIME type of the file
 * @param {string} entity - Entity type
 * @param {string} fileType - File type
 * @param {string} entityId - Optional ID of related entity
 * @returns {Promise<object>} - Upload result
 */
const uploadToS3 = async (
  fileBuffer,
  fileName,
  contentType,
  entity,
  fileType,
  entityId = null,
  additionalMetadata = {}
) => {
  try {
    // Get configuration for this entity and file type
    const config = entityTypes[entity]?.[fileType];

    if (!config) {
      throw new Error(
        `Invalid entity type or file type: ${entity}.${fileType}`
      );
    }

    // Validate mime type
    if (!config.mimeTypes.includes(contentType)) {
      return {
        success: false,
        error: `Invalid file type. Allowed types: ${config.mimeTypes.join(
          ", "
        )}`,
      };
    }

    // Validate file size
    if (fileBuffer.length > config.maxSize) {
      return {
        success: false,
        error: `File too large. Maximum size: ${
          config.maxSize / (1024 * 1024)
        }MB`,
      };
    }

    // Generate file path
     const fileExtension = path.extname(fileName);
    let fileKey;
    
    // Special handling for signatures
    if (entity === 'tournament' && fileType === 'signatures') {
      const signatureType = additionalMetadata.signatureType || 'signature';
      fileKey = `${config.folder}/${signatureType}-${uuidv4()}${fileExtension}`;
    } else {
      const idPart = entityId ? `${entityId}/` : "";
      fileKey = `${config.folder}/${idPart}${uuidv4()}${fileExtension}`;
    }
    const params = {
      Bucket: environment.aws.bucket_name,
      Key: fileKey,
      Body: fileBuffer,
      ContentType: contentType,
      Metadata: {
        originalName: fileName,
        entityId: entityId || "unknown",
        ...additionalMetadata,
      },
    };

    await s3.send(new PutObjectCommand(params));

    // Return the full URL of the uploaded file
    const fileUrl = `https://${environment.aws.bucket_name}.s3.${environment.aws.region}.amazonaws.com/${fileKey}`;

    return {
      success: true,
      fileUrl,
      fileKey,
    };
  } catch (error) {
    console.error("Error uploading file to S3:", error);
    return {
      success: false,
      error: error.message,
    };
  }
};

// Helper function to handle file replacement
const replaceFile = async (
  oldFileUrl,
  newFile,
  entity,
  fileType,
  entityId = null,
  additionalMetadata = {}
) => {
  try {
    // Delete the old file if it exists
    if (oldFileUrl) {
      await deleteFromS3(oldFileUrl);
    }

    // If no new file was uploaded, just return success with no new URL
    if (!newFile) {
      return {
        success: true,
        fileUrl: oldFileUrl,
        message: "No new file uploaded, kept existing file",
      };
    }

    // If we have a multer-s3 file object
    if (newFile.location) {
      return {
        success: true,
        fileUrl: newFile.location,
        fileKey: newFile.key,
      };
    }

    // If we have a file buffer (from direct upload)
    if (Buffer.isBuffer(newFile.buffer)) {
      return await uploadToS3(
        newFile.buffer,
        newFile.originalname,
        newFile.mimetype,
        entity,
        fileType,
        entityId,
        additionalMetadata
      );
    }

    return {
      success: false,
      error: "Invalid file format for replacement",
    };
  } catch (error) {
    console.error("Error replacing file:", error);
    return {
      success: false,
      error: error.message,
    };
  }
};

/**
 * Error handling middleware for multer errors
 */
const handleUploadError = (err, req, res, next) => {
  if (err instanceof multer.MulterError) {
    if (err.code === "LIMIT_FILE_SIZE") {
      return res.status(413).json({
        success: false,
        message: "File too large",
      });
    }
    return res.status(400).json({
      success: false,
      message: `Upload error: ${err.message}`,
    });
  } else if (err) {
    return res.status(500).json({
      success: false,
      message: err.message,
    });
  }
  next();
};


/**
 * Generates a presigned URL for an S3 object
 * @param fileUrl - The complete S3 URL of the file
 * @param expirationSeconds - How long the URL should be valid (in seconds)
 * @returns The presigned URL
 */
const generatePresignedUrl = async (fileUrl, expirationSeconds = 600) => {
  // Extract the S3 key from the URL
  const s3Key = fileUrl.split(".com/")[1];

  if (!s3Key) {
    throw new Error("Invalid S3 URL format");
  }

  // Initialize S3 client
  const s3Client = new S3Client({
    region: config.aws.region,
    credentials: {
      accessKeyId: config.aws.access_key_id,
      secretAccessKey: config.aws.secret_access_key,
    },
  });

  // Generate presigned URL
  const command = new GetObjectCommand({
    Bucket: config.aws.bucket_name,
    Key: s3Key,
  });

  const presignedUrl = await getSignedUrl(s3Client, command, {
    expiresIn: expirationSeconds,
  });

  return presignedUrl;
};

module.exports = {
  s3,
  deleteFromS3,
  uploadToS3,
  uploadFactory,
  replaceFile,
  handleUploadError,
  fileTypes,
  entityTypes,
  generatePresignedUrl,
};
