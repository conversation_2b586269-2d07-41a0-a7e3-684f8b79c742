#!/usr/bin/env node

/**
 * Development Seed Runner
 * 
 * This script creates development data using the existing seed functions
 * 
 * Usage: node utils/seed/runSeed.js
 */

const { sequelize, models } = require('../../config/db');
const { seedDatabase } = require('./seed');

async function runSeed() {
    try {
        console.log('🚀 Starting Development Database Seeding...');
        console.log('This will create development data for testing and development.');
        console.log('');

        // Test database connection
        await sequelize.authenticate();
        console.log('✅ Database connection established successfully.');

        // Run the development seed
        const startTime = Date.now();
        
        await seedDatabase(models, {
            ignoreDuplicates: true
        });

        const endTime = Date.now();
        const duration = ((endTime - startTime) / 1000).toFixed(2);

        console.log('');
        console.log('🎉 Development Database Seeding Completed Successfully!');
        console.log(`⏱️  Total time taken: ${duration} seconds`);
        console.log('');
        console.log('📊 Summary of created data:');
        
        // Get counts of created data
        const userCount = await models.User.count();
        const clubCount = await models.ClubDetail.count();
        const playerCount = await models.PlayerDetail.count();
        const arbiterCount = await models.ArbiterDetails.count();
        const tournamentCount = await models.Tournament.count();

        console.log(`  👥 Total Users: ${userCount}`);
        console.log(`  🏢 Clubs: ${clubCount}`);
        console.log(`  🎯 Players: ${playerCount}`);
        console.log(`  ⚖️  Arbiters: ${arbiterCount}`);
        console.log(`  🏆 Tournaments: ${tournamentCount}`);
        console.log('');
        console.log('🔍 You can now use this data for development and testing!');
        
    } catch (error) {
        console.error('❌ Error during development seeding:', error);
        console.error('Stack trace:', error.stack);
        process.exit(1);
    } finally {
        // Close database connection
        await sequelize.close();
        console.log('🔌 Database connection closed.');
    }
}

// Handle process termination gracefully
process.on('SIGINT', async () => {
    console.log('\n⚠️  Received SIGINT. Closing database connection...');
    await sequelize.close();
    process.exit(0);
});

process.on('SIGTERM', async () => {
    console.log('\n⚠️  Received SIGTERM. Closing database connection...');
    await sequelize.close();
    process.exit(0);
});

// Run the script if called directly
if (require.main === module) {
    runSeed();
}

module.exports = runSeed;
