#!/usr/bin/env node

/**
 * Load Test Seed Runner
 * 
 * This script creates comprehensive load testing data:
 * - 500 players
 * - 100 clubs  
 * - 100 arbiters
 * - 200 tournaments
 * - 500 player registrations (350 individual + 150 bulk)
 * - All associated payments
 * 
 * Usage: node utils/seed/runLoadTestSeed.js
 */

const path = require('path');
const { sequelize, models } = require('../../config/db');
const loadTestSeed = require('./loadTestSeed');

async function runLoadTestSeed() {
    try {
        console.log('🚀 Starting Load Test Database Seeding...');
        console.log('This will create:');
        console.log('  - 500 players');
        console.log('  - 100 clubs');
        console.log('  - 100 arbiters');
        console.log('  - 200 tournaments');
        console.log('  - 100,000 player registrations (500 per tournament)');
        console.log('  - 100,000 payments (500 per tournament)');
        console.log('');

        // Test database connection
        await sequelize.authenticate();
        console.log('✅ Database connection established successfully.');

        // Run the load test seed
        const startTime = Date.now();
        
        await loadTestSeed(models, {
            ignoreDuplicates: true
        });

        const endTime = Date.now();
        const duration = ((endTime - startTime) / 1000).toFixed(2);

        console.log('');
        console.log('🎉 Load Test Database Seeding Completed Successfully!');
        console.log(`⏱️  Total time taken: ${duration} seconds`);
        console.log('');
        console.log('📊 Summary of created data:');
        
        // Get counts of created data
        const userCount = await models.User.count({
            where: {
                email: {
                    [require('sequelize').Op.like]: '<EMAIL>'
                }
            }
        });
        
        const clubCount = await models.ClubDetail.count({
            include: [{
                model: models.User,
                as: 'user',
                where: {
                    email: {
                        [require('sequelize').Op.like]: '<EMAIL>'
                    }
                }
            }]
        });

        const playerCount = await models.PlayerDetail.count({
            include: [{
                model: models.User,
                where: {
                    email: {
                        [require('sequelize').Op.like]: '<EMAIL>'
                    }
                }
            }]
        });

        const arbiterCount = await models.ArbiterDetails.count({
            include: [{
                model: models.User,
                as: 'user',
                where: {
                    email: {
                        [require('sequelize').Op.like]: '<EMAIL>'
                    }
                }
            }]
        });
        
        const tournamentCount = await models.Tournament.count({
            where: {
                title: {
                    [require('sequelize').Op.like]: 'loadtest-%'
                }
            }
        });
        
        const registrationCount = await models.Registration.count({
            where: {
                regId: {
                    [require('sequelize').Op.like]: 'LTR%'
                }
            }
        });
        
        const bulkRegistrationCount = await models.BulkRegistration.count({
            where: {
                bulkRegistrationId: {
                    [require('sequelize').Op.like]: 'ltbr-%'
                }
            }
        });
        
        const paymentCount = await models.Payment.count({
            where: {
                paymentTransactionId: {
                    [require('sequelize').Op.like]: 'LT-%'
                }
            }
        });

        console.log(`  👥 Total Users: ${userCount}`);
        console.log(`  🏢 Clubs: ${clubCount}`);
        console.log(`  🎯 Players: ${playerCount}`);
        console.log(`  ⚖️  Arbiters: ${arbiterCount}`);
        console.log(`  🏆 Tournaments: ${tournamentCount}`);
        console.log(`  📝 Individual Registrations: ${registrationCount}`);
        console.log(`  📋 Bulk Registrations: ${bulkRegistrationCount}`);
        console.log(`  💳 Payments: ${paymentCount}`);
        console.log('');
        console.log('🔍 You can now use this data for load testing your application!');
        
    } catch (error) {
        console.error('❌ Error during load test seeding:', error);
        console.error('Stack trace:', error.stack);
        process.exit(1);
    } finally {
        // Close database connection
        await sequelize.close();
        console.log('🔌 Database connection closed.');
    }
}

// Handle process termination gracefully
process.on('SIGINT', async () => {
    console.log('\n⚠️  Received SIGINT. Closing database connection...');
    await sequelize.close();
    process.exit(0);
});

process.on('SIGTERM', async () => {
    console.log('\n⚠️  Received SIGTERM. Closing database connection...');
    await sequelize.close();
    process.exit(0);
});

// Run the script if called directly
if (require.main === module) {
    runLoadTestSeed();
}

module.exports = runLoadTestSeed;
