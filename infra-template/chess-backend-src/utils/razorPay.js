const crypto = require("crypto");
const fs = require("fs");
const path = require("path");
const { config } = require("../config/config");
const axios = require("axios");
const { generateAbbreviation } = require("./utils");

/**
 * Initialize the Razorpay SDK
 */
let razorpayInstance = null;
try {
  const Razorpay = require("razorpay");
  razorpayInstance = new Razorpay({
    key_id: config.razorpay.key,
    key_secret: config.razorpay.secret,
  });
} catch (error) {
  console.error("Razorpay SDK initialization failed:", error.message);
  throw new Error("Razorpay SDK is required for payment processing");
}

const createRazorpayClient = () => {
  const auth = Buffer.from(
    `${config.razorpay.key}:${config.razorpay.secret}`
  ).toString("base64");

  return axios.create({
    baseURL: config.razorpay.url_v1,
    headers: {
      Authorization: `Basic ${auth}`,
      "Content-Type": "application/json",
    },
  });
};

const razorpayClient = createRazorpayClient();

/**
 * Format amount to paise (Razorpay uses smallest currency unit)
 * @param {string|number} amount - Amount in rupees
 * @returns {number} - Amount in paise
 */
const formatAmount = (amount) => {
  const parsed = parseFloat(amount);
  if (isNaN(parsed)) {
    throw new Error("Invalid amount");
  }
  return Math.round(parsed * 100);
};

/**
 * Format amount from paise to rupees
 * @param {number} amountInPaise - Amount in paise
 * @returns {number} - Amount in rupees
 */
const formatAmountFromPaise = (amountInPaise) => {
  return parseFloat((amountInPaise / 100).toFixed(2));
};

/**
 * Create Razorpay order
 * @param {Object} orderData - Order creation data
 * @returns {Promise<Object>} - Created order details
 */
const createOrder = async (orderData) => {
  try {
    const {
      amount,
      currency = "INR",
      receipt,
      notes = {},
      partial_payment = false,
    } = orderData;

    // Validate required fields
    if (!amount) {
      throw new Error("Amount is required");
    }
    if (!receipt) {
      throw new Error("Receipt is required");
    }

    const amountInPaise = formatAmount(amount);

    const orderOptions = {
      amount: amountInPaise,
      currency,
      receipt,
      notes,
      partial_payment,
    };

    const order = await razorpayInstance.orders.create(orderOptions);

    return {
      success: true,
      order,
      formattedAmount: formatAmountFromPaise(order.amount),
    };
  } catch (error) {
    console.error("❌ Failed to create Razorpay order:", {
      message: error.message,
      code: error.code,
      statusCode: error.statusCode,
      description: error.description,
      field: error.field,
      step: error.step,
      reason: error.reason,
      source: error.source,
      metadata: error.metadata,
    });

    return {
      success: false,
      error: error.message,
      details: error,
    };
  }
};

/**
 * Verify Razorpay payment signature
 * @param {Object} params - Payment verification parameters
 * @returns {boolean} - Whether the signature is valid
 */
const verifyPaymentSignature = (params) => {
  try {
    const { razorpay_order_id, razorpay_payment_id, razorpay_signature } =
      params;

    if (!razorpay_order_id || !razorpay_payment_id || !razorpay_signature) {
      console.error(
        "❌ Missing required parameters for signature verification"
      );
      return false;
    }

    // Create the signature string
    const signatureString = `${razorpay_order_id}|${razorpay_payment_id}`;

    // Generate expected signature
    const expectedSignature = crypto
      .createHmac("sha256", config.razorpay.secret)
      .update(signatureString)
      .digest("hex");

    const isValid = expectedSignature === razorpay_signature;

    if (isValid) {
    } else {
      console.error("❌ Payment signature verification failed");
    }

    return isValid;
  } catch (error) {
    console.error("❌ Error during signature verification:", error);
    return false;
  }
};

/**
 * Verify Razorpay webhook signature
 * @param {string} webhookBody - Raw webhook body
 * @param {string} signature - Webhook signature from headers
 * @param {string} webhookSecret - Webhook secret from Razorpay dashboard
 * @returns {boolean} - Whether the webhook signature is valid
 */
const verifyWebhookSignature = (webhookBody, signature, webhookSecret) => {
  try {
    if (!webhookBody || !signature || !webhookSecret) {
      console.error("❌ Missing required parameters for webhook verification");
      return false;
    }

    const expectedSignature = crypto
      .createHmac("sha256", webhookSecret)
      .update(webhookBody)
      .digest("hex");

    const isValid = expectedSignature === signature;

    if (isValid) {
    } else {
      console.error("❌ Webhook signature verification failed");
    }

    return isValid;
  } catch (error) {
    console.error("❌ Error during webhook signature verification:", error);
    return false;
  }
};

/**
 * Fetch payment details from Razorpay
 * @param {string} paymentId - Razorpay payment ID
 * @returns {Promise<Object>} - Payment details
 */
const fetchPaymentDetails = async (paymentId) => {
  try {
    const payment = await razorpayInstance.payments.fetch(paymentId);

    return {
      success: true,
      payment: {
        ...payment,
        formattedAmount: formatAmountFromPaise(payment.amount),
      },
    };
  } catch (error) {
    console.error("❌ Failed to fetch payment details:", error);
    return {
      success: false,
      error: error.message,
      details: error,
    };
  }
};

/**
 * Fetch order details from Razorpay
 * @param {string} orderId - Razorpay order ID
 * @returns {Promise<Object>} - Order details
 */
const fetchOrderDetails = async (orderId) => {
  try {
    const order = await razorpayInstance.orders.fetch(orderId);

    return {
      success: true,
      order: {
        ...order,
        formattedAmount: formatAmountFromPaise(order.amount),
      },
    };
  } catch (error) {
    console.error("❌ Failed to fetch order details:", error);
    return {
      success: false,
      error: error.message,
      details: error,
    };
  }
};

/**
 * Capture payment (for payments that are authorized but not captured)
 * @param {string} paymentId - Razorpay payment ID
 * @param {number} amount - Amount to capture in paise
 * @param {string} currency - Currency code
 * @returns {Promise<Object>} - Capture result
 */
const capturePayment = async (paymentId, amount, currency = "INR") => {
  try {
    const captureResult = await razorpayInstance.payments.capture(
      paymentId,
      amount,
      currency
    );

    return {
      success: true,
      payment: {
        ...captureResult,
        formattedAmount: formatAmountFromPaise(captureResult.amount),
      },
    };
  } catch (error) {
    console.error("❌ Failed to capture payment:", error);
    return {
      success: false,
      error: error.message,
      details: error,
    };
  }
};

/**
 * Create refund for a payment
 * @param {string} paymentId - Razorpay payment ID
 * @param {Object} refundData - Refund details
 * @returns {Promise<Object>} - Refund result
 */
const createRefund = async (paymentId, refundData = {}) => {
  try {
    const { amount, notes = {}, receipt, speed = "normal" } = refundData;

    const refundOptions = {
      ...(amount && { amount: formatAmount(amount) }),
      notes,
      ...(receipt && { receipt }),
      speed,
    };

    const refund = await razorpayInstance.payments.refund(
      paymentId,
      refundOptions
    );

    return {
      success: true,
      refund: {
        ...refund,
        formattedAmount: formatAmountFromPaise(refund.amount),
      },
    };
  } catch (error) {
    console.error("❌ Failed to create refund:", error);
    return {
      success: false,
      error: error.message,
      details: error,
    };
  }
};

/**
 * Generate checkout options for frontend integration
 * @param {Object} checkoutData - Checkout configuration
 * @returns {Object} - Razorpay checkout options
 */
const generateCheckoutOptions = (checkoutData) => {
  const {
    order_id,
    amount,
    currency = "INR",
    name,
    description,
    image,
    prefill = {},
    notes = {},
    theme = {},
    modal = {},
    callback_url,
    redirect = false,
  } = checkoutData;

  const checkoutOptions = {
    key: config.razorpay.key,
    order_id,
    amount: formatAmount(amount),
    currency,
    name,
    description,
    image,
    prefill: {
      name: prefill.name || "",
      email: prefill.email || "",
      contact: prefill.contact || "",
    },
    notes,
    theme: {
      color: theme.color || "#3399cc",
    },
    modal: {
      ondismiss: modal.ondismiss || function () {},
    },
    ...(callback_url && { callback_url }),
    redirect,
  };

  return checkoutOptions;
};

/**
 * Process webhook event
 * @param {Object} webhookEvent - Webhook event data
 * @returns {Object} - Processed event information
 */
const processWebhookEvent = (webhookEvent) => {
  try {
    const { event, payload } = webhookEvent;

    const processedEvent = {
      event,
      entity:
        payload.payment?.entity ||
        payload.order?.entity ||
        payload.refund?.entity ||
        payload.fund_account?.entity ||
        payload.fund_account_validation?.entity,
      timestamp: new Date().toISOString(),
    };

    // Add formatted amounts where applicable
    if (payload.payment?.entity?.amount) {
      processedEvent.formattedAmount = formatAmountFromPaise(
        payload.payment.entity.amount
      );
    }

    if (payload.order?.entity?.amount) {
      processedEvent.formattedAmount = formatAmountFromPaise(
        payload.order.entity.amount
      );
    }

    if (payload.refund?.entity?.amount) {
      processedEvent.formattedAmount = formatAmountFromPaise(
        payload.refund.entity.amount
      );
    }

    // Handle fund account validation webhooks
    if (payload.fund_account_validation?.entity) {
      const validation = payload.fund_account_validation.entity;
      processedEvent.validation_id = validation.id;
      processedEvent.fund_account_id = validation.fund_account.id;
      processedEvent.validation_status = validation.status;
      processedEvent.validation_results = validation.results;
      processedEvent.amount_deposited = validation.amount_deposited;
      processedEvent.formattedAmount = validation.amount_deposited
        ? formatAmountFromPaise(validation.amount_deposited)
        : "₹0.00";
    }

    return {
      success: true,
      processedEvent,
      originalPayload: webhookEvent,
    };
  } catch (error) {
    console.error("❌ Failed to process webhook event:", error);
    return {
      success: false,
      error: error.message,
      originalPayload: webhookEvent,
    };
  }
};

/**
 * Log Razorpay transaction details
 * @param {Object} transactionData - Transaction data to log
 * @param {string} logType - Type of log (order, payment, refund, etc.)
 */
const logTransaction = (transactionData, logType = "transaction") => {
  try {
    const logDir = path.join(__dirname, "../logs");
    if (!fs.existsSync(logDir)) {
      fs.mkdirSync(logDir, { recursive: true });
    }

    const logFile = path.join(logDir, "razorpay_transactions.log");
    const logData =
      JSON.stringify(
        {
          timestamp: new Date().toISOString(),
          type: logType,
          data: transactionData,
        },
        null,
        2
      ) + "\n\n";

    fs.appendFileSync(logFile, logData);
  } catch (err) {
    console.error("Failed to log transaction:", err);
  }
};

/**
 * Utility function to get payment status in a standardized format
 * @param {string} razorpayStatus - Razorpay payment status
 * @returns {string} - Standardized status
 */
const getStandardizedStatus = (razorpayStatus) => {
  const statusMap = {
    created: "pending",
    authorized: "authorized",
    captured: "success",
    refunded: "refunded",
    failed: "failed",
  };

  return statusMap[razorpayStatus] || razorpayStatus;
};
/**
 * Enhanced Tournament Payout System with Razorpay Integration
 * Combines bank verification and tournament payout functionality
 */

/**
 * Create contact using direct API (not available in SDK)
 * @param {Object} contactData - Contact details
 * @returns {Promise<Object>} - Contact creation result
 */
const createContact = async (contactData) => {
  try {
    const {
      name,
      email,
      contact,
      type = "vendor",
      reference_id,
      notes = {},
    } = contactData;

    console.log("🏢 Creating contact:", name);

    const response = await razorpayClient.post("/contacts", {
      name,
      email,
      contact,
      type,
      reference_id: reference_id || `contact_${Date.now()}`,
      notes,
    });

    const contact_created = response.data;
    console.log("✅ Contact created:", contact_created.id);

    return {
      success: true,
      contact_id: contact_created.id,
      contact: contact_created,
    };
  } catch (error) {
    console.error("❌ Failed to create contact:", error);
    return {
      success: false,
      error: error.response?.data?.error?.description || error.message,
      error_code:
        error.response?.data?.error?.code || "CONTACT_CREATION_FAILED",
    };
  }
};

/**
 * Fetch contact using direct API
 * @param {string} contactId - Contact ID
 * @returns {Promise<Object>} - Contact details
 */
const fetchContact = async (contactId) => {
  try {
    const response = await razorpayClient.get(`/contacts/${contactId}`);
    return {
      success: true,
      contact: response.data,
    };
  } catch (error) {
    console.error("❌ Failed to fetch contact:", error);
    return {
      success: false,
      error: error.response?.data?.error?.description || error.message,
    };
  }
};

// ================== FUND ACCOUNT FUNCTIONS (MIXED - SDK + API) ==================

/**
 * Create fund account using SDK (available)
 * @param {string} contactId - Contact ID
 * @param {Object} bankDetails - Bank account details
 * @returns {Promise<Object>} - Fund account creation result
 */
const createFundAccount = async (contactId, bankDetails) => {
  try {
    console.log("🏦 Creating fund account for contact:", contactId);

    const fundAccount = await razorpayInstance.fundAccount.create({
      contact_id: contactId,
      account_type: "bank_account",
      bank_account: {
        name: bankDetails.accountHolderName || bankDetails.name,
        account_number: bankDetails.accountNumber || bankDetails.account_number,
        ifsc: (bankDetails.ifscCode || bankDetails.ifsc).toUpperCase(),
      },
    });

    console.log("✅ Fund account created:", fundAccount.id);

    return {
      success: true,
      fund_account_id: fundAccount.id,
      fundAccount,
    };
  } catch (error) {
    console.error("❌ Failed to create fund account:", error);
    return {
      success: false,
      error: error.error?.description || error.message,
      error_code: error.error?.code || "FUND_ACCOUNT_CREATION_FAILED",
    };
  }
};

/**
 * Verify bank account using Fund Account Validation - Mixed approach
 * @param {Object} bankDetails - Bank account details
 * @returns {Promise<Object>} - Verification result
 */
const verifyBankAccount = async ({ account_number, ifsc, name }) => {
  try {
    console.log("🏦 Starting bank account verification with Razorpay FAV");

    // Step 1: Create a contact
    const contactResult = await createContact({
      name: name,
      type: "vendor",
      reference_id: `contact_${Date.now()}`,
    });

    if (!contactResult.success) {
      throw new Error(`Contact creation failed: ${contactResult.error}`);
    }

    console.log("✅ Contact created:", contactResult.contact_id);

    // Step 2: Create a fund account
    const fundAccountResult = await createFundAccount(
      contactResult.contact_id,
      {
        name: name,
        ifsc: ifsc.toUpperCase(),
        account_number: account_number,
      }
    );

    if (!fundAccountResult.success) {
      throw new Error(
        `Fund account creation failed: ${fundAccountResult.error}`
      );
    }

    console.log("✅ Fund account created:", fundAccountResult.fund_account_id);

    // Step 3: Create Fund Account Validation
    const validationPayload = {
      fund_account: {
        id: fundAccountResult.fund_account_id,
      },
      amount: 100, // Amount in paise (₹1.00)
      currency: "INR",
      notes: {
        purpose: "bank_account_verification",
        timestamp: new Date().toISOString(),
      },
    };

    console.log("📤 Creating validation with payload:", validationPayload);

    const validationResponse = await razorpayClient.post(
      "/fund_accounts/validations",
      validationPayload
    );
    const validation = validationResponse.data;

    console.log("✅ Fund account validation initiated:", validation.id);
    console.log("📋 Initial validation status:", validation.status);

    // Step 4: Quick initial status check (no long polling)
    let validationStatus = validation;
    let attempts = 0;
    const maxAttempts = 2; // Only 2 quick attempts
    const delay = 2000; // Reduced to 2 seconds

    console.log(
      "⚡ Performing quick verification check (webhook will handle completion)..."
    );

    while (
      (validationStatus.status === "created" ||
        validationStatus.status === "initiated") &&
      attempts < maxAttempts
    ) {
      attempts++;
      console.log(`⏳ Quick check ${attempts}/${maxAttempts}...`);
      await new Promise((resolve) => setTimeout(resolve, delay));

      try {
        const statusResponse = await razorpayClient.get(
          `/fund_accounts/validations/${validation.id}`
        );
        validationStatus = statusResponse.data;

        console.log(`📋 Quick status check ${attempts}:`, {
          status: validationStatus.status,
          id: validationStatus.id,
        });

        // Break if status changed from created/initiated
        if (!["created", "initiated"].includes(validationStatus.status)) {
          console.log("🔄 Status changed quickly, verification complete!");
          break;
        }
      } catch (fetchError) {
        console.error(
          `❌ Error in quick status check ${attempts}:`,
          fetchError.message
        );
        break; // Don't retry on errors
      }
    }

    // Final status evaluation
    const isSuccess = validationStatus.status === "completed";
    const isFailed = validationStatus.status === "failed";
    const isPending =
      validationStatus.status === "created" ||
      validationStatus.status === "initiated";

    console.log("🏁 Quick verification result:", {
      status: validationStatus.status,
      success: isSuccess,
      pending: isPending,
      results: validationStatus.results,
    });

    // Extract error information properly
    let errorMessage = null;
    let statusMessage = null;

    if (isFailed && validationStatus.results) {
      errorMessage =
        validationStatus.results.bank_response ||
        validationStatus.results.reason ||
        validationStatus.results.error ||
        "Bank verification failed - unknown reason";
    } else if (isPending) {
      statusMessage =
        "Verification initiated successfully. You'll be notified when complete (usually within 5-10 minutes).";
    } else if (isSuccess) {
      statusMessage = "Bank account verified successfully!";
    }

    return {
      success: isSuccess || isPending, // Consider pending as success for immediate response
      status: validationStatus.status,
      validation_id: validation.id,
      fund_account_id: fundAccountResult.fund_account_id,
      contact_id: contactResult.contact_id,
      results: validationStatus.results || {},
      amount_deposited: validationStatus.amount_deposited || 0,
      error: errorMessage,
      message: statusMessage,
      is_pending: isPending,
      webhook_required: isPending, // Indicates webhook will provide final status
      attempts_made: attempts,
      metadata: {
        account_number: account_number,
        ifsc: ifsc.toUpperCase(),
        account_holder_name: name,
        verification_timestamp: new Date().toISOString(),
        total_polling_time: `${(attempts * delay) / 1000} seconds`,
        webhook_expected: isPending,
      },
    };
  } catch (error) {
    console.error("❌ Error verifying bank account with Razorpay:", error);

    // Enhanced error extraction
    let errorMessage = "Bank verification failed";
    let errorCode = "VERIFICATION_ERROR";

    if (error.response?.data) {
      const errorData = error.response.data;
      errorMessage =
        errorData.error?.description ||
        errorData.description ||
        errorData.message ||
        errorMessage;
      errorCode = errorData.error?.code || errorData.code || errorCode;

      console.error("🔍 Detailed error data:", errorData);
    } else {
      errorMessage = error.message || errorMessage;
      errorCode = error.code || errorCode;
    }

    return {
      success: false,
      status: "failed",
      error: errorMessage,
      error_code: errorCode,
      metadata: {
        account_number: account_number,
        ifsc: ifsc?.toUpperCase(),
        account_holder_name: name,
        verification_timestamp: new Date().toISOString(),
      },
    };
  }
};

/**
 * Fetch validation details using direct API
 * @param {string} validationId - Validation ID
 * @returns {Promise<Object>} - Validation details
 */
const fetchValidationDetails = async (validationId) => {
  try {
    const response = await razorpayClient.get(
      `/fund_accounts/validations/${validationId}`
    );
    const validation = response.data;

    return {
      success: true,
      validation: {
        ...validation,
        formattedAmount: validation.amount_deposited
          ? (validation.amount_deposited / 100).toFixed(2)
          : "0.00",
      },
    };
  } catch (error) {
    console.error("❌ Failed to fetch validation details:", error);
    return {
      success: false,
      error: error.response?.data?.error?.description || error.message,
    };
  }
};

// ================== PAYOUT FUNCTIONS (NOT IN SDK - DIRECT API) ==================

/**
 * Create payout using direct API (not available in SDK)
 * @param {Object} payoutData - Payout details
 * @returns {Promise<Object>} - Payout creation result
 */
const createPayout = async (payoutData) => {
  try {
    const {
      fund_account_id,
      amount, // Amount in rupees
      mode = "IMPS",
      purpose = "payout",
      reference_id,
      narration,
      notes = {},
    } = payoutData;

    console.log("💸 Creating payout:", reference_id);

    if (!fund_account_id || !amount) {
      throw new Error("Missing required payout parameters");
    }

    if (amount < 1) {
      throw new Error("Payout amount must be at least ₹1");
    }

    const amountInPaise = Math.round(amount * 100);

    const sanitizedNarration = sanitizeNarration(narration);

    const response = await razorpayClient.post("/payouts", {
      account_number: process.env.RAZORPAY_ACCOUNT_NUMBER,
      fund_account_id,
      amount: amountInPaise,
      currency: "INR",
      mode,
      purpose,
      queue_if_low_balance: true,
      reference_id: reference_id || `payout_${Date.now()}`,
      narration: sanitizedNarration,
      notes,
    });

    const payout = response.data;
    console.log("✅ Payout created:", payout.id);
    console.log("📋 Payout details:", payout);

    return {
      success: true,
      payout_id: payout.id,
      amount: amount,
      amount_in_paise: payout.amount,
      status: payout.status,
      mode: payout.mode,
      reference_id: payout.reference_id,
      narration: payout.narration,
      created_at: payout.created_at,
      fund_account_id: payout.fund_account_id,
    };
  } catch (error) {
    console.error("❌ Failed to create payout:", error);
    return {
      success: false,
      error: error.response?.data?.error?.description || error.message,
      error_code: error.response?.data?.error?.code || "PAYOUT_CREATION_FAILED",
    };
  }
};

/**
 * Check payout status using direct API
 * @param {string} payoutId - Payout ID
 * @returns {Promise<Object>} - Payout status
 */
const checkPayoutStatus = async (payoutId) => {
  try {
    const response = await razorpayClient.get(`/payouts/${payoutId}`);
    const payout = response.data;

    return {
      success: true,
      payout: {
        id: payout.id,
        status: payout.status,
        amount: payout.amount / 100,
        mode: payout.mode,
        reference_id: payout.reference_id,
        created_at: payout.created_at,
        processed_at: payout.processed_at,
        failure_reason: payout.failure_reason,
        utr: payout.utr,
        fees: payout.fees / 100,
        tax: payout.tax / 100,
        fund_account_id: payout.fund_account.id,
        status_description: getStatusDescription(payout.status),
      },
    };
  } catch (error) {
    console.error("❌ Status check failed:", error);
    return {
      success: false,
      error: error.response?.data?.error?.description || error.message,
      error_code: error.response?.data?.error?.code || "STATUS_CHECK_FAILED",
    };
  }
};

/**
 * Get account balance using direct API
 * @returns {Promise<Object>} - Account balance
 */
const getAccountBalance = async () => {
  try {
    const response = await razorpayClient.get("/accounts/balance");
    const balance = response.data;

    return {
      success: true,
      balance: {
        total: balance.balance / 100,
        available: balance.balance / 100,
        currency: "INR",
        last_updated: new Date().toISOString(),
      },
    };
  } catch (error) {
    console.error("❌ Failed to fetch account balance:", error);
    return {
      success: false,
      error: error.response?.data?.error?.description || error.message,
    };
  }
};

/**
 * Cancel payout using direct API
 * @param {string} payoutId - Payout ID
 * @returns {Promise<Object>} - Cancellation result
 */
const cancelPayout = async (payoutId) => {
  try {
    const response = await razorpayClient.post(`/payouts/${payoutId}/cancel`);
    const payout = response.data;

    return {
      success: true,
      payout_id: payout.id,
      status: payout.status,
      cancelled_at: payout.cancelled_at,
      amount_refunded: payout.amount / 100,
    };
  } catch (error) {
    console.error("❌ Failed to cancel payout:", error);
    return {
      success: false,
      error: error.response?.data?.error?.description || error.message,
    };
  }
};

/**
 * Get payouts with filters using direct API
 * @param {Object} filters - Filter options
 * @returns {Promise<Object>} - Filtered payouts
 */
const getPayoutsWithFilters = async (filters = {}) => {
  try {
    const {
      count = 100,
      skip = 0,
      from_timestamp,
      to_timestamp,
      status,
      mode,
      reference_id,
    } = filters;

    const params = new URLSearchParams();
    params.append("count", count.toString());
    params.append("skip", skip.toString());

    if (from_timestamp) params.append("from", from_timestamp.toString());
    if (to_timestamp) params.append("to", to_timestamp.toString());
    if (status) params.append("status", status);
    if (mode) params.append("mode", mode);
    if (reference_id) params.append("reference_id", reference_id);

    const response = await razorpayClient.get(`/payouts?${params.toString()}`);
    const data = response.data;

    return {
      success: true,
      payouts: data.items.map((payout) => ({
        ...payout,
        amount: payout.amount / 100,
        fees: payout.fees / 100,
        tax: payout.tax / 100,
      })),
      count: data.count,
      total_count: data.total_count,
    };
  } catch (error) {
    console.error("❌ Failed to fetch payouts:", error);
    return {
      success: false,
      error: error.response?.data?.error?.description || error.message,
    };
  }
};

// ================== TOURNAMENT SPECIFIC FUNCTIONS ==================

/**
 * Setup club for tournament payouts
 * @param {Object} clubData - Club information with bank details
 * @returns {Promise<Object>} - Setup result
 */
const setupClubForPayouts = async (clubData) => {
  try {
    console.log("🚀 Setting up club for payouts:", clubData.name);

    // Step 1: Create contact using direct API
    const contactResult = await createContact({
      name: clubData.name,
      email: clubData.email,
      contact: clubData.phone,
      type: "vendor",
      reference_id: `club_${clubData.clubId}_${Date.now()}`,
      notes: {
        club_id: clubData.clubId,
        registration_date: new Date().toISOString(),
        tournament_organizer: "yes",
      },
    });

    if (!contactResult.success) {
      throw new Error(`Contact creation failed: ${contactResult.error}`);
    }

    // Step 2: Create fund account using SDK
    const fundAccountResult = await createFundAccount(
      contactResult.contact_id,
      clubData.bankDetails
    );

    if (!fundAccountResult.success) {
      throw new Error(
        `Fund account creation failed: ${fundAccountResult.error}`
      );
    }

    // Step 3: Verify bank account (optional but recommended)
    const verificationResult = await verifyBankAccount({
      account_number:
        clubData.bankDetails.accountNumber ||
        clubData.bankDetails.account_number,
      ifsc: clubData.bankDetails.ifscCode || clubData.bankDetails.ifsc,
      name: clubData.bankDetails.accountHolderName || clubData.bankDetails.name,
    });

    const setupResult = {
      success: true,
      club_id: clubData.id,
      contact_id: contactResult.contact_id,
      fund_account_id: fundAccountResult.fund_account_id,
      verification: verificationResult,
      setup_date: new Date().toISOString(),
    };

    console.log("✅ Club setup completed:", setupResult);
    return setupResult;
  } catch (error) {
    console.error("❌ Club payout setup failed:", error);
    return {
      success: false,
      error: error.message,
    };
  }
};

/**
 * Create tournament payout
 * @param {Object} payoutData - Tournament payout data
 * @returns {Promise<Object>} - Tournament payout result
 */
const createTournamentPayout = async (payoutData) => {
  try {
    const {
      tournament_id,
      club_id,
      fund_account_id,
      amount,
      tournament_name,
      urgent = false,
      notes = {},
    } = payoutData;

    const transferMode = getOptimalTransferMode(amount, urgent);
    const rawNarration =  `Tournament ${tournament_name || "Settlement"}`;
    const reference_id = generateAbbreviation(tournament_name, 4);

    const result = await createPayout({
      fund_account_id,
      amount,
      mode: transferMode,
      reference_id: `${reference_id}_${Date.now()}`,
      narration: rawNarration,
      notes: {
        tournament_id,
        tournament_name,
        club_id,
        payout_type: "tournament_settlement",
        transfer_mode: transferMode,
        urgent_transfer: urgent,
        created_at: new Date().toISOString(),
        ...notes,
      },
    });

    if (result.success) {
      result.estimated_processing_time =
        getEstimatedProcessingTime(transferMode);
    }

    return result;
  } catch (error) {
    console.error("❌ Failed to create tournament payout:", error);
    return {
      success: false,
      error: error.message,
    };
  }
};

// ================== UTILITY FUNCTIONS ==================

/**
 * Get optimal transfer mode based on amount and urgency
 * @param {number} amount - Amount in rupees
 * @param {boolean} urgent - Whether transfer is urgent
 * @returns {string} - Transfer mode
 */
const getOptimalTransferMode = (amount, urgent = false) => {
  const currentHour = new Date().getHours();
  const isBusinessHours = currentHour >= 9 && currentHour <= 17;

  if (urgent && amount <= 200000) {
    return "IMPS";
  } else if (amount <= 200000) {
    return "IMPS";
  } else if (amount <= 1000000) {
    return "NEFT";
  } else if (isBusinessHours) {
    return "RTGS";
  } else {
    return "NEFT";
  }
};

/**
 * Get estimated processing time for transfer modes
 * @param {string} mode - Transfer mode
 * @returns {string} - Processing time description
 */
const getEstimatedProcessingTime = (mode) => {
  switch (mode) {
    case "IMPS":
      return "Instant (within 5 minutes)";
    case "NEFT":
      return "Next working day";
    case "RTGS":
      return "Same day (within 2 hours during business hours)";
    default:
      return "Processing time depends on bank and transfer mode";
  }
};

/**
 * Get human-readable status description
 * @param {string} status - Payout status
 * @returns {string} - Status description
 */
const getStatusDescription = (status) => {
  const descriptions = {
    created: "Payout has been created and is being processed",
    queued: "Payout is queued due to insufficient balance",
    processing: "Payout is being processed by the bank",
    processed: "Payout has been successfully completed",
    reversed: "Payout failed and amount has been reversed",
    cancelled: "Payout has been cancelled",
  };

  return descriptions[status] || "Unknown status";
};

/**
 * Get bank details from IFSC code
 * @param {string} ifsc - IFSC code
 * @returns {Promise<Object>} - Bank details
 */
const getBankDetailsByIFSC = async (ifsc) => {
  try {
    const ifscRegex = /^[A-Z]{4}0[A-Z0-9]{6}$/;
    const isValidIFSC = ifscRegex.test(ifsc.toUpperCase());

    if (!isValidIFSC) {
      return {
        success: false,
        error: "Invalid IFSC format",
        ifsc: ifsc.toUpperCase(),
        valid: false,
      };
    }

    try {
      const response = await axios.get(
        `https://ifsc.razorpay.com/${ifsc.toUpperCase()}`
      );
      const bankData = response.data;

      return {
        success: true,
        ifsc: ifsc.toUpperCase(),
        valid: true,
        bank_name: bankData.BANK,
        branch: bankData.BRANCH,
        address: bankData.ADDRESS,
        city: bankData.CITY,
        district: bankData.DISTRICT,
        state: bankData.STATE,
        contact: bankData.CONTACT,
        rtgs: bankData.RTGS,
        neft: bankData.NEFT,
        imps: bankData.IMPS,
        upi: bankData.UPI,
      };
    } catch (apiError) {
      return {
        success: true,
        ifsc: ifsc.toUpperCase(),
        valid: true,
        message: "IFSC format is valid (bank details not available)",
      };
    }
  } catch (error) {
    console.error("Error validating IFSC:", error);
    return {
      success: false,
      error: error.message,
      ifsc: ifsc.toUpperCase(),
      valid: false,
    };
  }
};
// Add this to your razorpay utils file
/**
 * Calculate tournament payout amounts
 * @param {Object} tournamentData - Tournament data with entry fees and participant count
 * @param {Object} options - Optional configuration for fee percentages
 * @returns {Object} - Calculated payout details
 */
const calculateTournamentPayout = (tournamentData, options = {}) => {
  const {
    platformFeePercentage = 5,
    processingFeePercentage = 2.5,
    gstPercentage = 18,
    customDeductions = 0,
  } = options;

  const totalCollected =
    tournamentData.totalCollected ||
    tournamentData.participantCount * tournamentData.entryFee;

  const platformFee = (totalCollected * platformFeePercentage) / 100;
  const processingFee = (totalCollected * processingFeePercentage) / 100;
  const gstOnProcessingFee = (processingFee * gstPercentage) / 100;
  const totalDeductions =
    platformFee + processingFee + gstOnProcessingFee + customDeductions;
  const clubPayout = totalCollected - totalDeductions;

  return {
    success: true,
    calculation: {
      totalCollected,
      platformFee,
      processingFee,
      gstOnProcessingFee,
      totalDeductions,
      clubPayout,
      clubPayoutPercentage: ((clubPayout / totalCollected) * 100).toFixed(2),
    },
  };
};
module.exports = {
  // Core payment functions
  createOrder,
  verifyPaymentSignature,
  verifyWebhookSignature,

  // Data fetching functions
  fetchPaymentDetails,
  fetchOrderDetails,

  // Payment operations
  capturePayment,
  createRefund,

  // Frontend integration
  generateCheckoutOptions,

  // Webhook handling
  processWebhookEvent,

  // Contact functions (Direct API)
  createContact,
  fetchContact,

  // Fund Account functions (Mixed)
  createFundAccount,
  verifyBankAccount,
  fetchValidationDetails,

  // Payout functions (Direct API)
  createPayout,
  checkPayoutStatus,
  getAccountBalance,
  cancelPayout,
  getPayoutsWithFilters,
  calculateTournamentPayout,

  // Tournament specific functions
  setupClubForPayouts,
  createTournamentPayout,
  // Utility functions
  getOptimalTransferMode,
  getEstimatedProcessingTime,
  getStatusDescription,
  getBankDetailsByIFSC,
  checkPayoutStatus,
  formatAmount,
  formatAmountFromPaise,
  logTransaction,

  // Direct access to Razorpay instance for advanced usage
  razorpayInstance,
};
const sanitizeNarration = (text) => {
  if (!text) return "Payout Transaction";

  // Remove special characters - only allow alphanumeric and spaces
  const sanitized = text
    .replace(/[^a-zA-Z0-9\s]/g, "") // Remove all special chars including hyphens/underscores
    .replace(/\s+/g, " ") // Replace multiple spaces with single space
    .trim(); // Remove leading/trailing spaces

  // Ensure we have content after sanitization
  const result = sanitized || "Payout Transaction";
  
  // Ensure minimum length (pad if too short)
  const minLength = 10;
  const paddedResult = result.length < minLength 
    ? result.padEnd(minLength, " Txn") 
    : result;

  // Truncate to 30 characters and trim again
  return paddedResult.substring(0, 30).trim();
};