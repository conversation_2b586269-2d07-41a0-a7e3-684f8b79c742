const { sendSMS } = require("./index");
const { config } = require("../../config/config");

/**
 * SMS service with predefined SMS templates
 */
const smsService = {
  /**
   * Send a welcome SMS to a new user
   * @param {Object} user - User object
   * @param {string} user.name - User's name
   * @param {string} user.mobile - User's mobile number
   * @returns {Promise<Object>} - SMS send result
   */
  sendWelcomeSMS: async (user) => {
    return sendSMS({
      templateId: "", // Replace with actual template ID
      mobile: user.mobile,
      variables: {
        VAR1: user.name,
        VAR2: config.frontend_url,
      },
    });
  },

  /**
   * Send an OTP SMS for verification
   * @param {Object} options - OTP options
   * @param {string} options.mobile - User's mobile number
   * @param {string} options.otp - OTP code
   * @param {number} options.expiryMinutes - OTP expiry in minutes
   * @returns {Promise<Object>} - SMS send result
   */
  sendOtpSMS: async ({ mobile, otp, expiryMinutes = 10 }) => {
    return sendSMS({
      templateId: config.msg91.templates.otp_template_id, // Replace with actual template ID
      mobile,
      variables: {
        var1: otp,
        var2: expiryMinutes,
      },
    });
  },

  /**
   * Send a tournament registration confirmation SMS
   * @param {Object} registration - Registration details
   * @param {Object} player - Player details
   * @param {Object} tournament - Tournament details
   * @returns {Promise<Object>} - SMS send result
   */
  sendRegistrationConfirmationSMS: async ({
    mobile,
    tournamentTitle,
    startDate,
  }) => {
    return sendSMS({
      templateId: config.msg91.templates.tournament_registration_template_id,
      mobile,
      variables: {
        tournament: tournamentTitle,
        startdate: startDate,
      },
    });
  },

  /**
   * Send a payment confirmation SMS
   * @param {Object} payment - Payment details
   * @param {Object} player - Player details
   * @param {Object} tournament - Tournament details
   * @returns {Promise<Object>} - SMS send result
   */
  sendPaymentConfirmationSMS: async ({
    transactionId,
    paymentAmount,
    mobile,
    tournamentTitle,
  }) => {
 
    return sendSMS({
      templateId: config.msg91.templates.payment_confirmation_template_id,
      mobile: mobile,
      variables: {
        amount: paymentAmount,
        tournament: tournamentTitle,
        transactionid: transactionId,
      },
    });
  },

  /**
   * Send a tournament withdrawal notification SMS
   * @param {Object} registration - Registration details
   * @param {Object} player - Player details
   * @param {Object} tournament - Tournament details
   * @returns {Promise<Object>} - SMS send result
   */
  sendTournamentWithdrawalSMS: async ({ mobile, tournament }) => {
    return sendSMS({
      templateId: config.msg91.templates.tournament_withdrawal_template_id,
      mobile,
      variables: {
        tournament: tournament.title,
      },
    });
  },

  /**
   * Send a tournament pairing notification SMS
   * @param {Object} registration - Registration details
   * @param {Object} player - Player details
   * @param {Object} tournament - Tournament details
   * @returns {Promise<Object>} - SMS send result
   */
  sendTournamentPairingSMS: async ({ registration, mobile }) => {
    return sendSMS({
      templateId: config.msg91.templates.pairing_notification_template_id,
      mobile,
      variables: {
        roundno: registration.pairing.round,
        boardno: registration.pairing.board,
        moredetailslink: config.frontend_url,
      },
    });
  },

  sendCustomSMS: async ({
    templateId,
    mobile,
    variables,
    shortUrl,
    shortUrlExpiry,
  }) => {
    return sendSMS({
      templateId,
      mobile,
      variables,
      shortUrl,
      shortUrlExpiry,
    });
  },
  
  /**
   * Send a tournament cancellation SMS
   * @param {Object} options - Cancellation options
   * @param {string} options.mobile - User's mobile number
   * @param {string} options.tournamentTitle - Tournament title
   * @param {string} options.refundAmount - Refund amount
   * @param {string} options.reason - Cancellation reason
   * @returns {Promise<Object>} - SMS send result
   */
  sendTournamentCancellationSMS: async ({ mobile, tournamentTitle, refundAmount, reason }) => {
    // Note: You'll need to create a template in MSG91 for this
    // This is a placeholder using a custom template
    return sendSMS({
      templateId: config.msg91.templates.tournament_withdrawal_template_id, // Replace with actual cancellation template ID
      mobile,
      variables: {
        tournament: tournamentTitle,
        amount: refundAmount,
        reason: reason,
      },
    });
  },

  /**
   * Send a refund initiated SMS
   * @param {Object} options - Refund options
   * @param {string} options.mobile - User's mobile number
   * @param {string} options.refundAmount - Refund amount
   * @param {string} options.tournamentTitle - Tournament title
   * @returns {Promise<Object>} - SMS send result
   */
  sendRefundInitiatedSMS: async ({ mobile, refundAmount, tournamentTitle }) => {
    // Note: You'll need to create a template in MSG91 for this
    // This is a placeholder using a custom template
    return sendSMS({
      templateId: config.msg91.templates.refund_confirmation_template_id, // Replace with actual refund template ID
      mobile,
      variables: {
        tournament: tournamentTitle,
      },
    });
  },

  /**
   * Send a refund completed SMS
   * @param {Object} options - Refund options
   * @param {string} options.mobile - User's mobile number
   * @param {string} options.refundAmount - Refund amount
   * @param {string} options.tournamentTitle - Tournament title
   * @returns {Promise<Object>} - SMS send result
   */
  sendRefundCompletedSMS: async ({ mobile, refundAmount, tournamentTitle }) => {
    // Note: You'll need to create a template in MSG91 for this
    // This is a placeholder using a custom template
    return sendSMS({
      templateId: config.msg91.templates.payment_confirmation_template_id, // Replace with actual refund template ID
      mobile,
      variables: {
        amount: refundAmount,
        tournament: tournamentTitle,
      },
    });
  },
};

module.exports = smsService;
