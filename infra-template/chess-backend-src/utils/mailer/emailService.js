const { sendEmail, sendCustomEmail:customEmail } = require("./index");
const { config } = require("../../config/config");
require("dotenv").config();

/**
 * Email service with predefined email templates
 */
const emailService = {
  /**
   * Send a welcome email to a new user
   * @param {Object} user - User object
   * @param {string} user.name - User's name
   * @param {string} user.email - User's email
   * @param {string} user.role - User's role
   * @returns {Promise<Object>} - Email send result
   */
  sendWelcomeEmail: async (user) => {
    const currentYear = new Date().getFullYear();

    return sendEmail({
      to: user.email,
      subject: "Welcome to Chess Brigade!",
      templateName: "welcome",
      templateData: {
        name: user.name,
        username: user.name,
        email: user.email,
        role: user.role,
        loginUrl: `${config.frontend_url}`,
        currentYear,
      },
    });
  },

  /**
   * Send a tournament registration confirmation email
   * @param {Object} registration - Registration details
   * @param {Object} player - Player details
   * @param {Object} tournament - Tournament details
   * @param {boolean} paymentRequired - Whether payment is required
   * @param {string} paymentUrl - URL for payment (if required)
   * @returns {Promise<Object>} - Email send result
   */
  sendTournamentRegistrationEmail: async (
    registration,
    player,
    tournament,
    paymentRequired = false,
    paymentUrl = ""
  ) => {
    const currentYear = new Date().getFullYear();

    return sendEmail({
      to: player.email,
      subject: `Registration Confirmation: ${tournament.title}`,
      templateName: "tournament-registration",
      templateData: {
        playerName: player.name,
        playerEmail: player.email,
        tournamentName: tournament.title,
        tournamentStartDate: tournament.startDate,
        tournamentEndDate: tournament.endDate,
        venue: tournament.venue,
        registrationId: registration.id,
        registrationStatus: registration.registrationStatus,
        paymentStatus: registration.paymentStatus,
        entryFee: tournament.entryFee,
        paymentRequired,
        paymentUrl,
        organizerEmail: "<EMAIL>",
        organizerPhone: "+91 9876543210",
        dashboardUrl: `${config.frontend_url}/dashboard`,
        currentYear,
      },
    });
  },

  /**
   * Send a payment confirmation email
   * @param {Object} payment - Payment details
   * @param {Object} player - Player details
   * @param {Object} tournament - Tournament details
   * @param {Object} registration - Registration details
   * @returns {Promise<Object>} - Email send result
   */
  sendPaymentConfirmationEmail: async (
    payment,
    player,
    tournament,
    registration
  ) => {
    const currentYear = new Date().getFullYear();

    return sendEmail({
      to: player.email,
      subject: `Payment Confirmation: ${tournament.title}`,
      templateName: "payment-confirmation",
      templateData: {
        playerName: player.name,
        playerEmail: player.email,
        tournamentName: tournament.title,
        tournamentStartDate: tournament.startDate,
        tournamentEndDate: tournament.endDate,
        venue: tournament.venueAddress,
        registrationId: registration.id,
        transactionId: payment.paymentTransactionId,
        paymentDate: payment.paymentDate,
        amount: payment.paymentAmount,
        paymentMethod: payment.paymentMethod,
        paymentStatus: payment.paymentStatus,
        dashboardUrl: `${config.frontend_url}/dashboard`,
        currentYear,
      },
    });
  },

  /**
   * Send a payment failure email
   * @param {Object} payment - Payment details
   * @param {Object} player - Player details
   * @param {Object} tournament - Tournament details
   * @param {Object} registration - Registration details
   * @param {string} errorMessage - Error message from payment gateway
   * @returns {Promise<Object>} - Email send result
   */
  sendPaymentFailureEmail: async (
    payment,
    player,
    tournament,
    registration,
    errorMessage = "Payment processing failed"
  ) => {
    const currentYear = new Date().getFullYear();

    return sendEmail({
      to: player.email,
      subject: `Payment Failed: ${tournament.title}`,
      templateName: "payment-failure",
      templateData: {
        playerName: player.name,
        playerEmail: player.email,
        tournamentName: tournament.title,
        tournamentStartDate: tournament.startDate,
        tournamentEndDate: tournament.endDate,
        venue: tournament.venueAddress,
        registrationId: registration.id,
        transactionId: payment.paymentTransactionId || payment.paymentId,
        paymentDate: payment.paymentDate || new Date(),
        amount: payment.paymentAmount,
        paymentMethod: payment.paymentMethod || "Online Payment",
        paymentStatus: payment.paymentStatus,
        errorMessage: errorMessage,
        dashboardUrl: `${config.frontend_url}/dashboard`,
        currentYear,
      },
    });
  },

  /**
   * Send a password reset email
   * @param {Object} user - User object
   * @param {string} resetToken - Password reset token
   * @returns {Promise<Object>} - Email send result
   */
  sendPasswordResetEmail: async (user, otp, expiryMinutes) => {
    const currentYear = new Date().getFullYear();

    return sendEmail({
      to: user.email,
      subject: "Password Reset Request",
      templateName: "password-reset",
      templateData: {
        name: user.name,
        email: user.email,
        otp: otp,
        expiryMinutes: expiryMinutes,
        currentYear,
      },
    });
  },

  /**
   * Send a club enquiry notification email
   * @param {Object} enquiry - Club enquiry details
   * @param {string} recipientEmail - Email address to send the notification to
   * @returns {Promise<Object>} - Email send result
   */
  sendClubEnquiryEmail: async (enquiry) => {
    const currentYear = new Date().getFullYear();
    return sendEmail({
      to: enquiry.Mail,
      subject: `New Club Enquiry: ${enquiry.clubName}`,
      templateName: "club-enquiry",
      templateData: {
        clubName: enquiry.clubName,
        contactName: enquiry.name,
        email: enquiry.email,
        phone: enquiry.mobile,
        message: enquiry.query,
        submissionDate: new Date(),
        currentYear,
      },
    });
  },

  /**
   * Send a contact enquiry notification email
   * @param {Object} enquiry - Contact enquiry details
   * @param {string} recipientEmail - Email address to send the notification to
   * @returns {Promise<Object>} - Email send result
   */
  sendContactEnquiryEmail: async (enquiry) => {
    const currentYear = new Date().getFullYear();

    return sendEmail({
      to: process.env.MAIL,
      subject: `New Contact Enquiry: ${enquiry.subject}`,
      templateName: "contact-enquiry",
      templateData: {
        name: enquiry.name,
        email: enquiry.email,
        phone: enquiry.phone,
        subject: enquiry.subject,
        message: enquiry.message,
        submissionDate: new Date(),
        currentYear,
      },
    });
  },

  /**
   * Send a club invitation email
   * @param {Object} invitation - Invitation details
   * @param {string} invitation.email - Email of the person being invited
   * @param {string} invitation.clubName - Name of the club
   * @param {string} invitation.message - Optional message to include in the invitation
   * @param {string} invitation.subject - Optional custom subject line
   * @param {string} invitation.joinUrl - URL to join the club
   * @returns {Promise<Object>} - Email send result
   */
  sendClubInviteEmail: async (invitation) => {
    const currentYear = new Date().getFullYear();
    const subject =
      invitation.subject || `Invitation to join ${invitation.clubName}`;

    return sendEmail({
      to: invitation.email,
      subject: subject,
      templateName: "club-invitation",
      templateData: {
        clubName: invitation.clubName,
        message: invitation.message,
        joinUrl: invitation.joinUrl,
        currentYear,
      },
    });
  },

  /**
   * Send a friend request invitation email
   * @param {Object} request - Friend request details
   * @param {string} request.recipientEmail - Email of the person receiving the request
   * @param {string} request.senderName - Name of the person sending the request
   * @param {string} request.message - Optional message included with the request
   * @param {string} request.actionUrl - URL to view and respond to the request
   * @returns {Promise<Object>} - Email send result
   */
  sendFriendRequestEmail: async (request) => {
    const currentYear = new Date().getFullYear();

    return sendEmail({
      to: request.recipientEmail,
      subject: `${request.senderName} sent you a friend request on Chess Brigade`,
      templateName: "friend-request",
      templateData: {
        senderName: request.senderName,
        message:
          request.message ||
          `${request.senderName} would like to connect with you on Chess Brigade.`,
        actionUrl: request.actionUrl,
        currentYear,
      },
    });
  },

  /**
   * Send a friend request acceptance email
   * @param {Object} request - Friend request details
   * @param {string} request.recipientEmail - Email of the person who sent the original request
   * @param {string} request.acceptorName - Name of the person who accepted the request
   * @param {string} request.profileUrl - URL to view the acceptor's profile
   * @returns {Promise<Object>} - Email send result
   */
  sendFriendRequestAcceptEmail: async (request) => {
    const currentYear = new Date().getFullYear();

    return sendEmail({
      to: request.recipientEmail,
      subject: `${request.acceptorName} accepted your friend request on Chess Brigade`,
      templateName: "friend-request-accept",
      templateData: {
        acceptorName: request.acceptorName,
        profileUrl: request.profileUrl,
        currentYear,
      },
    });
  },

  /**
   * Send a friend removed notification email
   * @param {Object} notification - Notification details
   * @param {string} notification.recipientEmail - Email of the person being notified
   * @param {string} notification.removedFriendName - Name of the removed friend
   * @returns {Promise<Object>} - Email send result
   */
  sendFriendRemovedEmail: async (notification) => {
    const currentYear = new Date().getFullYear();

    return sendEmail({
      to: notification.recipientEmail,
      subject: `Friend connection update on Chess Brigade`,
      templateName: "friend-removed",
      templateData: {
        removedFriendName: notification.removedFriendName,
        dashboardUrl: `${config.frontend_url}/dashboard`,
        currentYear,
      },
    });
  },

  /**
   * Send an email when a club accepts a player's join request
   * @param {Object} notification - Notification details
   * @param {string} notification.playerEmail - Email of the player
   * @param {string} notification.playerName - Name of the player
   * @param {string} notification.clubName - Name of the club
   * @param {string} notification.clubProfileUrl - URL to the club's profile
   * @returns {Promise<Object>} - Email send result
   */
  sendClubAcceptRequestEmail: async (notification) => {
    const currentYear = new Date().getFullYear();

    return sendEmail({
      to: notification.email,
      subject: `${notification.clubName} has accepted your request to join`,
      templateName: "club-accept-request",
      templateData: {
        playerName: notification.playerName,
        clubName: notification.clubName,
        clubProfileUrl: notification.clubProfileUrl,
        currentYear,
      },
    });
  },

  /**
   * Send an email when a player accepts a club's invitation
   * @param {Object} notification - Notification details
   * @param {string} notification.clubEmail - Email of the club
   * @param {string} notification.clubName - Name of the club
   * @param {string} notification.playerName - Name of the player
   * @param {string} notification.playerProfileUrl - URL to the player's profile
   * @returns {Promise<Object>} - Email send result
   */
  sendPlayerAcceptClubRequestEmail: async (notification) => {
    const currentYear = new Date().getFullYear();

    return sendEmail({
      to: notification.clubEmail,
      subject: `${notification.playerName} has accepted your club invitation`,
      templateName: "player-accept-club",
      templateData: {
        clubName: notification.clubName,
        playerName: notification.playerName,
        playerProfileUrl: notification.playerProfileUrl,
        currentYear,
      },
    });
  },

  /**
   * Send an email when a tournament refund is initiated
   * @param {Object} refund - Refund details
   * @param {string} refund.playerEmail - Email of the player
   * @param {string} refund.playerName - Name of the player
   * @param {string} refund.tournamentName - Name of the tournament
   * @param {string} refund.refundAmount - Amount being refunded
   * @param {string} refund.refundId - Refund transaction ID
   * @param {string} refund.initiatedDate - Date the refund was initiated
   * @param {string} refund.estimatedCompletionDate - Estimated completion date
   * @returns {Promise<Object>} - Email send result
   */
  sendTournamentRefundInitiatedEmail: async (refund) => {
    const currentYear = new Date().getFullYear();

    return sendEmail({
      to: refund.playerEmail,
      subject: `Refund Initiated for ${refund.tournamentName}`,
      templateName: "tournament-refund-initiated",
      templateData: {
        playerName: refund.playerName,
        tournamentName: refund.tournamentName,
        refundAmount: refund.refundAmount,
        refundId: refund.refundId,
        initiatedDate: refund.initiatedDate,
        estimatedCompletionDate: refund.estimatedCompletionDate,
        dashboardUrl: `${config.frontend_url}/dashboard`,
        currentYear,
      },
    });
  },

  /**
   * Send an email when a tournament refund is successful
   * @param {Object} refund - Refund details
   * @param {string} refund.playerEmail - Email of the player
   * @param {string} refund.playerName - Name of the player
   * @param {string} refund.tournamentName - Name of the tournament
   * @param {string} refund.refundAmount - Amount refunded
   * @param {string} refund.refundId - Refund transaction ID
   * @param {string} refund.completionDate - Date the refund was completed
   * @returns {Promise<Object>} - Email send result
   */
  sendTournamentRefundSuccessfulEmail: async (refund) => {
    const currentYear = new Date().getFullYear();

    return sendEmail({
      to: refund.playerEmail,
      subject: `Refund Completed for ${refund.tournamentName}`,
      templateName: "tournament-refund-successful",
      templateData: {
        playerName: refund.playerName,
        tournamentName: refund.tournamentName,
        refundAmount: refund.refundAmount,
        refundId: refund.refundId,
        completionDate: refund.completionDate,
        dashboardUrl: `${config.frontend_url}/dashboard`,
        currentYear,
      },
    });
  },

  /**
   * Send an email when a club invites a player
   * @param {Object} invitation - Invitation details
   * @param {string} invitation.playerEmail - Email of the player
   * @param {string} invitation.playerName - Name of the player
   * @param {string} invitation.clubName - Name of the club
   * @param {string} invitation.message - Optional message from the club
   * @param {string} invitation.inviteUrl - URL to accept the invitation
   * @returns {Promise<Object>} - Email send result
   */
  sendPlayerClubInviteEmail: async (invitation) => {
    const currentYear = new Date().getFullYear();

    return sendEmail({
      to: invitation.playerEmail,
      subject: `${invitation.clubName} has invited you to join their club`,
      templateName: "player-club-invite",
      templateData: {
        playerName: invitation.playerName,
        clubName: invitation.clubName,
        message:
          invitation.message ||
          `${invitation.clubName} would like you to join their club on Chess Brigade.`,
        inviteUrl: invitation.inviteUrl,
        currentYear,
      },
    });
  },

  /**
   * Send an email when a player invites a club
   * @param {Object} invitation - Invitation details
   * @param {string} invitation.clubEmail - Email of the club
   * @param {string} invitation.clubName - Name of the club
   * @param {string} invitation.playerName - Name of the player
   * @param {string} invitation.message - Optional message from the player
   * @param {string} invitation.inviteUrl - URL to accept the invitation
   * @returns {Promise<Object>} - Email send result
   */
  sendClubPlayerInviteEmail: async (invitation) => {
    const currentYear = new Date().getFullYear();

    return sendEmail({
      to: invitation.clubEmail,
      subject: `${invitation.playerName} would like to join ${invitation.clubName}`,
      templateName: "club-player-invite",
      templateData: {
        clubName: invitation.clubName,
        playerName: invitation.playerName,
        message:
          invitation.message ||
          `${invitation.playerName} would like to join your club on Chess Brigade.`,
        inviteUrl: invitation.inviteUrl,
        currentYear,
      },
    });
  },

  /**
   * Send an email when a tournament invites an arbiter
   * @param {Object} invitation - Invitation details
   * @param {string} invitation.arbiterEmail - Email of the arbiter
   * @param {string} invitation.arbiterName - Name of the arbiter
   * @param {string} invitation.tournamentName - Name of the tournament
   * @param {string} invitation.clubName - Name of the organizing club
   * @param {string} invitation.tournamentDates - Dates of the tournament
   * @param {string} invitation.venue - Venue of the tournament
   * @param {string} invitation.message - Optional message from the club
   * @param {string} invitation.inviteUrl - URL to accept the invitation
   * @returns {Promise<Object>} - Email send result
   */
  sendArbiterTournamentInviteEmail: async (invitation) => {
    const currentYear = new Date().getFullYear();

    return sendEmail({
      to: invitation.arbiterEmail,
      subject: `Invitation to arbiter ${invitation.tournamentName}`,
      templateName: "arbiter-tournament-invite",
      templateData: {
        arbiterName: invitation.arbiterName,
        tournamentName: invitation.tournamentName,
        clubName: invitation.clubName,
        tournamentDates: invitation.tournamentDates,
        venue: invitation.venue,
        message:
          invitation.message ||
          `${invitation.clubName} would like you to arbiter their tournament on Chess Brigade.`,
        inviteUrl: invitation.inviteUrl,
        currentYear,
      },
    });
  },

  /**
   * Send an email to inform a club that an arbiter has accepted their tournament invitation
   * @param {Object} notification - Notification details
   * @param {string} notification.clubEmail - Email of the club
   * @param {string} notification.clubName - Name of the club
   * @param {string} notification.arbiterName - Name of the arbiter
   * @param {string} notification.tournamentName - Name of the tournament
   * @param {string} notification.tournamentUrl - URL to view the tournament
   * @returns {Promise<Object>} - Email send result
   */
  sendClubArbiterAcceptEmail: async (notification) => {
    const currentYear = new Date().getFullYear();

    return sendEmail({
      to: notification.clubEmail,
      subject: `${notification.arbiterName} has accepted your arbiter invitation`,
      templateName: "club-arbiter-accept",
      templateData: {
        clubName: notification.clubName,
        arbiterName: notification.arbiterName,
        tournamentName: notification.tournamentName,
        tournamentUrl: notification.tournamentUrl,
        currentYear,
      },
    });
  },

  /**
   * Send an email when tournament dates are changed
   * @param {Object} notification - Notification details
   * @param {string} notification.recipientEmail - Email of the recipient
   * @param {string} notification.recipientName - Name of the recipient
   * @param {string} notification.tournamentName - Name of the tournament
   * @param {string} notification.oldStartDate - Original start date
   * @param {string} notification.oldEndDate - Original end date
   * @param {string} notification.newStartDate - New start date
   * @param {string} notification.newEndDate - New end date
   * @param {string} notification.tournamentUrl - URL to view the tournament
   * @returns {Promise<Object>} - Email send result
   */
  sendTournamentDateChangedEmail: async (notification) => {
    const currentYear = new Date().getFullYear();

    return sendEmail({
      to: notification.recipientEmail,
      subject: `Date Change for ${notification.tournamentName}`,
      templateName: "tournament-date-changed",
      templateData: {
        recipientName: notification.recipientName,
        tournamentName: notification.tournamentName,
        oldStartDate: notification.oldStartDate,
        oldEndDate: notification.oldEndDate,
        newStartDate: notification.newStartDate,
        newEndDate: notification.newEndDate,
        tournamentUrl: notification.tournamentUrl,
        currentYear,
      },
    });
  },

  /**
   * Send an email when a tournament is cancelled and refund is initiated
   * @param {Object} notification - Notification details
   * @param {string} notification.recipientEmail - Email of the recipient
   * @param {string} notification.recipientName - Name of the recipient
   * @param {string} notification.tournamentName - Name of the tournament
   * @param {string} notification.cancellationReason - Reason for cancellation
   * @param {string} notification.refundAmount - Amount being refunded
   * @param {string} notification.refundId - Refund transaction ID
   * @param {string} notification.estimatedCompletionDate - Estimated completion date
   * @returns {Promise<Object>} - Email send result
   */
  sendTournamentCancelRefundEmail: async (notification) => {
    const currentYear = new Date().getFullYear();

    return sendEmail({
      to: notification.recipientEmail,
      subject: `${notification.tournamentName} Cancelled - Refund Initiated`,
      templateName: "tournament-cancel-refund",
      templateData: {
        recipientName: notification.recipientName,
        tournamentName: notification.tournamentName,
        cancellationReason:
          notification.cancellationReason ||
          "The tournament has been cancelled by the organizer.",
        refundAmount: notification.refundAmount,
        refundId: notification.refundId,
        estimatedCompletionDate: notification.estimatedCompletionDate,
        dashboardUrl: `${config.frontend_url}/dashboard`,
        currentYear,
      },
    });
  },

  /**
   * Send an email when a tournament is cancelled and refund is initiated
   * @param {Object} notification - Notification details
   * @param {string} notification.recipientEmail - Email of the recipient
   * @param {string} notification.recipientName - Name of the recipient
   * @param {string} notification.tournamentName - Name of the tournament
   * @param {string} notification.cancellationReason - Reason for cancellation
   *
   **/

  sendPlayerLeaveClubEmail: async (notification) => {
    const currentYear = new Date().getFullYear();

    return sendEmail({
      to: notification.email,
      subject: `${notification.playerName} has left your club`,
      templateName: "player-leave-club",
      templateData: {
        playerName: notification.playerName,
        clubName: notification.clubName,
        message: notification.message,
        currentYear,
      },
    });
  },

  sendMemberRemovedEmail: async (notification) => {
    const currentYear = new Date().getFullYear();

    return sendEmail({
      to: notification.email,
      subject: `You have been removed from ${notification.clubName}`,
      templateName: "club-remove-player",
      templateData: {
        playerName: notification.playerName,
        clubName: notification.clubName,
        reason: notification.reason,
        currentYear,
      },
    });
  },

  /**
   * Send a custom email using a template
   * @param {Object} options - Email options
   * @param {string} options.to - Recipient email
   * @param {string} options.subject - Email subject
   * @param {string} options.templateName - Template name
   * @param {Object} options.templateData - Template data
   * @param {Array} options.attachments - Optional attachments
   * @returns {Promise<Object>} - Email send result
   */
  sendCustomEmail: async ({ to, subject, html }) => {
    return customEmail({
      to,
      subject,
      html,
    });
  },
  sendTournamentReminderEmail: async (notification) => {
    const currentYear = new Date().getFullYear();

    return sendEmail({
      to: notification.recipientEmail,
      subject: `Tournament Registration Reminder: ${notification.tournamentName}`,
      templateName: "tournament-reminder",
      templateData: {
        recipientName: notification.recipientName,
        tournamentName: notification.tournamentName,
        registrationDeadline: notification.registrationDeadline,
        tournamentStartDate: notification.tournamentStartDate,
        tournamentEndDate: notification.tournamentEndDate,
        tournamentLocation: notification.tournamentLocation,
        registrationUrl: notification.registrationUrl,
        currentYear,
      },
    });
  },

  sendTournamentPromotionEmail: async (notification) => {
    const currentYear = new Date().getFullYear();

    return sendEmail({
      to: notification.recipientEmail,
      subject: `Tournament Promotion: ${notification.tournamentName}`,
      templateName: "tournament-promotion",
      templateData: {
        name: notification.recipientName,
        tournamentName: notification.tournamentName,
        tournamentDate: notification.tournamentDate,
        tournamentLocation: notification.tournamentLocation,
        prizePool: notification.prizePool,
        tournamentDescription: notification.tournamentDescription,
        registrationLink: notification.registrationLink,
        registrationDeadline: notification.registrationDeadline,

      },
    });
  },

  /**
   * Send a tournament cancellation email
   * @param {Object} user - User object
   * @param {Object} tournament - Tournament details
   * @param {Object} refund - Refund details
   * @returns {Promise<Object>} - Email send result
   */
  sendTournamentCancellationEmail: async (user, tournament, refund) => {
    const currentYear = new Date().getFullYear();

    return sendEmail({
      to: user.email,
      subject: `Tournament Cancelled: ${tournament.title}`,
      templateName: "tournament-cancellation",
      templateData: {
        playerName: user.name,
        playerEmail: user.email,
        tournamentName: tournament.title,
        tournamentStartDate: tournament.startDate,
        tournamentEndDate: tournament.endDate,
        cancellationReason: tournament.cancellationReason,
        cancellationDate: tournament.cancellationDate,
        refundAmount: refund.refundAmount,
        refundTransactionId: refund.refundTransactionId,
        refundStatus: refund.refundStatus,
        refundType: refund.refundType,
        dashboardUrl: `${config.frontend_url}/dashboard/tournaments`,
        currentYear,
      },
    });
  },

  /**
   * Send a refund initiated email
   * @param {Object} user - User object
   * @param {Object} tournament - Tournament details
   * @param {Object} refund - Refund details
   * @returns {Promise<Object>} - Email send result
   */
  sendRefundInitiatedEmail: async (user, tournament, refund) => {
    const currentYear = new Date().getFullYear();

    return sendEmail({
      to:  user.email,
      subject: `Refund Initiated: ${tournament.title}`,
      templateName: "tournament-refund-initiated",
      templateData: {
        playerName: user.name,
        playerEmail: user.email,
        tournamentName: tournament.title,
        refundAmount: refund.refundAmount,
        refundTransactionId: refund.refundTransactionId,
        refundStatus: refund.refundStatus,
        refundType: refund.refundType,
        refundRemarks: refund.refundRemarks,
        refundDate: refund.createdAt,
        dashboardUrl: `${config.frontend_url}/dashboard/payments`,
        currentYear,
      },
    });
  },

  /**
   * Send a refund completed email
   * @param {Object} user - User object
   * @param {Object} tournament - Tournament details
   * @param {Object} refund - Refund details
   * @returns {Promise<Object>} - Email send result
   */
  sendRefundCompletedEmail: async (user, tournament, refund) => {
    const currentYear = new Date().getFullYear();

    return sendEmail({
      to: user.email,
      subject: `Refund Completed: ${tournament.title}`,
      templateName: "tournament-refund-successful",
      templateData: {
        playerName: user.name,
        playerEmail: user.email,
        tournamentName: tournament.title,
        refundAmount: refund.refundAmount,
        refundTransactionId: refund.refundTransactionId,
        refundStatus: refund.refundStatus,
        refundType: refund.refundType,
        refundRemarks: refund.refundRemarks,
        refundDate: refund.refundDate,
        dashboardUrl: `${config.frontend_url}/dashboard/payments`,

        currentYear,
      },
    });
  },

  sendTournamentCertificate: async (data) => {
    const currentYear = new Date().getFullYear();
    return sendEmail({
      to:data.email,
      subject: ` Tournament Certificate Generated `,
      templateName: "tournament-certificate-generation",
      templateData: {
        playerName: data.name,
        tournamentName:data.title,
        baseUrl:'https://www.chessbrigade.com/dashboard/my-certificate',
        currentYear,
      },
    });
  },

  sendCourseRegister: async (data) => {
  return sendEmail({
    to: data.email,
    subject: `Request to Join Course - ${data.title}`,
    templateName: "course-register-admin", // This should match your HTML file name
    templateData: {
      playerName: data.name,
      playerId: data.playerId,
      playerEmail: data.email,
      courseTitle: data.title,
      startDate: data.startDate,
      endDate: data.endDate,
      courseFee: data.fee,
      paymentMethod: data.paymentMethod,
      // adminCourseLink: data.adminLink, 
    },
  });
}


}

module.exports = emailService;
