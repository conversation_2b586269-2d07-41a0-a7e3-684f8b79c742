<!doctype html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <link rel="icon" type="image/png" href="/icon.png" />
  <!-- In public/index.html <head> -->
  <meta property="og:title" content="ChessBrigade | Register for Tournaments Around You" />
  <meta property="og:description" content="Register now and showcase your chess skills in top tournaments!" />
  <meta property="og:image" content="/icon.png" />
  <meta property="og:url" content="https://www.chessbrigade.com" />
  <meta property="og:type" content="website" />
  <meta property="og:site_name" content="ChessBrigade" />
  <title>ChessBrigade</title>

  <script>
    !function (f, b, e, v, n, t, s) {
      if (f.fbq) return; n = f.fbq = function () {
        n.callMethod ?
          n.callMethod.apply(n, arguments) : n.queue.push(arguments)
      };
      if (!f._fbq) f._fbq = n; n.push = n; n.loaded = !0; n.version = '2.0';
      n.queue = []; t = b.createElement(e); t.async = !0;
      t.src = v; s = b.getElementsByTagName(e)[0];
      s.parentNode.insertBefore(t, s)
    }(window, document, 'script',
      'https://connect.facebook.net/en_US/fbevents.js');
    fbq('init', '1142171351034254');
    fbq('track', 'PageView');
  </script>

  <!-- Font imports -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  <link href="https://fonts.googleapis.com/css2?family=Yantramanav:wght@300;400;500;700&display=swap" rel="stylesheet">
  <link href="https://fonts.googleapis.com/css2?family=Prosto+One&display=swap" rel="stylesheet">
  <link href="https://fonts.googleapis.com/css2?family=Yeseva+One&display=swap" rel="stylesheet">
</head>

<body>
  <!-- Facebook Pixel noscript tag -->
  <noscript><img height="1" width="1" style="display:none"
      src="https://www.facebook.com/tr?id=1142171351034254&ev=PageView&noscript=1" /></noscript>

  <div id="root"></div>
  <script type="module" src="/src/main.jsx"></script>
  <!-- Google tag (gtag.js) -->
  <script async src="https://www.googletagmanager.com/gtag/js?id=G-BGV4WY39NW"></script>
  <script>
    window.dataLayer = window.dataLayer || [];
    function gtag() { dataLayer.push(arguments); }
    gtag('js', new Date());

    gtag('config', 'G-BGV4WY39NW');
  </script>
</body>

</html>