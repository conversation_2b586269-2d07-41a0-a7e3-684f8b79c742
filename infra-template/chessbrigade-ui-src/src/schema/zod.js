import { optional, z } from "zod";

// Base Schema
const baseProfileSchema = z.object({
  playerTitle: z.string().optional(),

  dob: z
    .string({ errorMap: () => ({ message: "Date of birth is required" }) })
    .refine((date) => {
      const currentDate = new Date();
      const dobDate = new Date(date);
      const age = currentDate.getFullYear() - dobDate.getFullYear();
      return age >= 5 && age <= 150;
    }, "Age should be above 5"),

  alternateContact: z.string().optional(),
  gender: z.enum(["male", "female", "other"]),
  fideRating: z.string().optional(),
  aicfId: z.string().optional(),
  districtId: z.string().optional(),
  club: z.string().optional(),

  state: z.string().min(1, "State is required"),
  city: z.string().min(1, "City is required"),
  pincode: z.string().regex(/^\d{6}$/, "Invalid pincode"),
 // address: z.string().min(10, "Address must be at least 10 characters"),
  parentGuardianName: z.string().optional(),
  emergencyContact: z.string().optional(),
  fideId: z.string().optional(),
  stateId: z.string().optional(),
  association: z.string().optional(),
  district: z.string().min(1, "District is required"),
  country: z.string().min(1, "Country is required"),
  profileImage: z
    .any()
    .optional()
    .refine(
      (file) => {
        if (!file || file === "") return true; // Skip validation if no file
        return file instanceof File;
      },
      { message: "Please upload a valid file" }
    )
    .refine(
      (file) => {
        if (!file || file === "") return true; // Skip validation if no file
        return file.size <= 1 * 1024 * 1024; // 5MB limit
      },
      { message: "File must be less than 5MB" }
    )
    .refine(
      (file) => {
        if (!file || file === "") return true; // Skip validation if no file
        return ["image/jpeg", "image/png", "image/jpg", "image/webp"].includes(
          file.type
        );
      },
      { message: "File must be JPEG, PNG, or WebP format" }
    ),
  // This field is for preview purposes only, not sent to API
  profileImagePreview: z.string().optional(),
  email:z.string().optional(),

  termsAndConditions: z
    .boolean({ errorMap: () => ({ message: "Required" }) })
    .refine((val) => val === true, {
      message: "You must accept the terms and conditions",
    }),

  // Additional fields that are only relevant in edit mode
  phoneNumber: z
    .string()
    .min(10, { message: "Mobile number is required" })
    .regex(/^(91)?\d{10}$/, {
      message: "Phone number be 10 digits",
    })
    .max(12, {
      message: "Mobile number must be 12 digits (91 + 10 digits)",
    }),
  firstName: z.string().min(1, "First Name must be at least 2 characters"),
  lastName: z.string().min(1, "Last Name must be at least 2 characters"),
  otp: z.string().length(6),
  myfile: z
    .any()
    .optional()
    .refine(
      (file) => {
        if (!file || file === "") return true; // Skip validation if no file
        return file instanceof File;
      },
      { message: "Please upload a valid file" }
    )
    .refine(
      (file) => {
        if (!file || file === "") return true; // Skip validation if no file
        return file.size <= 10 * 1024 * 1024; // 10MB limit
      },
      { message: "File must be less than 10MB" }
    )
    .refine(
      (file) => {
        if (!file || file === "") return true; // Skip validation if no file
        return [
          "image/jpeg",
          "image/png",
          "image/jpg",
          "image/webp",
          "application/pdf",
        ].includes(file.type);
      },
      { message: "File must be PDF, or JPEG, PNG, or WebP format" }
    ),
  myfilePreview: z.string().nullable().optional(),


});

// 🔒 Full schema for profile creation (no OTP, phone, etc.)
export const playerProfileSchema = baseProfileSchema
  .omit({
    otp: true,
    phoneNumber: true,
    firstName: true,
    lastName: true,
  })
  .extend({
    pincode: z
      .string()
      .optional()
      .refine((val) => !val || /^\d{6}$/.test(val), {
        message: "Invalid pincode",
      }),
  })
  .superRefine((data, ctx) => {
    if (data.dob) {
      const currentDate = new Date();
      const dobDate = new Date(data.dob);
      let age = currentDate.getFullYear() - dobDate.getFullYear();
      const monthDiff = currentDate.getMonth() - dobDate.getMonth();

      if (
        monthDiff < 0 ||
        (monthDiff === 0 && currentDate.getDate() < dobDate.getDate())
      ) {
        age--;
      }

      if (age < 18) {
        if (!data.parentGuardianName || data.parentGuardianName.trim() === "") {
          ctx.addIssue({
            path: ["parentGuardianName"],
            message:
              "Parent/Guardian Name is required for players under 18 years old",
            code: z.ZodIssueCode.custom,
          });
        }
        if (!data.emergencyContact || data.emergencyContact.trim() === "") {
          ctx.addIssue({
            path: ["emergencyContact"],
            message:
              "Emergency Contact is required for players under 18 years old",
            code: z.ZodIssueCode.custom,
          });
        }
      }
    }
  });
export const playerProfileEditSchema = baseProfileSchema
  .partial()
  .extend({
    pincode: z
      .string()
      .optional()
      .refine((val) => !val || /^\d{6}$/.test(val), {
        message: "Invalid pincode",
      }),
  })
  .superRefine((data, ctx) => {
    if (data.dob) {
      const currentDate = new Date();
      const dobDate = new Date(data.dob);
      let age = currentDate.getFullYear() - dobDate.getFullYear();
      const monthDiff = currentDate.getMonth() - dobDate.getMonth();
      if (
        monthDiff < 0 ||
        (monthDiff === 0 && currentDate.getDate() < dobDate.getDate())
      ) {
        age--;
      }

      if (age < 18) {
        if (!data.parentGuardianName || data.parentGuardianName.trim() === "") {
          ctx.addIssue({
            path: ["parentGuardianName"],
            message:
              "Parent/Guardian Name is required for players under 18 years old",
            code: z.ZodIssueCode.custom,
          });
        }
        if (!data.emergencyContact || data.emergencyContact.trim() === "") {
          ctx.addIssue({
            path: ["emergencyContact"],
            message:
              "Emergency Contact is required for players under 18 years old",
            code: z.ZodIssueCode.custom,
          });
        }
      }
    }
  });

export const arbiterDetailSchema = z.object({
  title: z.string().optional(),
  officialId: z.string().optional(),
  alternateContact: z.string().optional(),
  fideRating: z.string().optional(),
  aicfId: z.string().optional(),
  districtId: z.string().optional(),

  state: z.string().optional(),
  city: z.string().optional(),
 pincode: z
  .string()
  .transform((val) => val.trim() === "" ? undefined : val)
  .optional()
  .refine((val) => val === undefined || /^\d{6}$/.test(val), {
    message: "pincode must be 6 digits",
  }),

  profileImage: z
    .any()
    .optional()
    .refine(
      (file) => {
        if (!file || file === "") return true; // Skip validation if no file
        return file instanceof File;
      },
      { message: "Please upload a valid file" }
    )
    .refine(
      (file) => {
        if (!file || file === "") return true; // Skip validation if no file
        return file.size <= 1 * 1024 * 1024; // 5MB limit
      },
      { message: "File must be less than 5MB" }
    )
    .refine(
      (file) => {
        if (!file || file === "") return true; // Skip validation if no file
        return ["image/jpeg", "image/png", "image/jpg", "image/webp"].includes(
          file.type
        );
      },
      { message: "File must be JPEG, PNG, or WebP format" }
    ),
  // This field is for preview purposes only, not sent to API
  profileImagePreview: z.string().optional(),

  parentGuardianName: z.string().optional(),
  emergencyContact: z.string().optional(),
  fideId: z.string().optional(),

  district: z.string().optional(),
  country: z.string().optional(),

  // Additional fields that are only relevant in edit mode
  phoneNumber: z
    .string()
    .min(10, { message: "Mobile number is required" })
    .regex(/^(91)?\d{10}$/, {
      message: "Phone number be 10 digits",
    })
    .max(12, {
      message: "Mobile number must be 10 digits",
    }),
  firstName: z.string().min(1, "First Name must be at least 1 characters").optional(),
  lastName: z.string().min(1, "Last Name must be at least 1 characters").optional(),
});

export const coachDetailSchema = z.object({
  title: z.string().optional(),
  districtId: z.string().optional(),
  about: z.string().max(250).optional(),
  dob: z
    .string({ errorMap: () => ({ message: "Date of birth is required" }) })
    .refine((date) => {
      const currentDate = new Date();
      const dobDate = new Date(date);
      const age = currentDate.getFullYear() - dobDate.getFullYear();
      return age >= 18 && age <= 150;
    }, "Age should be above 18"),
  state: z.string().optional(),
  city: z.string().optional(),

  profileImage: z
    .any()
    .optional()
    .refine(
      (file) => {
        if (!file || file === "") return true; // Skip validation if no file
        return file instanceof File;
      },
      { message: "Please upload a valid file" }
    )
    .refine(
      (file) => {
        if (!file || file === "") return true; // Skip validation if no file
        return file.size <= 1 * 1024 * 1024; // 5MB limit
      },
      { message: "File must be less than 5MB" }
    )
    .refine(
      (file) => {
        if (!file || file === "") return true; // Skip validation if no file
        return ["image/jpeg", "image/png", "image/jpg", "image/webp"].includes(
          file.type
        );
      },
      { message: "File must be JPEG, PNG, or WebP format" }
    ),
  // This field is for preview purposes only, not sent to API
  profileImagePreview: z.string().optional(),

  district: z.string().optional(),
  country: z.string().optional(),

  // Additional fields that are only relevant in edit mode
  phoneNumber: z
    .string()
    .min(10, { message: "Mobile number is required" })
    .regex(/^(91)?\d{10}$/, {
      message: "Phone number be 10 digits",
    })
    .max(12, {
      message: "Mobile number must be 10 digits",
    }),
  firstName: z.string().min(1, "First Name must be at least 1 characters").optional(),
  lastName: z.string().min(1, "Last Name must be at least 1 characters").optional(),
   profileLinks: z
    .object({
  fide: z.string().url("Invalid FIDE URL").optional(),
  li_chess: z.string().url("Invalid Lichess URL").optional(),
  chess: z.string().url("Invalid Chess.com URL").optional(),
    })
    .optional(),
});

const courseBaseSchema = z.object({
  title: z.string().min(1, "Title is required"),
  description: z.string().min(1, "Description is required"),
  startDate: z.string().min(1, "Start date is required"),
  endDate: z.string().min(1, "End date is required"),
  registrationStartDate: z.string().min(1, "Registration start date is required"),
  registrationEndDate: z.string().min(1, "Registration end date is required"),
  courseBanner: z.any().nullable(),
  courseDuration: z.string().min(1, "Course duration is required"),
  totalSessions: z.number().min(1, "Total sessions is required"),
  sessionsPerWeek: z.number().min(1, "Sessions per week is required"),
  courseFee: z.number().min(1, "Course fee is required"),
  paymentStructure: z.string().min(1, "Payment structure is required"),
  sessionStartTime: z
    .string()
    .regex(/^(0[1-9]|1[0-2]):([0-5][0-9])\s(AM|PM)$/i, "Please enter a valid Time")
    .transform((time) => {
      const match = time.match(/^(0[1-9]|1[0-2]):([0-5][0-9])\s(AM|PM)$/i);
      if (!match) return time;
      const [, hour, minute, period] = match;
      return `${hour}:${minute} ${period.toUpperCase()}`;
    }),
  sessionEndTime: z
    .string()
    .regex(/^(0[1-9]|1[0-2]):([0-5][0-9])\s(AM|PM)$/i, "Please enter a valid Time")
    .transform((time) => {
      const match = time.match(/^(0[1-9]|1[0-2]):([0-5][0-9])\s(AM|PM)$/i);
      if (!match) return time;
      const [, hour, minute, period] = match;
      return `${hour}:${minute} ${period.toUpperCase()}`;
    }),
  sessionDuration: z
    .string()
    .trim()
    .regex(/^\d*(\.\d{1,2})?$/, {
      message: "Duration must be a valid number with up to 2 decimal places",
    })
    .transform((val) => parseFloat(val))
    .refine((val) => !isNaN(val) && val > 0, {
      message: "Duration must be a number greater than 0",
    }),
   selectedDays: z
    .array(z.string())
    .nonempty({ message: "Please select at least one valid day" }),
});

export const courseSchema = courseBaseSchema
  .refine(
    (data) => {
      const parseTimeToMinutes = (timeString) => {
        const [time, period] = timeString.split(" ");
        const [hours, minutes] = time.split(":").map(Number);
        let totalMinutes = hours % 12 * 60 + minutes;
        if (period === "PM") totalMinutes += 12 * 60;
        return totalMinutes;
      };

      return (
        parseTimeToMinutes(data.sessionEndTime) >
        parseTimeToMinutes(data.sessionStartTime)
      );
    },
    {
      path: ["sessionEndTime"],
      message: "Session end time must be after session start time",
    }
  )
  .refine(
    (data) => data.selectedDays.length === data.sessionsPerWeek,
    {
      path: ["selectedDays"],
      message: "Number of selected days must match sessions per week",
    }
  );

// For edit schema
export const EditCourseSchema = courseBaseSchema.partial().optional();

//  z.object({
//   title: z.string().min(1, "Title is required"),
//   description: z.string().min(1, "Description is required"),
//   startDate: z.string().min(1, "Start date is required"), // You can change to z.coerce.date() if using Date object
//   endDate: z.string().min(1, "End date is required"),
//   registrationStartDate: z.string().min(1, "Registration start date is required"),
//   registrationEndDate: z.string().min(1, "Registration end date is required"),
//   courseBanner: z.any().nullable(),
//   courseDuration: z.string().min(1, "Course duration is required"),
//   totalSessions: z.string().min(1, "Total sessions is required"),
//   sessionsPerWeek: z.string().min(1, "Sessions per week is required"),
//   courseFee: z.string().min(1, "Course fee is required"),
//   paymentStructure: z.string().min(1, "Payment structure is required"),
//   sessionStartTime: z.string().min(1, "Session start time is required"),
//   sessionEndTime: z.string().min(1, "Session end time is required"),
//   sessionDuration: z
//     .string()
//     .trim()
//     .regex(/^\d*(\.\d{1,2})?$/, {
//       message: "Duration must be a valid number with up to 2 decimal places",
//     })
//     .transform((val) => parseFloat(val))
//     .refine((val) => !isNaN(val) && val > 0, {
//       message: "Duration must be a number greater than 0",
//     }),
// });

// 📝 Editable profile (includes optional OTP, phone, name)
// export const playerProfileEditSchema = baseProfileSchema.partial();
export const adminPlayerProfile = baseProfileSchema.omit({
  termsAndConditions: true,
  otp: true,
  myfile: true,
  myfilePreview: true,
  
}).partial().optional();


export const adminAribterProfileEdit = arbiterDetailSchema.omit({
  termsAndConditions: true,
  otp: true,
}).partial().optional();
