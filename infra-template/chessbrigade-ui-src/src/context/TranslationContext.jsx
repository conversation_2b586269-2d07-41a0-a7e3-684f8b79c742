import React, { createContext, useState, useEffect, useContext } from "react";

const TranslationContext = createContext();

export function TranslationProvider({ children }) {
  const [lang, setLang] = useState(localStorage.getItem("lang") || "en");
  const [translations, setTranslations] = useState({});
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    setLoading(true);
    localStorage.setItem("lang", lang); // Save language choice
    const apiUrl = import.meta.env.VITE_API_URL || "http://localhost:3000/api/v1";
    fetch(`${apiUrl}/i18n/${lang}`)
      .then((res) => res.json())
      .then((apiTranslations) => {
        setTranslations(apiTranslations || {});
      })
      .catch((err) => {
        console.error("Translation fetch failed", err);
        setTranslations({});
      })
      .finally(() => setLoading(false));
  }, [lang]);

  const changeLanguage = (newLang) => {
    setLang(newLang);
  };

  // Translation function
  
  // If key doesn't exist OR value is same as key (i.e., untranslated), use fallback
const translate = (key, fallback) => {
  const value = translations[key];
  if (value === undefined || value === null || value === '') {
    return fallback || key;
  }
  return value;
};


  return (
    <TranslationContext.Provider value={{ lang, changeLanguage, translations, loading, translate }}>
      {children}
    </TranslationContext.Provider>
  );
}

export function useTranslation() {
  return useContext(TranslationContext);
}
