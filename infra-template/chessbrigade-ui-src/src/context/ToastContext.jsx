import React, { createContext, useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>bar,
  <PERSON>ack,
  Typography,
  CircularProgress,
  Box
} from "@mui/material";
import { styled } from "@mui/material/styles";
import Slide from "@mui/material/Slide";
import { useNavigate } from "react-router-dom";

// Styled components for animations
const StyledSnackbar = styled(Snackbar)(({ theme }) => ({
  "& .MuiAlert-root": {
    minWidth: "250px",
    boxShadow: theme.shadows[3],
    borderRadius: theme.shape.borderRadius,
  },
  "& .MuiAlert-filledSuccess": {
    background: "#ffffff",
    color: "#2e7d32", // Green text for success
  },
  "& .MuiAlert-filledError": {
    background: "#d32f2f", // Red background for error
    color: "#ffffff",
  },
  "& .MuiAlert-filledInfo": {
    background: "#0288d1", // Blue background for info
    color: "#ffffff",
  },
  "& .Mui<PERSON>lert-filledWarning": {
    background: "#ed6c02", // Orange background for warning
    color: "#ffffff",
  },
  "& .MuiAlert-filledDisclaimer": {
    background: "#757575", // Gray background for disclaimer
    color: "#ffffff",
  },
  "& .MuiAlert-filledRedirect": {
    background: "#ffffff", // White background for redirect
    color: "#000000", // Black text for redirect
    display: "flex",
    alignItems: "center",
    padding: "8px 16px",
  },
}));

// Custom transition component
function SlideTransition(props) {
  return <Slide {...props} direction="left" />;
}

// Create context
// eslint-disable-next-line react-refresh/only-export-components
export const ToastContext = createContext({
  showToast: () => {},
  hideToast: () => {},
  success: () => {},
  error: () => {},
  warning: () => {},
  info: () => {},
  disclaimer: () => {},
  redirect: () => {},
});

// Toast provider component
const ToastProvider = ({ children, maxToasts = 3 }) => {
  const [toasts, setToasts] = useState([]);
  const navigate = useNavigate();

  const showToast = (message, variant = "info", autoHideDuration, redirectPath = null, redirectDelay = 2000) => {
    const id = Date.now().toString();

  // Set default durations per variant if not provided
  const durationByVariant = {
    success: 1000,
    error: 3000,
    warning: 3000,
    info: 4000,
    redirect: 3000,
    disclaimer: 4000,
  };
   const duration = autoHideDuration ?? durationByVariant[variant] ?? 3000;

    setToasts((prevToasts) => {
      // Remove oldest toasts if we're exceeding the maximum
      const newToasts = [...prevToasts];
      if (newToasts.length >= maxToasts) {
        newToasts.shift();
      }
      return [...newToasts, { id, message, variant, autoHideDuration:duration, redirectPath, redirectDelay }];
    });

    // Handle redirect if path is provided
    if (redirectPath) {
      setTimeout(() => {
        navigate(redirectPath);
        // Remove the toast after navigation
        hideToast(id);
      }, redirectDelay);
    }
  };

  const hideToast = (id) => {
    const filteredToasts = toasts.filter((toast) => toast.id !== id);
    setToasts(filteredToasts);
  };

  // Create helper methods inside the component
  const success = (message, duration) =>
    showToast(message, "success", duration);
  const error = (message, duration) => showToast(message, "error", duration);
  const info = (message, duration) => showToast(message, "info", duration);
  const warning = (message, duration) =>
    showToast(message, "warning", duration);
  const disclaimer = (message, duration) =>
    showToast(message, "info", duration);
  const redirect = (message, redirectPath, duration = 3000, redirectDelay = 2000) =>
    showToast(message, "redirect", duration, redirectPath, redirectDelay);

  const contextValue = {
    showToast,
    hideToast,
    success,
    error,
    info,
    warning,
    disclaimer,
    redirect,
  };
  const getIconStyle = (variant) => {
    if (variant === "success") {
      return { color: "#2e7d32" }; // Green icon for success on white background
    }
    return {}; // Default color for others
  };

  return (
    <ToastContext.Provider value={contextValue}>
      {children}

      <Stack
        spacing={2}
        sx={{ position: "fixed", top: 16, right: 16, zIndex: 2000 }}
      >
        {toasts.map((toast) => (
          <StyledSnackbar
            key={toast.id}
            open={true}
            autoHideDuration={toast.autoHideDuration}
            onClose={() => hideToast(toast.id)}
            TransitionComponent={SlideTransition}
            anchorOrigin={{ vertical: "top", horizontal: "right" }}
            sx={{ position: "static" }}
          >
            <Alert
              variant="filled"
              icon={false}
              severity={toast.variant}
              onClose={() => hideToast(toast.id)}
              sx={{
                width: "100%",
                "& .MuiAlert-icon": getIconStyle(toast.variant),
              }}
            >
              {toast.variant === "redirect" ? (
                <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
                  <CircularProgress size={24} thickness={4} sx={{ color: "#000000" }} />
                  <Typography variant="h6" sx={{ fontSize: { xs: 12, sm: 16 } }}>
                    {toast.message}
                  </Typography>
                </Box>
              ) : (
                <Typography variant="h6" sx={{ fontSize: { xs: 12, sm: 16 } }}>
                  {toast.message}
                </Typography>
              )}
            </Alert>
          </StyledSnackbar>
        ))}
      </Stack>
    </ToastContext.Provider>
  );
};

export default ToastProvider;
