import { Container } from "@mui/material";
import React, { useEffect, useState } from "react";
import { useParams } from "react-router-dom";
import BackButton from "../../components/common/BackButton";

import { Client } from "../../api/client";

import LeaderBoardUi from "../../components/common/LeaderBoardUi";

const LeaderBoard = () => {
  const { title: id } = useParams();
  const  newTitle = encodeURIComponent(id);
  const [tournamentDetails, setTournamentDetails] = useState([]);
  const [loading, setLoading] = useState(false);
  const [currentRound, setCurrentRound] = useState(0)

  const fetchCurrentRound = async () => {
    setLoading(true);
    try {
    
      const response = await Client.get(`/ranking-import/tournament/round`, { params: { id:newTitle } })
    
      if (response.data.success) {
        setCurrentRound(response.data.currentRound);
      } else {
        console.error(response.data.error.message || "something went wrong in current Round");
      }
    } catch (error) {
      console.error("Error fetching current round:", error);

      // navigate("/tournaments");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    const fetchTournamentDetails = async () => {
      setLoading(true);
      try {
        const response = await Client.get(`/tournament/${newTitle}`);
        if (response.data.success) {
          setTournamentDetails(response.data.data);
        } else {
          console.error(response.data.error.message || "something went wrong");
        }
      } catch (error) {
        console.error("Error fetching tournament details:", error);

        // navigate("/tournaments");
      } finally {
        setLoading(false);
      }
    };


    fetchTournamentDetails();
    fetchCurrentRound();
  }, [newTitle]);


  return (
    <Container maxWidth="xl" sx={{ py: 4, pt: 2, minHeight: "70dvh" }}>
      <BackButton />

      <LeaderBoardUi
        loading={loading}
        setLoading={setLoading}
        tournamentDetails={tournamentDetails}
        currentRound={currentRound}
      />
    </Container>
  );
};

export default LeaderBoard;
