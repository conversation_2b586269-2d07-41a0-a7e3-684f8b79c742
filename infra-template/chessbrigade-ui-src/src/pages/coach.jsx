import React, { useState, useEffect } from "react";
import { Box, Button, Container } from "@mui/material";
import { Client } from "../api/client";
import UseToast from "../lib/hooks/UseToast";
import { AxiosError } from "axios";
import DynamicTable from "../components/common/DynamicTable";
import { Link } from "react-router-dom";
import { ArrowBack } from "@mui/icons-material";
import CourseSearchForm from "../components/common/CourseSearchForm";

const CoursesPage = () => {
  const [courses, setCourses] = useState([]);
  const [loading, setLoading] = useState(true);
  const [totalPages, setTotalPages] = useState(0);
  const [total, setTotal] = useState(0);

  // States for form inputs
  const [search, setSearch] = useState({
    title: "",
    duration: "",
    fee: "",
    startDate: "",
    endDate: "",
  });
  const [page, setPage] = useState(1);
  const limit = 10; // Adjust as needed
  const toast = UseToast();

  const handlePageChange = (value) => {
    setPage(value);
    fetchCourses(value);
  };

  const handleSearch = (newPage) => {
    fetchCourses(newPage);
  };

  const handleReset = () => {
    setSearch({
      title: "",
      duration: "",
      fee: "",
      startDate: "",
      endDate: "",
    });
    setPage(1);
  };

  const fetchCourses = async (pageNumber) => {
    setLoading(true);
    try {
      const params = {
        page: pageNumber,
        limit,
      };

      // Only add non-empty search parameters
      Object.keys(search).forEach((key) => {
        if (search[key] !== "") {
          params[key] = search[key];
        }
      });

      const response = await Client.get("/course", { params });
      if (!response.data.success) {
        toast.info("No courses found");
        setCourses([]);
        setTotal(0);
        setTotalPages(0);
        setPage(1);
        return;
      }
      if (response.status === 204) {
        toast.info("No courses found");
        setCourses([]);
        setTotal(0);
        setTotalPages(0);
        setPage(1);
        return;
      }
      console.log("data",response.data.data)
      setCourses(response.data.data);
      setTotal(response.data.data.total);
      const totalPage = Math.ceil(response.data.data.total / limit);
      setTotalPages(totalPage);
    } catch (error) {
      if (error instanceof AxiosError && error.response) {
        toast.info(error.response.data.error);
        return;
      }
      console.error("Error fetching courses:", error);
      toast.info("An error occurred. Please try again later.");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchCourses(page);
  }, [page]);

  return (
    <Container
      maxWidth="xl"
      sx={{
        py: 2,
        pb: 8,
        minHeight: "100vh",
        display: "flex",
        flexDirection: "column",
      }}
    >
      <Box>
        <Button sx={{ mb: 1, fontSize: 16 }} component={Link} to={-1}>
          <ArrowBack sx={{ fontSize: 16 }} /> Back
        </Button>
      </Box>
      {/* Search Form */}
      <CourseSearchForm
        search={search}
        setSearch={setSearch}
        handleSearch={handleSearch}
        handleReset={handleReset}
      />
      {/* Courses Table */}
      <DynamicTable
        columns={[
          {
            id: "index",
            label: "S No",
            width: "80px",
            format: (_, item, index) => (page - 1) * limit + index + 1,
          },
          {
            id: "title",
            label: "Course Title",
            format: (value) => (
              <Box
                sx={{
                  textTransform: "capitalize",
                  textOverflow: "ellipsis",
                  whiteSpace: "nowrap",
                  overflow: "hidden",
                  maxWidth: { xs: "300px", sm: "400px", md: "600px" },
                }}
              >
                {value.toLowerCase().replace(/-/g, " ")}
              </Box>
            ),
          },
          {
            id: "sessionDuration",
            label: "Duration",
             format: (value) => (
              <Box
                sx={{
                  textTransform: "capitalize",
                  textOverflow: "ellipsis",
                  whiteSpace: "nowrap",
                  overflow: "hidden",
                  maxWidth: { xs: "300px", sm: "400px", md: "600px" },
                }}
              >
                {`${value} Months`}
              </Box>
            ),
          },
          {
            id: "courseFee",
            label: "Course Fee",
          },
          {
            id: "startDate",
            label: "Start Date",
            // format: (value) => formatDateToDMY(value),
          },
          {
            id: "endDate",
            label: "End Date",
            // format: (value) => formatDateToDMY(value),
          },
        ]}
        data={courses}
        loading={loading}
        page={page}
        totalPages={totalPages}
        onPageChange={handlePageChange}
        detailsPath="/dashboard/course/"
        idField="title"
        tableContainerProps={{
          sx: {
            minHeight: "400px",
            maxHeight: "600px",
          },
        }}
      />
    </Container>
  );
};

export default CoursesPage;
