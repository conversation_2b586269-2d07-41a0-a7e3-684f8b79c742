import React from "react";
import ClubProfileForm from "../../components/form/ClubProfileForm";
import { Box, Container, Typography } from "@mui/material";
import BackButton from "../../components/common/BackButton";
import { useTranslation } from "../../context/TranslationContext";

const ClubProfileEdit = () => {
  const { translate } = useTranslation();
  const [edit] = React.useState(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const editParam = urlParams.get("edit");
    return editParam === "1";
  });

  return (
    <Container maxWidth="xl" sx={{ py: 4, pt: 2 }}>
      <BackButton to={"/dashboard"} />
      <Box maxWidth="lg" minHeight={"100vh"} sx={{ margin: "auto", my: 2 }}>
        <Typography variant="h4" sx={{ mb: 6 }}>
          {translate('clubProfile', 'Club Profile')}
        </Typography>
        <ClubProfileForm edit={edit} />
      </Box>
    </Container>
  );
};

export default ClubProfileEdit;
