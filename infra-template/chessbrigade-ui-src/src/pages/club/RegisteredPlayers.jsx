import { Box, Button, Container } from "@mui/material";
import React, { useEffect, useState } from "react";
import DynamicTable from "../../components/common/DynamicTable";
import BackButton from "../../components/common/BackButton";
import PlayerSearchForm from "../../components/registration/PlayerSearchForm";
import { useLocation, useParams } from "react-router-dom";
import { Client } from "../../api/client";
import UseGlobalContext from "../../lib/hooks/UseGlobalContext";
import UseToast from "../../lib/hooks/UseToast";
import {capitalizeFirstLetter} from "../../utils/formatters"

const RegisterPlayers = () => {
  const { title } = useParams();
  const newTitle = encodeURIComponent(title);
  const toast = UseToast();
  const limit = 10;
  const { user } = UseGlobalContext();
  const [page, setPage] = useState(1);
  const [tournamentDetails, setTournamentDetails] = useState([]);
  const [loading, setLoading] = useState(false);
  const [search, setSearch] = useState({
    playerName: "",
    playerId: "",
    ageCategory: "",
    genderCategory: "",
    registerId: "",
  });
  const [players, setPlayers] = useState([]);
  const [total, setTotal] = useState(0);
  const [totalPages, setTotalPages] = useState(0);
   const location = useLocation();
  const { Id } = location.state || {};
  useEffect(() => {
    const fetchTournamentDetails = async () => {
      setLoading(true);
      try {
        const response = await Client.get(`/tournament/${newTitle}`);
        if (response.data.success) {
          setTournamentDetails(response.data.data);
        } else {
          toast.error(
            response.data.message || "Failed to fetch tournament details"
          );
          // navigate("/tournaments");
        }
      } catch (error) {
        console.error("Error fetching tournament details:", error);
        toast.error("An error occurred while fetching tournament details");
        // navigate("/tournaments");
      } finally {
        setLoading(false);
      }
    };

    fetchTournamentDetails();
  }, [newTitle]);
  const columns = [
    {
      id: "sno",
      label: "S.No",
      align: "center",
      format: (_, item, index) => total - ((page - 1) * limit + index),
    },
    { id: "playerTitle", label: "Title" },
    { id: "playerName", label: "Name" },
    { id: "fideRating", label: "FIDE Rating", align: "center" },
    { id: "fideId", label: "FIDE ID" },
    { id: "aicfId", label: "National ID" },
    { id: "stateId", label: "State ID" },
    { id: "districtId", label: "District ID" },
    { id: "club", label: "Club" },
    { id: "ageCategory", label: "Age Category" },
    { id: "genderCategory", label: "Gender Category" },
  ];
  const newColumns =
    (user?.role === "club" && user?.userId === tournamentDetails?.clubId) ||
    (user?.role === "arbiter" && user?.userId === tournamentDetails?.arbiterId)
      ? [...columns,{id:"status",label:"Status",format:(item)=>capitalizeFirstLetter(item)}]
      : columns;
  // Reset search form
  const handleReset = () => {
    setSearch({
      playerName: "",
      playerId: "",
      ageCategory: "",
      genderCategory: "",
    });
  };

  const fetchPlayers = async (requestedPage) => {
    setLoading(true);
    try {
      const params = {
        playerName: search.playerName,
        playerId: search.playerId,
        ageCategory: search.ageCategory,
        genderCategory: search.genderCategory,
        registerId: search.registerId,
        page: requestedPage || page,
        limit: 10,
        tournamentId: title,
        isPlayer: !user || (user && user?.role === "player") ? "true" : "false",
      };
      const response = await Client.get(`club/tournament/players?Id=${Id}`, {
        params,
      });
      if (!response.data.success) {
        toast.info(response.data.error.massage);
        return;
      }
      if (response.data.status === 204) {
        toast.info("No Player found");
        setPlayers([]);
        setPage(0);
        setTotal(0);
        setTotalPages(0);
        return;
      }
      const data = response.data?.data;

    // const sortedPlayers = data?.players
    //   ? [...data.players].sort((a, b) => b.serialNumber - a.serialNumber)
    //   : [];

    const sortedPlayers = data?.players


      setPlayers(sortedPlayers);
      setTotalPages(data?.totalPages);
      // Use requestedPage if provided, otherwise use API response
      setPage(requestedPage || data?.currentPage);
      setTotal(data?.total);
    } catch (error) {
      console.error("Error searching players:", error);
      toast.error("An error occurred while searching for players");
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = async () => {
    fetchPlayers();
  };

  const fetchPlayersReport = async () => {
    // return;
    // setLoading(true);

    try {
      const response = await Client.get("/report/players-tournament", {
        params: {
          genderCategory: search.genderCategory || "",
          ageCategory: search.ageCategory || "",
          tournamentTitle: title,
        },
        responseType: "blob",
      });

      // if (!response.data) {
      //   toast.error(response.error || "Failed to export player details"
      //   );
      //   return;
      // }
      const url = window.URL.createObjectURL(new Blob([response.data]));

      const link = document.createElement("a");
      link.href = url;
      link.setAttribute("download", "Players_report.xlsx");
      document.body.appendChild(link);
      link.click();
      link.remove();
    } catch (error) {
      console.error("Download failed:", error);
      toast.error(error || "Failed to export player details");
    }
  };
 const fetchPlayersForSwissManager = async () => {
  try {
    const response = await Client.get("/report/players-tournament/swiss", {
      params: { tournamentTitle: title },
      responseType: "blob", // Important for downloading files
    });

    const blob = new Blob([response.data], { type: "application/xml" });

    const url = window.URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.href = url;

    const fileName = `Players_report${
      title ? `_${title.replace(/\s+/g, "_")}` : ""
    }.xml`;

    link.setAttribute("download", fileName);
    document.body.appendChild(link);
    link.click();
    link.remove();
    window.URL.revokeObjectURL(url); // Clean up
  } catch (error) {
    console.error("Download failed:", error);
    toast.error("Failed to export player details.");
  }
};

  const handlePageChange = (value) => {
    // Immediately update page state for UI responsiveness
    const pageNum = Number(value);
    setPage(pageNum);
    fetchPlayers(pageNum);
  };

  return (
    <Container maxWidth={"xl"} sx={{ minHeight: "70dvh", my: 4, mt: 2 }}>
      <BackButton />

      <PlayerSearchForm
        tournamentDetails={tournamentDetails}
        loading={loading}
        search={search}
        setSearch={setSearch}
        handleReset={handleReset}
        handleSearch={handleSearch}
        setLoading={setLoading}
      />
      <DynamicTable
        columns={newColumns}
        data={players}
        loading={loading}
        page={page}
        detailsPath="/players/"
        totalPages={totalPages}
        onPageChange={handlePageChange}
        idField="cbid"
        showDetailsButton={false}
      />
      {/* Download Report */}
      {((user?.role === "club" && user?.userId === tournamentDetails?.clubId) ||
        (user?.role === "arbiter" &&
          user?.userId === tournamentDetails?.arbiterId)) && (
        <Box
          mt={5}
          sx={{
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            gap: 2,
          }}
        >
          <Button
            size="small"
            variant="contained"
            disabled={players?.length === 0}
            onClick={fetchPlayersReport}
          >
            Download Report
          </Button>
          <Button
            size="small"
            variant="contained"
            disabled={players?.length === 0}
            onClick={fetchPlayersForSwissManager}
          >
            Download Players for Swiss Manager
          </Button>
        </Box>
      )}
    </Container>
  );
};

export default RegisterPlayers;
