import React, { useState, useEffect, useCallback } from "react";
import {
  Box,
  Button,
  Container,
  Paper,
  Typography,
  Grid,
  TextField,
  Card,
  CardContent,
  Autocomplete,
  CircularProgress,
  MenuItem,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
} from "@mui/material";

import { Client } from "../../api/client";
import UseToast from "../../lib/hooks/UseToast";
import { AxiosError } from "axios";
import { Link } from "react-router-dom";
import SearchIcon from "@mui/icons-material/Search";
import PaymentIcon from "@mui/icons-material/Payment";
import PersonIcon from "@mui/icons-material/Person";
import EventIcon from "@mui/icons-material/Event";
import RefundIcon from "@mui/icons-material/MoneyOff";
import AccountBalanceIcon from "@mui/icons-material/AccountBalance";
import { RestartAlt, Close } from "@mui/icons-material";
import DynamicTable from "../../components/common/DynamicTable";
import BackButton from "../../components/common/BackButton";
import { capitalizeWords, upperCaseWords } from "../../utils/formatters";



const TournamentEarningsPage = () => {
  const [clubPayments, setClubPayments] = useState([]);
  const [playerPayments, setPlayerPayments] = useState([]);
  const [loading, setLoading] = useState(false);
  const [tournamentLoading, setTournamentLoading] = useState(false);
  const [totalPages, setTotalPages] = useState(0);
  const [platformFeePercent, setPlatformFeePercent] = useState(0);

  // Separate state for club and player payments
  const [clubTotal, setClubTotal] = useState(0);
  const [clubTotalPages, setClubTotalPages] = useState(1);
  const [clubPage, setClubPage] = useState(1);
  const [playerTotal, setPlayerTotal] = useState(0);
  const [playerTotalPages, setPlayerTotalPages] = useState(1);
  const [playerPage, setPlayerPage] = useState(1);

  const [shouldFetch, setShouldFetch] = useState(false);
  const [tournaments, setTournaments] = useState([]);
  const [selectedTournament, setSelectedTournament] = useState(null);
  const [summaryStats, setSummaryStats] = useState({
    totalPayers: 0,
    totalAmount: 0,
    refundAmount: 0,
    refundedPayers: 0,
    platformFeeAmount: 0,
    tournamentPayout: 0,
    currency: "INR",
    successfulPayments: 0,
    refundedPayments: 0,
    pendingPayments: 0,
    failedPayments: 0,
    authorizedPayments: 0,
    createdPayments: 0,
  });

  const [clubSummaryStats, setClubSummaryStats] = useState({
    totalPayers: 0,
    totalAmount: 0,
    refundAmount: 0,
    refundedPayers: 0,
    platformFeeAmount: 0,
    tournamentPayout: 0,
    currency: "INR",
    successfulPayments: 0,
    refundedPayments: 0,
    pendingPayments: 0,
    failedPayments: 0,
    authorizedPayments: 0,
    createdPayments: 0,
  });

  // States for form inputs
  const [search, setSearch] = useState({
    transactionId: "",
    tournamentTitle: "",
    paymentType: "all",
  });

  // Separate filters for Club Payments
  const [clubFilters, setClubFilters] = useState({
    tournamentTitle: "",
    paymentStatus: "all",
    startDate: "",
    endDate: "",
  });

  // Separate filters for Player Payments
  const [playerFilters, setPlayerFilters] = useState({
    tournamentTitle: "",
    paymentStatus: "all",
    paymentMethod: "all",
    startDate: "",
    endDate: "",
  });

  // Player list popup state
  const [playerListPopup, setPlayerListPopup] = useState({
    open: false,
    players: [],
    paymentInfo: null,
  });
  const [page, setPage] = useState(1);
  const limit = 10;
  const toast = UseToast();

  const handlePageChange = (_, value) => {
    setPage(value);
    fetchPayments(value);
  };

  // Separate page change handlers for club and player tables
  const handleClubPageChange = (value) => {
    setClubPage(value);
    fetchClubPayments(value);
  };

  const handlePlayerPageChange = (value) => {
    setPlayerPage(value);
    fetchPlayerPayments(value);
  };

  // Handle tournament selection
  const handleTournamentChange = (_, newValue) => {
    try {
      setSelectedTournament(newValue);
      // Reset page numbers for all tables
      setPage(1);
      setClubPage(1);
      setPlayerPage(1);

      if (newValue && newValue.title) {
        setSearch((prev) => ({ ...prev, tournamentTitle: newValue.title }));
        fetchPayments(1, newValue.title);
        // Fetch separate data for club and player payment tables for selected tournament
        fetchClubPayments(1, {}, newValue.title);
        fetchPlayerPayments(1, {}, newValue.title);
      } else {
        setSearch((prev) => ({ ...prev, tournamentTitle: "" }));
        // Clear table data when no tournament is selected
        setClubPayments([]);
        setPlayerPayments([]);
        setClubTotal(0);
        setPlayerTotal(0);
        setClubTotalPages(1);
        setPlayerTotalPages(1);
        // Only fetch summary data (no tournament title - don't pass parameter)
        fetchPayments(1);
      }
    } catch (error) {
      console.error('Error in handleTournamentChange:', error);
      toast.error('Error changing tournament selection');
    }
  };

  const handleReset = () => {
    // Reset main search form
    setSearch({
      transactionId: "",
      tournamentTitle: "",
      paymentType: "all",
    });

    // Reset Club Payments table filters
    setClubFilters({
      tournamentTitle: "",
      paymentStatus: "all",
      startDate: "",
      endDate: "",
    });

    // Reset Player Payments table filters
    setPlayerFilters({
      tournamentTitle: "",
      paymentStatus: "all",
      paymentMethod: "all",
      startDate: "",
      endDate: "",
    });

    // Reset tournament selection
    setSelectedTournament(null);

    // Fetch fresh data
    setShouldFetch(true);

    // Show user feedback
    toast.info("All filters reset successfully");
  };

  const handleSearch = () => {
    setShouldFetch(true);
  };

  // Handle player count click to show player list popup
  const handlePlayerCountClick = (payment) => {
    if (payment.playerList && payment.playerList.length > 0) {
      setPlayerListPopup({
        open: true,
        players: payment.playerList,
        paymentInfo: {
          clubName: payment.clubName,
          tournamentTitle: payment.tournamentTitle,
          paymentAmount: payment.paymentAmount,
          paymentDate: payment.paymentDate,
          playersCount: payment.playersCount,
        },
      });
    } else {
      toast.info("No player details available for this payment");
    }
  };

  // Close player list popup
  const handleClosePlayerListPopup = () => {
    setPlayerListPopup({
      open: false,
      players: [],
      paymentInfo: null,
    });
  };

  // Fetch platform fee
  const fetchPlatformFee = useCallback(async () => {
    try {
      const response = await Client.get("/tournament/platform-fee");
      if (response.data.success) {
        const platformFeePercentage = parseFloat(response.data.data.fee);
        setPlatformFeePercent(platformFeePercentage);
      }
    } catch (error) {
      console.error("Error fetching platform fee:", error);
    }
  }, []);


  // Define fetchPayments with enhanced refund and payout calculations (no pagination for complete stats)
  const fetchPayments = useCallback(
    async (pageNumber, tournamentTitle = null) => {
      setLoading(true);
      try {
        const params = {
          // Backend expects 'page' and 'limit' parameters
          page: pageNumber || 1,
          limit: limit, // Use original limit value
        };

        // Add tournament filter - use passed parameter first, then state
        if (tournamentTitle && tournamentTitle !== null) {
          // Tournament title passed directly (from dropdown change)
          params.tournamentTitle = tournamentTitle;
        } else if (selectedTournament && selectedTournament.title) {
          // Use tournament from state
          params.tournamentTitle = selectedTournament.title;
        } else if (search.tournamentTitle && search.tournamentTitle !== "") {
          // Use tournament from search state
          params.tournamentTitle = search.tournamentTitle;
        }
        // If no tournament specified, don't add tournamentTitle parameter (fetch all)
        if (search.transactionId !== "") {
          params.transactionId = search.transactionId;
        }
        if (search.paymentType !== "all") {
          params.paymentType = search.paymentType;
        }
        const response = await Client.get("/payment/club/tournament", {
          params,
        });

        if (response.status === 204) {
          toast.info("No payment records found");
          setClubPayments([]);
          setPlayerPayments([]);
          setTotalPages(0);
          setPage(1);
          setSummaryStats({
            totalPayers: 0,
            totalAmount: 0,
            refundAmount: 0,
            refundedPayers: 0,
            platformFeeAmount: 0,
            tournamentPayout: 0,
            currency: "INR",
            successfulPayments: 0,
            pendingPayments: 0,
            failedPayments: 0,
            refundedPayments: 0,
          });
          return;
        }

        if (!response.data.success) {
          toast.info("No payment records found");
          return;
        }

        const paymentsData = response.data.data.payments;


        // setClubPayments and setPlayerPayments are managed by fetchClubPayments and fetchPlayerPayments

        const totalPage = Math.ceil(response.data.data.total / limit);
        setTotalPages(response.data.data.totalPages);

        // Use backend summaryStats if available, otherwise calculate
        if (response.data.data.summaryStats) {
          // Backend already calculated the stats, use them directly
          setSummaryStats(response.data.data.summaryStats);
        } else {
          // Fallback: Calculate stats from payments data (for backward compatibility)
          const stats = {
            totalPayers: 0,
            totalAmount: 0,
            refundAmount: 0,
            refundedPayers: 0,
            platformFeeAmount: 0,
            tournamentPayout: 0,
            currency: "INR",
            successfulPayments: 0,
            pendingPayments: 0,
            failedPayments: 0,
            refundedPayments: 0,
          };

          // Calculate stats from payments data
          paymentsData.forEach((payment) => {
            const status = payment.paymentStatus?.toLowerCase();
            const amount = parseFloat(payment.paymentAmount || 0);

            // Set currency if available
            if (payment.paymentCurrency) {
              stats.currency = payment.paymentCurrency;
            }

            // Count players
            if (payment.paymentType === "club") {
              if (status === "captured") {
                stats.totalPayers += payment.playersCount || 0;
              }
            } else if (payment.paymentType === "player") {
              if (status === "captured") {
                stats.totalPayers += 1;
              }
            }

            // Calculate amounts based on status
            if (
              status === "captured" ||
              status === "paid" ||
              status === "success"
            ) {
              stats.totalAmount += amount;
              stats.successfulPayments++;
            } else if (status === "refunded") {
              stats.refundAmount += amount;
              stats.refundedPayments++;

              // Count refunded players
              if (payment.paymentType === "club") {
                stats.refundedPayers += payment.playersCount || 0;
              } else if (payment.paymentType === "player") {
                stats.refundedPayers += 1;
              }
            } else if (status === "pending") {
              stats.pendingPayments++;
            } else if (status === "failed") {
              stats.failedPayments++;
            }
          });

          // Calculate platform fee and tournament payout
          stats.platformFeeAmount = (stats.totalAmount * platformFeePercent) / 100;
          stats.tournamentPayout = stats.totalAmount - stats.platformFeeAmount;

          setSummaryStats(stats);
        }
      } catch (error) {
        if (error instanceof AxiosError && error.response) {
          toast.info(error.response.data.error);
          return;
        }
        console.error("Error fetching payment history:", error);
        toast.info("An error occurred. Please try again later.");
      } finally {
        setLoading(false);
      }
    },
    [selectedTournament, search, platformFeePercent, limit]
  );

  // Define fetchTournaments with useCallback
  const fetchTournaments = useCallback(async () => {
    setTournamentLoading(true);
    try {
      const response = await Client.get("/tournament/club", {
        params: { limit: 100, page: 1 },
      });

      if (response.status === 204 || !response.data.success) {
        setTournaments([]);
        return;
      }

      setTournaments(response.data.data.tournaments || []);
    } catch (error) {
      console.error("Error fetching tournaments:", error);
      toast.error("Failed to load tournaments");
    } finally {
      setTournamentLoading(false);
    }
  }, []);

  // Fetch club payments only (requires tournament selection)
  const fetchClubPayments = useCallback(
    async (pageNumber, additionalFilters = {}, tournamentTitle = null) => {
      console.log("fetchClubPayments called with:", { pageNumber, tournamentTitle, selectedTournament: selectedTournament?.title });

      // Determine which tournament title to use
      const targetTournament = tournamentTitle || selectedTournament?.title;

      // Only fetch if tournament is specified
      if (!targetTournament) {
        console.log("No tournament specified, clearing club payments");
        setClubPayments([]);
        setClubTotal(0);
        setClubTotalPages(1);
        setClubSummaryStats({
          totalPayers: 0,
          totalAmount: 0,
          refundAmount: 0,
          refundedPayers: 0,
          platformFeeAmount: 0,
          tournamentPayout: 0,
          currency: "INR",
          successfulPayments: 0,
          refundedPayments: 0,
          pendingPayments: 0,
          failedPayments: 0,
          authorizedPayments: 0,
          createdPayments: 0,
        });
        return;
      }

      setLoading(true);
      try {
        const params = {
          page: pageNumber,
          limit,
        };

        // Add tournament filter - use passed parameter first, then state
        if (targetTournament) {
          params.tournamentTitle = targetTournament;
        }

        if (search.transactionId !== "") {
          params.transactionId = search.transactionId;
        }
        if (search.paymentType !== "all") {
          params.paymentType = search.paymentType;
        }

        // Add additional filters (club specific)
        Object.assign(params, additionalFilters);

        console.log("Club payments API call params:", params);

        const response = await Client.get("/payment/club/tournament/clubpayments", {
          params,
        });

        if (response.status === 204) {
          setClubPayments([]);
          setClubTotal(0);
          setClubTotalPages(1);
          return;
        }

        if (response.data.success) {
          setClubPayments(response.data.data.payments || []);
          setClubTotal(response.data.data.total || 0);
          setClubTotalPages(response.data.data.totalPages || 1);

          // Set club summary stats if available
          if (response.data.data.summaryStats) {
            setClubSummaryStats(response.data.data.summaryStats);
          }
        } else {
          setClubPayments([]);
          setClubTotal(0);
          setClubTotalPages(1);
          setClubSummaryStats({
            totalPayers: 0,
            totalAmount: 0,
            refundAmount: 0,
            refundedPayers: 0,
            platformFeeAmount: 0,
            tournamentPayout: 0,
            currency: "INR",
            successfulPayments: 0,
            refundedPayments: 0,
            pendingPayments: 0,
            failedPayments: 0,
            authorizedPayments: 0,
            createdPayments: 0,
          });
        }
      } catch (error) {
        console.error("Error fetching club payments:", error);
        console.error("API Error Details:", error.response?.data);
        console.error("Status Code:", error.response?.status);
        setClubPayments([]);
        setClubTotal(0);
        setClubTotalPages(1);
      } finally {
        setLoading(false);
      }
    },
    [selectedTournament, search, limit]
  );

  // Fetch player payments only (requires tournament selection)
  const fetchPlayerPayments = useCallback(
    async (pageNumber, additionalFilters = {}, tournamentTitle = null) => {
      console.log("fetchPlayerPayments called with:", { pageNumber, tournamentTitle, selectedTournament: selectedTournament?.title });

      // Determine which tournament title to use
      const targetTournament = tournamentTitle || selectedTournament?.title;

      // Only fetch if tournament is specified
      if (!targetTournament) {
        console.log("No tournament specified, clearing player payments");
        setPlayerPayments([]);
        setPlayerTotal(0);
        setPlayerTotalPages(1);
        return;
      }

      setLoading(true);
      try {
        const params = {
          page: pageNumber,
          limit,
        };

        // Add tournament filter - use passed parameter first, then state
        if (targetTournament) {
          params.tournamentTitle = targetTournament;
        }

        if (search.transactionId !== "") {
          params.transactionId = search.transactionId;
        }
        if (search.paymentType !== "all") {
          params.paymentType = search.paymentType;
        }

        // Add additional filters (player specific)
        Object.assign(params, additionalFilters);

        console.log("Player payments API call params:", params);

        const response = await Client.get("/payment/club/tournament/playerpayments", {
          params,
        });

        if (response.status === 204) {
          setPlayerPayments([]);
          setPlayerTotal(0);
          setPlayerTotalPages(1);
          return;
        }

        if (response.data.success) {
          setPlayerPayments(response.data.data.payments || []);
          setPlayerTotal(response.data.data.total || 0);
          setPlayerTotalPages(response.data.data.totalPages || 1);
        } else {
          setPlayerPayments([]);
          setPlayerTotal(0);
          setPlayerTotalPages(1);
        }
      } catch (error) {
        console.error("Error fetching player payments:", error);
        console.error("API Error Details:", error.response?.data);
        console.error("Status Code:", error.response?.status);
        setPlayerPayments([]);
        setPlayerTotal(0);
        setPlayerTotalPages(1);
      } finally {
        setLoading(false);
      }
    },
    [selectedTournament, search, limit]
  );

  useEffect(() => {
    if (shouldFetch) {
      fetchPayments(1);
      fetchClubPayments(1);
      fetchPlayerPayments(1);
      setShouldFetch(false);
    }
  }, [shouldFetch, fetchPayments, fetchClubPayments, fetchPlayerPayments]);

  // Fetch tournaments and platform fee on component mount
  useEffect(() => {
    fetchTournaments();
    fetchPlatformFee();
  }, [fetchTournaments, fetchPlatformFee]);

  // Initial data fetch on component mount (only summary, tables load when tournament selected)
  // useEffect(() => {
  //   fetchPayments(1);
  //   // Note: Club and player tables remain empty until tournament is selected
  // }, []);

  // Format date for display (date only)
  const formatDate = (dateString) => {
    if (!dateString) return "N/A";
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return dateString;
    const options = {
      day: "2-digit",
      month: "short",
      year: "numeric",
    };
    return date.toLocaleDateString("en-US", options);
  };

  // Format time for display (time only)
  const formatTime = (dateString) => {
    if (!dateString) return "N/A";
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return dateString;
    const options = {
      hour: "2-digit",
      minute: "2-digit",
      hour12: true,
    };
    return date.toLocaleTimeString("en-US", options);
  };

  // Format payment status to user-friendly display
  const formatPaymentStatus = (status) => {
    if (!status) return "N/A";

    const statusMap = {
      captured: "Paid",
      authorized: "Authorized",
      pending: "Processing",
      failed: "Failed",
      refunded: "Refunded",
      created: "Initiated"
    };

    return statusMap[status.toLowerCase()] || capitalizeWords(status);
  };

  // Fetch payments with filters (no pagination for complete stats)
  const fetchPaymentsWithFilters = useCallback(
    async (pageNumber, additionalFilters = {}) => {
      setLoading(true);
      try {
        const params = {
          // Backend expects 'page' and 'limit' parameters
          page: pageNumber || 1,
          limit: limit, // Use original limit value
        };

        // Add main search filters
        // Add tournament filter from dropdown selection
        if (selectedTournament && selectedTournament.title) {
          params.tournamentTitle = selectedTournament.title;
        } else if (search.tournamentTitle !== "") {
          params.tournamentTitle = search.tournamentTitle;
        }
        if (search.transactionId !== "") {
          params.transactionId = search.transactionId;
        }
        if (search.paymentType !== "all") {
          params.paymentType = search.paymentType;
        }

        // Add additional filters (club or player specific)
        Object.assign(params, additionalFilters);

        const response = await Client.get("/payment/club/tournament", {
          params,
        });

        if (response.status === 204) {
          toast.info("No payment records found");
          setClubPayments([]);
          setPlayerPayments([]);
          setTotalPages(0);
          setPage(1);
          setSummaryStats({
            totalPayers: 0,
            totalAmount: 0,
            refundAmount: 0,
            refundedPayers: 0,
            platformFeeAmount: 0,
            tournamentPayout: 0,
            currency: "INR",
            successfulPayments: 0,
            pendingPayments: 0,
            failedPayments: 0,
            refundedPayments: 0,
          });
          return;
        }

        if (!response.data.success) {
          toast.info("No payment records found");
          return;
        }

        const paymentsData = response.data.data.payments;

        // Note: Club and player payments are now handled by separate endpoints
        // setClubPayments and setPlayerPayments are managed by fetchClubPayments and fetchPlayerPayments

        const totalPage = Math.ceil(response.data.data.total / limit);
        setTotalPages(totalPage);

        // Use backend summaryStats if available, otherwise calculate
        if (response.data.data.summaryStats) {
          // Backend already calculated the stats, use them directly
          setSummaryStats(response.data.data.summaryStats);
        } else {
          // Fallback: Calculate stats from payments data (for backward compatibility)
          const stats = {
            totalPayers: 0,
            totalAmount: 0,
            refundAmount: 0,
            refundedPayers: 0,
            platformFeeAmount: 0,
            tournamentPayout: 0,
            currency: "INR",
            successfulPayments: 0,
            pendingPayments: 0,
            failedPayments: 0,
            refundedPayments: 0,
          };

          // Calculate stats from payments data
          paymentsData.forEach((payment) => {
            const status = payment.paymentStatus?.toLowerCase();
            const amount = parseFloat(payment.paymentAmount || 0);

            // Set currency if available
            if (payment.paymentCurrency) {
              stats.currency = payment.paymentCurrency;
            }

            // Count players
            if (payment.paymentType === "club") {
              if (status === "captured") {
                stats.totalPayers += payment.playersCount || 0;
              }
            } else if (payment.paymentType === "player") {
              if (status === "captured") {
                stats.totalPayers += 1;
              }
            }

            // Calculate amounts based on status
            if (
              status === "captured" ||
              status === "paid" ||
              status === "success"
            ) {
              stats.totalAmount += amount;
              stats.successfulPayments++;
            } else if (status === "refunded") {
              stats.refundAmount += amount;
              stats.refundedPayments++;

              // Count refunded players
              if (payment.paymentType === "club") {
                stats.refundedPayers += payment.playersCount || 0;
              } else if (payment.paymentType === "player") {
                stats.refundedPayers += 1;
              }
            } else if (status === "pending") {
              stats.pendingPayments++;
            } else if (status === "failed") {
              stats.failedPayments++;
            }
          });

          // Calculate platform fee and tournament payout
          stats.platformFeeAmount = (stats.totalAmount * platformFeePercent) / 100;
          stats.tournamentPayout = stats.totalAmount - stats.platformFeeAmount;

          setSummaryStats(stats);
        }
      } catch (error) {
        if (error instanceof AxiosError && error.response) {
          toast.info(error.response.data.error);
          return;
        }
        console.error("Error fetching payment history:", error);
        toast.info("An error occurred. Please try again later.");
      } finally {
        setLoading(false);
      }
    },
    [selectedTournament, search, platformFeePercent, limit]
  );

  // Handle club filter search
  const handleClubFilterSearch = () => {
    const clubFilterParams = {};

    if (clubFilters.tournamentTitle) {
      clubFilterParams.tournamentTitle = clubFilters.tournamentTitle;
    }
    if (clubFilters.paymentStatus !== "all") {
      clubFilterParams.paymentStatus = clubFilters.paymentStatus;
    }
    if (clubFilters.startDate) {
      clubFilterParams.startDate = clubFilters.startDate;
    }
    if (clubFilters.endDate) {
      clubFilterParams.endDate = clubFilters.endDate;
    }

    // Validate date range
    if (clubFilters.startDate && clubFilters.endDate && clubFilters.startDate > clubFilters.endDate) {
      toast.error("Start date cannot be after end date");
      return;
    }

    // Use the new club payments endpoint
    fetchClubPayments(1, clubFilterParams);
  };

  // Handle player filter search
  const handlePlayerFilterSearch = () => {
    const playerFilterParams = {};

    if (playerFilters.tournamentTitle) {
      playerFilterParams.tournamentTitle = playerFilters.tournamentTitle;
    }
    if (playerFilters.paymentStatus !== "all") {
      playerFilterParams.paymentStatus = playerFilters.paymentStatus;
    }
    if (playerFilters.paymentMethod !== "all") {
      playerFilterParams.paymentMethod = playerFilters.paymentMethod;
    }
    if (playerFilters.startDate) {
      playerFilterParams.startDate = playerFilters.startDate;
    }
    if (playerFilters.endDate) {
      playerFilterParams.endDate = playerFilters.endDate;
    }

    // Validate date range
    if (playerFilters.startDate && playerFilters.endDate && playerFilters.startDate > playerFilters.endDate) {
      toast.error("Start date cannot be after end date");
      return;
    }

    // Use the new player payments endpoint
    fetchPlayerPayments(1, playerFilterParams);
  };

  const fetchPaymentReport = async () => {
    try {
      const response = await Client.get("/report/payment", {
        params: { ...search },
        responseType: "blob",
      });
      const url = window.URL.createObjectURL(new Blob([response.data]));

      const link = document.createElement("a");
      link.href = url;
      link.setAttribute("download", "Players_report.xlsx");
      document.body.appendChild(link);
      link.click();
      link.remove();
    } catch (error) {
      console.error("Download failed:", error);
    }
  };

  return (
    <Container
      maxWidth="xl"
      sx={{
        py: 4,
        pt: 2,
        pb: 8,
        minHeight: "100vh",
        display: "flex",
        flexDirection: "column",
      }}
    >
      <BackButton />
      {(clubPayments.length > 0 || playerPayments.length > 0) && (
        <Box
          mb={2}
          sx={{
            display: "flex",
            justifyContent: "flex-end",
            alignItems: "center",
          }}
        >
          <Button
            size="small"
            variant="contained"
            disabled={tournaments?.length === 0}
            onClick={fetchPaymentReport}
          >
            Download report
          </Button>
        </Box>
      )}

      {/* Search Form */}
      <Paper
        sx={{ mb: 3, p: 3, bgcolor: "#f9f9f9", borderRadius: 2, boxShadow: 3 }}
      >
        <Typography
          variant="h5"
          gutterBottom
          sx={{ mb: 2, fontWeight: "bold", color: "#3f51b5" }}
        >
          Tournament Payment History
        </Typography>
        <Grid container spacing={3} alignItems="center">
          <Grid item xs={12} sm={6} md={6}>
            <Autocomplete
              options={tournaments}
              getOptionLabel={(option) =>
                option && option.title ? option.title.replace(/-/g, " ") : ""
              }
              value={selectedTournament}
              onChange={handleTournamentChange}
              loading={tournamentLoading}
              renderInput={(params) => (
                <TextField
                  {...params}
                  variant="outlined"
                  placeholder="Select a tournament"
                  fullWidth
                  InputProps={{
                    ...params.InputProps,
                    endAdornment: (
                      <React.Fragment>
                        {tournamentLoading ? (
                          <CircularProgress color="inherit" size={20} />
                        ) : null}
                        {params.InputProps.endAdornment}
                      </React.Fragment>
                    ),
                  }}
                  sx={{ bgcolor: "white" }}
                />
              )}
            />
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <TextField
              variant="outlined"
              fullWidth
              label="Transaction ID"
              value={search.transactionId}
              onChange={(e) =>
                setSearch({ ...search, transactionId: e.target.value })
              }
              sx={{ bgcolor: "white" }}
              placeholder="Enter transaction ID"
            />
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <TextField
              select
              variant="outlined"
              fullWidth
              label="Payment Type"
              value={search.paymentType || "all"}
              onChange={(e) =>
                setSearch({ ...search, paymentType: e.target.value })
              }
              sx={{ bgcolor: "white" }}
              placeholder="Select payment type"
            >
              <MenuItem value="all">All Types</MenuItem>
              <MenuItem value="club">Club</MenuItem>
              <MenuItem value="player">Player</MenuItem>
            </TextField>
          </Grid>

          <Grid item xs={12} sm={6} md={12} sx={{ display: "flex", gap: 1 }}>
            <Button
              variant="outlined"
              color="secondary"
              sx={{ width: "40px" }}
              onClick={handleReset}
              disabled={loading}
              title="Reset all filters and refresh data"
            >
              <RestartAlt />
            </Button>
            <Button
              variant="contained"
              color="primary"
              fullWidth
              onClick={handleSearch}
              startIcon={<SearchIcon />}
              sx={{
                bgcolor: "#3f51b5",
                textTransform: "none",
                height: "56px",
                fontSize: "16px",
                fontWeight: "bold",
              }}
            >
              Search
            </Button>
          </Grid>
        </Grid>
      </Paper>

      {/* Enhanced Summary Statistics */}
      <Paper
        sx={{ mb: 3, p: 3, bgcolor: "#f5f9ff", borderRadius: 2, boxShadow: 2 }}
      >
        <Typography
          variant="h6"
          gutterBottom
          sx={{ fontWeight: "bold", color: "#3f51b5" }}
        >
          Payment Summary & Tournament Payout
        </Typography>
        <Grid container spacing={3} sx={{ mt: 1 }}>
          {/* Row 1 */}
          <Grid item xs={12} sm={6} md={3}>
            <Card sx={{ bgcolor: "#e3f2fd", height: "100%" }}>
              <CardContent>
                <Typography variant="subtitle2" color="primary">
                  Total Payers
                </Typography>
                <Typography
                  variant="h4"
                  sx={{ mt: 1, fontWeight: "bold", textAlign: "center" }}
                >
                  {summaryStats.totalPayers}
                </Typography>
                <Typography variant="body2" color="primary" sx={{ mt: 1 }}>
                  <PersonIcon
                    fontSize="small"
                    sx={{ verticalAlign: "middle", mr: 0.5 }}
                  />
                  Players
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card sx={{ bgcolor: "#e8f5e9", height: "100%" }}>
              <CardContent>
                <Typography variant="subtitle2" color="secondary">
                  Total Amount
                </Typography>
                <Typography
                  variant="h4"
                  sx={{ mt: 1, fontWeight: "bold", textAlign: "center" }}
                >
                  {summaryStats.currency}{" "}
                  {summaryStats.totalAmount.toLocaleString(undefined, {
                    minimumFractionDigits: 0,
                    maximumFractionDigits: 0,
                  })}
                </Typography>
                <Typography variant="body2" color="secondary" sx={{ mt: 1 }}>
                  <PaymentIcon
                    fontSize="small"
                    sx={{ verticalAlign: "middle", mr: 0.5 }}
                  />
                  Collected
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card sx={{ bgcolor: "#ffebee", height: "100%" }}>
              <CardContent>
                <Typography variant="subtitle2" sx={{ color: "#d32f2f" }}>
                  Refunded Players
                </Typography>
                <Typography
                  variant="h4"
                  sx={{
                    mt: 1,
                    fontWeight: "bold",
                    textAlign: "center",
                    color: "#d32f2f",
                  }}
                >
                  {summaryStats.refundedPayers}
                </Typography>
                <Typography variant="body2" sx={{ mt: 1, color: "#d32f2f" }}>
                  <PersonIcon
                    fontSize="small"
                    sx={{ verticalAlign: "middle", mr: 0.5 }}
                  />
                  Players
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card sx={{ bgcolor: "#ffebee", height: "100%" }}>
              <CardContent>
                <Typography variant="subtitle2" sx={{ color: "#d32f2f" }}>
                  Refund Amount
                </Typography>
                <Typography
                  variant="h4"
                  sx={{
                    mt: 1,
                    fontWeight: "bold",
                    textAlign: "center",
                    color: "#d32f2f",
                  }}
                >
                  {summaryStats.currency}{" "}
                  {summaryStats.refundAmount.toLocaleString(undefined, {
                    minimumFractionDigits: 0,
                    maximumFractionDigits: 0,
                  })}
                </Typography>
                <Typography variant="body2" sx={{ mt: 1, color: "#d32f2f" }}>
                  <RefundIcon
                    fontSize="small"
                    sx={{ verticalAlign: "middle", mr: 0.5 }}
                  />
                  Refunded
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          {/* Row 2 */}
          <Grid item xs={12} sm={6} md={3}>
            <Card sx={{ bgcolor: "#fff3e0", height: "100%" }}>
              <CardContent>
                <Typography variant="subtitle2" sx={{ color: "#f57c00" }}>
                  Platform Fee ({platformFeePercent}%)
                </Typography>
                <Typography
                  variant="h4"
                  sx={{
                    mt: 1,
                    fontWeight: "bold",
                    textAlign: "center",
                    color: "#f57c00",
                  }}
                >
                  {summaryStats.currency}{" "}
                  {summaryStats.platformFeeAmount.toLocaleString(undefined, {
                    minimumFractionDigits: 0,
                    maximumFractionDigits: 0,
                  })}
                </Typography>
                <Typography variant="body2" sx={{ mt: 1, color: "#f57c00" }}>
                  <PaymentIcon
                    fontSize="small"
                    sx={{ verticalAlign: "middle", mr: 0.5 }}
                  />
                  Platform Fee
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card sx={{ bgcolor: "#e8f5e9", height: "100%" }}>
              <CardContent>
                <Typography variant="subtitle2" color="secondary">
                  Tournament Payout
                </Typography>
                <Typography
                  variant="h4"
                  sx={{
                    mt: 1,
                    fontWeight: "bold",
                    textAlign: "center",
                    color: "#2e7d32",
                  }}
                >
                  {summaryStats.currency}{" "}
                  {summaryStats.tournamentPayout.toLocaleString(undefined, {
                    minimumFractionDigits: 0,
                    maximumFractionDigits: 0,
                  })}
                </Typography>
                <Typography variant="body2" sx={{ mt: 1, color: "#2e7d32" }}>
                  <AccountBalanceIcon
                    fontSize="small"
                    sx={{ verticalAlign: "middle", mr: 0.5 }}
                  />
                  Net Payout
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card sx={{ bgcolor: "#f3e5f5", height: "100%" }}>
              <CardContent>
                <Typography variant="subtitle2" color="primary">
                  Tournament
                </Typography>
                <Typography
                  variant="h6"
                  sx={{
                    mt: 1,
                    fontWeight: "bold",
                    textTransform: "capitalize",
                    textAlign: "center",
                  }}
                >
                  {selectedTournament && selectedTournament.title
                    ? selectedTournament.title.replace(/-/g, " ")
                    : "All Tournaments"}
                </Typography>
                <Typography variant="body2" color="primary" sx={{ mt: 1 }}>
                  <EventIcon
                    fontSize="small"
                    sx={{ verticalAlign: "middle", mr: 0.5 }}
                  />
                  {selectedTournament && selectedTournament.startDate
                    ? new Date(selectedTournament.startDate).toLocaleDateString(
                      "en-US",
                      { day: "2-digit", month: "short", year: "numeric" }
                    )
                    : "All Dates"}
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card sx={{ bgcolor: "#f5f5f5", height: "100%" }}>
              <CardContent>
                <Typography variant="subtitle2" color="textSecondary">
                  Payment Status
                </Typography>
                <Typography variant="body2" sx={{ mt: 1 }}>
                  <span style={{ color: "green" }}>
                    ✓ Paid: {summaryStats.successfulPayments}
                  </span>
                </Typography>
                <Typography variant="body2">
                  <span style={{ color: "#d32f2f" }}>
                    ↩ Refunded: {summaryStats.refundedPayments}
                  </span>
                </Typography>
                <Typography variant="body2" sx={{ mt: 0.5 }}>
                  <span style={{ color: "#ff9800" }}>
                    ⏳ In Progress: {summaryStats.pendingPayments}
                  </span>
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </Paper>

      {/* Club Payment History Table */}
      {(search.paymentType === "all" || search.paymentType === "club") && (
        <>
          <Typography
            variant="h6"
            gutterBottom
            sx={{ mt: 4, mb: 2, fontWeight: "bold", color: "#3f51b5" }}
          >
            Club Payments
          </Typography>

          {/* Club Payment Status Count Labels */}
          <Paper sx={{ mb: 2, p: 2, bgcolor: "#e3f2fd", borderRadius: 2 }}>
            <Typography variant="subtitle2" sx={{ mb: 1, fontWeight: "bold", color: "#1976d2" }}>
              Club Payment Status Counts:
            </Typography>
            <Typography variant="body2" sx={{ fontSize: "20px", color: "#666" }}>
              <strong>All Status:</strong> {(clubSummaryStats?.successfulPayments || 0) + (clubSummaryStats?.authorizedPayments || 0)} |
              <strong> Paid:</strong> {clubSummaryStats?.successfulPayments || 0} |
              <strong> Authorized:</strong> {clubSummaryStats?.authorizedPayments || 0} |
              <strong> Processing:</strong> {clubSummaryStats?.pendingPayments || 0} |
              <strong> Failed:</strong> {clubSummaryStats?.failedPayments || 0} |
              <strong> Refunded:</strong> {clubSummaryStats?.refundedPayments || 0} |
              <strong> Initiated:</strong> {clubSummaryStats?.createdPayments || 0}
            </Typography>
          </Paper>

          {/* Club Payments Filters */}
          <Paper sx={{ mb: 2, p: 2, bgcolor: "#f0f8ff", borderRadius: 2 }}>
            <Grid container spacing={2} alignItems="center">
              <Grid item xs={12} sm={3} md={2}>
                <TextField
                  select
                  variant="outlined"
                  fullWidth
                  size="small"
                  label="Payment Status"
                  value={clubFilters.paymentStatus}
                  onChange={(e) => {
                    const newStatus = e.target.value;
                    setClubFilters({ ...clubFilters, paymentStatus: newStatus });

                    // Trigger API call immediately when status changes
                    const clubFilterParams = {};
                    if (newStatus !== "all") {
                      clubFilterParams.paymentStatus = newStatus;
                    }
                    if (clubFilters.startDate) {
                      clubFilterParams.startDate = clubFilters.startDate;
                    }
                    if (clubFilters.endDate) {
                      clubFilterParams.endDate = clubFilters.endDate;
                    }
                    fetchClubPayments(1, clubFilterParams);
                  }}
                  sx={{ bgcolor: "white" }}
                >
                  <MenuItem value="all">All Status</MenuItem>
                  <MenuItem value="captured">Paid</MenuItem>
                  <MenuItem value="authorized">Authorized</MenuItem>
                  <MenuItem value="pending">Processing</MenuItem>
                  <MenuItem value="failed">Failed</MenuItem>
                  <MenuItem value="refunded">Refunded</MenuItem>
                  <MenuItem value="created">Initiated</MenuItem>
                </TextField>
              </Grid>

              <Grid item xs={12} sm={4} md={3}>
                <TextField
                  type="date"
                  variant="outlined"
                  fullWidth
                  size="small"
                  label="Start Date"
                  value={clubFilters.startDate}
                  onChange={(e) => {
                    setClubFilters({ ...clubFilters, startDate: e.target.value });
                  }}
                  sx={{ bgcolor: "white" }}
                  InputLabelProps={{ shrink: true }}
                />
              </Grid>

              <Grid item xs={12} sm={4} md={3}>
                <TextField
                  type="date"
                  variant="outlined"
                  fullWidth
                  size="small"
                  label="End Date"
                  value={clubFilters.endDate}
                  onChange={(e) => {
                    if (clubFilters.startDate && e.target.value < clubFilters.startDate) {
                      toast.error("End date cannot be before start date");
                      return;
                    }
                    setClubFilters({ ...clubFilters, endDate: e.target.value });
                  }}
                  sx={{ bgcolor: "white" }}
                  InputLabelProps={{ shrink: true }}
                  inputProps={{ min: clubFilters.startDate }}
                />
              </Grid>

              <Grid item xs={12} sm={12} md={3}>
                <Box sx={{ display: "flex", gap: 1, flexDirection: { xs: "column", sm: "row" } }}>
                  <Button
                    variant="contained"
                    size="small"
                    onClick={handleClubFilterSearch}
                    startIcon={<SearchIcon />}
                    sx={{ bgcolor: "#3f51b5" }}
                  >
                    Search
                  </Button>
                  <Button
                    variant="outlined"
                    size="small"
                    onClick={() => {
                      setClubFilters({
                        tournamentTitle: "",
                        paymentStatus: "all",
                        startDate: "",
                        endDate: "",
                      });
                      // Reset to original data
                      setClubPage(1);
                      fetchPayments(1);
                      fetchClubPayments(1);
                    }}
                  >
                    Reset
                  </Button>
                </Box>
              </Grid>
            </Grid>
          </Paper>
          <DynamicTable
            columns={[
              {
                id: "index",
                label: "S No",
                width: "80px",
                format: (_, item, index) => (page - 1) * limit + index + 1,
              },
              {
                id: "clubName",
                label: "Club Name",
                format: (_, payment) => (
                  <Typography
                    component={Link}
                    to={`/clubs/${payment.clubId}`}
                    sx={{
                      color: "#1976d2",
                      textDecoration: "none",
                      fontWeight: "medium",
                    }}
                  >
                    {payment.clubName || "N/A"}
                  </Typography>
                ),
              },
              {
                id: "transactionId",
                label: "TID",
                format: (_, payment) => (payment.paymentTransactionId || "N/A").toUpperCase(),
              },
              {
                id: "date",
                label: "Date",
                format: (_, payment) => formatDate(payment.paymentDate),
              },
              {
                id: "time",
                label: "Time",
                format: (_, payment) => formatTime(payment.paymentDate),
              },
              {
                id: "amount",
                label: "Amount",
                format: (_, payment) => (
                  <Typography variant="h6" fontSize={"16px"}>
                    {payment.paymentCurrency || "INR"}{" "}
                    {payment.paymentAmount || "0.00"}
                  </Typography>
                ),
              },
              {
                id: "playersCount",
                label: "Players Count",
                format: (_, payment) => (
                  <Typography
                    variant="h6"
                    fontSize={"16px"}
                    fontWeight="medium"
                    sx={{
                      color: (payment.playerList && payment.playerList.length > 0) ? "primary.main" : "text.primary",
                      textDecoration: (payment.playerList && payment.playerList.length > 0) ? "underline" : "none",
                      cursor: payment.playerList && payment.playerList.length > 0 ? "pointer" : "default",
                    }}
                    onClick={() => {
                      if (payment.playerList && payment.playerList.length > 0) {
                        handlePlayerCountClick(payment);
                      }
                    }}
                  >
                    {payment.playersCount || "0"}
                  </Typography>
                ),
              },
              {
                id: "registrationType",
                label: "Registration Type",
                format: (_, payment) => payment.registrationType || "N/A",
              },
              {
                id: "paymentStatus",
                label: "Status",
                format: (_, payment) => (
                  <Typography
                    variant="body1"
                    sx={{
                      color:
                        payment.paymentStatus?.toLowerCase() === "captured"
                          ? "green"
                          : payment.paymentStatus?.toLowerCase() === "pending"
                            ? "orange"
                            : "red",
                      fontWeight: "medium",
                    }}
                  >
                    {formatPaymentStatus(payment.paymentStatus)}
                  </Typography>
                ),
              },
              // {
              //   id: "tournament",
              //   label: "Tournament",
              //   format: (_, payment) =>
              //     payment?.tournament?.title || payment.tournamentTitle ? (
              //       <Link
              //         to={`/tournaments/${
              //           payment?.tournament?.title || payment.tournamentTitle
              //         }`}
              //         style={{ textDecoration: "none", color: "inherit" }}
              //       >
              //         <Typography
              //           variant="h6"
              //           sx={{
              //             textTransform: "capitalize",
              //             fontWeight: "medium",
              //             textWrap: "balance",
              //             fontSize: "16px",
              //           }}
              //         >
              //           {(payment?.tournament?.title || payment.tournamentTitle)
              //             .toLowerCase()
              //             .replace(/-/g, " ")}
              //         </Typography>
              //       </Link>
              //     ) : (
              //       "N/A"
              //     ),
              // },
            ]}
            data={clubPayments}
            loading={loading}
            page={clubPage}
            totalPages={clubTotalPages}
            onPageChange={handleClubPageChange}
            detailsPath="/players/"
            idField="id"
            showDetailsButton={false}
            tableContainerProps={{
              sx: {
                minHeight: "200px",
                maxHeight: "400px",
              },
            }}
          />
        </>
      )}

      {/* Player Payment History Table */}
      {(search.paymentType === "all" || search.paymentType === "player") && (
        <>
          <Typography
            variant="h6"
            gutterBottom
            sx={{ mt: 4, mb: 2, fontWeight: "bold", color: "#3f51b5" }}
          >
            Player Payments
          </Typography>

          {/* Player Payment Status Count Labels */}
          <Paper sx={{ mb: 2, p: 2, bgcolor: "#e3f2fd", borderRadius: 2 }}>
            <Typography variant="subtitle2" sx={{ mb: 1, fontWeight: "bold", color: "#1976d2" }}>
              Player Payment Status Counts:
            </Typography>
            <Typography variant="body2" sx={{ fontSize: "20px", color: "#666" }}>
              <strong>All Status:</strong> {(summaryStats?.successfulPayments || 0) + (summaryStats?.authorizedPayments || 0)} |
              <strong> Paid:</strong> {summaryStats?.successfulPayments || 0} |
              <strong> Authorized:</strong> {summaryStats?.authorizedPayments || 0} |
              <strong> Processing:</strong> {summaryStats?.pendingPayments || 0} |
              <strong> Failed:</strong> {summaryStats?.failedPayments || 0} |
              <strong> Refunded:</strong> {summaryStats?.refundedPayments || 0} |
              <strong> Initiated:</strong> {summaryStats?.createdPayments || 0}
            </Typography>
          </Paper>

          {/* Player Payments Filters */}
          <Paper sx={{ mb: 2, p: 2, bgcolor: "#f0fff0", borderRadius: 2 }}>
            <Grid container spacing={2} alignItems="center">
              <Grid item xs={12} sm={3} md={2}>
                <TextField
                  select
                  variant="outlined"
                  fullWidth
                  size="small"
                  label="Payment Status"
                  value={playerFilters.paymentStatus}
                  onChange={(e) => {
                    const newStatus = e.target.value;
                    setPlayerFilters({ ...playerFilters, paymentStatus: newStatus });

                    const playerFilterParams = {};
                    if (newStatus !== "all") {
                      playerFilterParams.paymentStatus = newStatus;
                    }
                    if (playerFilters.startDate) {
                      playerFilterParams.startDate = playerFilters.startDate;
                    }
                    if (playerFilters.endDate) {
                      playerFilterParams.endDate = playerFilters.endDate;
                    }
                    fetchPlayerPayments(1, playerFilterParams);
                  }}
                  sx={{ bgcolor: "white" }}
                >
                  <MenuItem value="all">All Status</MenuItem>
                  <MenuItem value="captured">Paid</MenuItem>
                  <MenuItem value="authorized">Authorized</MenuItem>
                  <MenuItem value="pending">Processing</MenuItem>
                  <MenuItem value="failed">Failed</MenuItem>
                  <MenuItem value="refunded">Refunded</MenuItem>
                  <MenuItem value="created">Initiated</MenuItem>
                </TextField>
              </Grid>

              <Grid item xs={12} sm={3} md={2}>
                <TextField
                  select
                  variant="outlined"
                  fullWidth
                  size="small"
                  label="Method"
                  value={playerFilters.paymentMethod}
                  onChange={(e) => {
                    const newMethod = e.target.value;
                    setPlayerFilters({ ...playerFilters, paymentMethod: newMethod });

                    const playerFilterParams = {};
                    if (playerFilters.paymentStatus !== "all") {
                      playerFilterParams.paymentStatus = playerFilters.paymentStatus;
                    }
                    if (newMethod !== "all") {
                      playerFilterParams.paymentMethod = newMethod;
                    }
                    if (playerFilters.startDate) {
                      playerFilterParams.startDate = playerFilters.startDate;
                    }
                    if (playerFilters.endDate) {
                      playerFilterParams.endDate = playerFilters.endDate;
                    }
                    fetchPlayerPayments(1, playerFilterParams);
                  }}
                  sx={{ bgcolor: "white" }}
                >
                  <MenuItem value="all">All Methods</MenuItem>
                  <MenuItem value="card">Card</MenuItem>
                  <MenuItem value="upi">UPI</MenuItem>
                  <MenuItem value="netbanking">Net Banking</MenuItem>
                  <MenuItem value="wallet">Wallet</MenuItem>
                  <MenuItem value="paylater">Pay Later</MenuItem>
                </TextField>
              </Grid>

              <Grid item xs={12} sm={4} md={3}>
                <TextField
                  type="date"
                  variant="outlined"
                  fullWidth
                  size="small"
                  label="Start Date"
                  value={playerFilters.startDate}
                  onChange={(e) => {
                    setPlayerFilters({ ...playerFilters, startDate: e.target.value });
                  }}
                  sx={{ bgcolor: "white" }}
                  InputLabelProps={{ shrink: true }}
                />
              </Grid>

              <Grid item xs={12} sm={4} md={3}>
                <TextField
                  type="date"
                  variant="outlined"
                  fullWidth
                  size="small"
                  label="End Date"
                  value={playerFilters.endDate}
                  onChange={(e) => {
                    if (playerFilters.startDate && e.target.value < playerFilters.startDate) {
                      toast.error("End date cannot be before start date");
                      return;
                    }
                    setPlayerFilters({ ...playerFilters, endDate: e.target.value });
                  }}
                  sx={{ bgcolor: "white" }}
                  InputLabelProps={{ shrink: true }}
                  inputProps={{ min: playerFilters.startDate }}
                />
              </Grid>

              <Grid item xs={12} sm={12} md={2}>
                <Box sx={{ display: "flex", gap: 1, flexDirection: { xs: "column", sm: "row" } }}>
                  <Button
                    variant="contained"
                    size="small"
                    onClick={handlePlayerFilterSearch}
                    startIcon={<SearchIcon />}
                    sx={{ bgcolor: "#3f51b5" }}
                  >
                    Search
                  </Button>
                  <Button
                    variant="outlined"
                    size="small"
                    onClick={() => {
                      setPlayerFilters({
                        tournamentTitle: "",
                        paymentStatus: "all",
                        paymentMethod: "all",
                        startDate: "",
                        endDate: "",
                      });
                      // Reset to original data
                      setPlayerPage(1);
                      fetchPayments(1);
                      fetchPlayerPayments(1);
                    }}
                  >
                    Reset
                  </Button>
                </Box>
              </Grid>


            </Grid>
          </Paper>
          <DynamicTable
            columns={[
              {
                id: "index",
                label: "S No",
                width: "80px",
                format: (_, item, index) => (page - 1) * limit + index + 1,
              },
              {
                id: "playerName",
                label: "Player",
                format: (_, payment) => (
                  <Typography
                    variant="h6"
                    sx={{ fontWeight: "medium", fontSize: "16px" }}
                  >
                    {payment.playerName || "Unknown Player"}
                  </Typography>
                ),
              },
              {
                id: "cbid",
                label: "CBID",
                format: (_, payment) => (
                  <Typography
                    component={Link}
                    to={`/players/${payment.cbid}`}
                    sx={{ color: "#1976d2", textDecoration: "none" }}
                  >
                    {payment.cbid || "N/A"}
                  </Typography>
                ),
              },
              {
                id: "transactionId",
                label: "TID",
                format: (_, payment) => (payment.paymentTransactionId || "N/A").toUpperCase(),
              },
              {
                id: "date",
                label: "Date",
                format: (_, payment) => formatDate(payment.paymentDate),
              },
              {
                id: "time",
                label: "Time",
                format: (_, payment) => formatTime(payment.paymentDate),
              },
              {
                id: "amount",
                label: "Amount",
                format: (_, payment) => (
                  <Typography variant="h6" fontSize={"16px"}>
                    {payment.paymentCurrency || "INR"}{" "}
                    {payment.paymentAmount || "0.00"}
                  </Typography>
                ),
              },
              {
                id: "paymentMethod",
                label: "Method",
                format: (_, payment) => (
                  <Typography
                    variant="body1"
                    sx={{
                      textAlign: "left",
                      fontWeight: "medium",
                      paddingRight: "10px"
                    }}
                  >
                    {!payment.paymentMethod
                      ? "Online"
                      : payment.paymentMethod === "upi"
                        ? upperCaseWords(payment.paymentMethod)
                        : capitalizeWords(payment.paymentMethod)
                    }
                  </Typography>
                ),
              },
              {
                id: "paymentStatus",
                label: "Status",
                format: (_, payment) => (
                  <Typography
                    variant="body1"
                    sx={{
                      color:
                        payment.paymentStatus?.toLowerCase() === "captured"
                          ? "green"
                          : payment.paymentStatus?.toLowerCase() === "pending"
                            ? "orange"
                            : "red",
                      fontWeight: "medium",
                    }}
                  >
                    {formatPaymentStatus(payment.paymentStatus)}
                  </Typography>
                ),
              },
              // {
              //   id: "tournament",
              //   label: "Tournament",
              //   format: (_, payment) =>
              //     payment?.tournament?.title || payment.tournamentTitle ? (
              //       <Link
              //         to={`/tournaments/${
              //           payment?.tournament?.title || payment.tournamentTitle
              //         }`}
              //         style={{ textDecoration: "none", color: "inherit" }}
              //       >
              //         <Typography
              //           variant="h6"
              //           sx={{
              //             textTransform: "capitalize",
              //             fontWeight: "medium",
              //             textWrap: "balance",
              //             fontSize: "16px",
              //           }}
              //         >
              //           {(payment?.tournament?.title || payment.tournamentTitle)
              //             .toLowerCase()
              //             .replace(/-/g, " ")}
              //         </Typography>
              //       </Link>
              //     ) : (
              //       "N/A"
              //     ),
              // },
            ]}
            data={playerPayments}
            loading={loading}
            page={playerPage}
            totalPages={playerTotalPages}
            onPageChange={handlePlayerPageChange}
            detailsPath="/players/"
            idField="cbid"
            showDetailsButton={false}
            tableContainerProps={{
              sx: {
                minHeight: "200px",
                maxHeight: "400px",
              },
            }}
          />
        </>
      )}

      {/* Player List Popup Dialog */}
      <Dialog
        open={playerListPopup.open}
        onClose={handleClosePlayerListPopup}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle sx={{ display: "flex", justifyContent: "space-between", alignItems: "center" }}>
          <Typography variant="h6" sx={{ fontWeight: "bold", color: "#3f51b5" }}>
            Player Details ({playerListPopup.players.length} Players)
          </Typography>
          <IconButton onClick={handleClosePlayerListPopup} size="small">
            <Close />
          </IconButton>
        </DialogTitle>

        <DialogContent sx={{ maxHeight: "70vh", overflowY: "auto", p: 2 }}>
          {playerListPopup.players.length > 0 ? (
            <TableContainer
              component={Paper}
              sx={{
                mt: 1,
                maxHeight: "60vh",
                overflowY: "auto",
                border: "1px solid #e0e0e0"
              }}
            >
              <Table size="small" stickyHeader>
                <TableHead>
                  <TableRow sx={{ bgcolor: "#f5f5f5" }}>
                    <TableCell sx={{ fontWeight: "bold" }}>S.No</TableCell>
                    <TableCell sx={{ fontWeight: "bold" }}>Player Name</TableCell>
                    <TableCell sx={{ fontWeight: "bold" }}>CBID</TableCell>
                    <TableCell sx={{ fontWeight: "bold" }}>Email</TableCell>
                    <TableCell sx={{ fontWeight: "bold" }}>Phone</TableCell>
                    <TableCell sx={{ fontWeight: "bold" }}>Age Category</TableCell>
                    <TableCell sx={{ fontWeight: "bold" }}>Gender</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {playerListPopup.players.map((player, index) => (
                    <TableRow
                      key={player.playerId || index}
                      sx={{
                        '&:nth-of-type(odd)': { bgcolor: "#fafafa" },
                        '&:hover': { bgcolor: "#e3f2fd" }
                      }}
                    >
                      <TableCell>{index + 1}</TableCell>
                      <TableCell sx={{ fontWeight: "medium" }}>
                        {player.playerName || "N/A"}
                      </TableCell>
                      <TableCell>
                        {player.cbid || player.playerCbid || "N/A"}
                      </TableCell>
                      <TableCell>
                        {player.playerEmail || "N/A"}
                      </TableCell>
                      <TableCell>
                        {player.playerPhone || "N/A"}
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={player.ageCategory || "OPEN"}
                          size="small"
                          color="primary"
                          variant="outlined"
                        />
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={player.genderCategory || player.playerGender || "N/A"}
                          size="small"
                          color="secondary"
                          variant="outlined"
                        />
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          ) : (
            <Typography variant="body1" color="text.secondary" sx={{ textAlign: "center", py: 4 }}>
              No player details available
            </Typography>
          )}
        </DialogContent>

        <DialogActions sx={{ p: 2 }}>
          <Button onClick={handleClosePlayerListPopup} variant="contained" color="primary">
            Close
          </Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
};

export default TournamentEarningsPage;
