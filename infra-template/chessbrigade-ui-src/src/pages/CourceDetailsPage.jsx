import React, { useEffect, useState } from "react";
import {
  <PERSON>,
  But<PERSON>,
  Container,
  Al<PERSON>,
  Typo<PERSON>,
  Stack,
} from "@mui/material";
import { Link, useNavigate, useParams } from "react-router-dom";

import { Client } from "../api/client";
import UseToast from "../lib/hooks/UseToast";
import PaymentModal from "../components/payment/PaymentModal";
import CancellationModal from "../components/payment/CancellationModal";
import useGlobalContext from "../lib/hooks/UseGlobalContext";
import BackButton from "../components/common/BackButton";
import { Helmet } from "react-helmet";
import CourseDetailsView from "../components/common/CourceDetailsView";
import CourseDetailsSkeleton from "../components/common/CourseDetailsSkeleton";

const CourseDetailsPage = () => {
  const { title } = useParams();
  const newTitle = encodeURIComponent(title);

  const [course, setCourse] = useState({});
  const [loading, setLoading] = useState(true);
  const [paymentModalOpen, setPaymentModalOpen] = useState(false);
  const [userDetails, setUserDetails] = useState({});
  const [isLoading, setIsLoading] = useState(false);
  const navigate = useNavigate();
  const { user, setOpenModel, isLoggedIn } = useGlobalContext();
  const toast = UseToast();
  const [cancellationModalOpen, setCancellationModalOpen] = useState(false);
  const [isRegistered, setIsRegistered] = useState({
    isRegistered: false,
    registrationStatus: "pending",
    registrationType: "student",
  });

  const fetchDetails = async () => {
    if (!title) {
      setLoading(false);
      return;
    }

    setLoading(true);
    try {
      const response = await Client.get(`/course/${newTitle}`);
      if (response.data.success) {
        setCourse(response.data.data);
      } else {
        toast.info(response.data.message || "Failed to load course details");
      }
    } catch (error) {
      console.error("Error fetching course details:", error);
      toast.error("Error fetching course details. Please try again later.");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchDetails();
  }, [newTitle, user]);

  const handleRegister = async () => {
    if (!user) {
      toast.info("Please login to register for the course");
      setOpenModel((prev) => ({ ...prev, login: true }));
      return false;
    }
    // Additional registration logic can be added here
    setPaymentModalOpen(true);
  };

  const handleOpenCancellationModal = () => {
    setCancellationModalOpen(true);
  };

  return (
    <>
      <Helmet>
        <title>{`${title} | CoursePlatform.com`}</title>
        <meta
          name="description"
          content="Enroll now and enhance your skills with our courses!"
        />
        <link
          rel="canonical"
          href={`https://www.courseplatform.com/courses/${title}`}
        />
      </Helmet>
      {loading ? (
        <CourseDetailsSkeleton />
      ) : (
        <Container maxWidth="xl" sx={{ py: 4, pt: 2, minHeight: "100vh" }}>
          <BackButton />
          <div style={{ minHeight: "100vh" }}>
            {course && <CourseDetailsView course={course} />}
          </div>
          <Box
            sx={{
              display: "flex",
              alignItems: "center",
              flexWrap: "wrap",
              justifyContent: "center",
              gap: 2,
              p: 2,
              borderBottom: "1px solid #f0f0f0",
            }}
          >
            {(!user || (user && user?.role === "student")) && (
              <Stack
                spacing={2}
                alignItems="center"
                sx={{
                  display: "flex",
                  flexDirection: "row",
                  alignItems: "center",
                  justifyContent: "center",
                  gap: 2,
                }}
              >
                {isRegistered.registrationStatus === "active" && (
                  <Alert
                    sx={{
                      fontSize: "1rem",
                      display: "flex",
                      justifyContent: "center",
                      alignItems: "center",
                      gap: 2,
                      p: 1,
                      "& .MuiAlert-message": { p: "0px !important" },
                    }}
                    severity="success"
                    icon={false}
                  >
                    You are registered for this course.
                  </Alert>
                )}
                {/* Additional alerts for registration status can be added here */}
                {(!user || (user && !isRegistered?.isRegistered)) && (
                  <Button
                    onClick={handleRegister}
                    sx={{
                      borderRadius: 1,
                      textTransform: "none",
                      fontWeight: 500,
                      fontSize: 16,
                      maxWidth: "250px",
                      px: 2,
                      bgcolor: "#166DA3",
                      color: "#fff",
                      "&:hover": {
                        bgcolor: "#1A7FBF",
                      },
                    }}
                  >
                    Register Now
                  </Button>
                )}
              </Stack>
            )}
          </Box>
          {/* Payment Modal */}
          {user && user?.role === "student" && (
            <>
              <PaymentModal
                open={paymentModalOpen}
                setLoading={setLoading}
                onClose={() => setPaymentModalOpen(false)}
                course={course}
                userDetails={userDetails}
              />
              {isRegistered.isRegistered && (
                <CancellationModal
                  open={cancellationModalOpen}
                  onClose={() => setCancellationModalOpen(false)}
                  course={course}
                  onsuccess={() => fetchDetails()}
                />
              )}
            </>
          )}
        </Container>
      )}
    </>
  );
};

export default CourseDetailsPage;
