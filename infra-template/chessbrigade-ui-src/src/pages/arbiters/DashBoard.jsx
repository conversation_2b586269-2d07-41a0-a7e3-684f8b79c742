import BankBuildingIcon from "@mui/icons-material/AccountBalance";
import PersonalTrainerIcon from "@mui/icons-material/FitnessCenter";
import MemberIcon from "@mui/icons-material/People";
import AddUserMaleIcon from "@mui/icons-material/PersonAdd";
import PaymentHistoryIcon from "@mui/icons-material/Receipt";
import TrainingIcon from "@mui/icons-material/SportsHandball";
import useGlobalContext from "../../lib/hooks/UseGlobalContext";
import { Box, Typography, CircularProgress } from "@mui/material";
import React, { useEffect, useState } from "react";

import LinkItemDashboard from "../../components/common/LinkItemDashboard";
import UpcomingTournaments from "../../components/common/UpcomingTournaments";
import { NotificationAdd } from "@mui/icons-material";
import UseToast from "../../lib/hooks/UseToast";
import { Client } from "../../api/client";

// Dashboard card data for mapping
const dashboardCards = [
  {
    title: "My Profile",
    color: "#ebccff",
    icon: <AddUserMaleIcon />,
    link: "profile",
  },
  {
    title: "Notifications",
    color: "#FFD1DC",
    icon: <NotificationAdd />,
    link: "notifications",
  },
  // {
  //   title: "Payments",
  //   color: "#beddf0",
  //   icon: <PaymentHistoryIcon />,
  //   link: "payments",
  // },
  {
    title: "Tournament",
    color: "#f3beb9",
    icon: <PersonalTrainerIcon />,
    link: "tournaments",
  },
  // {
  //   title: "Game History",
  //   color: "#f0e6be",
  //   icon: <TrainingIcon />,
  //   link: "history",
  // },
  { title: "My Chats", color: "#c4b8f0", icon: null, 
    //link: "my-chats"
     },
];

const ArbiterDashboard = () => {
    const { user, currentProfile, fetchProfileData, authLoading } =
    useGlobalContext();
  const [loading, setLoading] = React.useState(false);
  const [data,setData]=useState([])

  const getAd = async ()=>{
    setLoading(true)
    try{
       const response= await Client.get('/user/ad/arbiter')
       if(response.data.success){
       setData(response.data.data)
       }
    }catch(e){
      console.error("error while get ad",e)
    }finally{
      setLoading(false)
    }
  }

  // Fetch profile data if not already available
  // Fetch profile data if not already available
  useEffect(() => {
    const getProfileData = async () => {
      if (user && !currentProfile && !authLoading) {
        setLoading(true);
        try {
          await fetchProfileData();
        } catch (error) {
          console.error("Error fetching club profile:", error);
        } finally {
          setLoading(false);
        }
      }
    };

    getProfileData();
    getAd();
  }, [user, currentProfile, authLoading]);

  return (
    <Box sx={{ px: "5vw", py: "5vh", maxWidth: "100%" }}>
      {loading ? (
        <Box display="flex" justifyContent="center" alignItems="center" height="50vh">
          <CircularProgress />
        </Box>
      ) : (
        <>
          <Typography variant="h4" fontWeight="bold" gutterBottom>
            Welcome, {user?.name || "back"}!
          </Typography>

          <Typography variant="h4" gutterBottom sx={{ mt: 2 }}>
            Arbiter Dashboard
          </Typography>

          <LinkItemDashboard links={dashboardCards} Data={data} />
          <UpcomingTournaments arbiter={true} />
        </>
      )}
    </Box>
  );
};

export default ArbiterDashboard;