import React, { useState, useEffect } from "react";
import {
  Avatar,
  Box,
  Button,
  Container,
  IconButton,
  Paper,
  Skeleton,
  Stack,
  Tooltip,
  Typography,
} from "@mui/material";
import { <PERSON><PERSON>, <PERSON>, PinDrop } from "@mui/icons-material";
import { Client } from "../../api/client";
import UseToast from "../../lib/hooks/UseToast";
import { useNavigate } from "react-router-dom";
import { DetailTable } from "../../components/common/DetailTable";
import BackButton from "../../components/common/BackButton";
import { useTranslation } from "../../context/TranslationContext";
/**
 * Format arbiter data for display in the detail table
 * @param {Object} arbiter - Arbiter data from API
 * @returns {Object} Formatted arbiter data with title and details
 */
function formatArbiterData(arbiter, translate) {
  if (!arbiter) {
    return {
      title: "",
      details: [],
    };
  }

  // Create a list of details to display
  const details = [
    {
      label: translate('arbiterName', "Arbiter Name"),
      value: arbiter.user.name || "-",
    },
    // {
    //   label: "Official ID",
    //   value: arbiter?.officialId || "-",
    // },
    {
      label: translate('fideId', "FIDE ID"),
      value: arbiter?.fideId || "-",
    },
    {
      label: translate('aicfId', "AICF ID"),
      value: arbiter?.aicfId || "-",
    },
    {
      label: translate('phoneNumber', "Phone Number"),
      value: arbiter?.user?.phoneNumber || "-",
    },
    {
      label: translate('alternateContactNumber', "Alternate Contact Number"),
      value: arbiter?.alternateContact || "-",
    },
    {
      label: translate('email', "Email"),
      value: arbiter.user?.email || "-",
    },
    {
      label: translate('country', "Country"),
      value: arbiter?.country || "-",
    },
    {
      label: translate('state', "State"),
      value: arbiter?.state || "-",
    },
    {
      label: translate('district', "District"),
      value: arbiter?.district || "-",
    },
    {
      label: translate('city', "City"),
      value: arbiter?.city || "-",
    },
  ];

  return {
    title: arbiter.user.name || "",
    details: details,
  };
}

const ArbiterProfilePage = () => {
  const { translate } = useTranslation();
  const [arbiterInfo, setArbiterInfo] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  // const { user } = useGlobalContext();
  const toast = UseToast();
  const Navigate = useNavigate();

  useEffect(() => {
    const fetchArbiterInfo = async () => {
      setLoading(true);
      setError(null);
      try {
        const response = await Client.get("/arbiter/profile");
        if (response.status === 204) {
          Navigate("edit?edit=0");
          return;
        }
        if (!response.data.success) {
          toast.error(response.data.message);
          setError(response.data.message);
          return;
        }
        setArbiterInfo(response.data.data);
        setLoading(false);
      } catch (error) {
        console.error("Error fetching arbiter info:", error);

        const errorMessage =
          error.response?.data?.error || translate('unknownErrorOccurred', "An unknown error occurred");
        setError(errorMessage);
        setLoading(false);
        toast.error(
          `${translate('failedToFetchArbiterInfo', "Failed to fetch arbiter information")}: ${errorMessage}. ${translate('pleaseTryAgainLater', "Please try again later.")}`
        );
      }
    };

    fetchArbiterInfo();
  }, [Navigate]);

  const handleRemoveProfile = async () => {
    try {
      const response = await Client.delete(
        "/arbiter/profile/remove-profile-image"
      );
      if (response.data.success) {
        toast.success(translate('profileImageRemovedSuccessfully', "Profile image removed successfully"));
        setArbiterInfo((prev) => ({
          ...prev,
          profileUrl: null,
        }));
      } else {
        toast.error(response.data.message || translate('failedToRemoveProfileImage', "Failed to remove profile image"));
      }
    } catch (error) {
      console.error("Error removing profile image:", error);
      toast.error(translate('failedToRemoveProfileImageTryAgain', "Failed to remove profile image. Please try again later."));
    }
  };


  // Format arbiter data for display in the detail table
  const formattedArbiterData = formatArbiterData(arbiterInfo, translate);

  // const handleSkipFieldChange = (event) => {
  //   setSkipFields(event.target.value.split(",").map((field) => field.trim()));
  // };

  return (
    <Container maxWidth="xl" sx={{ py: 4,pt: 2 }}>
      <BackButton to={"/dashboard"} />
      <Paper
        elevation={1}
        sx={{
          bgcolor: "background.default",
          minHeight: "80vh",
          borderRadius: 2,
          overflow: "hidden",
        }}
      >
        {error ? (
          <Typography
            variant="h6"
            color="error"
            sx={{ p: 2, textAlign: "center" }}
          >
            {translate('somethingWentWrong', 'Something went Wrong.')}
          </Typography>
        ) : (
          <>
            {/* Header */}
            <Box
              sx={{
                display: "flex",
                alignItems: "center",
                justifyContent: "space-between",
                px: "12vw",
                py: 2,
                borderBottom: "1px solid #f0f0f0",
              }}
            >
              <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
                <Stack>
                  {!loading ? (
                    <>
                      <Typography
                        variant="h4"
                        component="h4"
                        fontWeight="500"
                        sx={{ textDecoration: "underline" }}
                      >
                        Arbiter Profile
                      </Typography>
                    </>
                  ) : (
                    <>
                      <Skeleton variant="text" width={200} height={32} />
                    </>
                  )}
                </Stack>
              </Box>
              {loading ? (
                <Skeleton variant="circular" width={60} height={60} />
              ) : (
                <Box sx={{ position: "relative", display: "inline-block" }}>
                  <Avatar
                    src={arbiterInfo?.profileUrl}
                    sx={{
                      width: 100,
                      height: 100,
                      bgcolor: "#f5f5f5",
                      color: "#000",
                    }}
                  >
                    {!arbiterInfo?.profileUrl && <Person sx={{ fontSize: 60 }} />}
                  </Avatar>

                  {arbiterInfo?.profileUrl && (
                    <Tooltip title="Remove profile image" placement="top">
                      <IconButton
                        size="small"
                        onClick={handleRemoveProfile} // define this function in your component
                        sx={{
                          position: "absolute",
                          top: 4,
                          right: 4,
                          backgroundColor: "#fff",
                          color: "red",
                          p: "2px",
                          "&:hover": {
                            backgroundColor: "#eee",
                          },
                        }}
                      >
                        <Cancel fontSize="small" />
                      </IconButton>
                    </Tooltip>
                  )}
                </Box>
              )}
            </Box>

            {/* Arbiter Information */}
            <Box>
              {loading ? (
                // Loading skeleton
                Array(6)
                  .fill(0)
                  .map((_, index) => (
                    <Box key={index} sx={{ mb: 2 }}>
                      <Skeleton variant="rectangular" height={40} />
                    </Box>
                  ))
              ) : error ? (
                // Error message
                <Typography color="error" variant="h6" align="center">
                  {error}
                </Typography>
              ) : (
                // Arbiter details table
                <DetailTable
                  details={formattedArbiterData.details}
                  rowColor={{ odd: "#D4B8A226", even: "#CCF7E840" }}
                />
              )}
            </Box>
            <Box
              sx={{
                p: 2,
                textAlign: "center",
                display: "flex",
                justifyContent: "center",
                gap: 2,
              }}
            >
              {arbiterInfo && arbiterInfo.locationUrl && (
                <Box>
                  <Button
                    variant="contained"
                    startIcon={<PinDrop />}
                    sx={{
                      bgcolor: "hsla(132, 56%, 36%, 1) ",
                      fontSize: "16px",
                      ":hover": { bgcolor: "rgb(33, 107, 48)" },
                    }}
                    onClick={() => Navigate("edit")}
                  >
                    {translate('location', 'Location')}
                  </Button>
                </Box>
              )}
              <Box>
                <Button
                  variant="contained"
                  sx={{
                    bgcolor: "hsla(242, 56%, 36%, 1) ",
                    fontSize: "16px",
                    ":hover": { bgcolor: "rgb(37, 34, 110)" },
                  }}
                  onClick={() => {
                    Navigate("edit?edit=1");
                  }}
                >
                  {translate('editProfile', 'Edit Profile')}
                </Button>
              </Box>
            </Box>
          </>
        )}
      </Paper>
    </Container>
  );
};

export default ArbiterProfilePage;
