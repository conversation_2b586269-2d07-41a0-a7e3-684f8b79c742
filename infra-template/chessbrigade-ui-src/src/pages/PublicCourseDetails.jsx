import React, { useEffect, useState } from "react";
import {
  <PERSON>,
  But<PERSON>,
  Container,
  <PERSON><PERSON>,
  Stack,
} from "@mui/material";
import { useNavigate, useParams } from "react-router-dom";
import { Helmet } from "react-helmet";
import CourseDetailsSkeleton from "../components/common/CourseDetailsSkeleton";
import CourseDetailsView from "../components/common/CourceDetailsView";
import UseGlobalContext from "../lib/hooks/UseGlobalContext";
import UseToast from "../lib/hooks/UseToast";
import BackButton from "../components/common/BackButton";
import { Client } from "../api/client";
import ConfirmationPopup from "../components/admin/ConfirmationPopup";
import CourseRegPopup from "../components/common/CourseRegPopup";

const PublicCourseDetails = () => {
  const { title } = useParams();
  const newTitle = encodeURIComponent(title);

  const [course, setCourse] = useState({});
  const [loading, setLoading] = useState(true);
  const [paymentModalOpen, setPaymentModalOpen] = useState(false);
  const [userDetails, setUserDetails] = useState({});
  const navigate = useNavigate();
  const { user, setOpenModel, isLoggedIn } = UseGlobalContext();
  const toast = UseToast();
  const [isRegistered, setIsRegistered] = useState({
    isRegistered: false,
    registrationStatus: "pending",
    registrationType: "student",
  });
  const [open, setOpen] = useState(false);

  const fetchDetails = async () => {
    if (!title) {
      setLoading(false);
      return;
    }

    setLoading(true);
    try {
      const response = await Client.get(`/course/${newTitle}`);
      if (response.data.success) {
        setCourse(response.data.data);
      } else {
        toast.info(response.data.message || "Failed to load course details");
      }
    } catch (error) {
      console.error("Error fetching course details:", error);
      toast.error("Error fetching course details. Please try again later.");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchDetails();
  }, [newTitle, user]);

  const handleRegister = async () => {
  if (!user) {
    toast.info("Please login to register for the course");
    setOpenModel((prev) => ({ ...prev, login: true }));
    return;
  }

  setLoading(true);

  try {
    const response = await Client.post(`/course/register/${newTitle}`);
    if (response.data.success) {
      toast.success("Request sent. Admin will approve your registration.");
      setIsRegistered((prev) => ({
        ...prev,
        isRegistered: true,
        registrationStatus: "pending",
      }));
    } else {
      toast.error(response.data.message || "Registration failed");
    }
  } catch (error) {
    console.error("Error during registration:", error);
    toast.error("Something went wrong. Please try again.");
  } finally {
    setLoading(false);
  }
};

  return (
    <>
      <Helmet>
        <title>{`${title} | CoursePlatform.com`}</title>
        <meta
          name="description"
          content="Enroll now and enhance your skills with our courses!"
        />
        <link
          rel="canonical"
          href={`https://www.courseplatform.com/courses/${title}`}
        />
      </Helmet>

      {loading ? (
        <CourseDetailsSkeleton />
      ) : (
        course && (
          <Container maxWidth="xl" sx={{ py: 4, pt: 2, minHeight: "100vh" }}>
            <BackButton />
            <div style={{ minHeight: "100vh" }}>
              {course && <CourseDetailsView course={course} />}
            </div>

            <Box
              sx={{
                display: "flex",
                alignItems: "center",
                flexWrap: "wrap",
                justifyContent: "center",
                gap: 2,
                p: 2,
                borderBottom: "1px solid #f0f0f0",
              }}
            >
              {(!user || (user && user?.role === "player")) && (
                <Stack
                  spacing={2}
                  alignItems="center"
                  sx={{
                    display: "flex",
                    flexDirection: "row",
                    alignItems: "center",
                    justifyContent: "center",
                    gap: 2,
                  }}
                >
                    <Button
                      onClick={()=> setOpen(true)}            
                      sx={{
                        borderRadius: 1,
                        textTransform: "none",
                        fontWeight: 500,
                        fontSize: 16,
                        maxWidth: "250px",
                        px: 2,
                        bgcolor: "#166DA3",
                        color: "#fff",
                        "&:hover": {
                          bgcolor: "#1A7FBF",
                        },
                      }}
                    >
                      Register Now
                    </Button>
                
                </Stack>
              )}
            </Box>
    {open &&(
      <CourseRegPopup
        open={open}
        onClose={() => setOpen(false)}
        title = 'Regarding to Register course'
        message=" Your registration request will be sent to the admin. You can enroll once approved. Click conform"
        onConfirm={()=>handleRegister()}
        // value={isAccess}
        // Info={arbiterInfo}
      />)}

          </Container>
        )
      )}
    </>
  );
};

export default PublicCourseDetails;
