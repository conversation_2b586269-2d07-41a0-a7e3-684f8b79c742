import React, { useState, useEffect } from "react";
import {
  Container,
  Paper,
  Typography,
  TextField,
  Button,
  Box,
  Alert,
  CircularProgress,
  Card,
  CardContent,
  InputAdornment,
  Divider,
  Grid,
} from "@mui/material";
import { Save as SaveIcon, Refresh as RefreshIcon } from "@mui/icons-material";
import { Client } from "../../api/client";
import UseToast from "../../lib/hooks/UseToast";
import BackButton from "../../components/common/BackButton";

const PlatformFeePage = () => {
  const [platformFee, setPlatformFee] = useState(null);
  const [feePercentage, setFeePercentage] = useState("");
  const [loading, setLoading] = useState(false);
  const [updating, setUpdating] = useState(false);

  const toast = UseToast();

  // Fetch current platform fee
  const fetchPlatformFee = async () => {
    setLoading(true);
    try {
      const response = await Client.get("/admin/platform-fee");
      if (response.data.success) {
        setPlatformFee(response.data.data);
        setFeePercentage(response.data.data.fee_amount.toString());
      }
    } catch (error) {
      console.error("Error fetching platform fee:", error);
    } finally {
      setLoading(false);
    }
  };

  // Update platform fee
  const updatePlatformFee = async () => {
    if (!feePercentage || parseFloat(feePercentage) < 0) {
      return;
    }

    setUpdating(true);
    try {
      const response = await Client.post("/admin/platform-fee", {
        fee_percentage: feePercentage,
      });
      if (response.data.success) {
        setPlatformFee(response.data.data);
        setFeePercentage(response.data.data.fee_percentage.toString());
        toast.success("Platform fee updated successfully");
      }
    } catch (error) {
      console.error("Error updating platform fee:", error);

      toast.error("Failed to update platform fee");
    } finally {
      setUpdating(false);
    }
  };

  // Handle form submission
  const handleSubmit = (e) => {
    e.preventDefault();
    updatePlatformFee();
  };

  // Load data on component mount
  useEffect(() => {
    fetchPlatformFee();
  }, []);

  if (loading) {
    return (
      <Container maxWidth="md" sx={{ mt: 4 }}>
        <Box
          display="flex"
          justifyContent="center"
          alignItems="center"
          minHeight="50vh"
        >
          <CircularProgress size={60} />
        </Box>
      </Container>
    );
  }

  return (
    <Container maxWidth="xl" sx={{ mt: 4, mb: 4 }}>
      <BackButton />
      <Paper elevation={3} sx={{ p: 4 }}>
        <Typography
          variant="h4"
          component="h1"
          gutterBottom
          align="start"
          color="primary"
        >
          Platform Fee Management
        </Typography>

        <Divider sx={{ mb: 4 }} />

        <Grid container spacing={3}>
          {/* Current Fee Display */}
          {platformFee && (
            <Grid item xs={12}>
              <Card
                sx={{
                  mb: 3,
                  background:
                    "linear-gradient(135deg,rgb(235, 235, 235) 0%,rgb(235, 235, 235) 100%)",
                  color: "white",
                }}
              >
                <CardContent sx={{ textAlign: "start", py: 3 }}>
                  <Typography variant="h6" gutterBottom color="black">
                    Current Platform Fee Percentage
                  </Typography>
                  <Box
                    display="flex"
                    alignItems="start"
                    justifyContent="start"
                    gap={1}
                  >
                    <Typography
                      variant="h3"
                      component="span"
                      color="black"
                      sx={{ fontWeight: "bold" }}
                    >
                      {platformFee.fee_percentage}%
                    </Typography>
                  </Box>
                  <Typography
                    variant="body2"
                    color="black"
                    sx={{
                      mt: 2,
                      opacity: 0.8,
                      textAlign: "start",
                      fontSize: "0.8rem",
                    }}
                  >
                    Last updated:{" "}
                    {new Date(platformFee.updatedAt).toLocaleString()}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          )}

          {/* Update Form */}
          <Grid item xs={12}>
            <Card sx={{ p: 3 }}>
              <Typography variant="h6" gutterBottom color="primary">
                Update Platform Fee
              </Typography>

              <Box component="form" onSubmit={handleSubmit} sx={{ mt: 2 }}>
                <TextField
                  fullWidth
                  label="Fee Percentage"
                  type="number"
                  value={feePercentage}
                  onChange={(e) => setFeePercentage(e.target.value)}
                  InputProps={{
                    endAdornment: (
                      <InputAdornment position="start">%</InputAdornment>
                    ),
                    inputProps: {
                      min: 0,
                      step: 0.01,
                    },
                  }}
                  helperText="Enter fee in percerntage (e.g., 3.75)"
                  variant="outlined"
                  sx={{ mb: 3 }}
                  required
                  error={feePercentage !== "" && parseFloat(feePercentage) < 0}
                />

                <Box
                  display="flex"
                  justifyContent="center"
                  gap={2}
                  flexWrap="wrap"
                >
                  <Button
                    type="button"
                    variant="outlined"
                    startIcon={<RefreshIcon />}
                    onClick={fetchPlatformFee}
                    disabled={updating}
                    size="large"
                  >
                    Refresh
                  </Button>

                  <Button
                    type="submit"
                    variant="contained"
                    startIcon={
                      updating ? (
                        <CircularProgress size={20} color="inherit" />
                      ) : (
                        <SaveIcon />
                      )
                    }
                    disabled={
                      updating ||
                      !feePercentage ||
                      parseFloat(feePercentage) < 0
                    }
                    size="large"
                    sx={{ minWidth: 140 }}
                  >
                    {updating ? "Updating..." : "Update Fee"}
                  </Button>
                </Box>
              </Box>
            </Card>
          </Grid>

          {/* Info Section */}
          <Grid item xs={12}>
            <Box
              sx={{
                p: 3,
                bgcolor: "grey.100",
                borderRadius: 2,
                border: "1px solid",
                borderColor: "grey.300",
              }}
            >
              <Typography variant="body1" color="text.secondary">
                <strong>📝 Note:</strong> The platform fee is applied to all
                transactions. Changes will take effect immediately for new
                transactions. Please ensure the fee percentage is reasonable and
                competitive.
              </Typography>
            </Box>
          </Grid>
        </Grid>
      </Paper>
    </Container>
  );
};

export default PlatformFeePage;
