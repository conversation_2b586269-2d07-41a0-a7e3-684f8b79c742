import BankBuildingIcon from "@mui/icons-material/AccountBalance";
import PersonalTrainerIcon from "@mui/icons-material/FitnessCenter";
import MemberIcon from "@mui/icons-material/People";
import AddUserMaleIcon from "@mui/icons-material/PersonAdd";
import PaymentHistoryIcon from "@mui/icons-material/Receipt";
import TrainingIcon from "@mui/icons-material/SportsHandball";
import useGlobalContext from "../../lib/hooks/UseGlobalContext";
import { Box, Button, Stack, Typography } from "@mui/material";
import React from "react";

import LinkItemDashboard from "../../components/common/LinkItemDashboard";
import { ChatBubble, Extension, Gamepad, Notifications } from "@mui/icons-material";
import UpcomingTournaments from "../../components/common/UpcomingTournaments";
import { Client } from "../../api/client";

// Dashboard card data for mapping
const dashboardCards = [
    {
        title: "My Profile",
        color: "hsla(276, 100%, 90%, 1)",
        icon: <AddUserMaleIcon />,
        link: "profile",
    },
    {
        title: "Notifications",
        color: "#FFD1DC",
        icon: <Notifications />,
        link: "notifications",
    },
    {
        title: "My Docs",
        color: "hsla(35, 93%, 73%, 1)",
        icon: <MemberIcon />,
        link: "#",
    },
    {
        title: "My Certificates",
        color: "#F5F54647",
        icon: <BankBuildingIcon />,
        link: "#",
    },
    { title: "My Chats", color: "#BEDDF0", icon: <ChatBubble />, link: "#" },
    { title: "Solve Puzzles", color: "#D2E8A8", icon: <Extension />, link: "#" },
    {
        title: "Play Online",
        color: "#9ED4CE",
        icon: <Gamepad />,
        link: "#",
    },
    {
        title: "Payments",
        color: "#F0D7D7",
        icon: <PaymentHistoryIcon />,
        link: "payments-history",
    },
    {
        title: "Game History",
        color: "#7868617D",
        icon: <TrainingIcon />,
        link: "history",
    },
    {
        title: "Tournament History",
        color: "#4641E557",
        icon: <PersonalTrainerIcon />,
        link: "tournament-history",
    },
];



const AdminDetails = () => {
    const { user } = useGlobalContext();

    // Define fetchPlayers function without useCallback to avoid dependency issues
    const fetchPlayersReport = async (type) => {
       
        // return;
        // setLoading(true);

        try {

            const response = await Client.get("/player/report", {
                params: { type: type },
                responseType: 'blob',
            });

          

            if (type !== 'pdf') {
                const url = window.URL.createObjectURL(new Blob([response.data]));
                
                const link = document.createElement('a');
                link.href = url;
                link.setAttribute('download', 'player_report.xlsx');
                document.body.appendChild(link);
                link.click();
                link.remove();
            } else {
                const blob = new Blob([response.data], { type: "application/pdf" });
                const url = window.URL.createObjectURL(blob);

                const a = document.createElement("a");
                a.href = url;
                a.download = "Player_Details.pdf";
                document.body.appendChild(a);
                a.click();
                a.remove();
                window.URL.revokeObjectURL(url);
            }


        } catch (error) {
            console.error("Download failed:", error);
        }
    };

    const fetchClubReport = async (type) => {
      
        // return;
        // setLoading(true);

        try {

            const response = await Client.get("/club/report", {
                params: { type: type },
                responseType: 'blob',
            });

           

            if (type !== 'pdf') {
                const url = window.URL.createObjectURL(new Blob([response.data]));
                
                const link = document.createElement('a');
                link.href = url;
                link.setAttribute('download', 'club_report.xlsx');
                document.body.appendChild(link);
                link.click();
                link.remove();
            } else {
                const blob = new Blob([response.data], { type: "application/pdf" });
                const url = window.URL.createObjectURL(blob);

                const a = document.createElement("a");
                a.href = url;
                a.download = "club_Details.pdf";
                document.body.appendChild(a);
                a.click();
                a.remove();
                window.URL.revokeObjectURL(url);
            }


        } catch (error) {
            console.error("Download failed:", error);
        }
    };

    const fetchArbiterReport = async (type) => {
       
        // return;
        // setLoading(true);

        try {

            const response = await Client.get("/arbiter/report", {
                params: { type: type },
                responseType: 'blob',
            });

          

            if (type !== 'pdf') {
                const url = window.URL.createObjectURL(new Blob([response.data]));
                
                const link = document.createElement('a');
                link.href = url;
                link.setAttribute('download', 'arbiter_report.xlsx');
                document.body.appendChild(link);
                link.click();
                link.remove();
            } else {
                const blob = new Blob([response.data], { type: "application/pdf" });
                const url = window.URL.createObjectURL(blob);

                const a = document.createElement("a");
                a.href = url;
                a.download = "arbiter_Details.pdf";
                document.body.appendChild(a);
                a.click();
                a.remove();
                window.URL.revokeObjectURL(url);
            }


        } catch (error) {
            console.error("Download failed:", error);
        }
    };

    return (
        <Box sx={{ px: "5vw", py: "5vh", maxWidth: "100%" }}>
            <Stack>
                <Typography variant="h4" fontWeight="bold" gutterBottom>
                    Welcome, {user?.name || ""}!
                </Typography>
                {user?.cbid && (
                    <Typography variant="h4" gutterBottom sx={{ mt: 2 }}>
                        CBID: {user?.cbid}
                    </Typography>
                )}
            </Stack>
            <Stack spacing={4}>

                <Box sx={{ display: 'flex', gap: '15px', alignItems: 'center' }} >
                     <Stack spacing={1}>
                    <Typography>Download Players Details</Typography>
                    <Button size="small" variant="contained" onClick={() => fetchPlayersReport('excel')}>Download Excel</Button>
                    </Stack>
                </Box>
                <Box sx={{ display: 'flex', gap: '15px', alignItems: 'center' }} >
                    <Stack spacing={1}>
                    <Typography>Download Club Details</Typography>
                    <Button size="small" variant="contained" onClick={() => fetchClubReport('excel')}>Download Excel</Button>
                    </Stack>
                </Box>
                <Box sx={{ display: 'flex', gap: '15px', alignItems: 'center' }} >
                    <Stack spacing={1}>
                        <Typography>Download Arbiter Details</Typography>
                        <Button  size="small" variant="contained" onClick={() => fetchArbiterReport('excel')}>Download Excel</Button>
                    </Stack>
                </Box>
            </Stack>
        </Box>
    );
};

export default AdminDetails;
