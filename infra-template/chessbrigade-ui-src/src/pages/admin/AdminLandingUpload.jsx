import {
  Box,
  Typography,
  Paper,
} from '@mui/material';
import {
  CloudUpload as UploadIcon,
} from '@mui/icons-material';
import BackButton from '../../components/common/BackButton';
import BannerUpload from '../../components/admin/BannerUpload';
import BannerDisplay from '../../components/admin/Bannerdisplay';

const AdminLandingUpload = () => {

  return (
    <Paper sx={{ p: 3, mb: 3, gap: '10px', display: 'flex', flexDirection: 'column' }} elevation={3} >
      <BackButton />
      <Typography variant="h5" mb={2} display="flex" alignItems="center">
        <UploadIcon sx={{ mr: 1 }} />
        Upload Banner & video for Landing Page
      </Typography>
      <Box
        sx={{
          border: '2px dashed',
          borderColor: 'grey.400',
          borderRadius: 2,
        }}
      >
        <BannerUpload />
      </Box>
      <>
        <BannerDisplay />
      </>
    </Paper>
  );
};

export default AdminLandingUpload