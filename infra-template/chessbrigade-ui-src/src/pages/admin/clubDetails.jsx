import React, { useEffect, useState } from "react";
import {
  Avatar,
  Box,
  Button,
  Container,
  Paper,
  Skeleton,
  Stack,
  Typography,
} from "@mui/material";

import AccountBalanceIcon from "@mui/icons-material/AccountBalance";
import { Client } from "../../api/client";
import { useNavigate, useParams } from "react-router-dom";
import UseToast from "../../lib/hooks/UseToast";
import EnquireButton from "../../components/common/EnquireButton";
import { DetailTable } from "../../components/common/DetailTable";
import UseGlobalContext from "../../lib/hooks/UseGlobalContext";
import ClubInvitationModal from "../../components/club/ClubInvitationModal";
import BackButton from "../../components/common/BackButton";
import CloseIcon from "@mui/icons-material/Close";
import CheckIcon from "@mui/icons-material/Check";
import EditIcon from "@mui/icons-material/Edit";
import BlockIcon from "@mui/icons-material/Block";
import GppBadIcon from "@mui/icons-material/GppBad"; // alternative to Shield
import GppGoodIcon from "@mui/icons-material/GppGood"; // alternative to ShieldOff
import DeleteIcon from "@mui/icons-material/Delete";
import SmsIcon from "@mui/icons-material/Sms";
import EmailIcon from "@mui/icons-material/Email";
import WhatsAppIcon from "@mui/icons-material/WhatsApp";
import AdminBanVerificationPopup from "../../components/admin/Banpopup";
import ConfirmationPopup from "../../components/admin/ConfirmationPopup";


const ClubsDetails = () => {
  const [clubInfo, setClubInfo] = useState(null);
  const [loading, setLoading] = useState(true);

  const [invitationModalOpen, setInvitationModalOpen] = useState(false);
  const toast = UseToast();
  const navigate = useNavigate();
  const { user } = UseGlobalContext();
  const { id: clubId } = useParams();
  const [isAccess, setIsAccess] = useState(true);
  const [isActive, setIsActive] = useState(true);
  const [open, setOpen] = useState(false);
  const [openPopup, setOpenPopup] = useState(false)

  // Fetch club information
  const fetchClubInfo = async () => {
    setLoading(true);

    try {
      const response = await Client.get(`/admin/details/single/club/${clubId}`);
      if (response.status === 204) {
        navigate("edit");
        return;
      }
      if (!response.data.success) {
        toast.error(response.data.message);

        return;
      }
      setClubInfo(response.data.data);
      setIsActive(response.data.data.IsActive);
      setIsAccess(response.data.data.isAccess)
      setLoading(false);
    } catch (error) {
      console.error("Error fetching club info:", error);

      setLoading(false);
    }
  };
  useEffect(() => {
    fetchClubInfo();
  }, [clubId]);

  // Format club data for display
  const formattedClubData = formatClubData(clubInfo);

  const fetchClubPlayersReport = async (type) => {
   
    // return;
    // setLoading(true);
    try {
      const response = await Client.get(`/report/club-players/${clubInfo.id}`, {
        params: { type: type },
        responseType: "blob",
      });

  
      const url = window.URL.createObjectURL(new Blob([response.data]));
      
      const link = document.createElement("a");
      link.href = url;
      link.setAttribute("download", "club_report.xlsx");
      document.body.appendChild(link);
      link.click();
      link.remove();
    } catch (error) {
      console.error("Download failed:", error);
    }
  };

  const handlePayout = () => {
    navigate("payout", { state: { clubId: clubInfo.id } });
  };

  const handleUnActive = async () => {
    try {
    
      // return
      const response = await Client.post("/admin/access/ban", {
        value: true,
        userId: clubInfo.id,
      });

      if (response.data.success) {
        toast.success("Club Unblocked successfully");
        setIsActive(response.data.data);
        fetchClubInfo();
      }
    } catch (error) {
      console.error("Error", error);
      toast.error("An error occurred while updating player block");
    }
  };

  const actionButtons = [
    // { label: 'Allow', action: 'Allow Access', color: 'success', icon: <CheckIcon fontSize="small" />,variant:isAccess ?'contained':'outlined' },
    // { label: 'Deny', action: 'Deny Access', color: 'error', icon: <CloseIcon fontSize="small" /> ,variant:isAccess ?'outlined':'contained'},
    {
      label: "Edit",
      action: "Edit",
      color: "primary",
      icon: <EditIcon fontSize="small" />,
      variant: "contained",
    },
    {
      label: "Block",
      action: "Block",
      color: "warning",
      icon: <GppBadIcon fontSize="small" />,
      variant: "contained",
      disable: isAccess ? false : true,
    },
    {
      label: "Unblock",
      action: "Unblock",
      color: "warning",
      icon: <GppGoodIcon fontSize="small" />,
      variant: "contained",
      disable: isAccess ? true : false,
    },
     { label: 'Archive', action: 'Archive', color: 'error', icon: <DeleteIcon fontSize="small" />,variant: 'contained', disable: isActive ? false : true},
    { label: 'UnArchive', action: 'UnArchive', color: 'error', icon: <DeleteIcon fontSize="small" />,variant: 'contained', disable: isActive ? true : false },
    {
      label: "Email",
      action: "Email",
      color: "info",
      icon: <EmailIcon fontSize="small" />,
      variant: "contained",
    },
    {
      label: "Payout",
      action: "Payout",
      color: "secondary",
      icon: <AccountBalanceIcon fontSize="small" />,
      variant: "contained",
    },
    {label:"Create Tournament",action:"Createtournament",color:"secondary",variant:"contained"},
    // { label: 'WhatsApp', action: 'WhatsApp', color: 'success', icon: <WhatsAppIcon fontSize="small" />,variant:'contained' },
    {label:"OnBoard Players",action:"OnBoardPlayers",color:"primary",variant:"contained"}
  ];

  const handleMail = () => {
    navigate(`/dashboard/email/compose`, {
      state: {
        selectedUsers: [clubInfo],
        selectedType: "club",
        mode: "mail",
      },
    });
  };
  const handleAction = (action) => {
    switch (action) {
      case "Allow Access":
        // TODO: Implement Allow logic
        break;
      case "Deny Access":
        // TODO: Implement Deny logic
        break;
      case "Edit":
        navigate(`/dashboard/clubs/${clubId}/edit`, {
          state: {
            clubId: clubId,
          },
        });
        break;
      case "Block":
        setOpenPopup(true)
        break;
      case "Unblock":
        setOpenPopup(true)
        break;
      case 'Archive':
        setOpen(true)
        break;
      case 'UnArchive':
        handleUnActive()
        break;
      case "SMS":
        // TODO: Send SMS logic
        break;
      case "Email":
        handleMail();
        break;
      case "WhatsApp":
        // TODO: WhatsApp integration logic
        break;
      case "Payout":
        handlePayout();
        break;
      case "Createtournament":
        navigate(`/dashboard/clubs/${clubId}/create-tournament`, {
          state: {
            clubId: clubInfo.userId,
            admin:true
          },
        });
        case "OnBoardPlayers":
        navigate(`/dashboard/clubs/${clubId}/onboard-players`, {
          state: {
            clubId: clubInfo.userId,
            admin:true
          },
        });
        break;
      default:
        console.warn("Unknown action:", action);
    }
  };

  return (
    <Container maxWidth="xl" sx={{ py: 4, pt: 2 }}>
      <BackButton />
      <Paper
        elevation={3}
        sx={{
          bgcolor: "background.default",
          minHeight: "80vh",
          borderRadius: 2,
          overflow: "hidden",
        }}
      >
        {/* Header */}
        {clubInfo && (
          <Box
            sx={{
              display: "flex",
              alignItems: { xs: "flex-start", sm: "center" },
              justifyContent: "space-between",
              flexDirection: { xs: "column", sm: "row" },
              p: 2,
              gap: 2,
              px: { xs: "2vw", sm: "10vw" },
              py: 2,
              borderBottom: "1px solid #f0f0f0",
            }}
          >
            <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
              <Avatar
                src={clubInfo.profileUrl}
                sx={{
                  width: {
                    xs: 80,
                    sm: 80,
                    md: 100,
                    lg: 100,
                    xl: 100,
                  },
                  height: {
                    xs: 80,
                    sm: 80,
                    md: 100,
                    lg: 100,
                    xl: 100,
                  },
                  bgcolor: "#f5f5f5",
                  color: "#000",
                  border: "1px solid #********",
                }}
              >
                {!clubInfo.profileUrl && (
                  <AccountBalanceIcon sx={{ fontSize: { xs: 50, md: 60 } }} />
                )}
              </Avatar>
              <Stack>
                {!loading ? (
                  <>
                    <Typography
                      variant="h4"
                      component="h4"
                      fontWeight="500"
                      sx={{ fontSize: { xs: "20px", sm: "20px", md: "24px" } }}
                    >
                      {formattedClubData.title}
                    </Typography>
                  </>
                ) : (
                  <Skeleton variant="text" width={200} />
                )}
              </Stack>
            </Box>
            <Box>
              {user?.role === "player" && (
                <Button
                  size={"small"}
                  variant="contained"
                  sx={{
                    borderRadius: "4px",
                    textTransform: "none",
                    fontWeight: "bold",
                    mx: 1,
                    bgcolor: "#2C832C",
                    color: "#fff",
                    px: 4,
                    "&:hover": {
                      bgcolor: "#236723",
                    },
                  }}
                  onClick={() => setInvitationModalOpen(true)}
                >
                  join Club
                </Button>
              )}

              <Button
                size="small"
                variant="contained"
                onClick={() => fetchClubPlayersReport("excel")}
              >
                {" "}
                Download Club players Report
              </Button>
            </Box>
          </Box>
        )}

        {/* Club Information */}
        <Box>
          {loading ? (
            // Loading skeleton
            Array(6)
              .fill(0)
              .map((_, index) => (
                <Box key={index} sx={{ mb: 2 }}>
                  <Skeleton variant="rectangular" height={40} />
                </Box>
              ))
          ) : !clubInfo ? (
            // Error message
            <Typography
              color="error"
              variant="h6"
              align="center"
              sx={{ py: 10 }}
            >
              No club Found
            </Typography>
          ) : (
            // Club details table
            <DetailTable details={formattedClubData.details} />
          )}
        </Box>
      </Paper>
      {/* Club Invitation Modal */}
      <ClubInvitationModal
        open={invitationModalOpen}
        onClose={() => setInvitationModalOpen(false)}
        clubData={clubInfo}
        mode="join-request"
      />

      <Stack
        mt={5}
        sx={{ alignItems: "center", display: "flex", justifyContent: "center" }}
        direction="row"
        spacing={2}
        flexWrap="wrap"
      >
        {actionButtons.map(
          ({ label, action, color, icon, variant, disable }) => (
            <Button
              key={label}
              variant={variant}
              disabled={disable}
              color={color}
              size="small"
              onClick={() => handleAction(action)}
              startIcon={icon}
              sx={{
                textTransform: "none",
                fontWeight: 500,
                mb: "15px !important",
              }}
            >
              {label}
            </Button>
          )
        )}
      </Stack>
      {open && (
        <AdminBanVerificationPopup
          open={open}
          onClose={() => setOpen(false)}
          User={clubInfo}
          isActive={isActive}
          onSuccess={fetchClubInfo}
        />
      )}
      {openPopup &&(
      <ConfirmationPopup
        open={openPopup}
        onClose={() => setOpenPopup(false)}
        title="Do you really want to proceed?"
        apiEndpoint="/admin/access/archive"
        onSuccess={()=>fetchClubInfo()}
        value={isAccess}
        Info={clubInfo}
      />)}
    </Container>
  );
};

export default ClubsDetails;

function formatClubData(club) {
  if (!club) {
    return {
      title: "",
      details: [],
    };
  }

  // Create a list of details to display
  const details = [
    {
      label: "Club Name",
      value: club.clubName || "-",
    },
    {
      label: "Country",
      value: club.country || "-",
    },
    {
      label: "State",
      value: club.state || "-",
    },
    {
      label: "District",
      value: club.district || "-",
    },
    {
      label: "City",
      value: club.city || "-",
    },
    {
      label: "Address",
      value: club.address || "-",
    },
    {
      label: "Contact Person",
      value: club.contactPersonName || "-",
    },
    {
      label: "Contact Number",
      value: club.contactPersonNumber || "-",
    },
    {
      label: "Alternate Contact",
      value: club.alternateContactNumber || "-",
    },
    {
      label: "Contact Email",
      value: club.contactPersonEmail || "-",
    },
  ];

  // Add location link if available
  if (club.locationUrl) {
    details.push({
      label: "Location",
      value: "View Map",
      isLink: true,
      url: club.locationUrl,
      // Add target and rel attributes for security and new tab opening
      linkProps: {
        target: "_blank",
        rel: "noopener noreferrer",
      },
    });
  }
  return {
    title: club.clubName || "",
    details: details,
  };
}
