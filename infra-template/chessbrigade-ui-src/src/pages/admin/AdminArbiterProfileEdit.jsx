import React from "react";

import BackButton from "../../components/common/BackButton";

import { useEffect, useState } from "react";
import {
  Button,
  Typography,
  Grid,
  Box,
  Avatar,
  TextField,
  Container,
  Dialog,
  DialogTitle,
  DialogContentText,
  DialogActions,
  DialogContent,
} from "@mui/material";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { Client } from "../../api/client";
import UseToast from "../../lib/hooks/UseToast";
import { useNavigate, useParams } from "react-router-dom";
import FormTextField from "../../components/form/FormTextField";
import FormPhoneInput from "../../components/form/FormPhoneInput";
import FormAutocomplete from "../../components/form/FormAutocomplete";
import FormSelectField from "../../components/form/FormSelectField";
import UseGlobalContext from "../../lib/hooks/UseGlobalContext";

import { adminAribterProfileEdit } from "../../schema/zod";

import ProfileImageUpload from "../../components/common/ProfileImageUpload";

const AdminArbiterProfileEdit = () => {
  const {
    control,
    handleSubmit,
    formState: { isValid },
    setValue,
    watch,
    getValues,
    reset,
  } = useForm({
    resolver: zodResolver(adminAribterProfileEdit),
    defaultValues: {
      title: "",
      firstName: "",
      lastName: "",
      alternateContact: "",

      aicfId: "",
      // officialId: "",
      phoneNumber: "",
      club: "",
      state: "",
      city: "",
      otp: "",
      pincode: "",
      fideId: "",
      stateId: "",
      country: "",
      district: "",
      countryCode: "",
      stateCode: "",
    },
    reValidateMode: "onChange",
  });

  const [countries, setCountries] = useState([]);
  const [states, setStates] = useState([]);
  const [cities, setCities] = useState([]);
  const [districts, setDistricts] = useState([]);

  const [originalProfileData, setOriginalProfileData] = useState({});
  const toast = UseToast();
  const [showSuccessDialog, setShowSuccessDialog] = useState(false);


  const navigate = useNavigate();
  // const { geoInfo } = useUserGeoInfo();
  const { id } = useParams();

  // Load countries on component mount
  useEffect(() => {
    const fetchCountries = async () => {
      try {
        const response = await Client.get("/location/countries");
        if (!response.data.success) {
          console.error("Failed to fetch countries");
          return;
        }
        setCountries(response.data.data);
      } catch (error) {
        console.error("Error fetching countries:", error);
      }
    };

    fetchCountries();
  }, []);

  // Load states when country changes
  useEffect(() => {
    // Get current country value
    const country = getValues("country");

    if (country) {
      const countryObj = countries.find((c) => c.name === country);
      if (countryObj) {
        const fetchStates = async () => {
          try {
            const response = await Client.get(
              `/location/states/${countryObj.isoCode}`,
              {
                params: { country: countryObj.isoCode },
              }
            );
            if (!response.data.success) {
              toast.error("Failed to load states");
              return;
            }
            setStates(response.data.data);

            if (!getValues("state")) {
              setValue("state", "");
              setValue("stateCode", "");
              setValue("district", "");
              setValue("city", "");
            }
          } catch (error) {
            console.error("Error fetching states:", error);
            toast.error("Failed to load states. Please try again.");
          }
        };
        fetchStates();
      }
    } else {
      setStates([]);
      // Always reset when country is cleared
      setValue("state", "");
      setValue("stateCode", "");
      setValue("district", "");
      setValue("city", "");
    }
  }, [countries, setValue, getValues("country")]);

  useEffect(() => {
    if (getValues("country") === "India" && getValues("state")) {
      const countryObj = countries.find((c) => c.name === getValues("country"));
      const stateObj = states.find((s) => s.name === getValues("state"));
      if (countryObj && stateObj) {
        const fetchDistricts = async () => {
          const response = await Client.get(
            `/location/districts/${stateObj.isoCode}`,
            {
              params: { country: countryObj.isoCode, state: stateObj.isoCode },
            }
          );
          if (!response.data.success) return;
          setDistricts(response.data.data);
          setValue("countryCode", countryObj.isoCode);
          setValue("stateCode", stateObj.isoCode);
          if (!getValues("district")) {
            setValue("district", "");
          }
        };
        fetchDistricts();
      }
    } else {
      setDistricts([]);
    }
  }, [getValues("state"), countries, states, setValue]);

  // Load cities when state changes
  useEffect(() => {
    // Get current country and state values
    const country = getValues("country");
    const state = getValues("state");
    const countryCode = getValues("countryCode");
    const stateCode = getValues("stateCode");
    console.log("details", country, state, countryCode, stateCode);

    if (country && state && countryCode && stateCode) {
      const fetchCities = async () => {
        try {
          const response = await Client.get(
            `/location/cities/${countryCode}/${stateCode}`,
            {
              params: { country: countryCode, state: stateCode },
            }
          );
          if (!response.data.success) {
            toast.error("Failed to load cities");
            return;
          }
          setCities(response.data.data);

          if (!getValues("city")) {
            setValue("city", "");
          }
        } catch (error) {
          console.error("Error fetching cities:", error);
          toast.error("Failed to load cities. Please try again.");
        }
      };
      fetchCities();
    } else {
      setCities([]);
      // Reset city only if state is cleared
    }
  }, [getValues("state"), districts]);

  useEffect(() => {
    const fetchProfileData = async () => {
      try {
        // setSubmitting(true);
        const response = await Client.get(
          `/admin/details/single/arbiter/${id}`
        );
        if (response.status === 204) {
          toast.info("You haven't created a profile yet.");
          return;
        }
        if (!response.data.success) {
          toast.info(response.data.message);
          return;
        }
        const ArbiterDetails = response.data.data;
        const [firstName, lastName] = ArbiterDetails.name.split(" ");

        // Process arbiter details to handle null values
        const processedDetails = {
          ...ArbiterDetails,
          // Convert null values to empty strings or appropriate defaults
          title: ArbiterDetails.ArbiterDetail.title || "",
          firstName: firstName || "",
          lastName: lastName || "",
          phoneNumber: ArbiterDetails.phoneNumber || "",
          alternateContact: ArbiterDetails.ArbiterDetail.alternateContact || "",

          aicfId: ArbiterDetails.ArbiterDetail.aicfId || "",
          email: ArbiterDetails.email || "",
          officialId: ArbiterDetails.ArbiterDetail.officialId || "",
          club: ArbiterDetails.ArbiterDetail.club || "",
          state: ArbiterDetails.ArbiterDetail.state || "",
          city: ArbiterDetails.ArbiterDetail.city || "",
          pincode: ArbiterDetails.ArbiterDetail.pincode || "",

          fideId: ArbiterDetails.ArbiterDetail.fideId || "",
          stateId: ArbiterDetails.ArbiterDetail.stateId || "",
          country: ArbiterDetails.ArbiterDetail.country || "",
          district: ArbiterDetails.ArbiterDetail.district || "",
          id: ArbiterDetails.id || "",
        };

        // Reset with the processed details
        reset(processedDetails);
        setOriginalProfileData((prev) => ({ ...prev, ...processedDetails }));

        // Then find and set the country code
        if (ArbiterDetails.country && countries.length > 0) {
          const countryObj = countries.find(
            (c) => c.name === ArbiterDetails.country
          );
          if (countryObj) {
            setValue("countryCode", countryObj.isoCode);

            // Fetch states for this country
            const statesResponse = await Client.get(
              `/location/states/${countryObj.isoCode}`,
              { params: { country: countryObj.isoCode } }
            );

            if (statesResponse.data.success) {
              const statesData = statesResponse.data.data;
              setStates(statesData);

              // Find and set the state code
              if (ArbiterDetails.state) {
                const stateObj = statesData.find(
                  (s) => s.name === ArbiterDetails.state
                );
                if (stateObj) {
                  setValue("stateCode", stateObj.isoCode);

                  // Fetch cities for this state
                  const citiesResponse = await Client.get(
                    `/location/cities/${countryObj.isoCode}/${stateObj.isoCode}`,
                    {
                      params: {
                        country: countryObj.isoCode,
                        state: stateObj.isoCode,
                      },
                    }
                  );

                  if (citiesResponse.data.success) {
                    setCities(citiesResponse.data.data);
                  }
                }
              }
            }
          }
        }
      } catch (error) {
        console.error("Error fetching arbiter profile:", error);
        let errorMessage = "Error fetching arbiter data";
        if (error.response) {
          errorMessage = error.response.data?.message || "Server error";
        } else if (error.request) {
          errorMessage = "No response from server";
        } else {
          errorMessage = error.message;
        }
        toast.error("Failed to fetch arbiter profile: " + errorMessage);
      } finally {
        // setSubmitting(false);
      }
    };

    fetchProfileData();
  }, [reset, countries, setValue]);
  console.log("values", getValues());

  // Helper to scroll to error field and show error toast
  const onError = (errors) => {
    const firstField = Object.keys(errors)[0];
    const errorData = errors[firstField];

    const sectionElement = document.querySelector(`[name="${firstField}"]`);

    if (sectionElement) {
      const scrollOffset = 100;
      const y =
        sectionElement.getBoundingClientRect().top +
        window.pageYOffset -
        scrollOffset;
      window.scrollTo({ top: y, behavior: "smooth" });

      setTimeout(() => {
        if ("focus" in sectionElement) {
          sectionElement.focus();
        }
      }, 500);
    }

    toast.info(
      `Error in field: ${firstField} – ${errorData?.message || "Invalid input"}`
    );
  };

  // Form submission handler
  const onSubmit = async (data) => {
    // Save form data to localStorage as backup
    const dataForStorage = { ...data };
    delete dataForStorage.profileImage;
    delete dataForStorage.profileImagePreview; // Don't store preview in localStorage
    localStorage.setItem("arbiterProfileDraft", JSON.stringify(dataForStorage));

    toast.info(`Processing your profile update...`);

    try {
      const changedData = {};

      // Check each field and only include ones that have changed from original
      Object.keys(data).forEach((key) => {
        // Skip otp, phoneChanged, and preview fields from comparison
        if (["profileImagePreview"].includes(key)) return;

        // Special handling for file fields
        if (key === "profileImage") {
          if (data.profileImage && data.profileImage instanceof File) {
            changedData.profileImage = data.profileImage;
          }
          return;
        }

        // Compare with our stored original data
        if (
          JSON.stringify(data[key]) !== JSON.stringify(originalProfileData[key])
        ) {
          changedData[key] = data[key];
        }
      });

      if (
        data.firstName !== originalProfileData.firstName ||
        data.lastName !== originalProfileData.lastName
      ) {
        changedData.name = `${data.firstName} ${data.lastName}`;
      }

      // Only proceed with the API call if we have changes
      if (Object.keys(changedData).length === 0 && !changedData.profileImage) {
        toast.info("No changes detected to update.");
        return;
      }

      // Create FormData to handle file upload
      const formData = new FormData();

      // Add all changed fields to FormData
      Object.keys(changedData).forEach((key) => {
        const value = changedData[key];

        if (key === "profileImage" && value instanceof File) {
          formData.append("profileImage", value);
        } else if (typeof value === "object" && value !== null) {
          // Only stringify if it's an object (not null)
          formData.append(key, JSON.stringify(value));
        } else {
          // No quotes around strings, booleans, or numbers
          formData.append(key, String(value));
        }
      });

      // Log what's being sent (but not the actual file contents)
      const response = await Client.put(
        `admin/details/update/arbiter/${originalProfileData.id}`,
        formData,
        {
          headers: {
            "Content-Type": "multipart/form-data",
          },
        }
      );
      handleResponse(response);
    } catch (error) {
      handleError(error);
    } finally {
      // setSubmitting(false);
    }
  };
  const handleResponse = (response) => {
    if (!response.data.success) {
      if (response.data.zodErrors) {
        // Show the first Zod validation error
        const zodErrors = response.data.zodErrors;
        const firstZodError = zodErrors[Object.keys(zodErrors)[0]];
        toast.error(firstZodError || "Please correct the validation errors");
      } else {
        toast.error(response.data.error || "Unexpected error");
      }
      return;
    }

    toast.success(`Profile updated successfully`);

    // Clear draft from localStorage after successful submission
    localStorage.removeItem("arbiterProfileDraft");
    sessionStorage.removeItem("new_user");

    // Reset the form
    reset();

    setShowSuccessDialog(true);
  };

  // Handle dialog actions
  const handleEditAgain = () => {
    setShowSuccessDialog(false);
    navigate(`/dashboard/arbiters/${id}`);
  };

  const handleGoToDashboard = () => {
    setShowSuccessDialog(false);
    navigate("/dashboard");
  };

  const handleGoToProfile = () => {
    setShowSuccessDialog(false);
    navigate("/dashboard/profile");
  };

  // Helper function to handle errors
  const handleError = (error) => {
    console.error(`Error updating profile:`, error);

    if (error.response?.data?.error?.message === "Incorrect OTP.") {
      toast.error("Incorrect OTP. Please try again.");
    } else if (error.response?.status === 422 && error.response?.data?.data) {
      const validationErrors = error.response.data.data;
      Object.keys(validationErrors).forEach((key) => {
        if (key !== "_errors" && validationErrors[key]._errors) {
          toast.error(
            `Validation failed - ${key}: ${validationErrors[key]._errors.join(
              ", "
            )}`
          );
        }
      });
    } else if (error.response?.data?.error) {
      toast.error(error.response.data.error.message || "An error occurred");
    } else if (error.response?.data?.zodErrors) {
      // Handle Zod validation errors from the server
      const zodErrors = error.response.data.zodErrors;
      const firstZodError = zodErrors[Object.keys(zodErrors)[0]];
      toast.error(firstZodError || "Please correct the validation errors");
    } else {
      const errorMessage =
        error.response?.data?.message ||
        `An unexpected error occurred while updating the profile.`;
      toast.error(errorMessage);
    }
  };

  return (
    <Container maxWidth="xl" sx={{ py: 4, pt: 2 }}>
      <BackButton to={"/dashboard"} />
      <Box maxWidth="lg" sx={{ margin: "auto", my: 2 }}>
        <Typography variant="h4" sx={{ mb: 6 }}>
          My Profile
        </Typography>

        <form onSubmit={handleSubmit(onSubmit, onError)}>
          <Grid
            container
            spacing={3}
            sx={{
              ".MuiFormControl-root": { mt: "4px !important" },
              ".MuiInputBase-input": { py: 1.5 },
              ".MuiAutocomplete-input": { p: "4px !important" },
              ".MuiGrid-root.MuiGrid-item": { pt: "0px !important" },
              position: "relative",
            }}
          >
            {/* CBID and Profile Image Row */}
            <Grid container item xs={12} md={12} sx={{ mb: 2 }}>
              <Grid item xs={6} md={6}>
                <Typography variant="h6" sx={{ textAlign: "start" }}>
                  CBID: {id || ""}
                </Typography>
                {/* <Grid item xs={12} md={6}>
              <FormTextField
                name="officialId"
                control={control}
                title="Official ID"
                placeholder="Enter Official ID"
                specialCharAllowed={true}
              />
            </Grid> */}
                <Grid item xs={12} md={6}>
                  <FormSelectField
                    name="title"
                    control={control}
                    options={[
                      { value: "Untitled", label: "Untitled" },
                      { value: "NA", label: "National Arbiter" },
                      { value: "SNA", label: "Senior National Arbiter" },
                      { value: "FA", label: "Fide Arbiter" },
                      { value: "IA", label: "International Arbiter" },
                    ]}
                    title="Arbiter Title"
                    placeholder="Select Title"
                  />
                </Grid>
              </Grid>

              <Grid
                item
                xs={6}
                md={6}
                sx={{ display: "flex", justifyContent: "end" }}
              >
                <ProfileImageUpload
                  control={control}
                  setValue={setValue}
                  watch={watch}
                />
              </Grid>
            </Grid>

            {/* Basic Details Fields */}
            <Grid item xs={12} md={6} sx={{ display: "flex", gap: 2 }}>
              <Grid item xs={6}>
                <FormTextField
                  name="firstName"
                  control={control}
                  title="First Name"
                  placeholder="Enter First Name"
                />
              </Grid>
              <Grid item xs={6}>
                <FormTextField
                  name="lastName"
                  control={control}
                  title="Last Name"
                  placeholder="Enter Last Name"
                />
              </Grid>
            </Grid>

            <Grid item xs={12} md={6}>
              <Box sx={{ display: "flex", gap: 2, alignItems: "center" }}>
                <FormPhoneInput
                  name="phoneNumber"
                  control={control}
                  placeholder="Enter Mobile Number"
                  title="Phone Number"
                  rules={{
                    pattern: {
                      value: /^91[0-9]{10}$/,
                      message: "Please enter a valid 10-digit phone number",
                    },
                  }}
                />
              </Box>
            </Grid>

            <Grid item xs={12} md={6}>
              <Typography
                variant="h6"
                sx={{
                  textAlign: "start",
                  p: "0px !important",
                  m: "0px !important",
                }}
              >
                Email ID
              </Typography>

              <FormTextField
                name="email"
                control={control}
                fullWidth
                variant="outlined"
                margin="normal"
                specialCharAllowed={true}
                placeholder={"Enter email"}
                sx={{ minHeight: 70 }}
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <FormTextField
                name="fideId"
                control={control}
                title="FIDE ID"
                placeholder="Enter FIDE ID"
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <FormTextField
                name="aicfId"
                control={control}
                title="AICF ID"
                placeholder="Enter AICF ID"
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <FormPhoneInput
                name="alternateContact"
                control={control}
                title="Alternate Contact"
                placeholder="Enter Alternate Contact"
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <FormAutocomplete
                name="country"
                control={control}
                options={countries}
                sx={{ "& .MuiInputBase-root": { p: "4px 10px !important " } }}
                getOptionLabel={(option) => option.name}
                title="Country"
                placeholder="Select Country"
                onChange={(_, newValue) => {
                  if (newValue) {
                    setValue("country", newValue.name);
                    setValue("countryCode", newValue.isoCode);
                    setValue("state", "");
                    setValue("stateCode", "");
                    setValue("city", "");
                  }
                }}
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <FormAutocomplete
                name="state"
                control={control}
                options={states}
                sx={{ "& .MuiInputBase-root": { p: "4px 10px !important " } }}
                getOptionLabel={(option) => option.name}
                title="State"
                placeholder="Select State"
                onChange={(_, newValue) => {
                  if (newValue) {
                    setValue("state", newValue.name);
                    setValue("stateCode", newValue.isoCode);
                    setValue("city", "");
                  }
                }}
              />
            </Grid>

            {getValues("country") === "India" && (
              <Grid item xs={12} md={6}>
                <FormAutocomplete
                  name="district"
                  control={control}
                  sx={{ "& .MuiInputBase-root": { p: "4px 10px !important " } }}
                  title="District"
                  placeholder="Select District"
                  options={districts}
                  getOptionLabel={(option) => option.name}
                  onChange={(_, newValue) => {
                    if (newValue) {
                      setValue("district", newValue.name);
                    }
                  }}
                />
              </Grid>
            )}
            {(getValues("country") !== "India" ||
              getValues("country") === "") && (
                <Grid item xs={12} md={6}>
                  <FormTextField
                    name="district"
                    control={control}
                    title="District"
                    placeholder="Enter District"
                  />
                </Grid>
              )}

            <Grid item xs={12} md={6}>
              <FormAutocomplete
                name="city"
                control={control}
                sx={{ "& .MuiInputBase-root": { p: "4px 10px !important " } }}
                options={cities}
                getOptionLabel={(option) => option.name}
                title="City"
                placeholder="Select City"
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <FormTextField
                name="pincode"
                control={control}
                title="Pincode"
                placeholder="Enter Pincode"
                maxLength={6}
                inputProps={{
                  inputMode: "numeric",
                  pattern: "[0-9]*",
                }}
              />
            </Grid>

            {/* Submit Buttons */}
            <Grid item xs={12}>
              <Box
                sx={{
                  display: "flex",
                  justifyContent: "center",
                  gap: 2,
                  mt: 6,
                }}
              >
                <Button
                  type="submit"
                  variant="contained"
                  sx={{
                    fontSize: 16,
                    px: 2,
                    bgcolor: isValid
                      ? "hsla(120, 49%, 35%, 1)"
                      : "hsla(0, 3%, 80%, 1)",
                    "&:hover": {
                      bgcolor: "rgb(39, 104, 39)",
                    },
                  }}
                  size="large"
                >
                  Update
                </Button>
              </Box>
            </Grid>
            <Dialog
              open={showSuccessDialog}
              onClose={() => setShowSuccessDialog(false)}
              maxWidth="sm"
              fullWidth
            >
              <DialogTitle sx={{ textAlign: "center", fontWeight: "bold", color: "#4caf50" }}>
                Profile Updated Successfully!
              </DialogTitle>
              <DialogContent>
                <DialogContentText sx={{ textAlign: "center", fontSize: "16px", color: "#333" }}>
                  Your arbiter profile has been updated successfully. What would you like to do next?
                </DialogContentText>
              </DialogContent>
              <DialogActions sx={{ justifyContent: "center", gap: 2, pb: 3 }}>
                <Button
                  onClick={handleEditAgain}
                  variant="outlined"
                  color="primary"
                  sx={{
                    px: 3,
                    py: 1,
                    fontSize: "16px",
                    textTransform: "none",
                    borderRadius: 2,
                  }}
                >
                  Edit Again
                </Button>
                <Button
                  onClick={handleGoToDashboard}
                  variant="contained"
                  color="primary"
                  sx={{
                    px: 3,
                    py: 1,
                    fontSize: "16px",
                    textTransform: "none",
                    borderRadius: 2,
                    bgcolor: "#4caf50",
                    "&:hover": {
                      bgcolor: "#45a049",
                    },
                  }}
                >
                  Go to Dashboard
                </Button>
                <Button
                  variant="contained"
                  onClick={handleGoToProfile}
                  sx={{
                    minWidth: 120,
                    bgcolor: "#1976d2",
                    "&:hover": {
                      bgcolor: "#1565c0",
                    },
                  }}
                >
                  Go to Profile
                </Button>
              </DialogActions>
            </Dialog>
          </Grid>

        </form>
      </Box>
    </Container>
  );
};

export default AdminArbiterProfileEdit;
