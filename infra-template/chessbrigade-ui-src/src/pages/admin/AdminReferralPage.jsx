import React, { useState } from 'react';
import {
  Box,
  Container,
  Paper,
  Tabs,
  Tab,
  Typography,
  useTheme,
} from '@mui/material';
import { styled } from '@mui/material/styles';
import GroupIcon from '@mui/icons-material/Group';
import EmojiEventsIcon from '@mui/icons-material/EmojiEvents';

import TournamentReferralDetails from '../../components/admin/TournamentReferralDetails';
import BackButton from '../../components/common/BackButton';
import PlayerReferralDetails from '../../components/admin/PlayerReferralDetails';

// Custom styled tabs for better appearance
const StyledTabs = styled(Tabs)(({ theme }) => ({
  backgroundColor: '#ffffff',
  borderRadius: '12px 12px 0 0',
  minHeight: '64px',
  boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
  '& .MuiTabs-indicator': {
    height: '4px',
    borderRadius: '2px 2px 0 0',
    background: 'linear-gradient(45deg, #3f51b5 30%, #5c6bc0 90%)',
  },
  '& .MuiTabs-flexContainer': {
    height: '64px',
  },
}));

const StyledTab = styled(Tab)(({ theme }) => ({
  textTransform: 'none',
  fontWeight: 600,
  fontSize: '16px',
  minHeight: '64px',
  color: '#666',
  transition: 'all 0.3s ease',
  '&:hover': {
    color: '#3f51b5',
    backgroundColor: 'rgba(63, 81, 181, 0.04)',
  },
  '&.Mui-selected': {
    color: '#3f51b5',
    fontWeight: 700,
  },
  '& .MuiTab-iconWrapper': {
    marginBottom: '4px',
  },
}));

const TabPanel = styled(Box)(({ theme }) => ({
  backgroundColor: '#ffffff',
  borderRadius: '0 0 12px 12px',
  boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
  minHeight: '500px',
}));

const AdminReferralPage = () => {
  const [tabIndex, setTabIndex] = useState(0);
  const theme = useTheme();

  const handleTabChange = (event, newValue) => {
    setTabIndex(newValue);
  };

  return (
    <Container maxWidth="xl" sx={{ py: 4, pt: 2, minHeight: "100vh" }}>
      <BackButton />
      
      <Box sx={{ mb: 3 }}>
        <Typography 
          variant="h4" 
          sx={{ 
            mb: 1, 
            fontWeight: 'bold', 
            color: '#2c3e50',
            background: 'linear-gradient(45deg, #3f51b5 30%, #5c6bc0 90%)',
            backgroundClip: 'text',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
          }}
        >
          Referral Dashboard
        </Typography>
        <Typography 
          variant="subtitle1" 
          sx={{ 
            color: '#666', 
            mb: 3,
            fontWeight: 400
          }}
        >
          Manage and track referral activities across players and tournaments
        </Typography>
      </Box>

      <Paper 
        elevation={0} 
        sx={{ 
          bgcolor: 'transparent',
          overflow: 'hidden'
        }}
      >
        <StyledTabs
          value={tabIndex}
          onChange={handleTabChange}
          variant="fullWidth"
          aria-label="referral dashboard tabs"
        >
          <StyledTab 
            icon={<GroupIcon />}
            label="Player Referrals" 
            iconPosition="start"
            sx={{
              '&.Mui-selected': {
                background: 'linear-gradient(45deg, rgba(63, 81, 181, 0.08) 30%, rgba(92, 107, 192, 0.08) 90%)',
              }
            }}
          />
          <StyledTab 
            icon={<EmojiEventsIcon />}
            label="Tournament Referrals" 
            iconPosition="start"
            sx={{
              '&.Mui-selected': {
                background: 'linear-gradient(45deg, rgba(63, 81, 181, 0.08) 30%, rgba(92, 107, 192, 0.08) 90%)',
              }
            }}
          />
        </StyledTabs>

        <TabPanel>
          {tabIndex === 0 && (
            <Box sx={{ p: 0 }}>
              <PlayerReferralDetails />
            </Box>
          )}
          {tabIndex === 1 && (
            <Box sx={{ p: 0 }}>
              <TournamentReferralDetails />
            </Box>
          )}
        </TabPanel>
      </Paper>
    </Container>
  );
};

export default AdminReferralPage;