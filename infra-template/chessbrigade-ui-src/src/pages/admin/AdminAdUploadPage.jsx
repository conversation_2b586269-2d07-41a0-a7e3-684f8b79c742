import {
  Box,
  Typography,
  Paper,
} from '@mui/material';
import {
  CloudUpload as UploadIcon,
} from '@mui/icons-material';
import UploadComponent from '../../components/admin/UploadComponent';
import AddDisplay from '../../components/admin/AddDisplay';
import BackButton from '../../components/common/BackButton';

const AdminAdUploadPage = () => {

  return (
    <Paper sx={{ p: 3, mb: 3, gap: '10px', display: 'flex', flexDirection: 'column' }} elevation={3} >
      <BackButton />
      <Typography variant="h5" mb={2} display="flex" alignItems="center">
        <UploadIcon sx={{ mr: 1 }} />
        Upload Advertisement
      </Typography>
      <Box
        sx={{
          border: '2px dashed',
          borderColor: 'grey.400',
          borderRadius: 2,
        }}
      >
        <UploadComponent />
      </Box>
      <>
        <AddDisplay />
      </>
    </Paper>
  );
};

export default AdminAdUploadPage