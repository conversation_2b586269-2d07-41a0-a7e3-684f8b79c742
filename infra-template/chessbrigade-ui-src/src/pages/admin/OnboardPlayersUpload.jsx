import { useState } from 'react';
import {
  Container,
  Paper,
  Typography,
  Box,
  Button,
  Alert,
  LinearProgress,
  Chip,
} from '@mui/material';
import {
  CloudUpload,
  Download,
  Description,
  Group,
} from '@mui/icons-material';
import { useParams, useNavigate } from 'react-router-dom';
import UseToast from "../../lib/hooks/UseToast";
import BackButton from '../../components/common/BackButton';
import { Client } from '../../api/client';

const OnboardPlayersUpload = () => {
  const { clubId } = useParams();
  const navigate = useNavigate();
  
  const [selectedFile, setSelectedFile] = useState(null);
  const [uploading, setUploading] = useState(false);
  const [uploadResult, setUploadResult] = useState(null);
  const [dragActive, setDragActive] = useState(false);
  const toast = UseToast();

  // Handle file selection
  const handleFileSelect = (event) => {
    const file = event.target.files[0];
    validateAndSetFile(file);
  };

  // Handle drag and drop
  const handleDrag = (e) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  };

  const handleDrop = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      validateAndSetFile(e.dataTransfer.files[0]);
    }
  };

  // Validate file type and size
  const validateAndSetFile = (file) => {
    if (!file) return;

    // Check file type
    const allowedTypes = [
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
      'application/vnd.ms-excel', // .xls
      'text/csv' // .csv
    ];

    if (!allowedTypes.includes(file.type)) {
      toast.error('Please select a valid Excel file (.xlsx, .xls) or CSV file');
      return;
    }

    // Check file size (max 10MB)
    if (file.size > 10 * 1024 * 1024) {
      toast.error('File size should be less than 10MB');
      return;
    }

    setSelectedFile(file);
    setUploadResult(null);
  };

  // Upload file to backend
  const handleUpload = async () => {
    if (!selectedFile) {
      toast.error('Please select a file first');
      return;
    }

    setUploading(true);
    setUploadResult(null);

    try {
      const formData = new FormData();
      formData.append('file', selectedFile);
      formData.append('clubId', clubId);

      const response = await Client.post('/admin/clubs/onboard-players/upload', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      if (response.data.success) {
        setUploadResult({
          success: true,
          message: response.data.message,
          data: response.data.data,
        });
        toast.success('Players onboarded successfully!');
      } else {
        setUploadResult({
          success: false,
          message: response.data.message || 'Upload failed',
          errors: response.data.errors || [],
        });
        toast.error('Upload failed. Please check the file format.');
      }
    } catch (error) {
      console.error('Upload error:', error);
      setUploadResult({
        success: false,
        message: error.response?.data?.message || 'Upload failed. Please try again.',
        errors: error.response?.data?.errors || [],
      });
      toast.error('Upload failed. Please try again.');
    } finally {
      setUploading(false);
    }
  };

  // Download sample template
  const downloadTemplate = () => {
    // Create a sample CSV template
    const csvContent = `Player Name,Email,Phone Number,Date of Birth,Gender,City,State,District,Country,Pincode
John Doe,<EMAIL>,+91-9876543210,,,,,,,
Jane Smith,<EMAIL>,+91-9876543212,,,,,,,
Alex Johnson,<EMAIL>,+91-9876543214,,,,,,,`;

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'player_onboarding_template.csv';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
    
    toast.success('Template downloaded successfully!');
  };

  return (
    <Container maxWidth="lg" sx={{ py: 3 }}>
      <BackButton />

      <Typography variant="h4" gutterBottom sx={{ mb: 3, fontWeight: 'bold' }}>
        Onboard Players - Excel Upload
      </Typography>

      {/* Instruction */}
      <Box sx={{
        mb: 2,
        p: 1.5,
        backgroundColor: 'white',
        border: '1px solid #ddd',
        borderRadius: 1,
        boxShadow: '0 1px 3px rgba(0,0,0,0.1)'
      }}>
        <Typography variant="body1" sx={{
          fontSize: '14px',
          fontWeight: 'medium',
          color: 'black',
          textAlign: 'center'
        }}>
           <strong>Supported Formats:</strong> .xlsx, .xls, .csv (Maximum file size: 10MB)
        </Typography>
      </Box>

      {/* Download Template Button */}
      <Box sx={{ mb: 3 }}>
        <Button
          variant="outlined"
          startIcon={<Download />}
          onClick={downloadTemplate}
          size="medium"
          sx={{ fontSize: '14px', px: 3, py: 1 }}
        >
          Download Template
        </Button>
      </Box>

      {/* File Upload Area */}
      <Paper
        sx={{
          p: 4,
          border: dragActive ? '2px dashed #1976d2' : '2px dashed #ccc',
          backgroundColor: dragActive ? '#f5f5f5' : 'transparent',
          textAlign: 'center',
          cursor: 'pointer',
          transition: 'all 0.3s ease',
          mb: 3,
        }}
        onDragEnter={handleDrag}
        onDragLeave={handleDrag}
        onDragOver={handleDrag}
        onDrop={handleDrop}
        onClick={() => document.getElementById('file-input').click()}
      >
        <input
          id="file-input"
          type="file"
          accept=".xlsx,.xls,.csv"
          onChange={handleFileSelect}
          style={{ display: 'none' }}
        />

        <CloudUpload sx={{ fontSize: 60, color: '#ccc', mb: 2 }} />

        <Typography variant="h6" gutterBottom sx={{ fontSize: '18px' }}>
          {selectedFile ? 'File Selected' : 'Drop your Excel file here or click to browse'}
        </Typography>

        {selectedFile ? (
          <Box sx={{ mt: 2 }}>
            <Chip
              icon={<Description />}
              label={`${selectedFile.name} (${(selectedFile.size / 1024 / 1024).toFixed(2)} MB)`}
              color="primary"
              variant="outlined"
              size="medium"
            />
          </Box>
        ) : (
          <Typography variant="body2" color="textSecondary" sx={{ fontSize: '14px' }}>
            Supported formats: .xlsx, .xls, .csv (Maximum size: 10MB)
          </Typography>
        )}
      </Paper>

      {/* Upload Button */}
      <Box sx={{ mb: 3 }}>
        <Button
          variant="contained"
          size="large"
          startIcon={<Group />}
          onClick={handleUpload}
          disabled={!selectedFile || uploading}
          fullWidth
          sx={{ fontSize: '16px', py: 1.5 }}
        >
          {uploading ? 'Uploading Players...' : 'Upload and Onboard Players'}
        </Button>

        {uploading && (
          <Box sx={{ mt: 2 }}>
            <LinearProgress />
            <Typography variant="body2" color="textSecondary" sx={{ mt: 1, textAlign: 'center', fontSize: '14px' }}>
              Processing file and onboarding players...
            </Typography>
          </Box>
        )}
      </Box>

      {/* Upload Result */}
      {uploadResult && (
        <Alert 
          severity={uploadResult.success ? 'success' : 'error'} 
          sx={{ mb: 3 }}
        >
          <Typography variant="body1" gutterBottom>
            {uploadResult.message}
          </Typography>
          
          {uploadResult.success && uploadResult.data && (
            <Box sx={{ mt: 1 }}>
              <Typography variant="body2">
                Successfully onboarded: {uploadResult.data.successCount || 0} players
              </Typography>
              {uploadResult.data.failedCount > 0 && (
                <Typography variant="body2" color="warning.main">
                  Failed: {uploadResult.data.failedCount} players
                </Typography>
              )}
            </Box>
          )}
          
          {!uploadResult.success && uploadResult.errors && uploadResult.errors.length > 0 && (
            <Box sx={{ mt: 1 }}>
              <Typography variant="body2" gutterBottom>
                Validation Errors ({uploadResult.errors.length} total):
              </Typography>

              {/* Error Summary */}
              {(() => {
                const errorTypes = {
                  missing: uploadResult.errors.filter(e => e.includes('Missing required field')).length,
                  invalid: uploadResult.errors.filter(e => e.includes('Invalid') && e.includes('format')).length,
                  duplicate: uploadResult.errors.filter(e => e.includes('already exists')).length,
                  other: uploadResult.errors.filter(e =>
                    !e.includes('Missing required field') &&
                    !(e.includes('Invalid') && e.includes('format')) &&
                    !e.includes('already exists')
                  ).length
                };

                return (
                  <Box sx={{ mb: 2, p: 1, bgcolor: '#fff3e0', borderRadius: 1, border: '1px solid #ffcc02' }}>
                    <Typography variant="body2" sx={{ fontSize: '12px', fontWeight: 'bold', mb: 1 }}>
                      📊 Error Summary:
                    </Typography>
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                      {errorTypes.missing > 0 && (
                        <Chip size="small" label={`📝 Missing Fields: ${errorTypes.missing}`} color="error" variant="outlined" />
                      )}
                      {errorTypes.invalid > 0 && (
                        <Chip size="small" label={`⚠️ Invalid Format: ${errorTypes.invalid}`} color="warning" variant="outlined" />
                      )}
                      {errorTypes.duplicate > 0 && (
                        <Chip size="small" label={`🔄 Duplicates: ${errorTypes.duplicate}`} color="info" variant="outlined" />
                      )}
                      {errorTypes.other > 0 && (
                        <Chip size="small" label={`❌ Other: ${errorTypes.other}`} color="default" variant="outlined" />
                      )}
                    </Box>
                  </Box>
                );
              })()}
              {(() => {
                // Group errors by row
                const errorsByRow = {};
                uploadResult.errors.forEach(error => {
                  const rowMatch = error.match(/Row (\d+):/);
                  if (rowMatch) {
                    const rowNum = rowMatch[1];
                    if (!errorsByRow[rowNum]) {
                      errorsByRow[rowNum] = [];
                    }
                    errorsByRow[rowNum].push(error.replace(`Row ${rowNum}: `, ''));
                  } else {
                    // General errors not tied to specific rows
                    if (!errorsByRow['general']) {
                      errorsByRow['general'] = [];
                    }
                    errorsByRow['general'].push(error);
                  }
                });

                return (
                  <Box sx={{ maxHeight: 300, overflow: 'auto', border: '1px solid #ddd', borderRadius: 1, p: 2 }}>
                    {Object.entries(errorsByRow).map(([rowNum, rowErrors]) => (
                      <Box key={rowNum} sx={{ mb: 2, p: 1, bgcolor: '#fafafa', borderRadius: 1, border: '1px solid #eee' }}>
                        <Typography variant="subtitle2" sx={{ fontWeight: 'bold', color: 'error.main', mb: 1 }}>
                          {rowNum === 'general' ? '⚠️ General Errors:' : `📍 Row ${rowNum} Errors:`}
                        </Typography>
                        {rowErrors.map((error, index) => {
                          // Determine error type and icon based on error message
                          let errorIcon = '❌';
                          let errorType = 'Error';

                          if (error.includes('Missing required field')) {
                            errorIcon = '📝';
                            errorType = 'Missing Field';
                          } else if (error.includes('Invalid') && error.includes('format')) {
                            errorIcon = '⚠️';
                            errorType = 'Invalid Format';
                          } else if (error.includes('already exists')) {
                            errorIcon = '🔄';
                            errorType = 'Duplicate';
                          }

                          return (
                            <Box key={index} sx={{ display: 'flex', alignItems: 'center', mb: 0.5 }}>
                              <Typography variant="body2" sx={{ fontSize: '12px', color: 'error.main' }}>
                                {errorIcon} <strong>{errorType}:</strong> {error}
                              </Typography>
                            </Box>
                          );
                        })}
                      </Box>
                    ))}
                  </Box>
                );
              })()}

              <Box sx={{ mt: 2, p: 1, bgcolor: '#e3f2fd', borderRadius: 1 }}>
                <Typography variant="body2" sx={{ fontSize: '12px', fontWeight: 'bold', mb: 1 }}>
                  💡 Quick Fix Tips:
                </Typography>
                <Typography variant="body2" sx={{ fontSize: '11px', mb: 0.5 }}>
                  • <strong>Invalid email format:</strong> Use format like "<EMAIL>"
                </Typography>
                <Typography variant="body2" sx={{ fontSize: '11px', mb: 0.5 }}>
                  • <strong>User already exists:</strong> Check for duplicate phone numbers or emails
                </Typography>
                <Typography variant="body2" sx={{ fontSize: '11px', mb: 0.5 }}>
                  • <strong>Missing fields:</strong> Ensure all required columns are filled
                </Typography>
                <Typography variant="body2" sx={{ fontSize: '11px' }}>
                  • <strong>Date format:</strong> Use YYYY-MM-DD format (e.g., 1995-05-15)
                </Typography>
              </Box>
            </Box>
          )}
        </Alert>
      )}

      {/* Success Actions */}
      {uploadResult?.success && (
        <Box sx={{ display: 'flex', gap: 3, justifyContent: 'center', mt: 3 }}>
          <Button
            variant="outlined"
            size="medium"
            onClick={() => navigate(`/dashboard/clubs/${clubId}/players`)}
            sx={{ px: 3, py: 1 }}
          >
            View Players
          </Button>
          <Button
            variant="contained"
            size="medium"
            onClick={() => {
              setSelectedFile(null);
              setUploadResult(null);
            }}
            sx={{ px: 3, py: 1 }}
          >
            Upload More Players
          </Button>
        </Box>
      )}
    </Container>
  );
};

export default OnboardPlayersUpload;
