import React, { useEffect, useState } from "react";
import { <PERSON>, Button, Container } from "@mui/material";
import { Client } from "../../api/client";

import { Link, useNavigate } from "react-router-dom";
import DynamicTable from "../../components/common/DynamicTable";
import UseToast from "../../lib/hooks/UseToast";
import { AxiosError } from "axios";
import { ArrowBack } from "@mui/icons-material";
import BackButton from "../../components/common/BackButton";
import UseGlobalContext from "../../lib/hooks/UseGlobalContext";
import UserSearch from "../../components/admin/UserSearch";
import AdminBanVerificationPopup from "../../components/admin/Banpopup";
import { formatPhoneNumber } from "../../utils/formatters";

const AdminUsersPage = () => {

  const [loading, setLoading] = useState(true);
  const [totalPages, setTotalPages] = useState(0);
  const [page, setPage] = useState(1);
  const limit = 10;
  const [users, setUsers] = useState([]);
  const { user } = UseGlobalContext()
  const [open, setOpen] = useState(false)
  const [info,setInfo]=useState([])
  const [isActive, setIsActive] = useState(true);
  const [search,setSearch]=useState({
    role:'player',
    UserName:'',
    availability: true,
    })
  const navigate = useNavigate();

  const toast = UseToast();

  const fetchUsers = async (page) => {
    setLoading(true);
    try {
    const whereClause  = {
      type: search.role,
      page,
      limit,
    };

    if (search.UserName?.trim()) {
      whereClause .name = search.UserName.trim();
    }

    if (search.availability) {
      whereClause .availability = search.availability;
    }
      const response = await Client.get("/admin/users/All-user", {
        params: { ...whereClause},
      });
      if (response.status === 204) {
        setUsers([]);
        toast.info("No arbiters found");
        return;
      }
      const { user, pagination } = response.data;
      setTotalPages(pagination.totalPages || 1);
      setPage(pagination.page || 1);
      setUsers(user);
    } catch (error) {
      if (error instanceof AxiosError && error.response?.status === 422) {
        console.error("Validation error:", error.response.data);
        toast.info("Please correct the search criteria");
      } else {
        console.error("Error fetching arbiters:", error);
        toast.error("Failed to fetch arbiters. Please try again.");
      }
      setUsers([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchUsers();
  }, [search.UserName])

  const fetchArbiterReport = async (type) => {
    try {
      const response = await Client.get("/admin/report/arbiter-report", {
        params: { type: type },
        responseType: 'blob',
      });
    
      const url = window.URL.createObjectURL(new Blob([response.data]));
    
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', 'arbiter_report.xlsx');
      document.body.appendChild(link);
      link.click();
      link.remove();
    } catch (error) {
      console.error("Download failed:", error);
    }
  };

  // Handle page change
  const handlePageChange = (newPage) => {
    setPage(newPage);
    fetchUsers(newPage);
  };

  const handleSearchChange = (event) => {
  const { name, value } = event.target;
  setSearch((prev) => ({
    ...prev,
    [name]: value,
  }));
};

const handleSearch = ()=>{
  fetchUsers();
}
const handleReset=()=>{
 setSearch({
  role:"player",
  UserName:"",
  availability:true
 })
 setPage(1)
}
  const handleMail = (data) => {
    navigate(`/dashboard/email/compose`, {
      state: {
        selectedUsers: [data],
        selectedType: search.role,
        mode: 'mail',
      },
    });
  }

  const handleSms = (data) => {
    navigate(`/dashboard/sms/compose`, {
      state: {
        selectedUsers: [data],
        selectedType: search.role,
        mode: 'sms',
      },
    });
  }

  const handleBlack =(item)=>{
    setInfo(item)
    setIsActive(item.isActive)
    setOpen(true)
  } 
   const handleUnActive = async (id) => {
    try {
      
      // return
      const response = await Client.post('/admin/access/ban', {
        value: true,
        userId:id
      });

      if (response.data.success) {
        toast.success("player Unblocked successfully");
        setIsActive(response.data.data)
        fetchUsers()
      }
    } catch (error) {
      console.error("Error", error);
      toast.error("An error occurred while updating player block");
    }
  }

  const actionButtonStyles = {
  borderRadius: 1,
  textTransform: "none",
  color: 'white',
  fontSize: "0.75rem",
  minWidth: '80px',
};

  return (
    <Container maxWidth="xl" sx={{ pt: 2, pb: 8 }}>
      {/* Search Form */}
      <Box sx={{ display: "flex", justifyContent: "space-between" }}>
        <BackButton />
        {user?.role === "admin" && (
          <Box>
            {/* <Button
              size="small"
              disabled={!users}
              variant="contained"
              onClick={() => fetchArbiterReport('excel')}
            >
              {" "}
              Download Report
            </Button> */}
          </Box>
        )}
      </Box>
      <UserSearch value={search} onChange={handleSearchChange} onSearch={handleSearch} onReset={handleReset} />
      <DynamicTable
        columns={[
          {
            id: "index",
            label: "S No",
            width: "80px",
            format: (_, item, index) => (page - 1) * limit + index + 1,
          },
          {
            id: "name",
            label: " Name",
            format: (value, item) => (
              <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                <span>{value}</span>
              </Box>
            ),
            // Commented link functionality - will be enabled later
            // format: (_, item) => (
            //   <Link to={`/arbiters/${item.cbid}`}>{item.name}</Link>
            // ),
          },
          { id: "email", label: "Mail" },
          {
            id: "phoneNumber",
            label: "Phone",
            format: (value) => value ? formatPhoneNumber(value) : "-"
          },
          {
            id: "hasPlayerDetail", label: "Profile",
            format: (value) => (
              <Box color={value ? "green" : "red"}>
                {value ? "Updated" : "Not Updated"}
              </Box>
            )
          },
          {
            label: "Action",
            format: (_, item) => (
              <Box display={'flex'} justifyContent={'space-evenly'} >
                <Button sx={{
                  borderRadius: 1,
                  textTransform: "none",
                  color: 'white',
                  fontSize: "0.75rem",
                  bgcolor: "hsla(257, 62%, 32%, 0.85)",
                }} onClick={() => handleMail(item)}>Mail</Button>

                  <Button sx={{
                  ...actionButtonStyles,
                }} 
                variant={item.isActive ?"contained":"outlined"}
                disabled={!item.isActive}
                onClick={() => handleBlack(item)}>Block</Button>

                  <Button sx={{
                  ...actionButtonStyles,
                }} 
                variant={item.isActive ? "outlined":"contained"}
                disabled={item.isActive}
                onClick={() => handleUnActive(item.id)}>Un Block</Button>

                {/* <Button sx={{
                  borderRadius: 1,
                  textTransform: "none",
                  color:'white',
                  fontSize: "0.75rem",
                  bgcolor: "hsla(257, 62%, 32%, 0.85)",
                }} onClick={() => handleSms(item)}>Sms</Button> */}

              </Box>
            ),
          },
        ]}
        data={users}
        loading={loading}
        page={page}
        totalPages={totalPages}
        showDetailsButton={false}
        onPageChange={handlePageChange}
        skeletonRows={10}

        tableContainerProps={{
          sx: {
            minHeight: "400px",
            maxHeight: "600px",
          },
        }}
      />
      {open && (
        <AdminBanVerificationPopup
          open={open}
          onClose={() => setOpen(false)}
          User={info}
          isActive={isActive}
          onSuccess={fetchUsers}
        />
      )}
    </Container>
  );
};

export default AdminUsersPage;