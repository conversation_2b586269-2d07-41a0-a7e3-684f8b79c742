import Player from "@vimeo/player";

import {
  <PERSON>,
  <PERSON><PERSON>,
  Chip,
  Container,
  Grid,
  LinearProgress,
  Typography,
  useMediaQuery,
  useTheme,
} from "@mui/material";

import React, { useEffect, useRef } from "react";
import { keyframes } from "@emotion/react";
import ArrowForwardIcon from "@mui/icons-material/ArrowForward";
// import HeroBackgroundImage from "../assets/images/homepageBackgroundchess.png";
import HeroBackgroundImage from "../assets/images/crop.png";
import CorporateStrategy from "../assets/images/corporate-strategy.png";
import Calendar from "../assets/images/calendar.png";
import ComputerChess from "../assets/images/computerchess.png";
import { Link, useNavigate } from "react-router-dom";

import useGlobalContext from "../lib/hooks/UseGlobalContext";
import AutoPlayImageCarousel from "../components/common/Carousel";
import BillboardPlayer from "../components/common/Autoplay";
import RollDisplay from "../components/common/RollDisplay";

const features = [
  {
    title: "Puzzles",
    icon: CorporateStrategy,
    description: "Practice with chess puzzles and improve your game",
    link: "#",
  },
  {
    title: "Register for Tournaments",
    icon: Calendar,
    description: "Find and register for chess tournaments near you",
    link: "#",
  },
  {
    title: "Play Online",
    icon: ComputerChess,
    description: "Challenge players from around the world in online matches",
    link: "#",
  },
];

const LandingPage = () => {
  const { isLoggedIn } = useGlobalContext();
  const navigate = useNavigate();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));
  const playerRef = useRef(null);

  useEffect(() => {
    if (isLoggedIn) {
      navigate("/dashboard");
    }
  }, []);

  useEffect(() => {
    if (!playerRef.current) return;

    const player = new Player(playerRef.current, {
      id: 1093312681,
      autoplay: true,
      controls: true,
      loop: true,
      byline: false,
      title: false,
      portrait: false,
      muted: false,
      width: isMobile ? 300 : 800,
      height: isMobile ? 200 : 400
    });

    player.on("loaded", () => {
      console.log("Vimeo video loaded");
    });

    return () => {
      player.destroy();
    };
  }, []);

  const fadeInUp = keyframes`
    from { opacity: 0; transform: translateY(5px); }
    to { opacity: 1; transform: translateY(0); }
  `;

  // Animation for the Register Now button
  const pulseGlow = keyframes`
    0% { 
      box-shadow: 0 0 5px rgba(255, 87, 34, 0.7);
      transform: scale(1);
    }
    50% { 
      box-shadow: 0 0 20px rgba(255, 87, 34, 0.9), 0 0 30px rgba(255, 87, 34, 0.6);
      transform: scale(1.05);
    }
    100% { 
      box-shadow: 0 0 5px rgba(255, 87, 34, 0.7);
      transform: scale(1);
    }
  `;

  const pulseGlow2 = keyframes`
    0% { 
      box-shadow: 0 0 5px rgba(34, 255, 82, 0.7);
      transform: scale(1);
    }
    50% { 
      box-shadow: 0 0 20px rgba(34, 255, 82, 0.7), 0 0 30px rgba(34, 255, 82, 0.7);
      transform: scale(1.05);
    }
    100% { 
      box-shadow: 0 0 5px rgba(34, 255, 82, 0.7);
      transform: scale(1);
    }
  `;

  const shimmer = keyframes`
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
  `;

  return (
    <Box
      sx={{
        bgcolor: "background.paper",
        overflowX: "hidden",
        minHeight: {
          xs: "50vh",
          sm: "80vh",
          md: "130vh",
          lg: "110vh",
          xl: "110vh",
        }, // do some change in minheight for option remove
      }}
    >
      <Box
        sx={{
          bgcolor: "white",
          minHeight: {
            xs: "50vh",
            sm: "80vh",
            md: "120vh",
            lg: "110vh",
            xl: "110vh", // do some change in minheight for option remove
          },
          display: "flex",
          flexDirection: "column",
          overflow: "hidden",
        }}
        className="jagadeesh"
      >
        <Box
          sx={{
            position: "relative",
            textAlign: "center",
            pb: 2,
          }}
        >
          <Box
            sx={{
              position: "relative",
              zIndex: 5,
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
              direction: "column",
              py: 2,
            }}
          >
            <Box
              component="img"
              src={HeroBackgroundImage}
              alt="Chess pieces"
              sx={{
                position: "absolute",
                width: "100%",
                height: "auto",
                opacity: 0.3,
                top: {
                  xs: 100,
                  sm: 80,
                  md: 80,
                  lg: 60,
                  xl: 80,
                },
                left: 0,
                right: 0,
                transform: "rotate(0.55deg)",
                margin: "0 ",
                zIndex: 4,
              }}
            />
            <Box
              component="svg"
              xmlns="http://www.w3.org/2000/svg"
              preserveAspectRatio="xMidYMid meet"
              viewBox="0 0 900 414"
              sx={{
                position: "absolute",
                top: 0,
                margin: "0 auto",
                opacity: 0.4,
                width: {
                  xs: "150vw",
                  sm: "120vw",
                  md: "110vw",
                  lg: "100vw",
                  xl: "100%",
                },
                height: "auto",
                zIndex: -1,
              }}
            >
              <path
                d="M900 0.5V281.771L462.5 413.5L0 293.51V0.5H900Z"
                fill="url(#paint0_linear_220_1129)"
                fillOpacity="0.23"
              />
              <defs>
                <linearGradient
                  xmlns="http://www.w3.org/2000/svg"
                  id="paint0_linear_220_1129"
                  x1="900"
                  y1="177.331"
                  x2="-0.000375146"
                  y2="167.983"
                  gradientUnits="userSpaceOnUse"
                >
                  <stop stopColor="#9F9F9F" />
                  <stop offset="0.333333" stopColor="white" />
                  <stop offset="0.666667" stopColor="white" />
                  <stop offset="1" stopColor="#9F9F9F" />
                </linearGradient>
              </defs>
            </Box>
           <RollDisplay/>
          </Box>
        </Box>
         <Box width={'100%'} sx={{display:'flex', zIndex: 5,padding:1,gap:'10px',alignItems:'center',justifyContent:'center', flexDirection: { xs: 'column', md: 'column',lg:"row" }, }} Height={200}>
                <Box sx={{width:{xs:'100%',md:"100%",lg:'50%'}}} >
                  <BillboardPlayer />
                </Box>

                <Box  sx={{width:{xs:'100%',md:"100%",lg:'40%'},borderRadius:'15px',}}>
                  <AutoPlayImageCarousel />
                </Box>
              </Box>
                  <Box
        sx={{
          py: 8,
          bgcolor: "transparent",
          position: "relative",
          mt: { xs: 0, sm: 5, md: 5, lg: 5, xl: 5 },
          // mb: 10,
          zIndex: 6,
        }}
      >
        <Container maxWidth="lg" sx={{ pt: 2 }}>
          <Grid container spacing={4}>
            {features.map((feature, index) => (
              <Grid
                item
                xs={12}
                md={4}
                key={index}
                sx={{ textAlign: "center" }}
              >
                <Box
                  component="img"
                  src={feature.icon}
                  alt={feature.title}
                  sx={{
                    width: { xs: 40, sm: 60, md: 80 },
                    height: { xs: 40, sm: 60, md: 80 },
                    mb: 2,
                  }}
                />

                <Typography
                  variant="h5"
                  component="h3"
                  sx={{
                    fontWeight: "bold",
                    mb: { xs: 1, sm: 2, md: 3 },
                    fontSize: { xs: 16, sm: 18, md: 20 },
                  }}
                >
                  {feature.title}
                </Typography>

                <Typography
                  variant="h6"
                  sx={{
                    mb: 2,
                    fontSize: { xs: 14, sm: 16, md: 18 },
                    px: { xs: "50px", sm: "30px", md: "0px" },
                    textAlign: "center",
                  }}
                >
                  {feature.description}
                </Typography>

                <Link to={feature.link}>
                  <Typography
                    sx={{
                      display: "inline-flex",
                      alignItems: "center",
                      justifyContent: "center",
                      color: "#4CAF50",
                      fontWeight: "medium",
                      fontSize: { xs: 14, sm: 16, md: 18 },
                      textDecoration: "none",
                      ":hover": {
                        textDecoration: "underline",
                      },
                    }}
                  >
                    {/* Learn more */}
                    {/* <ArrowForwardIcon
                      sx={{ ml: 1, fontSize: { xs: 12, sm: 14, md: 16 } }}
                    /> */}
                  </Typography>
                </Link>
              </Grid>
            ))}
          </Grid>
        </Container>
      </Box>
      </Box>
    </Box>
  );
};

export default LandingPage;
