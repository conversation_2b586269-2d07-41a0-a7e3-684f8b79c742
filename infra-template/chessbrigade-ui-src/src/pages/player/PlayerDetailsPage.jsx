import React, { useEffect, useState } from "react";
import {
  Avatar,
  Box,
  Button,
  Container,
  Paper,
  Skeleton,
  Stack,
  Typography,
} from "@mui/material";
import { Person, Add, PersonAdd } from "@mui/icons-material";
import { useNavigate, useParams } from "react-router-dom";
import UseToast from "../../lib/hooks/UseToast";
import { Client } from "../../api/client";
import UseGlobalContext from "../../lib/hooks/UseGlobalContext";
import { DetailTable } from "../../components/common/DetailTable";
import ClubInvitationModal from "../../components/club/ClubInvitationModal";
import FriendRequestModal from "../../components/player/FriendRequestModal";
import BackButton from "../../components/common/BackButton";

const PlayerDetailsPage = () => {
  const [playerInfo, setPlayerInfo] = useState({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [invitationModalOpen, setInvitationModalOpen] = useState(false);
  const [friendRequestModalOpen, setFriendRequestModalOpen] = useState(false);
  const [isFriend, setIsFriend] = useState(false);
  const toast = UseToast();
  const { user, isLoggedIn, currentProfile } = UseGlobalContext();
  const navigate = useNavigate();
  const [isMember, setIsMember] = useState(false);
  const { id: cbid } = useParams();

  const handlePlayerRemove = async () => {
    try {
      const response = await Client.post(`/club/remove-player`, {
        playerId: playerInfo?.cbid,
      });
      if (response.data.success) {
        toast.success("Player removed successfully");
        setIsMember(false);
      } else {
        toast.error(response.data.message || "Failed to remove player");
      }
    } catch (error) {
      console.error("Error removing player:", error);
    }
  };

  useEffect(() => {
    if (
      currentProfile &&
      currentProfile?.id === playerInfo?.PlayerDetail?.clubId
    ) {
      setIsMember(true);
    } else {
      setIsMember(false);
    }
  }, [currentProfile, playerInfo]);

  // Fetch player information
  // Check if the player is already a friend
  const checkFriendStatus = async (playerId) => {
    if (!isLoggedIn || user?.role !== "player") return;

    try {
      const response = await Client.get(`/user/check-friend-status/${playerId}`);
      if (response.data.success) {
        setIsFriend(response.data.isFriend);
      }
    } catch (error) {
      console.error("Error checking friend status:", error);
    }
  };
  const handleRemoveFriend = async () => {
    try {
      const response = await Client.post("/user/friend/remove", {
        friendId: playerInfo.id,
      });
      if (response.data.success) {
        toast.success("Friend removed successfully");
        setIsFriend(false);
      } else {
        toast.error(response.data.message || "Failed to remove friend");
      }
    } catch (error) {
      console.error("Error removing friend:", error);
      toast.error("An error occurred while removing friend");
    }
  };

  useEffect(() => {
    const fetchPlayerInfo = async () => {
      setLoading(true);
      setError(null);

      try {
        const response = await Client.get(`/player/single/${cbid}`);
        if (response.status === 204) {
          toast.info("Player not found");
          navigate(-1);
          return;
        }
        if (!response.data.success) {
          toast.error(response.data.message);
          setError(response.data.message);
          return;
        }

        const playerData = response.data.data;
        setPlayerInfo(playerData);

        // Check if player is a friend
        if (playerData.id) {
          checkFriendStatus(playerData.id);
        }

        setLoading(false);
      } catch (error) {
        console.error("Error fetching player info:", error);
        setError("Error fetching player info");
        setLoading(false);
      }
    };

    fetchPlayerInfo();
  }, [cbid, navigate, toast, isMember, isLoggedIn, user]);

  // Format player data for display
  const formattedPlayerData = formatPlayerData(playerInfo);


  return (
    <Container maxWidth="xl" sx={{ py: 4, pt: 2 }}>
      <BackButton />
      <Paper
        elevation={3}
        sx={{
          bgcolor: "background.default",
          minHeight: "80vh",
          borderRadius: 2,
          overflow: "hidden",
          mb: 4,
        }}
      >
        {/* Header */}
        <Box
          sx={{
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
            flexDirection: { xs: "column", sm: "row" },
            flexFlow: "row",
            px: { xs: "4vw", sm: "10vw" },
            py: 2,
            borderBottom: "1px solid #f0f0f0",
          }}
        >
          <Box
            sx={{
              display: "flex",
              alignItems: "center",
              alignSelf: "flex-start",
              gap: 2,
              width: "100%",
            }}
          >
            <Avatar
              src={playerInfo?.PlayerDetail?.profileUrl || ""}
              sx={{
                width: {
                  xs: 80,
                  sm: 80,
                  md: 100,
                  lg: 100,
                  xl: 100,
                },
                height: {
                  xs: 80,
                  sm: 80,
                  md: 100,
                  lg: 100,
                  xl: 100,
                },
                bgcolor: "#f5f5f5",
                color: "#000",
              }}
            >
              {!playerInfo?.PlayerDetail?.profileUrl && (
                <Person sx={{ fontSize: { xs: 50, md: 60 } }} />
              )}
            </Avatar>
            <Stack sx={{ width: "100%", textAlign: "start" }}>
              {!loading ? (
                <>
                  <Typography variant="h4" component="h4" fontWeight="500">
                    {formattedPlayerData.title}
                  </Typography>
                  <Typography
                    variant="h6"
                    component="h6"
                    fontWeight="400"
                    sx={{ fontSize: { xs: "14px", sm: "16px" } }}
                  >
                    CBID: {playerInfo?.cbid || "-"}
                  </Typography>
                  <Typography
                    variant="h6"
                    component="h6"
                    fontWeight="400"
                    sx={{ fontSize: { xs: "14px", sm: "16px" } }}
                  >
                    FIDE Rating: {playerInfo?.PlayerDetail?.fideRating || "-"}
                  </Typography>
                </>
              ) : (
                <Skeleton variant="text" width={200} />
              )}
            </Stack>
          </Box>
          {isLoggedIn &&
            user?.role === "club" &&
            (currentProfile &&
            currentProfile?.id === playerInfo?.PlayerDetail?.clubId ? (
              <Button
                variant="contained"
                color="success"
                startIcon={<Add />}
                sx={{
                  borderRadius: 1,
                  textTransform: "none",
                  fontWeight: 500,
                  m: { xs: "10px" },
                  width: { xs: "100%", sm: "auto" },
                  fontSize: 16,
                  backgroundColor: "hsla(132, 56%, 36%, 1)",
                  ":hover": { bgcolor: "rgb(33, 107, 48)" },
                  px: 4,

                  textWrap: "nowrap",
                }}
                onClick={handlePlayerRemove}
              >
                Remove Member
              </Button>
            ) : (
              <Button
                variant="contained"
                color="success"
                startIcon={<Add />}
                sx={{
                  borderRadius: 1,
                  textTransform: "none",
                  fontWeight: 500,
                  m: { xs: "10px" },
                  width: { xs: "100%", sm: "auto" },
                  fontSize: 16,
                  backgroundColor: "hsla(132, 56%, 36%, 1)",
                  ":hover": { bgcolor: "rgb(33, 107, 48)" },
                  px: 2,
                  textWrap: "nowrap",
                }}
                onClick={() => setInvitationModalOpen(true)}
              >
                Add Member
              </Button>
            ))}

          {isLoggedIn &&
            user?.role === "player" &&
            playerInfo?.id !== user?.userId &&
            (isFriend ? (
              <Button
                variant="contained"
                color="error"
                startIcon={<PersonAdd />}
                sx={{
                  borderRadius: 1,
                  textTransform: "none",
                  fontWeight: 500,
                  m: { xs: "10px" },
                  width: { xs: "100%", sm: "auto" },
                  fontSize: 16,

                  px: 4,
                  textWrap: "nowrap",
                }}
                onClick={handleRemoveFriend}
              >
                Remove Friend
              </Button>
            ) : (
              <Button
                variant="contained"
                color="success"
                startIcon={<PersonAdd />}
                sx={{
                  borderRadius: 1,
                  textTransform: "none",
                  fontWeight: 500,
                  m: { xs: "10px" },
                  width: { xs: "100%", sm: "auto" },
                  fontSize: 16,
                  backgroundColor: "hsla(132, 56%, 36%, 1)",
                  ":hover": { bgcolor: "rgb(33, 107, 48)" },
                  px: 2,
                  textWrap: "nowrap",
                }}
                onClick={() => setFriendRequestModalOpen(true)}
              >
                Add Friend
              </Button>
            ))}
        </Box>

        {/* Player Information */}
        <Box>
          {loading ? (
            // Loading skeleton
            Array(6)
              .fill(0)
              .map((_, index) => (
                <Box key={index} sx={{ mb: 2 }}>
                  <Skeleton variant="rectangular" height={40} />
                </Box>
              ))
          ) : error ? (
            // Error message
            <Typography color="error" variant="h6" align="center">
              {error}
            </Typography>
          ) : (
            // Player details table
            <DetailTable
              details={formattedPlayerData.details}
              rowColor={{ odd: "#A5D8C626", even: "#BEDDF04F" }}
            />
          )}
        </Box>
      </Paper>

      {/* Club Invitation Modal */}
      <ClubInvitationModal
        open={invitationModalOpen}
        onClose={() => setInvitationModalOpen(false)}
        playerData={playerInfo}
      />

      {/* Friend Request Modal */}
      <FriendRequestModal
        open={friendRequestModalOpen}
        onClose={() => setFriendRequestModalOpen(false)}
        playerData={playerInfo}
      />
    </Container>
  );
};

export default PlayerDetailsPage;

/**
 * Format player data for display in the detail table
 * @param {Object} player - Player data from API
 * @returns {Object} Formatted player data with title and details
 */
function formatPlayerData(player) {
  if (!player || !player.PlayerDetail) {
    return {
      title: "",
      details: [],
    };
  }

  // Create a list of details to display
  const details = [
    // {
    //   label: "Full Name",
    //   value: player.name || "-",
    // },
    // {
    //   label: "CBID",
    //   value: player.cbid || "-",
    // },
    {
      label: "FIDE ID",
      value: player.PlayerDetail.fideId || "-",
    },
    {
      label: "AICF ID",
      value: player.PlayerDetail.aicfId || "-",
    },
    {
      label: "State ID",
      value: player.PlayerDetail.stateId || "-",
    },
    {
      label: "District ID",
      value: player.PlayerDetail.districtId || "-",
    },
    // {
    //   label: "FIDE Rating",
    //   value: player.PlayerDetail.fideRating || "-",
    // },
    {
      label: "Club",
      value: player.PlayerDetail.club || "-",
    },
    {
      label: "Country",
      value: player.PlayerDetail.country || "-",
    },
    {
      label: "State",
      value: player.PlayerDetail.state || "-",
    },
    {
      label: "District",
      value: player.PlayerDetail.district || "-",
    },
  ];

  return {
    title: player.name || "",
    details: details,
  };
}
