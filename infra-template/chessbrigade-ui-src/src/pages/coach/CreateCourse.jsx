import React from 'react'
import BackButton from '../../components/common/BackButton'
import CreateCourseForm from '../../components/form/CourceForm'
import { Box } from '@mui/material'
import UseGlobalContext from '../../lib/hooks/UseGlobalContext'
import { useLocation } from 'react-router-dom'

const CreateCourse = () => {
  const location = useLocation()
  const { user } = UseGlobalContext();
  const coachId = location.state?.admin ? location.state.coachId : user?.userId;
  const admin = location.state?.admin || false;
  return (
    <Box p={2}>
        <BackButton/>
        <Box>
            <CreateCourseForm coachId={coachId} admin={admin} />
        </Box>
    </Box>
  )
}

export default CreateCourse