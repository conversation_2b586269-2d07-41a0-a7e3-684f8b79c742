import React, { useState, useEffect } from "react";
import {
  Avatar,
  Box,
  Button,
  Container,
  IconButton,
  Paper,
  Skeleton,
  Stack,
  Tooltip,
  Typography,
} from "@mui/material";
import { <PERSON><PERSON>, <PERSON>, PinDrop } from "@mui/icons-material";
import { Client } from "../../api/client";
import UseToast from "../../lib/hooks/UseToast";
import { useNavigate } from "react-router-dom";
import { DetailTable } from "../../components/common/DetailTable";
import BackButton from "../../components/common/BackButton";
import { formatPhoneNumber } from "../../utils/formatters";

/**
 * Format Coach data for display in the detail table
 * @param {Object} Coach - Coach data from API
 * @returns {Object} Formatted Coach data with title and details
 */
function formatCoachData(Coach) {
  if (!Coach) {
    return {
      title: "",
      details: [],
    };
  }

  // Create a list of details to display
  const details = [
    {
      label: "Coach Name",
      value: Coach.user.name || "-",
    },
    {
      label: "Phone Number",
      value: Coach?.user?.phoneNumber ? formatPhoneNumber(Coach?.user?.phoneNumber): "-",
    },
    {
      label: "Email",
      value: Coach.user?.email || "-",
    },
    {
      label: "About Me",
      value: Coach?.about || "-",
    },
    {
      label: "Country",
      value: Coach?.country || "-",
    },
    {
      label: "State",
      value: Coach?.state || "-",
    },
    {
      label: "District",
      value: Coach?.district || "-",
    },
    {
      label: "City",
      value: Coach?.city || "-",
    },
     {
      label: "FIDE.com ",
      value: Coach?.profileLinks?.fide || "-",
    },
     {
      label: "Lichess.com",
      value: Coach?.profileLinks?.li_chess || "-",
    },
     {
      label: "Chess.com",
      value: Coach?.profileLinks?.chess || "-",
    },
  ];

  return {
    title: Coach.user.name || "",
    details: details,
  };
}

const CouchProfilePage = () => {
  const [coachInfo, setCoachInfo] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  // const { user } = useGlobalContext();
  const toast = UseToast();
  const Navigate = useNavigate();

  useEffect(() => {
    const fetchCoachInfo = async () => {
      setLoading(true);
      setError(null);
      try {
        const response = await Client.get("/coach/profile");
        if (response.status === 204) {
          Navigate("edit?edit=0");
          return;
        }
        if (!response.data.success) {
          toast.error(response.data.message);
          setError(response.data.message);
          return;
        }
        setCoachInfo(response.data.data);
        setLoading(false);
      } catch (error) {
        console.error("Error fetching Coach info:", error);

        const errorMessage =
          error.response?.data?.error || "An unknown error occurred";
        setError(errorMessage);
        setLoading(false);
        toast.error(
          `Failed to fetch Coach information: ${errorMessage}. Please try again later.`
        );
      }
    };

    fetchCoachInfo();
  }, [Navigate]);
 console.log("coach",coachInfo)
  const handleRemoveProfile = async () => {
    try {
      const response = await Client.delete(
        "/coach/profile/remove-profile-image"
      );
      if (response.data.success) {
        toast.success("Profile image removed successfully");
        setCoachInfo((prev) => ({
          ...prev,
          profileUrl: null,
        }));
      } else {
        toast.error(response.data.message || "Failed to remove profile image");
      }
    } catch (error) {
      console.error("Error removing profile image:", error);
      toast.error("Failed to remove profile image. Please try again later.");
    }
  };


  // Format Coach data for display in the detail table
  const formattedCoachData = formatCoachData(coachInfo);

  // const handleSkipFieldChange = (event) => {
  //   setSkipFields(event.target.value.split(",").map((field) => field.trim()));
  // };

  return (
    <Container maxWidth="xl" sx={{ py: 4,pt: 2 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <BackButton to={"/dashboard"} />
      </Box>
      <Paper
        elevation={1}
        sx={{
          bgcolor: "background.default",
          minHeight: "80vh",
          borderRadius: 2,
          overflow: "hidden",
        }}
      >
        {error ? (
          <Typography
            variant="h6"
            color="error"
            sx={{ p: 2, textAlign: "center" }}
          >
            Something went Wrong.
          </Typography>
        ) : (
          <>
            {/* Header */}
            <Box
              sx={{
                display: "flex",
                alignItems: "center",
                justifyContent: "space-between",
                px: "12vw",
                py: 2,
                borderBottom: "1px solid #f0f0f0",
              }}
            >
              <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
                <Stack>
                  {!loading ? (
                    <>
                      <Typography
                        variant="h4"
                        component="h4"
                        fontWeight="500"
                        sx={{ textDecoration: "underline" }}
                      >
                        Coach Profile
                      </Typography>
                    </>
                  ) : (
                    <>
                      <Skeleton variant="text" width={200} height={32} />
                    </>
                  )}
                </Stack>
              </Box>
              {loading ? (
                <Skeleton variant="circular" width={60} height={60} />
              ) : (
                <Box sx={{ position: "relative", display: "inline-block" }}>
                  <Avatar
                    src={coachInfo?.profileUrl}
                    sx={{
                      width: 100,
                      height: 100,
                      bgcolor: "#f5f5f5",
                      color: "#000",
                    }}
                  >
                    {!coachInfo?.profileUrl && <Person sx={{ fontSize: 60 }} />}
                  </Avatar>

                  {coachInfo?.profileUrl && (
                    <Tooltip title="Remove profile image" placement="top">
                      <IconButton
                        size="small"
                        onClick={handleRemoveProfile} // define this function in your component
                        sx={{
                          position: "absolute",
                          top: 4,
                          right: 4,
                          backgroundColor: "#fff",
                          color: "red",
                          p: "2px",
                          "&:hover": {
                            backgroundColor: "#eee",
                          },
                        }}
                      >
                        <Cancel fontSize="small" />
                      </IconButton>
                    </Tooltip>
                  )}
                </Box>
              )}
            </Box>

            {/* Coach Information */}
            <Box>
              {loading ? (
                // Loading skeleton
                Array(6)
                  .fill(0)
                  .map((_, index) => (
                    <Box key={index} sx={{ mb: 2 }}>
                      <Skeleton variant="rectangular" height={40} />
                    </Box>
                  ))
              ) : error ? (
                // Error message
                <Typography color="error" variant="h6" align="center">
                  {error}
                </Typography>
              ) : (
                // Coach details table
                <DetailTable
                  details={formattedCoachData.details}
                  rowColor={{ odd: "#D4B8A226", even: "#CCF7E840" }}
                />
              )}
            </Box>
            <Box
              sx={{
                p: 2,
                textAlign: "center",
                display: "flex",
                justifyContent: "center",
                gap: 2,
              }}
            >
              {coachInfo && coachInfo.locationUrl && (
                <Box>
                  <Button
                    variant="contained"
                    startIcon={<PinDrop />}
                    sx={{
                      bgcolor: "hsla(132, 56%, 36%, 1) ",
                      fontSize: "16px",
                      ":hover": { bgcolor: "rgb(33, 107, 48)" },
                    }}
                    onClick={() => Navigate("edit")}
                  >
                    Location
                  </Button>
                </Box>
              )}
              <Box>
                <Button
                  variant="contained"
                  sx={{
                    bgcolor: "hsla(242, 56%, 36%, 1) ",
                    fontSize: "16px",
                    ":hover": { bgcolor: "rgb(37, 34, 110)" },
                  }}
                  onClick={() => {
                    Navigate("edit?edit=1");
                  }}
                >
                  Edit Profile
                </Button>
              </Box>
            </Box>
          </>
        )}
      </Paper>
    </Container>
  );
};

export default CouchProfilePage;
