// Utility functions for Tawk.to user management

export const refreshTawkToUser = () => {
  try {
    if (typeof window === 'undefined' || !window.Tawk_API) {
      console.warn('Tawk.to not available');
      return;
    }

    // Get current user data
    const userData = localStorage.getItem('user');
    const user = userData ? JSON.parse(userData) : null;

    if (user && typeof user === 'object') {
      // Get specific ID based on user type
      let specificId = '';
      let displayName = '';

      if (user.role === 'player') {
        specificId = user.playerId || user.id || user.cbid || 'N/A';
        displayName = `${user.name || 'Unknown Player'} (Player ID: ${specificId})`;
      } else if (user.role === 'club') {
        specificId = user.clubId || user.id || user.cbid || 'N/A';
        displayName = `${user.clubName || user.name || 'Unknown Club'} (Club ID: ${specificId})`;
      } else if (user.role === 'arbiter') {
        specificId = user.arbiterId || user.id || user.cbid || 'N/A';
        displayName = `${user.arbiterName || user.name || 'Unknown Arbiter'} (Arbiter ID: ${specificId})`;
      } else {
        specificId = user.id || user.cbid || 'N/A';
        displayName = `${user.name || 'Unknown User'} (${user.role || 'user'})`;
      }

      // Update visitor info
      window.Tawk_API.visitor = {
        name: displayName,
        email: user.email || '',
        custom: {
          userId: user.id || user.cbid || 'guest',
          userType: user.role || 'player',
          specificId: specificId,
          playerId: user.playerId || '',
          clubId: user.clubId || '',
          arbiterId: user.arbiterId || '',
          cbid: user.cbid || '',
          phoneNumber: user.phoneNumber || '',
          clubName: user.clubName || '',
          arbiterName: user.arbiterName || '',
          sessionId: Date.now() + '_' + Math.random().toString(36).substring(2, 11),
          loginTime: new Date().toISOString(),
        },
      };

      // Set attributes with all relevant IDs
      if (typeof window.Tawk_API.setAttributes === 'function') {
        const attributes = {
          'User Type': user.role || 'player',
          'Primary ID': specificId,
          'CBID': user.cbid || 'N/A',
          'Phone': user.phoneNumber || 'N/A',
          'Email': user.email || 'N/A',
          'Current Page': window.location?.pathname || 'Unknown',
          'Login Time': new Date().toISOString(),
          'Session ID': Date.now() + '_' + specificId,
        };

        // Add role-specific attributes
        if (user.role === 'player') {
          attributes['Player ID'] = user.playerId || user.id || 'N/A';
          attributes['Player Name'] = user.name || 'N/A';
          attributes['FIDE ID'] = user.fideId || 'N/A';
          attributes['AICF ID'] = user.aicfId || 'N/A';
          attributes['State ID'] = user.stateId || 'N/A';
          attributes['District ID'] = user.districtId || 'N/A';
          attributes['FIDE Rating'] = user.fideRating || 'N/A';
        } else if (user.role === 'club') {
          attributes['Club ID'] = user.clubId || user.id || 'N/A';
          attributes['Club Name'] = user.clubName || user.name || 'N/A';
          attributes['Contact Person'] = user.contactPerson || 'N/A';
          attributes['Contact Person Phone'] = user.contactPersonPhoneNumber || 'N/A';
          attributes['Contact Person Email'] = user.contactPersonEmail || 'N/A';
          attributes['Address'] = user.address || 'N/A';
          attributes['City'] = user.city || 'N/A';
          attributes['State'] = user.state || 'N/A';
          attributes['Country'] = user.country || 'N/A';
        } else if (user.role === 'arbiter') {
          attributes['Arbiter ID'] = user.arbiterId || user.id || 'N/A';
          attributes['Arbiter Name'] = user.arbiterName || user.name || 'N/A';
          attributes['Alternate Contact'] = user.alternateContactNumber || 'N/A';
          attributes['FIDE ID'] = user.fideId || 'N/A';
          attributes['AICF ID'] = user.aicfId || 'N/A';
          attributes['State ID'] = user.stateId || 'N/A';
          attributes['District ID'] = user.districtId || 'N/A';
        }

        window.Tawk_API.setAttributes(attributes, (error) => {
          if (error) {
            console.warn('Error setting Tawk.to attributes:', error);
          } else {
            console.log('Tawk.to user refreshed:', user.role, specificId);
          }
        });
      }

      // Hide and show widget to force refresh
      if (typeof window.Tawk_API.hideWidget === 'function' && typeof window.Tawk_API.showWidget === 'function') {
        window.Tawk_API.hideWidget();
        setTimeout(() => {
          window.Tawk_API.showWidget();
        }, 500);
      }
    } else {
      // Guest user
      window.Tawk_API.visitor = {
        name: 'Guest User',
        email: '',
        custom: {
          userId: 'guest_' + Date.now(),
          userType: 'guest',
          sessionId: Date.now() + '_guest',
        },
      };
    }
  } catch (error) {
    console.warn('Error refreshing Tawk.to user:', error);
  }
};

export const endTawkToSession = () => {
  try {
    if (typeof window !== 'undefined' && window.Tawk_API && typeof window.Tawk_API.endChat === 'function') {
      window.Tawk_API.endChat();
      console.log('Tawk.to session ended');
    }
  } catch (error) {
    console.warn('Error ending Tawk.to session:', error);
  }
};
