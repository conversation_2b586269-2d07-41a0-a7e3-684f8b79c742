

const getEligibleCategories = (dob, gender, maleAgeCategory, femaleAgeCategory) => {
  const calculateAge = (birthDate) => {
    const today = new Date();
    const birth = new Date(birthDate);
    
    let age = today.getFullYear() - birth.getFullYear();
    const monthDiff = today.getMonth() - birth.getMonth();
    
    // Adjust age if birthday hasn't occurred this year
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
      age--;
    }
    
    return age;
  };

  const age = calculateAge(dob);
  const categories = gender === 'male' ? maleAgeCategory : femaleAgeCategory;

  const eligible = categories.filter(category => {
    if (category === 'OPEN') return age >= 18;
    const match = category.match(/^U(\d+)$/);
    if (match) {
      const maxAge = parseInt(match[1], 10);
      return age <= maxAge;
    }
    return false; // skip if unrecognized format
  });
  
  return eligible.length > 0 ? eligible : false;
};

export default getEligibleCategories;