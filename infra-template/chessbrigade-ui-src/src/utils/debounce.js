/**
 * Creates a debounced function that delays invoking the provided function
 * until after the specified wait time has elapsed since the last time it was invoked.
 * 
 * @param {Function} func - The function to debounce
 * @param {number} wait - The number of milliseconds to delay
 * @returns {Function} - The debounced function
 */
export const debounce = (func, wait) => {
  let timeout;
  
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
};

/**
 * Creates a debounced version of React Hook Form's setValue function
 * 
 * @param {Function} setValue - React Hook Form's setValue function
 * @param {number} delay - The number of milliseconds to delay
 * @returns {Function} - The debounced setValue function
 */
export const createDebouncedSetValue = (setValue, delay = 300) => {
  return debounce((name, value) => {
    setValue(name, value);
  }, delay);
};

export default debounce;
