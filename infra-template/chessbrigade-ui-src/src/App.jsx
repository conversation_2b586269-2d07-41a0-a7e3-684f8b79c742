import React from "react";
import { BrowserRouter } from "react-router-dom";
import AppRouter from "./pages/AppRoutes";
import GlobalContextProvider from "./context/GlobalContext";
import ToastProvider from "./context/ToastContext";
import { ThemeProvider } from "./theme/theme.jsx";
import ScrollToTop from "./components/common/ScrollToTop";
import TawkTo from "./components/TawkTo.jsx"
import { TranslationProvider } from "./context/TranslationContext.jsx";

function App() {
  return (
    <ThemeProvider>
      <GlobalContextProvider>
        <BrowserRouter>
          <ToastProvider>
             <TranslationProvider>
                 <TawkTo/>
                <AppRouter />
            </TranslationProvider>
          </ToastProvider>
          <ScrollToTop />
        </BrowserRouter>
      </GlobalContextProvider>
    </ThemeProvider>
  );
}

export default App;
