import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON>hem<PERSON><PERSON>rovider,
  createTheme,
} from "@mui/material";
import React from "react";

const appTheme = createTheme({
  palette: {
    primary: {
      main: "rgb(0, 0, 0)", // Black from the header
    },
    secondary: {
      main: "hsla(120, 49%, 35%, 1)", // <PERSON> from the "Learn more" text
    },
    background: {
      default: "#FFFFFF", // White background
      paper: "#FFFFFF",
    },
    button: { main: "hsla(242, 56%, 36%, 1)" },
    text: {
      primary: "#000000", // Black text
      secondary: "#000000", // White text for dark backgrounds
    },
    common: {
      black: "#000000",
      white: "#FFFFFF",
    },
  },
  typography: {
    fontFamily: [
      "Poppins",
      "Yantramanav",
      "Prosto One",
      "Yeseva One",
      "sans-serif",
    ].join(","),
    h1: {
      fontFamily: "'Prosto One', cursive",
      fontSize: "40px",
      fontWeight: 400,
      "@media (max-width:600px)": {
        fontSize: "32px",
      },
      "@media (max-width:1024px)": {
        fontSize: "36px",
      },
    },
    h2: {
      fontFamily: "'Poppins', sans-serif",
      fontSize: "42px",
      fontWeight: 400,
      textAlign: "center",
      "@media (max-width:600px)": {
        fontSize: "34px",
      },
      "@media (max-width:1024px)": {
        fontSize: "38px",
      },
    },
    h3: {
      fontFamily: "'Poppins', sans-serif",
      fontSize: "32px",
      fontWeight: 500,
      "@media (max-width:600px)": {
        fontSize: "26px",
      },
      "@media (max-width:1024px)": {
        fontSize: "29px",
      },
    },
    h4: {
      fontFamily: "'Poppins', sans-serif",
      fontSize: "24px",
      fontWeight: 400,
      "@media (max-width:600px)": {
        fontSize: "20px",
      },
      "@media (max-width:1024px)": {
        fontSize: "22px",
      },
    },
    h5: {
      fontFamily: "'Poppins', sans-serif",
      fontSize: "20px",
      fontWeight: 400,
      "@media (max-width:600px)": {
        fontSize: "18px",
      },
      "@media (max-width:1024px)": {
        fontSize: "19px",
      },
    },
    h6: {
      fontFamily: "'Poppins', sans-serif",
      fontSize: "20px",
      fontWeight: 400,
      "@media (max-width:600px)": {
        fontSize: "1rem",
      },
      "@media (max-width:1024px)": {
        fontSize: "1.2rem",
      },
    },
    subtitle1: {
      fontFamily: "'Yantramanav', sans-serif",
      fontSize: "24px",
      fontWeight: 300,
      textAlign: "center",
    },
    subtitle2: {
      fontFamily: "'Poppins', sans-serif",
      fontSize: "24px",
      fontWeight: 300,
      textAlign: "center",
      color: "#3f51b5",
    },
    body1: {
      fontFamily: "'Poppins', sans-serif",
      fontSize: "16px",
      fontWeight: 400,
    },
    body2: {
      fontFamily: "'Yantramanav', sans-serif",
      fontSize: "24px",
      fontWeight: 500,
      textAlign: "center",
    },
    button: {
      fontFamily: "'Poppins', sans-serif",
      fontSize: "1rem",
      fontWeight: 500,
      textTransform: "none",
    },
  },
  components: {
    MuiButton: {
      styleOverrides: {
        root: {
          textTransform: "none",
          borderRadius: "6px",
        },
        containedSecondary: {
          backgroundColor: "hsla(120, 49%, 35%, 1)",
          color: "#FFFFFF",
          "&:hover": {
            backgroundColor: "rgb(66, 165, 66)",
          },
        },
        containedPrimary: {
          backgroundColor: "hsla(242, 56%, 36%, 1)",
          color: "#FFFFFF",
          "&:hover": {
            backgroundColor: "rgb(63, 59, 170)",
          },
        },
        outlined: {
          borderColor: "#000000",
          color: "#000000",
          "&:hover": {
            backgroundColor: "rgba(0, 0, 0, 0.04)",
          },
        },
      },
    },
    MuiTextField: {
      defaultProps: {
        InputLabelProps: { shrink: true },
      },
    },
    MuiPickersYear: {
      styleOverrides: {
        yearButton: {
          padding: "0px !important",
          minWidth: "auto",
          minHeight: "auto",
        },
      },
    },
    MuiPickersMonth: {
      styleOverrides: {
        monthButton: {
          padding: "0px !important",
          minWidth: "auto",
          minHeight: "auto",
        },
      },
    },
    MuiCssBaseline: {
      styleOverrides: {
        "@global": {
          "@import": [
            "url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap')",
            "url('https://fonts.googleapis.com/css2?family=Yantramanav:wght@300;400;500;700&display=swap')",
            "url('https://fonts.googleapis.com/css2?family=Prosto+One&display=swap')",
            "url('https://fonts.googleapis.com/css2?family=Yeseva+One&display=swap')",
          ],
        },
        body: {
          backgroundColor: "#FFFFFF",
          fontFamily: "'Poppins', sans-serif",
        },
      },
    },
    MuiAppBar: {
      styleOverrides: {
        root: {
          backgroundColor: "#000000",
        },
      },
    },
    MuiTableCell: {
      styleOverrides: {
        root: ({ theme }) => ({
          ...theme.typography.body1,
        }),
        head: ({ theme }) => ({
          ...theme.typography.h5,
          fontWeight: 500,
        }),
        body: ({ theme }) => ({
          ...theme.typography.body1,
        }),
      },
    },
    MuiListItemText: {
      styleOverrides: {
        primary: ({ theme }) => ({
          ...theme.typography.h4,
        }),
        secondary: ({ theme }) => ({
          ...theme.typography.body1,
        }),
      },
    },
    MuiLink: {
      styleOverrides: {
        root: {
          color: "#2C832C",
          textDecoration: "none",
          "&:hover": {
            textDecoration: "underline",
          },
        },
      },
    },
  },
});

export const ThemeProvider = ({ children }) => {
  return (
    <MuiThemeProvider theme={appTheme}>
      <CssBaseline />
      {children}
    </MuiThemeProvider>
  );
};

export default appTheme;
