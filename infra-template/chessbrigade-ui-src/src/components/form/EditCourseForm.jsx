// src/components/course/EditCourseForm.jsx
import React, { useEffect, useState } from "react";
import {
  Button,
  Typography,
  Grid,
  Box,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  Card,
  CardContent,
  Divider,
  Paper,
  Container,
  Zoom,
  Fade,
  Chip,
  FormControl,
  FormLabel,
  FormHelperText,
} from "@mui/material";
import {
  School,
  Schedule,
  Payment,
  Info,
  CheckCircle,
  CalendarToday,
} from "@mui/icons-material";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm, Controller } from "react-hook-form";
import UseToast from "../../lib/hooks/UseToast";
import { useNavigate, useParams } from "react-router-dom";
import FormTextField from "./FormTextField";
import FormSelectField from "./FormSelectField";
import { Client } from "../../api/client";
import { EditCourseSchema } from "../../schema/zod";
import FormTimePicker from "./FormTimePicker";
import FormNumberField from "./FormNumberField";

const EditCourseForm = ({edit = true}) => {
  const {
    control,
    handleSubmit,
    formState: { errors },
    setValue,
    reset,
    getValues,
    watch,
  } = useForm({
    resolver: zodResolver(EditCourseSchema),
    defaultValues: {
      title: "",
      description: "",
      startDate: "",
      endDate: "",
      registrationStartDate: "",
      registrationEndDate: "",
      courseBanner: null,
      courseDuration: "",
      totalSessions: "",
      sessionsPerWeek: 0,
      courseFee: 0,
      paymentStructure: "",
      sessionStartTime: "",
      sessionEndTime: "",
      ageGroup: "",
      category: "",
      sessionDuration: "",
      selectedDays: [], // New field for selected days
    },
    reValidateMode: "onChange",
  });
  
  const toast = UseToast();
  const navigate = useNavigate();
  const [showSuccessDialog, setShowSuccessDialog] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [loading, setLoading] = useState(false);
  const [originalValue, setOriginalValue] = useState({});

  // Watch sessionsPerWeek to dynamically update day selection
  const sessionsPerWeek = watch("sessionsPerWeek");
  const selectedDays = watch("selectedDays");

  // Days of the week
  const daysOfWeek = [
    { value: "monday", label: "Monday", short: "Mon" },
    { value: "tuesday", label: "Tuesday", short: "Tue" },
    { value: "wednesday", label: "Wednesday", short: "Wed" },
    { value: "thursday", label: "Thursday", short: "Thu" },
    { value: "friday", label: "Friday", short: "Fri" },
    { value: "saturday", label: "Saturday", short: "Sat" },
    { value: "sunday", label: "Sunday", short: "Sun" },
  ];

  console.log("values", getValues());
  console.log("errors", errors);
  const { title: Id } = useParams();

  useEffect(() => {
    const fetchTournament = async () => {
      setLoading(true);
      try {
        const response = await Client.get(`/course/${Id}`);
        if (response.data.success) {
          const tournamentData = response.data.data;
          const { title, ...rest } = tournamentData;

          const newTitle = title.replace(/-/g, " ");
          
          // Set all form values at once, including selectedDays if it exists
          const formData = {
            title: newTitle,
            ...rest,
            selectedDays: rest.selectedDays || [], // Include selectedDays from API or default to empty array
          };
          
          reset(formData);
          setOriginalValue(formData);

          toast.success("Tournament details loaded successfully");
        } else {
          toast.error("Failed to fetch tournament details");
        }
      } catch (error) {
        console.error("Error fetching tournament details:", error);
        toast.error("Error fetching tournament details");
      } finally {
        setLoading(false);
      }
    };

    fetchTournament();
  }, [Id, setValue, reset]);

  // Reset selected days when sessions per week changes (but not during initial load)
  useEffect(() => {
    if (sessionsPerWeek > 0 && !loading) {
      const currentDays = getValues("selectedDays") || [];
      if (currentDays.length > parseInt(sessionsPerWeek)) {
        setValue("selectedDays", []);
      }
    }
  }, [sessionsPerWeek, setValue, loading, getValues]);

  const handleDaySelection = (dayValue) => {
    const currentDays = getValues("selectedDays") || [];
    const maxDays = parseInt(sessionsPerWeek) || 0;
    
    if (currentDays.includes(dayValue)) {
      // Remove day if already selected
      const newDays = currentDays.filter(day => day !== dayValue);
      setValue("selectedDays", newDays);
    } else if (currentDays.length < maxDays) {
      // Add day if limit not reached
      setValue("selectedDays", [...currentDays, dayValue]);
    }
  };

  const onSubmit = async (data) => {
    console.log("data", data);
    setIsSubmitting(true);
    try {
      const response = await Client.put(`/course/update?id=${originalValue.id}`, data, {
        headers: {
          "Content-Type": "multipart/form-data",
        },
      });

      if (response.data.success) {
        toast.success("Course updated successfully");
        setShowSuccessDialog(true);
      } else {
        toast.error(response.data.message || "Failed to update course");
      }
    } catch (err) {
      console.error("Error updating course:", err);
      toast.error("An error occurred while updating the course.");
    } finally {
      setIsSubmitting(false);
    }
  };

  const FormSection = ({ title, icon, children }) => (
    <Card
      elevation={3}
      sx={{
        mb: 3,
        alignItems: 'center',
        borderRadius: 3,
        border: '1px solid',
        borderColor: 'divider',
        boxShadow: "none"
      }}
    >
      <CardContent sx={{ p: 3, alignItems: "center" }} >
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          {icon}
          <Typography variant="h6" sx={{ ml: 1, fontWeight: 600, color: 'primary.main' }}>
            {title}
          </Typography>
        </Box>
        <Divider sx={{ mb: 3 }} />
        <Box className="main-for-children-body" sx={{ alignItems: "center" }}>
          {children}
        </Box>
      </CardContent>
    </Card>
  );

  // Day Selection Component
  const DaySelectionComponent = () => (
    <Controller
      name="selectedDays"
      control={control}
      render={({ field, fieldState: { error } }) => (
        <FormControl fullWidth error={!!error}>
          <FormLabel sx={{ mb: 2, fontWeight: 600, color: 'text.primary' }}>
            Select Days for Sessions
          </FormLabel>
          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 1 }}>
            {daysOfWeek.map((day) => {
              const isSelected = (field.value || []).includes(day.value);
              const isDisabled = !isSelected && (field.value || []).length >= (parseInt(sessionsPerWeek) || 0);
              
              return (
                <Chip
                  key={day.value}
                  label={day.label}
                  onClick={() => handleDaySelection(day.value)}
                  variant={isSelected ? "filled" : "outlined"}
                  color={isSelected ? "primary" : "default"}
                  disabled={isDisabled}
                  sx={{
                    cursor: isDisabled ? 'not-allowed' : 'pointer',
                    '&:hover': {
                      backgroundColor: isSelected ? 'primary.dark' : 'action.hover',
                    },
                    '&.Mui-disabled': {
                      opacity: 0.5,
                    },
                    transition: 'all 0.2s ease-in-out',
                  }}
                />
              );
            })}
          </Box>
          <FormHelperText>
            {sessionsPerWeek > 0 
              ? `Select ${sessionsPerWeek} day${sessionsPerWeek > 1 ? 's' : ''} for your sessions (${(field.value || []).length}/${sessionsPerWeek} selected)`
              : "Please set sessions per week first"
            }
          </FormHelperText>
          {error && (
            <FormHelperText error>
              {error.message}
            </FormHelperText>
          )}
        </FormControl>
      )}
    />
  );

  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      <Paper
        elevation={6}
        sx={{
          p: 4,
          borderRadius: 4,
          position: 'relative',
          overflow: 'hidden',
          '&::before': {
            content: '""',
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            height: '6px',
            background: 'linear-gradient(90deg, #667eea 0%, #764ba2 100%)',
          }
        }}
      >
        <Box sx={{ textAlign: 'center', mb: 4 }}>
          <Typography
            variant="h4"
            component="h1"
            sx={{
              fontWeight: 700,
              background: 'linear-gradient(45deg, #667eea, #764ba2)',
              backgroundClip: 'text',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
              mb: 1
            }}
          >
            Edit Course
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Update the course details to keep your content fresh and engaging
          </Typography>
        </Box>

        <form onSubmit={handleSubmit(onSubmit)}>
          <Fade in={true} timeout={600}>
            <Box>
              {/* Basic Information Section */}
              <FormSection title="Basic Information" icon={<Info color="primary" />}>
                <Grid container spacing={3}>
                  <Grid item xs={12} md={6}>
                    <FormTextField
                      name="title"
                      control={control}
                      title="Course Title"
                      required
                      placeholder="Enter an engaging course title"
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <FormTextField
                      name="description"
                      control={control}
                      title="Course Description"
                      multiline
                      rows={4}
                      placeholder="Provide a detailed description that highlights what students will learn"
                      required
                    />
                  </Grid>
                </Grid>
              </FormSection>

              {/* Course Schedule Section */}
              <FormSection title="Course Schedule" icon={<Schedule color="primary" />}>
                <Grid container spacing={3}>
                  <Grid item xs={12}>
                    <FormTextField
                      name="sessionDuration"
                      control={control}
                      title="Session Duration (In hours)"
                      type="number"
                      placeholder="e.g., 2"
                      InputProps={{
                        inputMode: 'decimal',
                        pattern: '^\\d*(\\.\\d{0,2})?$',
                      }}
                    />
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <FormTextField
                      name="startDate"
                      control={control}
                      title="Course Start Date"
                      type="date"
                      required
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <FormTextField
                      name="endDate"
                      control={control}
                      title="Course End Date"
                      type="date"
                      required
                      inputProps={{
                        min:
                          watch("startDate") ||
                          (!edit && new Date().toISOString().split("T")[0]),
                      }}
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <FormTimePicker
                      name="sessionStartTime"
                      control={control}
                      title="Session Start Time"
                      required
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <FormTimePicker
                      name="sessionEndTime"
                      control={control}
                      title="Session End Time"
                      required
                    />
                  </Grid>
                  <Grid item xs={12} md={4}>
                    <FormTextField
                      name="courseDuration"
                      control={control}
                      title="Course Duration (In weeks)" 
                      placeholder="e.g., 8 weeks"
                      isnumber={true}
                    />
                  </Grid>
                  <Grid item xs={12} md={4}>
                    <FormNumberField
                      name="totalSessions"
                      control={control}
                      title="Total Sessions"
                      placeholder="e.g., 16"
                      isnumber={true}
                      type="number"
                    />
                  </Grid>
                  <Grid item xs={12} md={4}>
                    <FormNumberField
                      name="sessionsPerWeek"
                      control={control}
                      title="Sessions Per Week"
                      placeholder="e.g., 2"
                      isnumber={true}
                      type="number"
                    />
                  </Grid>
                </Grid>
              </FormSection>

              {/* Day Selection Section - Only show if sessionsPerWeek is set */}
              {sessionsPerWeek > 0 && !loading && (
                <FormSection title="Session Days" icon={<CalendarToday color="primary" />}>
                  <Grid container spacing={3}>
                    <Grid item xs={12}>
                      <DaySelectionComponent />
                    </Grid>
                  </Grid>
                </FormSection>
              )}

              {/* Registration Period Section */}
              <FormSection title="Registration Period" icon={<School color="primary" />}>
                <Grid container spacing={3}>
                  <Grid item xs={12} md={6}>
                    <FormTextField
                      name="registrationStartDate"
                      control={control}
                      title="Registration Opens"
                      type="date"
                      required
                      inputProps={{
                        min: new Date(new Date().getFullYear(), new Date().getMonth()-2, 1).toISOString().split("T")[0],
                        max: watch("startDate"),
                      }}
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <FormTextField
                      name="registrationEndDate"
                      control={control}
                      title="Registration Closes"
                      type="date"
                      required
                      inputProps={{
                        min:
                          watch("registrationStartDate") ||
                          (!edit && new Date().toISOString().split("T")[0]),
                        max: watch("startDate"),
                      }}
                    />
                  </Grid>
                </Grid>
              </FormSection>

              {/* Pricing Section */}
              <FormSection title="Pricing & Payment" icon={<Payment color="primary" />}>
                <Grid container spacing={3} sx={{ alignItems: "center" }}>
                  <Grid item xs={12} md={6}>
                    <FormNumberField
                      name="courseFee"
                      control={control}
                      title="Course Fee "
                      placeholder="Enter amount in INR"
                      type="number"
                      required
                      isnumber={true}
                      InputProps={{
                        startAdornment: <Typography color="text.secondary">₹</Typography>,
                      }}
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <FormSelectField
                      style={{mb:2,mt:2}}
                      name="paymentStructure"
                      control={control}
                      title="Payment Structure"
                      placeholder="Select Payment Structure"
                      options={[
                        { label: "One-Time Payment", value: "one-time" },
                        { label: "Monthly Installments", value: "monthly" },
                        { label: "Weekly Payments", value: "weekly" },
                        { label: "Hourly Rate", value: "hourly" },
                      ]}
                      required
                    />
                  </Grid>
                </Grid>
              </FormSection>

              {/* Submit Button */}
              <Box sx={{ mt: 4, textAlign: 'center' }}>
                <Button
                  variant="contained"
                  type="submit"
                  size="large"
                  disabled={isSubmitting || loading}
                  sx={{
                    px: 6,
                    py: 2,
                    borderRadius: 3,
                    fontSize: '1.1rem',
                    fontWeight: 600,
                    background: 'linear-gradient(45deg, #667eea, #764ba2)',
                    boxShadow: '0 4px 15px rgba(102, 126, 234, 0.3)',
                    '&:hover': {
                      background: 'linear-gradient(45deg, #5a67d8, #6b46c1)',
                      boxShadow: '0 6px 20px rgba(102, 126, 234, 0.4)',
                      transform: 'translateY(-2px)',
                    },
                    '&:disabled': {
                      background: 'rgba(0, 0, 0, 0.12)',
                    }
                  }}
                >
                  {isSubmitting ? 'Updating Course...' : 'Update Course'}
                </Button>
              </Box>
            </Box>
          </Fade>
        </form>

        {/* Success Dialog */}
        <Dialog
          open={showSuccessDialog}
          onClose={() => setShowSuccessDialog(false)}
          TransitionComponent={Zoom}
          maxWidth="sm"
          fullWidth
        >
          <DialogTitle sx={{ textAlign: 'center', pt: 3 }}>
            <CheckCircle color="success" sx={{ fontSize: 48, mb: 1 }} />
            <Typography variant="h5" component="div" sx={{ fontWeight: 600 }}>
              Course Updated Successfully!
            </Typography>
          </DialogTitle>
          <DialogContent sx={{ textAlign: 'center', pb: 1 }}>
            <DialogContentText sx={{ fontSize: '1.1rem' }}>
              Your course has been updated and the changes are now live.
            </DialogContentText>
          </DialogContent>
          <DialogActions sx={{ justifyContent: 'center', p: 3, gap: 2 }}>
            <Button
              onClick={() => navigate("/dashboard/courses")}
              variant="contained"
              sx={{ px: 3, py: 1.5, borderRadius: 2 }}
            >
              View All Courses
            </Button>
            <Button
              onClick={() => navigate('/')}
              variant="outlined"
              sx={{ px: 3, py: 1.5, borderRadius: 2 }}
            >
             Home Page
            </Button>
          </DialogActions>
        </Dialog>
      </Paper>
    </Container>
  );
};

export default EditCourseForm;