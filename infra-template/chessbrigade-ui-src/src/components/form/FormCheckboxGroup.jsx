import React from "react";
import {
  Typo<PERSON>,
  FormControlLabel,
  Checkbox,
  Box,
  Stack,
} from "@mui/material";
import { Controller } from "react-hook-form";

/**
 * FormCheckboxGroup - A checkbox group component for React Hook Form
 *
 * @param {Object} props - Component props
 * @param {string} props.name - Field name
 * @param {Object} props.control - React Hook Form control object
 * @param {string} props.title - Label for the checkbox group
 * @param {Array} props.options - Array of options with value and label properties
 * @param {boolean} props.required - Whether the field is required
 * @param {Object} props.rules - Additional validation rules
 * @param {Object} props.sx - Additional styles for the component
 * @param {Object} props.direction - Direction of the checkbox group (row or column)
 */
const FormCheckboxGroup = ({
  name,
  control,
  title,
  options,
  required = false,
  rules = {},
  sx = {},
  direction = { xs: "column", sm: "row" },
  spacing = { xs: 1, sm: 2 },
}) => {
  // Combine required rule with other rules
  const validationRules = required
    ? { required: `${title} is required`, ...rules }
    : rules;

  return (
    <Box sx={{ mb: 2, display: "flex", ...sx }}>
      {title && (
        <Typography
          variant="h6"
          sx={{
            textAlign: "start",
            p: "0px !important",
            textWrap: "nowrap",
            fontSize: 20,
            m: "0px !important",
            mb: { xs: 2, sm: 0 },
          }}
        >
          {title}
          {required && <span style={{ color: "red" }}>*</span>}
        </Typography>
      )}

      <Controller
        name={name}
        control={control}
        rules={validationRules}
        render={({ field }) => (
          <Stack
            direction={direction}
            spacing={spacing}
            sx={{ width: "100%", flexWrap: "wrap" }}
          >
            {options.map((option) => (
              <FormControlLabel
                key={option.value}
                control={
                  <Checkbox
                    checked={
                      Array.isArray(field.value) &&
                      field.value.includes(option.value)
                    }
                    onChange={(e) => {
                      const checked = e.target.checked;
                      const newValue = Array.isArray(field.value)
                        ? [...field.value]
                        : [];

                      if (checked) {
                        if (!newValue.includes(option.value)) {
                          newValue.push(option.value);
                        }
                      } else {
                        const index = newValue.indexOf(option.value);
                        if (index !== -1) {
                          newValue.splice(index, 1);
                        }
                      }

                      field.onChange(newValue);
                    }}
                    sx={{
                      color: "hsla(0, 3%, 80%, 1)",
                      "&.Mui-checked": {
                        color: "black",
                      },
                      "&.MuiCheckbox-indeterminate": {
                        color: "hsla(0, 3%, 80%, 1)",
                      },
                    }}
                  />
                }
                label={option.label}
              />
            ))}
          </Stack>
        )}
      />
    </Box>
  );
};

export default FormCheckboxGroup;
