import React from "react";
import { Typography, Grid, Box, Stack } from "@mui/material";
import FormNumberField from "./FormNumberField";
import FormSelectField from "./FormSelectField";
import { useTranslation } from "../../context/TranslationContext";

const FormPrizeDetails = ({ control, setValue, watch }) => {
  const { translate } = useTranslation();

  return (
    <>
      <Typography
        variant="h4"
        fontWeight="500"
        sx={{ mb: 3, textAlign: "start", fontWeight: "bold" }}
      >
        {translate('prizeDetails', 'Prize Details')}:
      </Typography>

      <Box sx={{ mb: 4 }}>
        <Grid
          container
          spacing={4}
          sx={{ ".MuiGrid-item": { pt: "8px !important" } }}
        >
          <Grid item xs={12} md={4}>
            <FormNumberField
              name="numberOfTrophiesMale"
              control={control}
              title={translate('numberOfTrophiesMale', "Number Of Trophies for Male")}
              placeholder={translate('enterNumberOfTrophies', "Enter Number Of Trophies")}
              required
              minLength={1}
              maxLength={4}
              rules={{
                required: translate('numberOfTrophiesRequired', "Number of trophies is required"),
              }}
            />
          </Grid>
          <Grid item xs={12} md={4}>
            <FormNumberField
              name="numberOfTrophiesFemale"
              control={control}
              title={translate('numberOfTrophiesFemale', "Number Of Trophies for Female")}
              placeholder={translate('enterNumberOfTrophies', "Enter Number Of Trophies")}
              required
              minLength={1}
              maxLength={4}
              rules={{
                required: translate('numberOfTrophiesRequired', "Number of trophies is required"),
              }}
            />
          </Grid>
          <Grid item xs={12} md={4}>
            <Typography variant="h6" sx={{ textAlign: "start" }}>
              {translate('prizeAmount', 'Prize Amount')}<span style={{ color: "red" }}>*</span>
            </Typography>
            <Stack
              sx={{
                display: "flex",
                flexDirection: "row",
                gap: 1,
                justifyContent: "center",
                alignItems: "flex-start",
              }}
            >
              <FormSelectField
                name="totalCashPrizeCurrency"
                control={control}
                options={[{ value: "INR", label: "INR" }]}
                sx={{ width: "100px", mt: "4px" }}
              />

              <FormNumberField
                name="totalCashPrizeAmount"
                control={control}
                placeholder={translate('enterAmount', "Enter Amount")}
                minLength={1}
                maxLength={10}
                rules={{
                  required: translate('totalCashPrizeAmountRequired', "Total cash prize amount is required"),
                }}
              />
            </Stack>
          </Grid>
        </Grid>
      </Box>
    </>
  );
};

export default FormPrizeDetails;
