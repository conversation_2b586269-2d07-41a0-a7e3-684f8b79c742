import React from "react";
import { Typography, Grid, Box, Stack } from "@mui/material";
import FormTextField from "./FormTextField";
import FormRadioField from "./FormRadioField";
import FormSelectField from "./FormSelectField";
import FormDatePicker from "./FormDatePicker";
import FormTimePicker from "./FormTimePicker";
import FormFieldDownText from "./FormFieldDownText";
import FormN<PERSON>berField from "./FormNumberField";
import FormInputSelectField from "./FormInputSelectField";
import { useTranslation } from "../../context/TranslationContext";

const FormTournamentDetails = ({ control, watch, edit = false, setValue }) => {
  const { translate } = useTranslation();

  return (
    <>
      <Typography
        variant="h4"
        fontWeight="500"
        sx={{ mb: 3, textAlign: "start", fontWeight: "bold" }}
      >
        {translate('tournamentDetails', 'Tournament Details')}:
      </Typography>

      <Box sx={{ mb: 4, mt: 8 }}>
        <Grid
          container
          spacing={4}
          sx={{ ".MuiGrid-item": { pt: "8px !important" } }}
        >
          <Grid item xs={12} md={6}>
            <FormTextField
              name="title"
              control={control}
              title={translate('tournamentTitle', 'Tournament Title')}
              placeholder={translate('enterTournamentTitle', 'Enter Tournament Title')}
              maxLength={250}
              required
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <FormTextField
              name="subTitle"
              control={control}
              title={translate('subTitle', 'Sub Title')}
              placeholder={translate('enterSubTitle', 'Enter Sub Title')}
              maxLength={250}
              specialCharAllowed={true}
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <FormTextField
              name="presentedBy"
              control={control}
              title={translate('presentedBy', "Presented By")}
              placeholder={translate('enterPresentedBy', "Enter Presented By")}
              maxLength={250}
              specialCharAllowed={true}
            />
          </Grid>

          <Grid item xs={12} md={6}>
            <FormRadioField
              name="fideRated"
              control={control}
              title={translate('fideRated', "FIDE Rated")}
              options={[
                { value: true, label: translate('yes', "Yes") },
                { value: false, label: translate('no', "No") },
              ]}
            />
          </Grid>

          <Grid item xs={12} md={6}>
            <FormTextField
              name="organizerName"
              control={control}
              title={translate('organizerName', "Organizer Name")}
              maxLength={50}
              placeholder={translate('enterOrganizerName', "Enter Organizer Name")}
              required
            />
          </Grid>

          <Grid item xs={12} md={6}>
            <FormSelectField
              name="tournamentLevel"
              control={control}
              title={translate('tournamentLevel', "Tournament Level")}
              placeholder={translate('selectTournamentLevel', "Select Tournament Level")}
              required
              options={[
                { value: "state", label: translate('state', "State") },
                { value: "national", label: translate('national', "National") },
                { value: "district", label: translate('district', "District") },
                { value: "global", label: translate('global', "Global") },
              ]}
            />
          </Grid>

          <Grid item xs={12} md={6}>
            <Typography variant="h6" sx={{ textAlign: "start" }}>
              {translate('tournamentDates', 'Tournament Dates')}<span style={{ color: "red" }}>*</span>
            </Typography>
            <Stack direction="row" spacing={2}>
              <Box sx={{ width: "50%" }}>
                <FormDatePicker
                  name="startDate"
                  title={translate('startDate', "Start Date")}
                  control={control}
                  required
                  inputProps={{
                    min: !edit && new Date(new Date().getFullYear(), new Date().getMonth() - 2, 1).toISOString().split("T")[0],
                    max: watch("startDate"),
                  }}
                />
              </Box>
              <Box sx={{ width: "50%" }}>
                <FormDatePicker
                  name="endDate"
                  title={translate('endDate', "End Date")}
                  control={control}
                  required
                  inputProps={{
                    min:
                      watch("startDate") ||
                      (!edit && new Date().toISOString().split("T")[0]),
                  }}
                />
              </Box>
            </Stack>
          </Grid>
          <Grid item xs={12} md={6}>
            <FormTimePicker
              name="reportingTime"
              control={control}
              title={translate('reportingTime', "Reporting Time")}
              required
            />
          </Grid>

          <Grid item xs={12} md={6}>
            <Typography variant="h6" sx={{ textAlign: "start" }}>
              {translate('registrationDates','Registration Dates')}<span style={{ color: "red" }}>*</span>
            </Typography>
            <Stack direction="row" spacing={2}>
              <Box sx={{ width: "50%" }}>
                <FormDatePicker
                  name="registrationStartDate"
                  title={translate('startDate') || "Start Date"}
                  control={control}
                  required
                  inputProps={{
                    min: !edit && new Date(new Date().getFullYear(), new Date().getMonth() - 2, 1).toISOString().split("T")[0],
                    max: watch("startDate"),
                  }}
                />
              </Box>
              <Box sx={{ width: "50%" }}>
                <FormDatePicker
                  name="registrationEndDate"
                  title={translate('endDate') || "End Date"}
                  control={control}
                  required
                  inputProps={{
                    min:
                      watch("registrationStartDate") ||
                      (!edit && new Date().toISOString().split("T")[0]),
                    max: watch("startDate"),
                  }}
                />
              </Box>
            </Stack>
          </Grid>
          <Grid item xs={12} md={6}>
            <FormTimePicker
              name="registrationEndTime"
              control={control}
              title={translate('registrationEndTime', "Registration End Time")}
              required
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <Typography variant="h6" sx={{ textAlign: "start" }}>
              {translate('registrationFees', 'Registration Fees')}<span style={{ color: "red" }}>*</span>
            </Typography>
            <Stack
              sx={{
                display: "flex",
                flexDirection: "row",
                gap: 1,
                justifyContent: "center",
                alignItems: "flex-start",
              }}
            >
              <FormSelectField
                name="entryFeeCurrency"
                control={control}
                options={[{ value: "INR", label: "INR" }]}
                sx={{ width: "100px", mt: "4px" }}
              />

              <FormNumberField
                name="entryFee"
                control={control}
                placeholder={translate('enterAmount') || "Enter Amount"}
                minLength={1}
                maxLength={10}
                rules={{
                  required: translate('entryFeeRequired', "Entry fee is required"),
                }}
              />
            </Stack>
          </Grid>
          <Grid item xs={12} md={6}>
            <FormSelectField
              name="tournamentCategory"
              control={control}
              title={translate('tournamentCategory', "Tournament Category")}
              placeholder={translate('selectTournamentCategory', "Select Tournament Category")}
              required
              options={[
                { value: "open", label: translate('open', "Open") },
                { value: "male", label: translate('male', "Male") },
                { value: "female", label: translate('female', "Female")},
                { value: "children", label: translate('children', "Children") },
                { value: "children-open", label: translate('childrenAndOpen', "Children & Open") },
              ]}
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <FormFieldDownText
              name="maleAgeCategory"
              control={control}
              title={translate('maleAgeCategory', "Male Age Category")}
              required={watch("tournamentCategory") !== "female"}
              setValue={(value) => {
                setValue("maleAgeCategory", value);
              }}
              disabled={watch("tournamentCategory") === "female"}
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <FormFieldDownText
              name="femaleAgeCategory"
              control={control}
              title={translate('femaleAgeCategory', "Female Age Category")}
              setValue={(value) => {
                setValue("femaleAgeCategory", value);
              }}
              required={watch("tournamentCategory") !== "male"}
              disabled={watch("tournamentCategory") === "male"}
            />
          </Grid>

          <Grid item xs={12} md={6}>
            <Grid container spacing={2}>
              <Grid item xs={12} container spacing={1}>
                <Grid item xs={12} sm={4}>
                  <FormSelectField
                    name="timeControl"
                    control={control}
                    title={translate('timeControl', "Time Control")}
                    required
                    options={[
                      { value: "classical", label: translate('classical', "Classical") },
                      { value: "rapid", label: translate('rapid', "Rapid") },
                      { value: "bullet", label: translate('bullet', "Bullet") },
                      { value: "blitz", label: translate('blitz', "Blitz")},
                    ]}
                  />
                </Grid>
                <Grid item xs={12} sm={4}>
                  <FormInputSelectField
                    name="timeControlDuration"
                    control={control}
                    key={"duration"}
                    title={translate('duration', "Duration")}
                    placeholder={translate('enterDuration', "Enter Duration")}
                    required
                    max={200}
                    options={[
                      { value: "mins", label: translate('mins', "Mins") },
                      { value: "secs", label: translate('secs', "Secs") },
                    ]}
                  />
                </Grid>

                <Grid item xs={12} sm={4}>
                  <FormInputSelectField
                    name="timeControlIncrement"
                    key={"increment"}
                    control={control}
                    title={translate('increment', "Increment")}
                    placeholder={translate('enterIncrement', "Enter Increment")}

                    max={200}
                    options={[
                      { value: "mins", label: translate('mins') || "Mins" },
                      { value: "secs", label: translate('secs') || "Secs" },
                    ]}
                  />
                </Grid>
              </Grid>

              <Grid item xs={12} sx={{ display: "flex", gap: 2 }}>
                <Grid item xs={6}>
                  <FormSelectField
                    name="tournamentSystem"
                    control={control}
                    title={translate('tournamentSystem', "Tournament System")}
                    required
                    options={[
                      { value: "swiss-system", label: translate('swissSystem', "Swiss System")},
                      { value: "round-robin", label: translate('roundRobin', "Round Robin")},
                      { value: "knockout", label: translate('knockout', "Knockout") },
                    ]}
                  />
                </Grid>
                <Grid item xs={6}>
                  <FormSelectField
                    name="tournamentType"
                    control={control}
                    required
                    title={translate('tournamentType', "Tournament Type")}
                    options={[
                      { value: "individual", label: translate('individual', "Individual")},
                      { value: "team", label: translate('team', "Team") },
                    ]}
                  />
                </Grid>
              </Grid>

              <Grid item xs={12}>
                <FormTextField
                  name="chatUrl"
                  control={control}
                  title={translate('whatsappGroupUrl', "Whatsapp Group Url")}
                  placeholder={translate('enterWhatsappGroupUrl', "Enter Whatsapp Group Url")}
                  specialCharAllowed={true}
                />
              </Grid>
            </Grid>
          </Grid>
          <Grid item xs={12} md={6}>
            <Grid container spacing={2}>
              <Grid item xs={12}>
                <FormTextField
                  name="nationalApproval"
                  control={control}
                  maxLength={50}
                  title={translate('nationalApproval', "National Approval")}
                  placeholder={translate('enterNationalApprovalNo', "Enter National Approval No")}
                  required={watch("tournamentLevel") === "national"}
                  specialCharAllowed={true}
                />
              </Grid>
              <Grid item xs={12}>
                <FormTextField
                  name="stateApproval"
                  control={control}
                  maxLength={50}
                  title={translate('stateApproval', "State Approval")}
                  placeholder={translate('enterStateApprovalNo', "Enter State Approval No")}
                  required={watch("tournamentLevel") === "state"}
                  specialCharAllowed={true}
                />
              </Grid>
              <Grid item xs={12}>
                <FormTextField
                  name="districtApproval"
                  control={control}
                  maxLength={50}
                  title={translate('districtApproval', "District Approval")}
                  placeholder={translate('enterDistrictApprovalNo', "Enter District Approval No")}
                  required={watch("tournamentLevel") === "district"}
                  specialCharAllowed={true}
                />
              </Grid>
              <Grid item xs={12}>
                <FormTextField
                  name="fideApproval"
                  control={control}
                  maxLength={50}
                  title={translate('fideApproval', "FIDE Approval")}
                  placeholder={translate('enterFideApprovalNo', "Enter FIDE Approval No")}
                  required={watch("tournamentLevel") === "global"}
                  specialCharAllowed={true}
                />
              </Grid>
            </Grid>
          </Grid>
        </Grid>
      </Box>
    </>
  );
};

export default FormTournamentDetails;
