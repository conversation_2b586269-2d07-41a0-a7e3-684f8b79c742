import React from "react";
import { Grid, Typography, Stack, Box } from "@mui/material";
import FormRadioField from "./FormRadioField";
import FormCheckboxGroup from "./FormCheckboxGroup";
import { useTranslation } from "../../context/TranslationContext";

/**
 * FormOtherDetails - A form component for other tournament details using React Hook Form
 *
 * @param {Object} props - Component props
 * @param {Object} props.control - React Hook Form control object
 */
const FormOtherDetails = ({ control }) => {
  const { translate } = useTranslation();

  // Food facility options
  const foodOptions = [
    { value: "breakfast", label: translate('breakfast', "Breakfast") },
    { value: "lunch", label: translate('lunch', "Lunch") },
    { value: "dinner", label: translate('dinner', "Dinner") },
    { value: "snacks", label: translate('snacks', "Snacks") },
    { value: "beverages", label: translate('beverages', "Beverages") },
  ];

  return (
    <>
      <Typography
        variant="h4"
        fontWeight="500"
        sx={{ mt: 6, mb: 3, textAlign: "start", fontWeight: "bold" }}
      >
        {translate('otherDetails', 'Others')}:
      </Typography>
      <Grid container spacing={4}>
        <Grid item xs={12} md={6}>
          <FormRadioField
            name="chessboardProvided"
            control={control}
            title={translate('chessboardProvided', 'Chess Board Provided')}
            options={[
              { value: true, label: translate('yes', "Yes") },
              { value: false, label: translate('no', "No") },
            ]}
          />
        </Grid>

        <Grid item xs={12}>
          <FormRadioField
            name="timerProvided"
            control={control}
            title={translate('timerProvided', 'Timer Provided')}
            options={[
              { value: true, label: translate('yes', "Yes") },
              { value: false, label: translate('no', "No") },
            ]}
          />
        </Grid>

        <Grid item xs={12}>
          <FormRadioField
            name="spotEntry"
            control={control}
            title={translate('spotEntry', 'Spot Entry')}
            options={[
              { value: true, label: translate('yes', "Yes") },
              { value: false, label: translate('no', "No") },
            ]}
          />
        </Grid>

        <Grid item xs={12}>
          <FormRadioField
            name="parkingFacility"
            control={control}
            title={translate('parkingFacility', 'Parking Facility')}
            options={[
              { value: "no", label: translate('parkingNotAvailable', "Parking Not Available") },
              { value: "limited", label: translate('twoWheelerOnly', "2 Wheeler Only") },
              { value: "yes", label: translate('twoAndFourWheeler', "2 & 4 Wheeler") },
            ]}
          />
        </Grid>

        <Grid item xs={12}>
          <FormCheckboxGroup
            name="foodFacility"
            control={control}
            title={translate('foodFacility', 'Food Facility')}
            options={foodOptions}
            direction={{ xs: "column", sm: "row" }}
            spacing={{ xs: 1, sm: 2 }}
          />
        </Grid>
      </Grid>
    </>
  );
};

export default FormOtherDetails;
