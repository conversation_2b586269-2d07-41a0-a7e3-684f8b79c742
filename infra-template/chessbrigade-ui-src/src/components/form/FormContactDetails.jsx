import { Typography, Grid, Box, Stack } from "@mui/material";
import FormTextField from "./FormTextField";
import FormPhoneInput from "./FormPhoneInput";
import FormAutocomplete from "./FormAutocomplete";
import ArbiterAutocomplete from "./AribiterAutocomplete";
import { useTranslation } from "../../context/TranslationContext";

const FormContactDetails = ({ control, watch, }) => {
  const { translate } = useTranslation();

  return (
    <>
      {" "}
      <Typography
        variant="h4"
        fontWeight="500"
        sx={{ mb: 3, textAlign: "start", fontWeight: "bold" }}
      >
        {translate('contactDetails', 'Contact Details')}:
      </Typography>
      <Box sx={{ mb: 4 }}>
        <Grid
          container
          spacing={4}
          sx={{ ".MuiGrid-item": { pt: "8px !important" } }}
        >
          <Grid item xs={12} md={6}>
            <FormTextField
              name="contact<PERSON>ersonName"
              maxLength={50}
              control={control}
              title={translate('contactPersonName', "Contact Person Name")}
              placeholder={translate('enterContactPersonName', "Enter Contact Person Name")}
              required
            />
          </Grid>

          <Grid item xs={12} md={6}>
            <FormTextField
              name="email"
              maxLength={100}
              control={control}
              title={translate('email', "Email")}
              placeholder={translate('enterEmail', "Enter Email Address")}
              type="email"
              specialCharAllowed={true}
            />
          </Grid>

          <Grid item xs={12} md={6}>
            <FormPhoneInput
              name="contactNumber"
              control={control}
              title={translate('contactNumber', "Contact Number")}
              placeholder={translate('enterContactNumber', "Enter Contact Number")}
              required
            />
          </Grid>

          <Grid item xs={12} md={6}>
            <FormPhoneInput
              name="alternateContactNumber"
              control={control}
              title={translate('alternateContactNumber', "Alternate Contact")}
              placeholder={translate('enterAlternateContactNumber', "Enter Alternate Contact")}
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <FormTextField
              name="tournamentDirectorName"
              maxLength={50}
              control={control}
              title={translate('tournamentDirectorName', "Tournament Director Name")}
              placeholder={translate('enterTournamentDirectorName', "Enter Director Name")}
            />
          </Grid>
          {/* <Grid item xs={12} md={6}>
            {!arbiterIsSelected ? (
              <ArbiterAutocomplete
                name="chiefArbiterName"
                control={control}
                title={translate('chiefArbiter', "Chief Arbiter")}
                placeholder={translate('searchForChiefArbiter', "Search for a chief arbiter")}
                required={true}
                defaultValue={{ name: null, id: null }}
                rules={{
                  required: translate('pleaseSelectChiefArbiter', "Please select a chief arbiter"),
                }}
              />
            ) : (
              // <Typography sx={{ mt: 2 }}>
              //   Chief Arbiter: <strong>{watch("chiefArbiterName.name")}</strong>
              // </Typography>
              <FormTextField
                name="chiefArbiterName"
                maxLength={50}
                control={control}
                value={watch("chiefArbiterName.name")}
                title={translate('arbiterName', "Arbiter Name")}
                placeholder={translate('enterArbiterName', "Enter Arbiter Name")}
                required
                disabled
              />
            )}
          </Grid> */}
        </Grid>
      </Box>
    </>
  );
};

export default FormContactDetails;
