import React from "react";
import {
  Typography,
  RadioGroup,
  FormControlLabel,
  Radio,
  Grid,
  Stack,
  Box,
} from "@mui/material";
import { Controller } from "react-hook-form";
const FormRadioField = React.memo(
  ({ name, control, options, title, required = false, rules, ...rest }) => {
    return (
      <Box sx={{ mb: 2,display:"flex",gap:2 ,alignItems:"center",direction:{xs:"column",sm:"row" } }}>
        <Typography
          variant="h6"
          sx={{ textAlign: "start", p: "0px !important", m: "0px !important" ,fontSize: 20}}
        >
          {title} {required && <span style={{ color: "red" }}>*</span>}
        </Typography>
        <Controller
          name={name}
          control={control}
          rules={rules}
          render={({ field }) => (
            <RadioGroup
              {...field}
              row
              sx={{ display: "inline-flex", ...rest?.sx }}
            >
              {options.map((option) => (
                <FormControlLabel
                  key={option.value}
                  value={option.value}
                  control={
                    <Radio
                      sx={{
                        color: "hsla(0, 3%, 80%, 1)",
                        "&.Mui-checked": {
                          color: "black",
                        },
                      }}
                    />
                  }
                  label={option.label}
                />
              ))}
            </RadioGroup>
          )}
        />
      </Box>
    );
  }
);

export default FormRadioField;
