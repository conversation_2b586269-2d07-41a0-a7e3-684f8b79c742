import React, { useEffect, useState } from "react";
import {
  Box,
  Button,
  Typography,
  FormControl,
  Select,
  MenuItem,
  IconButton,
  Alert,
  Tooltip,
  Stack,
  TextField,
  Radio,
  RadioGroup,
  FormControlLabel,
  FormLabel,
} from "@mui/material";
import { CloudUpload, Delete, Visibility, Info, Link, Timer } from "@mui/icons-material";
import { useForm, Controller } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { Client } from "../../api/client";
import UseToast from "../../lib/hooks/UseToast";
import ImagePreview from "./ImagePreview";

const fileFormats = [{ key: "image", value: "Banner" }, { key: "video", value: "Video" }];

// YouTube URL validation regex
const youtubeUrlRegex = /^(https?:\/\/)?(www\.)?(youtube\.com\/(watch\?v=|embed\/)|youtu\.be\/)([a-zA-Z0-9_-]+)([?&_-][\w=]*)*$/;

// URL validation regex for banner links
const urlRegex = /^(https?:\/\/)?((localhost|\d{1,3}(\.\d{1,3}){3}|([\da-z\.-]+\.[a-z\.]{2,})))(:\d+)?(\/[\w\-\.~:\/?#\[\]@!$&'()*+,;=]*)?$/;

// Schema
const formSchema = z.object({
  type: z.enum(["banner", 'video'], {
    required_error: "User type is required",
  }),
  format: z.enum(["image", "gif", "video"], {
    required_error: "File format is required",
  }),
  videoSource: z.enum(["upload", "youtube"]).optional().nullable(),
  file: z
    .any()
    .optional(),
  youtubeUrl: z
    .string()
    .optional(),
  bannerLink: z
    .string()
    .optional(),
  videoDuration: z
    .string().max(4),
}).superRefine((data, ctx) => {
  const { format, videoSource, file, youtubeUrl, bannerLink, type, videoDuration } = data;

  // Banner link validation - only required if type is banner and bannerLink is provided
  if (type === "banner") {
    if (!bannerLink || bannerLink.trim() === "") {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "Banner link is required",
        path: ["bannerLink"],
      });
    }
  }

  // Video duration validation - required for all video formats
  if (format === "video") {
    if (!videoDuration || videoDuration.trim() === "") {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "Video duration is required",
        path: ["videoDuration"],
      });
    } else {
      const duration = parseFloat(videoDuration);
      if (isNaN(duration) || duration <= 0) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: "Please enter a valid duration in minutes",
          path: ["videoDuration"],
        });
      }
    }
  }

  // File validation
  if (format !== "video") {
    // For image/gif formats, file is required
    if (!file || !(file instanceof File)) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "File is required",
        path: ["file"],
      });
    } else {
      // Validate file type
      const allowedTypes = {
        image: ["image/jpeg", "image/png", "image/jpg", "image/webp"],
        gif: ["image/gif"],
      };

      const validTypes = allowedTypes[format] || [];
      if (!validTypes.includes(file.type)) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: `Only ${format === 'image' ? 'JPEG, PNG, JPG, WEBP' : 'GIF'} files are allowed`,
          path: ["file"],
        });
      }
      if (type === 'banner' && file.size > 3 * 1024 * 1024) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: `File size Must under 3MB`,
          path: ["file"],
        });
      }
    }
  } else if (format === "video") {
    // For video format, check video source
    if (videoSource === "upload") {
      if (!file || !(file instanceof File)) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: "Video file is required",
          path: ["file"],
        });
      } else {
        // Validate video file type
        const videoTypes = ["video/mp4", "video/webm", "video/ogg"];
        const maxVideoSizeMB = 50;
        const maxVideoSizeBytes = maxVideoSizeMB * 1024 * 1024;
        if (!videoTypes.includes(file.type)) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: "Only MP4, WEBM, OGG video files are allowed",
            path: ["file"],
          });
        }
        if (file.size > maxVideoSizeBytes) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: `Video must be less than or equal to ${maxVideoSizeMB}MB`,
            path: ["file"],
          });
        }
      }
    } else if (videoSource === "youtube") {
      // YouTube URL validation
      if (!youtubeUrl || youtubeUrl.trim() === "") {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: "YouTube URL is required",
          path: ["youtubeUrl"],
        });
      } else if (!youtubeUrlRegex.test(youtubeUrl)) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: "Please enter a valid YouTube URL",
          path: ["youtubeUrl"],
        });
      }
    }
  }
});

function UploadComponent() {
  const [file, setFile] = useState(null);
  const [loading, setLoading] = useState(false)
  const toast = UseToast();
  const {
    control,
    handleSubmit,
    setValue,
    watch,
    reset,
    formState: { errors },
    trigger,
    clearErrors,
    getValues,
  } = useForm({
    resolver: zodResolver(formSchema),
    defaultValues: {
      type: "",
      format: "",
      videoSource: 'upload', // Set default to upload for video source
      file: null,
      youtubeUrl: "",
      bannerLink: "",
      videoDuration: "",
    },
  });

  const selectedFormat = watch("format");
  const videoSource = watch("videoSource");
  const youtubeUrl = watch("youtubeUrl");
  const files = watch("file");
  const type = watch("type");
  const bannerLink = watch("bannerLink");
  const videoDuration = watch("videoDuration");

  const fileAcceptTypes = {
    image: "image/jpeg,image/png,image/jpg,image/webp",
    gif: "image/gif",
    video: "video/mp4,video/webm,video/ogg",
  };

  useEffect(() => {
    const selected = watch("format");
    if (selected === "video") {
      setValue("type", "video");
      // Clear banner link when switching to video
      setValue("bannerLink", "");
      clearErrors("bannerLink");
    } else if (selected === "image" || selected === "gif") {
      setValue("type", "banner");
      // Clear video duration when switching away from video
      setValue("videoDuration", "");
      clearErrors("videoDuration");
    }
  }, [watch("format")]);

  // Updated onSubmit function to handle different endpoints
  const onSubmit = async (data) => {
    setLoading(true)
    const formData = new FormData();

    let endpoint = '/admin/banner'; // Default endpoint

    if (data.format === "video" && data.videoSource === "youtube") {
      // Extract video ID from YouTube URL
      const videoId = getYouTubeVideoId(youtubeUrl);
      console.log("videoId", videoId)
      const embedUrl = `https://www.youtube.com/embed/${videoId}`;
      console.log("videoId", videoId)

      formData.append("Url", embedUrl); // Keep full URL if backend 
      formData.append("name", 'youtube-video');
      endpoint = '/admin/banner/videos'; // Separate endpoint for YouTube videos
    } else if (data.format === "video" && data.videoSource === "upload") {
      // Video file upload
      formData.append("video", file); // Use 'video' field name for video uploads
      formData.append("name", file.name);
      endpoint = '/admin/banner/videos'; // Separate endpoint for video uploads
    } else {
      // Image/GIF upload (banner)
      formData.append("image", file);
      formData.append("name", file.name);
      endpoint = '/admin/banner'; // Original endpoint for banners
    }

    formData.append("type", type);
    formData.append("format", data.format);

    if (data.format === "video") {
      formData.append("videoSource", data.videoSource);
      // Add video duration to API call
      formData.append("videoDuration", data.videoDuration);
    }

    // Add banner link if provided
    if (data.bannerLink && data.bannerLink.trim() !== "") {
      formData.append("bannerUrl", data.bannerLink);
    }

    console.log('Using endpoint:', endpoint);
    console.log('FormData contents:');
    for (let [key, value] of formData.entries()) {
      console.log(key, value);
    }

    try {
      const response = await Client.post(endpoint, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      if (response.data.success) {
        toast.success("Upload submitted successfully!");
        reset();
        setFile(null);
      }
    } catch (err) {
      console.error(err);
      toast.error(err.response.data.message||"Upload failed");
    } finally {
      setLoading(false)
    }
  };

  const handleFileChange = (event) => {
    const uploadedFile = event.target.files?.[0];

    if (uploadedFile) {
      setValue("file", uploadedFile);
      setFile(uploadedFile);
      clearErrors("file");
      trigger("file");
    }
  };

  const handleFormatChange = (field, newFormat) => {
    field.onChange(newFormat);
    clearErrors("format");

    // Clear file and YouTube URL when format changes
    if (file) {
      setFile(null);
      setValue("file", null);
      clearErrors("file");
    }
    setValue("youtubeUrl", "");
    clearErrors("youtubeUrl");

    // Clear banner link when switching away from banner
    if (newFormat === "video") {
      setValue("bannerLink", "");
      clearErrors("bannerLink");
    }

    // Clear video duration when switching away from video
    if (newFormat !== "video") {
      setValue("videoDuration", "");
      clearErrors("videoDuration");
    }

    // Set default video source to 'upload' when video format is selected
    if (newFormat === "video") {
      setValue("videoSource", "upload");
    }
  };

  const handleVideoSourceChange = (field, newSource) => {
    field.onChange(newSource);

    // Clear file when switching to YouTube
    if (newSource === "youtube" && file) {
      setFile(null);
      setValue("file", null);
      clearErrors("file");
    }

    // Clear YouTube URL when switching to upload
    if (newSource === "upload") {
      setValue("youtubeUrl", "");
      clearErrors("youtubeUrl");
    }
  };

  const clearFile = () => {
    setFile(null);
    setValue("file", null);
    clearErrors("file");
  };

  const clearYouTubeUrl = () => {
    setValue("youtubeUrl", "");
    clearErrors("youtubeUrl");
  };

  const clearBannerLink = () => {
    setValue("bannerLink", "");
    clearErrors("bannerLink");
  };

  const clearVideoDuration = () => {
    setValue("videoDuration", "");
    clearErrors("videoDuration");
  };

  const isFileUploadDisabled = !selectedFormat || (selectedFormat === "video" && videoSource === "youtube");

  const getYouTubeVideoId = (url) => {
    const match = url.match(/(?:youtu\.be\/|youtube\.com\/(?:watch\?v=|embed\/|v\/))([a-zA-Z0-9_-]{11})/);
    return match ? match[1] : null;
  };

  return (
    <Box maxWidth={'80vw'} mx="auto" p={4} bgcolor="background.paper" borderRadius={2}>
      <form onSubmit={handleSubmit(onSubmit)}>
        <Box display="flex" flexDirection="column" gap={3}>
          {/* User Type */}
          <Stack direction={'row'} spacing={2}>
            {/* File Format */}
            <FormControl fullWidth error={!!errors.format}>
              <Typography mb={1} sx={{ display: 'flex', justifyContent: 'flex-start' }} fontWeight={700}>
                Select Type {' '}<Typography color="red">*</Typography>
              </Typography>
              <Controller
                name="format"
                control={control}
                render={({ field }) => (
                  <Select
                    {...field}
                    displayEmpty
                    onChange={(e) => handleFormatChange(field, e.target.value)}
                  >
                    <MenuItem value="">
                      <em>Select Type</em>
                    </MenuItem>
                    {fileFormats.map((format) => (
                      <MenuItem key={format.key} value={format.key}>
                        {format.value}
                      </MenuItem>
                    ))}
                  </Select>
                )}
              />
              {errors.format && (
                <Typography color="error" variant="caption" sx={{ mt: 0.5 }}>
                  {errors.format.message}
                </Typography>
              )}
            </FormControl>

            {/* Banner Link Field - Only show when Banner is selected */}
            <FormControl fullWidth error={!!errors.bannerLink}>
              <Typography mb={1} sx={{ display: 'flex', justifyContent: 'flex-start' }} fontWeight={700}>
                Banner Link
              </Typography>
              <Controller
                name="bannerLink"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    fullWidth
                    placeholder="https://example.com"
                    variant="outlined"
                    disabled={type !== "banner"}
                    error={!!errors.bannerLink}
                    helperText={errors.bannerLink?.message}
                    InputProps={{
                      startAdornment: <Link sx={{ mr: 1, color: type === "banner" ? 'action.active' : 'action.disabled' }} />,
                      endAdornment: field.value && type === "banner" && (
                        <IconButton
                          size="small"
                          onClick={clearBannerLink}
                          sx={{ mr: 1 }}
                        >
                          <Delete />
                        </IconButton>
                      )
                    }}
                    onChange={(e) => {
                      field.onChange(e);
                      clearErrors("bannerLink");
                    }}
                    sx={{
                      '& .MuiInputBase-input': {
                        opacity: type === "banner" ? 1 : 0.5,
                      },
                      '& .MuiOutlinedInput-root': {
                        '& fieldset': {
                          borderColor: type === "banner" ? undefined : 'action.disabled',
                        },
                      },
                    }}
                  />
                )}
              />
              {type !== "banner" && (
                <Typography variant="caption" color="text.secondary" sx={{ mt: 0.5 }}>
                  Banner link is only available when Banner type is selected
                </Typography>
              )}
            </FormControl>
          </Stack>

          {/* Video Source Selection - Only show when Video is selected */}
          {selectedFormat === "video" && (
            <FormControl component="fieldset">
              <FormLabel component="legend" sx={{ fontWeight: 700, mb: 1 }}>
                Video Source
              </FormLabel>
              <Controller
                name="videoSource"
                control={control}
                render={({ field }) => (
                  <RadioGroup
                    {...field}
                    row
                    onChange={(e) => handleVideoSourceChange(field, e.target.value)}
                  >
                    <FormControlLabel
                      value="upload"
                      control={<Radio />}
                      label="Upload Video File"
                    />
                    <FormControlLabel
                      value="youtube"
                      control={<Radio />}
                      label="YouTube Link"
                    />
                  </RadioGroup>
                )}
              />
            </FormControl>
          )}

          {/* YouTube URL Input - Only show when Video + YouTube is selected */}
          {selectedFormat === "video" && videoSource === "youtube" && (
            <Stack direction={'row'} spacing={2}>
              <Box flex={2}>
                <Typography display={'flex'} fontWeight={700} mb={2}>
                  YouTube URL<Typography color="red">*</Typography>
                </Typography>
                <Controller
                  name="youtubeUrl"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      placeholder="https://www.youtube.com/watch?v=..."
                      variant="outlined"
                      error={!!errors.youtubeUrl}
                      helperText={errors.youtubeUrl?.message}
                      InputProps={{
                        startAdornment: <Link sx={{ mr: 1, color: 'action.active' }} />,
                        endAdornment: field.value && (
                          <IconButton
                            size="small"
                            onClick={clearYouTubeUrl}
                            sx={{ mr: 1 }}
                          >
                            <Delete />
                          </IconButton>
                        )
                      }}
                      onChange={(e) => {
                        field.onChange(e);
                        clearErrors("youtubeUrl");
                      }}
                    />
                  )}
                />
              </Box>

              {/* Duration field for YouTube videos */}
              <Box flex={1}>
                <Typography display={'flex'} fontWeight={700} mb={2}>
                  Duration (Seconds)<Typography color="red">*</Typography>
                </Typography>
                <Controller
                  name="videoDuration"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      placeholder="e.g., 6000"
                      variant="outlined"
                      type="number"
                      inputProps={{ step: 0.1, min: 0 }}
                      error={!!errors.videoDuration}
                      helperText={errors.videoDuration?.message}
                      InputProps={{
                        startAdornment: <Timer sx={{ mr: 1, color: 'action.active' }} />,
                        endAdornment: field.value && (
                          <IconButton
                            size="small"
                            onClick={clearVideoDuration}
                            sx={{ mr: 1 }}
                          >
                            <Delete />
                          </IconButton>
                        )
                      }}
                      onChange={(e) => {
                        field.onChange(e);
                        clearErrors("videoDuration");
                      }}
                    />
                  )}
                />
              </Box>
            </Stack>
          )}

          {/* File Upload - Show when not YouTube source */}
          {(!selectedFormat || selectedFormat !== "video" || videoSource === "upload") && (
            <Stack direction={'row'} spacing={3} alignItems="flex-start">
              <Box flex={selectedFormat === "video" ? 2 : 1}>
                <Typography fontWeight={700} mb={2}>
                  Upload File {selectedFormat !== "video" && <Typography component="span" color="red">*</Typography>}
                  {selectedFormat === "video" && videoSource === "upload" && <Typography component="span" color="red">*</Typography>}
                </Typography>
                <Box>
                  <Button
                    variant="contained"
                    component="label"
                    startIcon={<CloudUpload />}
                    disabled={isFileUploadDisabled || loading}
                    sx={{
                      mb: 2,
                      opacity: isFileUploadDisabled ? 0.5 : 1,
                      cursor: isFileUploadDisabled ? 'not-allowed' : 'pointer'
                    }}
                  >
                    Choose File
                    {!isFileUploadDisabled && (
                      <input
                        type="file"
                        hidden
                        onChange={handleFileChange}
                        accept={fileAcceptTypes[selectedFormat]}
                      />
                    )}
                  </Button>
                  {!selectedFormat ? (
                    <Typography variant="caption" color="#000" sx={{ display: 'block' }}>
                      Please Select format first
                    </Typography>
                  ) : (
                    <Typography variant="caption" color="#000" sx={{ mt: 1, display: 'block' }}>
                      {selectedFormat === 'image' && 'Accepted formats: JPEG, PNG, JPG, WEBP'}
                      {selectedFormat === 'gif' && 'Accepted format: GIF'}
                      {selectedFormat === 'video' && 'Accepted formats: MP4, WEBM, OGG'}
                    </Typography>
                  )}
                </Box>

                {/* File Error */}
                {errors.file && (
                  <Typography color="error" variant="caption" display="block" sx={{ mb: 1 }}>
                    {errors.file.message}
                  </Typography>
                )}
              </Box>

              {/* Selected File Display */}
              {file && (
                <Box flex={1}>
                  <Typography fontWeight={700} mb={2} sx={{ visibility: 'hidden' }}>
                    Placeholder
                  </Typography>
                  <Box
                    display="flex"
                    alignItems="center"
                    justifyContent="space-between"
                    bgcolor="success.light"
                    color="success.contrastText"
                    p={2}
                    borderRadius={1}
                    sx={{ backgroundColor: 'rgba(46, 125, 50, 0.1)' }}
                  >
                    <Box display="flex" flexDirection="column" sx={{ minWidth: 0, flex: 1 }}>
                      <Typography
                        noWrap
                        sx={{ maxWidth: "100%", fontSize: 14, fontWeight: 500, color: 'success.dark' }}
                      >
                        {file.name}
                      </Typography>
                      <Typography variant="caption" sx={{ color: 'success.main' }}>
                        {file.type} • {(file.size / 1024 / 1024).toFixed(2)} MB • Format: {selectedFormat}
                      </Typography>
                    </Box>
                    <IconButton
                      size="small"
                      color="error"
                      onClick={clearFile}
                      sx={{ ml: 1 }}
                    >
                      <Delete />
                    </IconButton>
                  </Box>
                </Box>
              )}

              {/* Duration field for uploaded videos */}
              {selectedFormat === "video" && videoSource === "upload" && (
                <Box flex={1}>
                  <Typography display={'flex'} fontWeight={700} mb={2}>
                    Duration (Seconds)<Typography color="red">*</Typography>
                  </Typography>
                  <Controller
                    name="videoDuration"
                    control={control}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        fullWidth
                        placeholder="e.g., 6000"
                        variant="outlined"
                        type="number"
                        inputProps={{ step: 0.1, min: 0 }}
                        error={!!errors.videoDuration}
                        helperText={errors.videoDuration?.message}
                        InputProps={{
                          startAdornment: <Timer sx={{ mr: 1, color: 'action.active' }} />,
                          endAdornment: field.value && (
                            <IconButton
                              size="small"
                              onClick={clearVideoDuration}
                              sx={{ mr: 1 }}
                            >
                              <Delete />
                            </IconButton>
                          )
                        }}
                        onChange={(e) => {
                          field.onChange(e);
                          clearErrors("videoDuration");
                        }}
                      />
                    )}
                  />
                </Box>
              )}
            </Stack>
          )}

          {/* Action Buttons */}
          <Box display="flex" gap={2} maxWidth={'500px'}>
            <Button
              variant="contained"
              startIcon={<CloudUpload />}
              disabled={(!files && !(selectedFormat === "video" && videoSource === "youtube" && youtubeUrl)) || loading}
              type="submit"
              fullWidth
            >
              {loading ? '...Submitting' : 'Submit'}
            </Button>
          </Box>
        </Box>
      </form>
    </Box>
  );
}

export default UploadComponent;