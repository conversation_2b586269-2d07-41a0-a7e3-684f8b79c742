import React, { useEffect, useState } from 'react';
import {
  <PERSON>, But<PERSON>, Card, CardContent, Typography, Stack,
  Tabs, Tab,
  Select,
  FormControl,
  MenuItem,
  TextField
} from '@mui/material';
import { Client } from '../../api/client';
import UseToast from '../../lib/hooks/UseToast';
import CustomPagination from '../common/CustomPagination';
import AddSkeleton from './AddSkeleton';
import ConfirmDeleteDialog from './ConfirmDeleteDialog';

const BannerDisplay = () => {
  const [files, setFiles] = useState([]);
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [loading, setLoading] = useState(false);
  const [tab, setTab] = useState('banner'); // default to banner tab
  const toast = UseToast();
  const [value, setValue] = useState("");
  const [displayTime, setDisplayTime] = useState(3000);
  const [TimeDisable,setTimeDisable]= useState(true)
  const [open,setOpen]=useState(false)
  const [id,setId]=useState('')

  // Function to prevent special characters and allow only numbers
  const handleDisplayTimeChange = (inputValue) => {
    // Remove any non-numeric characters
    const numericValue = inputValue.replace(/[^0-9]/g, '');
    setDisplayTime(numericValue);
    if (numericValue !== 3000) {
      setTimeDisable(false);
    } else {
      setTimeDisable(true);
    }

  };

  // Function to update display time on the server
  const handleDisplayTimeUpdate = async () => {
    if (!displayTime || displayTime === '') {
      toast.error("Please enter a valid display time");
      return;
    }

    try {
      const data = {
        interval: parseInt(displayTime),
        type: tab 
      };
      
      const res = await Client.put(`/admin/banner/interval`, data);
      if (res.data.success) {
        toast.success(res.data.message);
        setTimeout(() => fetchFiles(page), 1000);
      }
    } catch (err) {
      console.error("Display time update failed", err);
      toast.error(err?.response?.data?.error || "Display time update error");
    }
  };

  const handleChange = async (event, data) => {
    try {
      console.log("details", data)
      const { id, format } = data;
      const mewData = {
        index: event.target.value,
        id,
        format,
      }
      const res = await Client.put(`/admin/banner/index`, mewData);
      if (res.data.success) {
        toast.success(res.data.message);
        setTimeout(() => fetchFiles(page), 1000);
      }
    } catch (err) {
      console.error("Activation failed", err);
      toast.error(err?.response?.data?.error || "Activation error");
    }
  };

  const fetchFiles = async (pageNum, selectedTab = tab) => {
    setLoading(true);
    try {
      const res = await Client.get(`/admin/banner?page=${pageNum}&type=${selectedTab}`);
      if (res.data.success) {
        setFiles(res.data.data);
        setTotalPages(res.data.pagination.totalPages);
        
   
        if (res.data.intervalTime) {
          setDisplayTime(res.data.intervalTime.toString());
        }
      }
    } catch (err) {
      console.error("Failed to fetch files", err);
      toast.error("Failed to get advertisements");
    } finally {
      setLoading(false);
    }
  };

  const handleTabChange = (event, newValue) => {
    setTab(newValue);
    setPage(1);
    fetchFiles(1, newValue);
  };

  const handleActivate = async ({ id, value, type, format, index }) => {
    try {
      const data = { id, isActive: value, type, format, index };
      const res = await Client.put(`/admin/banner/status`, data);
      if (res.data.success) {
        toast.success(res.data.message);
        setTimeout(() => fetchFiles(page), 1000);
      }
    } catch (err) {
      console.error("Activation failed", err);
      toast.error(err?.response?.data?.error || "Activation error");
    }
  };

  const handleDelete = async () => {
    try {
      const res = await Client.delete(`/admin/banner/${id}`);
      if (res.data.success) {
        toast.success(res.data.message);
        setOpen(false)
        setTimeout(() => fetchFiles(page), 1000);
      }
    } catch (err) {
      console.error("Delete failed", err);
      toast.error(err?.response?.data?.error || "Delete error");
    }
  };

  const handlePageChange = (newPage) => {
    setPage(newPage);
    fetchFiles(newPage);
  };

  useEffect(() => {
    fetchFiles(page);
  }, []);

  return (
    <Box p={2}>
      <Typography variant="h4" mb={2} sx={{ fontWeight: 'bold', color: 'primary.main' }}>
        Previous Uploads
      </Typography>

      <Tabs
        value={tab}
        onChange={handleTabChange}
        textColor="primary"
        indicatorColor="primary"
        sx={{ mb: 2 }}
      >
        <Tab label="Banner" value="banner" />
        <Tab label="Video" value="video" />
      </Tabs>

      {/* Global Display Time Field */}
      {tab === 'banner' &&
      <Card sx={{ mb: 3, backgroundColor: '#f5f5f5' }}>
        <CardContent>
          <Typography variant="h6" mb={2} sx={{ fontWeight: 'bold' }}>
            Set Display Time for {tab === 'banner' ? 'Banners' : 'Videos'}
          </Typography>
          <Box display="flex" alignItems="center" gap={2}>
            <TextField
              label="Display Time (seconds)"
              size="small"
              value={displayTime}
              onChange={(e) => handleDisplayTimeChange(e.target.value)}
              placeholder="Enter seconds"
              inputProps={{
                maxLength: 4, 
                pattern: '[0-9]*', 
                inputMode: 'numeric'
              }}
              sx={{ width: 200 }}
            />
            <Button
              variant="contained"
              onClick={handleDisplayTimeUpdate}
              disabled={TimeDisable}
            >
              Update Display Time
            </Button>
          </Box>
          <Typography variant="caption" color="text.secondary" mt={1}>
            This will apply to all {tab === 'banner' ? 'banners' : 'videos'} in this category
          </Typography>
        </CardContent>
      </Card>
}

      <Stack spacing={2}>
        {loading ? (
          <AddSkeleton />
        ) : files.length === 0 ? (
          <Typography>No uploads found.</Typography>
        ) : (
          files.map(file => (
            <Card key={file.id}>
              <CardContent sx={{ display: 'flex', justifyContent: 'space-between' }}>
                <Box>
                  <Typography><strong>File name:</strong> {file.name}</Typography>
                  <Typography><strong>Format:</strong> {file.format}</Typography>
                  <Typography><strong>Type:</strong> {file.type}</Typography>
                  <Typography><strong>Add ID:</strong> {file.addId}</Typography>
                  <Typography><strong>Order:</strong> {file.rowIndex}</Typography>
                  {tab === 'video' && 
                  <Typography><strong>Duration :</strong> {file.duration}{'-'}seconds</Typography>
                  }
                </Box>
                <Box mt={1} display={'flex'} sx={{ alignItems: 'center' }}>
                  <Box width={200} mr={2}>
                    <Typography>Select order</Typography>
                    <FormControl sx={{ marginBottom: 2 }} fullWidth>
                      <Select
                        value={file.rowIndex}
                        onChange={(event) => handleChange(event, file)}
                        disabled={file.isActive}
                      >
                        <MenuItem value={null}>
                          <em>Select Order</em>
                        </MenuItem>
                        {[...Array(file.format === 'image' ? 8 : 5)].map((_, index) => (
                          <MenuItem key={index + 1} value={index + 1}>
                            {index + 1}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Box>
                  <Box>
                    <Button
                      variant="contained"
                      color={file.isActive ? "success" : "error"}
                      onClick={() => handleActivate({ id: file.id, value: !file.isActive, type: file.type, format: file.format, index:file.rowIndex })}
                    >
                      {file.isActive ? "Active" : "Inactive"}
                    </Button>
                    <Button
                      variant="outlined"
                      color="error"
                      onClick={() => {setOpen(true);setId(file.id);}}
                      sx={{ ml: 2 }}
                    >
                      Delete
                    </Button>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          ))
        )}

        <Box mt={3} sx={{ display: 'flex', justifyContent: 'center' }}>
          {!loading && totalPages > 1 && (
            <CustomPagination
              totalPages={totalPages}
              currentPage={page}
              onPageChange={handlePageChange}
            />
          )}
        </Box>
      </Stack>
     <ConfirmDeleteDialog
        open={open}
        onClose={() => setOpen(false)}
        onConfirm={()=>handleDelete()}
      />
    </Box>
  );
};

export default BannerDisplay;