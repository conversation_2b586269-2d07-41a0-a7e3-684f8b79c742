import React, { useState, useEffect } from "react";
import {
    <PERSON>,
    Button,
    Container,
    Ty<PERSON><PERSON>,
    TextField,
    LinearProgress
} from "@mui/material";

import { Client } from "../../api/client";
import UseToast from "../../lib/hooks/UseToast";
import { useLocation } from "react-router-dom";
import SearchIcon from "@mui/icons-material/Search";
import RefreshIcon from "@mui/icons-material/Refresh";
import DynamicTable from "../../components/common/DynamicTable";
import BackButton from "../../components/common/BackButton";

const PlayerReferralDetails = () => {
    const [referral, setReferral] = useState([]);

    const toast = UseToast();
    const [loading, setLoading] = useState(false);
    const [page, setPage] = useState(1);
    const [totalPages, setTotalPages] = useState(1);

    const [searchParams, setSearchParams] = useState({
        cbid: "",
        name: "",
        email: "",
    });

    const fetchReferral = async () => {
        setLoading(true);
        try {
            const queryParams = new URLSearchParams();

            if (searchParams.cbid) queryParams.append("cbid", searchParams.cbid);
            if (searchParams.name) queryParams.append("name", searchParams.name);
            if (searchParams.email) queryParams.append("email", searchParams.email);

            const queryString = queryParams.toString();
           const url = `admin/details/referral/players?page=${page}${queryString ? `&${queryString}` : ""}`;

            const response = await Client.get(url);

            if (!response.data.success) {
                setReferral([]);
                setTotalPages(0)
                return;
            }

            if (response.data.status === 204) {
                toast.info("No Player found");
                setReferral([]);
                return;
            }

            setReferral(response.data.data);
            setTotalPages(response.data.pagination.totalPages)
            // If paginated response, update totalPages here
            // setTotalPages(response.data.pagination?.totalPages || 1);
        } catch (error) {
            console.error("Error searching players:", error);
            toast.error("An error occurred while searching for players");
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        fetchReferral();
    }, [page]);

    const handlePageChange = (newPage) => {
        setPage(newPage);
        // If paginated backend, include page in queryParams and call fetchReferral()
    };

    return (
        <Container maxWidth="xl" sx={{ py: 4, pt: 2, pb: 8, minHeight: "100vh" }}>
            {loading && <LinearProgress sx={{ mb: 2 }} />}

            <Typography variant="h6" mb={2}>Player Referral</Typography>

            {/* Search Fields */}
            <Box display="flex" gap={2} mb={3} flexWrap="wrap">
                <TextField
                    label="CBID"
                    variant="outlined"
                    size="small"
                    value={searchParams.cbid}
                    onChange={(e) => setSearchParams({ ...searchParams, cbid: e.target.value })}
                />
                <TextField
                    label="Name"
                    variant="outlined"
                    size="small"
                    value={searchParams.name}
                    onChange={(e) => setSearchParams({ ...searchParams, name: e.target.value })}
                />
                <TextField
                    label="Email"
                    variant="outlined"
                    size="small"
                    value={searchParams.email}
                    onChange={(e) => setSearchParams({ ...searchParams, email: e.target.value })}
                />
                <Button
                    variant="contained"
                    startIcon={<SearchIcon />}
                    onClick={fetchReferral}
                >
                    Search
                </Button>
                <Button
                    variant="outlined"
                    startIcon={<RefreshIcon />}
                    onClick={() => {
                        setSearchParams({ cbid: "", name: "", email: "" });
                        fetchReferral();
                    }}
                >
                    Reset
                </Button>
            </Box>

            {/* Referral Table */}
            <DynamicTable
                columns={[
                    {
                        id: "name",
                        label: "Name",
                        format: (_, row) => row.user.name || "-",
                    },
                    {
                        id: "cbid",
                        label: "CBID",
                        format: (_, row) => row.user.cbid || "-",
                    },
                    {
                        id: "email",
                        label: "Email",
                        format: (_, row) => row.user.email || "-",
                    },
                    {
                        id: "phoneNumber",
                        label: "Phone",
                        format: (_, row) => row.user.phoneNumber || "-",
                    },
                    {
                        id: "referralCount",
                        label: "Referral Count",
                        format: (_, row) => row.referralCount ?? 0,
                    },
                ]}
                data={referral}
                loading={loading}
                page={page}
                totalPages={totalPages}
                onPageChange={handlePageChange}
                showDetailsButton={false}
                tableContainerProps={{
                    sx: {
                        minHeight: "400px",
                        maxHeight: "600px",
                    },
                }}
            />
        </Container>
    );
};

export default PlayerReferralDetails;
