import React, { useState } from "react";
import {
  Box,
  Button,
  Typography,
  FormControl,
  Select,
  MenuItem,
  IconButton,
  Alert,
  Tooltip,
  Stack,
  TextField,
  Radio,
  RadioGroup,
  FormControlLabel,
  FormLabel,
} from "@mui/material";
import { CloudUpload, Delete, Visibility, Info, Link } from "@mui/icons-material";
import { useForm, Controller } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { Client } from "../../api/client";
import UseToast from "../../lib/hooks/UseToast";
import ImagePreview from "./ImagePreview";

const userTypes = [{key:"arbiter",value: "Arbiter"}, {key:"player",value: "Player"},{key:"club",value: "Club"},{key:"coach",value: "Coach"},];
const fileFormats = [{key:"image",value: "Image"}, {key:"gif",value:"GIF"}, {key:"video",value: "Video"}];

// YouTube URL validation regex
const youtubeUrlRegex = /^(https?:\/\/)?(www\.)?(youtube\.com\/(watch\?v=|embed\/)|youtu\.be\/)([a-zA-Z0-9_-]+)([?&][\w=]*)*$/;

// Schema
const formSchema = z.object({
  userType: z.enum(["arbiter", "player", "club","coach"], {
    required_error: "User type is required",
  }),
  fileFormat: z.enum(["image", "gif", "video"], {
    required_error: "File format is required",
  }),
  videoSource: z.enum(["upload", "youtube"]).optional().nullable(),
 file: z
    .any()
    .optional(),
  youtubeUrl: z
    .string()
    .optional(),
}).superRefine((data, ctx) => {
  const { fileFormat, videoSource, file, youtubeUrl } = data;
  
  // File validation
  if (fileFormat !== "video") {
    // For image/gif formats, file is required
    if (!file || !(file instanceof File)) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "File is required",
        path: ["file"],
      });
    } else {
      // Validate file type
      const allowedTypes = {
        image: ["image/jpeg", "image/png", "image/jpg", "image/webp"],
        gif: ["image/gif"],
      };
      
      const validTypes = allowedTypes[fileFormat] || [];
      if (!validTypes.includes(file.type)) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: `Only ${fileFormat === 'image' ? 'JPEG, PNG, JPG, WEBP' : 'GIF'} files are allowed`,
          path: ["file"],
        });
      }
    }
  } else if (fileFormat === "video") {
    // For video format, check video source
    if (videoSource === "upload") {
      if (!file || !(file instanceof File)) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: "Video file is required",
          path: ["file"],
        });
      } else {
        // Validate video file type
        const videoTypes = ["video/mp4", "video/webm", "video/ogg"];
        if (!videoTypes.includes(file.type)) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: "Only MP4, WEBM, OGG video files are allowed",
            path: ["file"],
          });
        }
      }
    } else if (videoSource === "youtube") {
      // YouTube URL validation
      if (!youtubeUrl || youtubeUrl.trim() === "") {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: "YouTube URL is required",
          path: ["youtubeUrl"],
        });
      } else if (!youtubeUrlRegex.test(youtubeUrl)) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: "Please enter a valid YouTube URL",
          path: ["youtubeUrl"],
        });
      }
    }
  }
});

function UploadComponent() {
  const [file, setFile] = useState(null);
  const [open,setOpen]=useState(false)
  const [loading,setLoading]=useState(false)
  const toast = UseToast();
  const {
    control,
    handleSubmit,
    setValue,
    watch,
    reset,
    formState: { errors },
    trigger,
    clearErrors,
  } = useForm({
    resolver: zodResolver(formSchema),
    defaultValues: {
      userType: "",
      fileFormat: "",
      videoSource: 'upload',
      file: null,
      youtubeUrl: "",
    },
  });
 
  const selectedFormat = watch("fileFormat");
  const videoSource = watch("videoSource");
  const youtubeUrl = watch("youtubeUrl");
  const files = watch("file");

  const fileAcceptTypes = {
    image: "image/jpeg,image/png,image/jpg,image/webp",
    gif: "image/gif",
    video: "video/mp4,video/webm,video/ogg",
  };

// Updated onSubmit function to send video ID to backend
const onSubmit = async (data) => {
  setLoading(true)
  const formData = new FormData();
  
  if (data.fileFormat === "video" && data.videoSource === "youtube") {
    // Extract video ID from YouTube URL
    const videoId = getYouTubeVideoId(youtubeUrl);
    const embedUrl = `https://www.youtube.com/embed/${videoId}`;
    

    formData.append("Url", embedUrl); // Keep full URL if backend 
    formData.append("name", 'youtube-video');
  } else {
    formData.append("image", file);
    formData.append("name", file.name);
  }
  
  formData.append("userType", data.userType);    
  formData.append("fileFormat", data.fileFormat);
  if (data.fileFormat === "video") {
    formData.append("videoSource", data.videoSource);
  }
console.log(formData)
  try {
    const response = await Client.post('/admin/add', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    
    if (response.data.success) {
      toast.success("Upload submitted successfully!");
      reset();
      setFile(null);
    }
  } catch (err) {
    console.error(err);
    toast.error("Upload failed");
  }finally{
    setLoading(false)
  }
};

  const handleFileChange = (event) => {
    const uploadedFile = event.target.files?.[0];

    
    if (uploadedFile) {
      setValue("file", uploadedFile);
      setFile(uploadedFile);
      clearErrors("file");
      trigger("file");
    }
  };

  const handleFormatChange = (field, newFormat) => {
    field.onChange(newFormat);
    clearErrors("fileFormat");
    
    // Clear file and YouTube URL when format changes
    if (file) {
      setFile(null);
      setValue("file", null);
      clearErrors("file");
    }
    setValue("youtubeUrl", "");
    clearErrors("youtubeUrl");
    
    // Reset video source to upload when changing format
    if (newFormat === "video") {
      setValue("videoSource", "youtube");
    }
  };

  const handleVideoSourceChange = (field, newSource) => {
    field.onChange(newSource);
    
    // Clear file when switching to YouTube
    if (newSource === "youtube" && file) {
      setFile(null);
      setValue("file", null);
      clearErrors("file");
    }
    
    // Clear YouTube URL when switching to upload
    if (newSource === "upload") {
      setValue("youtubeUrl", "");
      clearErrors("youtubeUrl");
    }
  };

  const clearFile = () => {
    setFile(null);
    setValue("file", null);
    clearErrors("file");
  };

  const clearYouTubeUrl = () => {
    setValue("youtubeUrl", "");
    clearErrors("youtubeUrl");
  };

  const isFileUploadDisabled = !selectedFormat || (selectedFormat === "video" && videoSource === "youtube");

  const getYouTubeVideoId = (url) => {
    const match = url.match(/^(https?:\/\/)?(www\.)?(youtube\.com\/(watch\?v=|embed\/)|youtu\.be\/)([a-zA-Z0-9_-]+)([?&][\w=]*)*$/);
    return match ? match[5] : null; // Changed from match[1] to match[5]
  };

  return (
    <Box maxWidth={'80vw'} mx="auto" p={4} bgcolor="background.paper" borderRadius={2}>
      {/* <Typography variant="h4" align="center" gutterBottom>
        File Upload
      </Typography> */}

      <form onSubmit={handleSubmit(onSubmit)}>
        <Box display="flex" flexDirection="column" gap={3}>
          {/* User Type */}
          <Stack direction={'row'} spacing={2}> 
          <FormControl fullWidth error={!!errors.userType}>
            <Typography mb={1} sx={{ display: 'flex', justifyContent: 'flex-start' }} fontWeight={700}>
              User Type {' '}<Typography color="red">*</Typography>
            </Typography>
            <Controller
              name="userType"
              control={control}
              render={({ field }) => (
                <Select 
                  {...field} 
                  displayEmpty
                  onChange={(e) => {
                    field.onChange(e);
                    clearErrors("userType");
                  }}
                >
                  <MenuItem value="">
                    <em>Select user type</em>
                  </MenuItem>
                  {userTypes.map((type) => (
                    <MenuItem key={type.key} value={type.key}>
                      {type.value}
                    </MenuItem>
                  ))}
                </Select>
              )}
            />
            {errors.userType && (
              <Typography color="error" variant="caption" sx={{ mt: 0.5 }}>
                {errors.userType.message}
              </Typography>
            )}
          </FormControl>

          {/* File Format */}
          <FormControl fullWidth error={!!errors.fileFormat}>
            <Typography mb={1} sx={{ display: 'flex', justifyContent: 'flex-start' }} fontWeight={700}>
              File Format {' '}<Typography color="red">*</Typography>
            </Typography>
            <Controller
              name="fileFormat"
              control={control}
              render={({ field }) => (
                <Select 
                  {...field} 
                  displayEmpty
                  onChange={(e) => handleFormatChange(field, e.target.value)}
                >
                  <MenuItem value="">
                    <em>Select file format</em>
                  </MenuItem>
                  {fileFormats.map((format) => (
                    <MenuItem key={format.key} value={format.key}>
                      {format.value}
                    </MenuItem>
                  ))}
                </Select>
              )}
            />
            {errors.fileFormat && (
              <Typography color="error" variant="caption" sx={{ mt: 0.5 }}>
                {errors.fileFormat.message}
              </Typography>
            )}
          </FormControl>
          
          </Stack>

          {/* Video Source Selection - Only show when Video is selected */}
          {selectedFormat === "video" && (
            <FormControl component="fieldset">
              <FormLabel component="legend" sx={{ fontWeight: 700, mb: 1 }}>
                Video Source
              </FormLabel>
              <Controller
                name="videoSource"
                control={control}
                render={({ field }) => (
                  <RadioGroup
                    {...field}
                    row
                    onChange={(e) => handleVideoSourceChange(field, e.target.value)}
                    defaultChecked={true}
                  >
                    <FormControlLabel 
                      value="youtube" 
                      defaultChecked={true}
                      control={<Radio />} 
                      label="YouTube Link" 
                    />
                    {/* <FormControlLabel 
                      value="upload" 
                      control={<Radio />} 
                      label="Upload Video File" 
                    /> */}
                  </RadioGroup>
                )}
              />
            </FormControl>
          )}

          {/* YouTube URL Input - Only show when Video + YouTube is selected */}
          {selectedFormat === "video" && videoSource === "youtube" && (
            <Box>
              <Typography display={'flex'} fontWeight={700} mb={2}>
                YouTube URL<Typography color="red">*</Typography>
              </Typography>
              <Controller
                name="youtubeUrl"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    fullWidth
                    placeholder="https://www.youtube.com/watch?v=..."
                    variant="outlined"
                    error={!!errors.youtubeUrl}
                    helperText={errors.youtubeUrl?.message}
                    InputProps={{
                      startAdornment: <Link sx={{ mr: 1, color: 'action.active' }} />,
                      endAdornment: field.value && (
                        <IconButton
                          size="small"
                          onClick={clearYouTubeUrl}
                          sx={{ mr: 1 }}
                        >
                          <Delete />
                        </IconButton>
                      )
                    }}
                    onChange={(e) => {
                      field.onChange(e);
                      clearErrors("youtubeUrl");
                    }}
                  />
                )}
              />
              
             
            </Box>
          )}

          {/* File Upload - Show when not YouTube source */}
          {(!selectedFormat || selectedFormat !== "video" || videoSource === "upload") && (
            <Stack direction={'row'} spacing={3}>
              <Box>
                <Typography fontWeight={700} mb={2}>
                  Upload File {selectedFormat !== "video" && <Typography component="span" color="red">*</Typography>}
                </Typography>
                <Box>
                  <Button
                    variant="contained"
                    component="label"
                    startIcon={<CloudUpload />}
                    disabled={isFileUploadDisabled}
                    sx={{ 
                      mb: 2,
                      opacity: isFileUploadDisabled ? 0.5 : 1,
                      cursor: isFileUploadDisabled ? 'not-allowed' : 'pointer'
                    }}
                  >
                    Choose File
                    {!isFileUploadDisabled && (
                      <input
                        type="file"
                        hidden
                        onChange={handleFileChange}
                        accept={fileAcceptTypes[selectedFormat]}
                      />
                    )}
                  </Button>
                  {!selectedFormat ? (
                    <Typography variant="caption" color="#000" sx={{ display: 'block' }}>
                      Please Select format first 
                    </Typography>
                  ) : (
                    <Typography variant="caption" color="#000" sx={{ mt: 1, display: 'block' }}>
                      {selectedFormat === 'image' && 'Accepted formats: JPEG, PNG, JPG, WEBP'}
                      {selectedFormat === 'gif' && 'Accepted format: GIF'}
                      {selectedFormat === 'video' && 'Accepted formats: MP4, WEBM, OGG'}
                    </Typography>
                  )}
                </Box>
                
                {/* File Error */}
                {errors.file && (
                  <Typography color="error" variant="caption" display="block" sx={{ mb: 1 }}>
                    {errors.file.message}
                  </Typography>
                )}
              </Box>
              
              {/* Selected File Display */}
              <Box>
                {file && (
                  <Box
                    display="flex"
                    alignItems="center"
                    justifyContent="space-between"
                    bgcolor="success.light"
                    color="success.contrastText"
                    p={2}
                    borderRadius={1}
                    sx={{ backgroundColor: 'rgba(46, 125, 50, 0.1)' }}
                  >
                    <Box display="flex" flexDirection="column" sx={{ minWidth: 0, flex: 1 }}>
                      <Typography 
                        noWrap 
                        sx={{ maxWidth: "100%", fontSize: 14, fontWeight: 500, color: 'success.dark' }}
                      >
                        {file.name}
                      </Typography>
                      <Typography variant="caption" sx={{ color: 'success.main' }}>
                        {file.type} • {(file.size / 1024 / 1024).toFixed(2)} MB • Format: {selectedFormat}
                      </Typography>
                    </Box>
                    <IconButton
                      size="small"
                      color="error"
                      onClick={clearFile}
                      sx={{ ml: 1 }}
                    >
                      <Delete />
                    </IconButton>
                  </Box>
                )}
              </Box>
            </Stack>
          )}

          {/* Action Buttons */}
          <Box display="flex" gap={2} maxWidth={'500px'}>
            <Button
              variant="outlined"
              startIcon={<Visibility />}
              disabled={!file && !(selectedFormat === "video" && videoSource === "youtube" && youtubeUrl)}
              fullWidth
              // onClick={() => {
              //   if (file) {
              //     const previewUrl = URL.createObjectURL(file);
              //     window.open(previewUrl, '_blank');
              //     setTimeout(() => URL.revokeObjectURL(previewUrl), 1000);
              //   } else if (selectedFormat === "video" && videoSource === "youtube" && youtubeUrl) {
              //     window.open(youtubeUrl, '_blank');
              //   }
              // }}
              onClick={()=>setOpen(true)}
            >
              Preview
            </Button>
            <Button
              variant="contained"
              startIcon={<CloudUpload />}
              disabled={!files && !(selectedFormat === "video" && videoSource === "youtube" && youtubeUrl) || loading}
              type="submit"
              fullWidth
            >
              {loading ? "Submitting...":"Submit"}
            </Button>
          </Box>
        </Box>
      </form>
      <ImagePreview
      open={open}
      onClose={()=>setOpen(false)}
      Link={file?URL.createObjectURL(file):youtubeUrl}
      Type={selectedFormat}
      />
    </Box>
  );
}

export default UploadComponent;



