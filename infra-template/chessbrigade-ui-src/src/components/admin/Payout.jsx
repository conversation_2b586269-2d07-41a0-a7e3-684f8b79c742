import React, { useState, useEffect, useCallback } from "react";
import {
  Box,
  Button,
  Container,
  Paper,
  Typography,
  Grid,
  Card,
  CardContent,
  CircularProgress,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  Divider,
  Chip,
  Skeleton,
} from "@mui/material";

import { Client } from "../../api/client";
import UseToast from "../../lib/hooks/UseToast";

import { Link, useParams } from "react-router-dom";

import MonetizationOnIcon from "@mui/icons-material/MonetizationOn";
import CheckCircleIcon from "@mui/icons-material/CheckCircle";
import PendingIcon from "@mui/icons-material/Pending";
import ErrorIcon from "@mui/icons-material/Error";
import HistoryIcon from "@mui/icons-material/History";

import BackButton from "../../components/common/BackButton";

const PayoutPage = () => {
  const { title: tournamentTitle } = useParams(); // Get tournament title from URL if coming from PaymentDetails

  // Tournament-specific states
  const [tournamentData, setTournamentData] = useState(null);
  const [tournamentPayments, setTournamentPayments] = useState(null);
  const [payoutStatus, setPayoutStatus] = useState(null);
  const [payoutStatusLoading, setPayoutStatusLoading] = useState(false);
  const [showCreatePayoutDialog, setShowCreatePayoutDialog] = useState(false);
  const [payoutCalculation, setPayoutCalculation] = useState(null);
  const [payoutLoading, setPayoutLoading] = useState(false);

  const toast = UseToast();

  // Fetch tournament data if we have a tournament title
  const fetchTournamentData = useCallback(async () => {
    if (!tournamentTitle) return;

    try {
      const response = await Client.get(
        `/tournament/${encodeURIComponent(tournamentTitle)}`
      );
      if (response.data.success) {
        setTournamentData(response.data.data);
      }
    } catch (error) {
      console.error(
        "Error fetching tournament data:",
        error.response?.data?.error || error.message
      );
    }
  }, [tournamentTitle]);

  // Fetch tournament payment summary
  const fetchTournamentPayments = useCallback(async () => {
    if (!tournamentTitle) return;

    try {
      const response = await Client.get("/admin/details/tournament/payment", {
        params: {
          page: 1,
          limit: 1000, // Get all payments for calculation
          tournamentTitle: tournamentTitle,
        },
      });

      if (response.data.success) {
        const paymentsData = response.data.data.payments;

        // Only count captured payments
        const capturedPayments = paymentsData.filter(
          (payment) => payment.paymentStatus === "captured"
        );

        const totalamount = capturedPayments.reduce((acc, payment) => {
          return acc + parseFloat(payment.paymentAmount || 0);
        }, 0);

        const totalPayers = capturedPayments.reduce((acc, payment) => {
          if (payment.paymentType === "club") {
            acc += payment.playersCount || 0;
          } else if (payment.paymentType === "player") {
            acc += 1;
          }
          return acc;
        }, 0);

        setTournamentPayments({
          totalAmount: totalamount,
          totalPayers,
          paymentsCount: capturedPayments.length,
        });
      }
    } catch (error) {
      console.error(
        "Error fetching tournament payments:",
        error.response?.data?.error || error.message
      );
    }
  }, [tournamentTitle]);

  // Fetch payout status for tournament
  const fetchPayoutStatus = useCallback(async () => {
    if (!tournamentTitle) return;

    try {
      setPayoutStatusLoading(true);
      const response = await Client.get(
        `/admin-payout/status/${tournamentData?.id}`
      );

      if (response.data.success) {
        setPayoutStatus(response.data.data);
      } else {
        // If no payout exists, set status as null
        setPayoutStatus(null);
      }
    } catch (error) {
      console.error(
        "Error fetching payout status:",
        error.response?.data?.error || error.message
      );
      if (error.response?.status === 404) {
        // No payout found for this tournament
        setPayoutStatus(null);
      } else {
        toast.error(
          error.response?.data?.error || "Failed to fetch payout status"
        );
      }
    } finally {
      setPayoutStatusLoading(false);
    }
  }, [tournamentData]);

  // Calculate payout preview for tournament
  const calculatePayoutPreview = async () => {
    if (!tournamentTitle) return;

    try {
      setPayoutLoading(true);
      const response = await Client.post(
        `/admin-payout/calculate/${encodeURIComponent(tournamentTitle)}`
      );

      if (response.data.success) {
        setPayoutCalculation(response.data.data);
        setShowCreatePayoutDialog(true);
      } else {
        toast.error(response.data.error || "Failed to calculate payout");
      }
    } catch (error) {
      console.error("Error calculating payout:", error);
      toast.error(error.response?.data?.error || "Failed to calculate payout");
    } finally {
      setPayoutLoading(false);
    }
  };

  // Process payout for tournament
  const processPayout = async (urgent = false) => {
    if (!tournamentTitle) return;

    try {
      setPayoutLoading(true);
      const response = await Client.post("/admin-payout/create", {
        tournament_id: tournamentData.id,
        urgent,
      });

      if (response.data.success) {
        toast.success("Payout created successfully!");
        setShowCreatePayoutDialog(false);
        // Refresh payout status after creation
        fetchPayoutStatus();
      } else {
        toast.error(response.data.error || "Failed to create payout");
      }
    } catch (error) {
      console.error("Error creating payout:", error);
      if (error.response?.status === 400) {
        toast.error(error.response.data.error || "Failed to create payout");
        error.response.data.message && toast.error(error.response.data.message);
      } else {
        toast.error("Failed to create payout");
      }
    } finally {
      setPayoutLoading(false);
    }
  };

  // Get status color and icon
  const getStatusDisplay = (status) => {
    switch (status?.toLowerCase()) {
      case "completed":
      case "success":
      case "paid":
        return {
          color: "success",
          icon: <CheckCircleIcon />,
          label: "Completed",
        };
      case "pending":
      case "processing":
        return {
          color: "warning",
          icon: <PendingIcon />,
          label: "Pending",
        };
      case "failed":
      case "error":
        return {
          color: "error",
          icon: <ErrorIcon />,
          label: "Failed",
        };
      default:
        return {
          color: "default",
          icon: <HistoryIcon />,
          label: status || "Unknown",
        };
    }
  };

  // Format date
  const formatDate = (dateString) => {
    if (!dateString) return "N/A";
    return new Date(dateString).toLocaleString("en-IN", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  // Calculate simplified payout estimation - matches backend logic
  const calculateEstimatedPayout = () => {
    if (!tournamentPayments || !tournamentData)
      return { estimatedPayout: 0, breakdown: {} };

    const totalCollected = tournamentPayments.totalAmount;
    const totalParticipants = tournamentPayments.totalPayers;
    const entryFee = tournamentData.entryFee || 0;

    // Based on your backend logic: payout = total amount collected from captured payments
    
    // Calculate expected total based on entry fee and participants for comparison
    const expectedTotal = totalParticipants * entryFee;
    const estimatedPayout = expectedTotal;

    return {
      estimatedPayout,
      breakdown: {
        totalCollected,
        totalParticipants,
        entryFee,
        expectedTotal,
      },
    };
  };

  useEffect(() => {
    if (tournamentTitle) {
      fetchTournamentData();
    }
  }, [tournamentTitle]);

  useEffect(() => {
    if (tournamentData) {
      fetchPayoutStatus();
    }
  }, [tournamentData]);

  useEffect(() => {
    if (payoutStatus === null) {
      fetchTournamentPayments();
    }
  }, [fetchTournamentPayments, payoutStatus]);

  // Calculate payout estimation when data is available
  const payoutEstimation = calculateEstimatedPayout();

  return (
    <Container maxWidth="xl" sx={{ py: 4, pt: 2, pb: 8, minHeight: "100vh" }}>
      <BackButton />

      {/* Tournament-Specific Payout Creation Section */}
      {tournamentTitle && tournamentData && tournamentPayments && (
        <Paper
          sx={{
            mb: 3,
            p: 3,
            bgcolor: "#fff8e1",
            borderRadius: 2,
            boxShadow: 2,
          }}
        >
          <Typography
            variant="h6"
            gutterBottom
            sx={{ fontWeight: "bold", color: "#f57c00", mb: 2 }}
          >
            <MonetizationOnIcon sx={{ mr: 1, verticalAlign: "middle" }} />
            Tournament Payout Management -{" "}
            {tournamentData.title?.replace(/-/g, " ")}
          </Typography>

          {/* Payout Status Section */}
          <Box sx={{ mb: 3 }}>
            <Typography variant="h6" gutterBottom sx={{ fontWeight: "medium" }}>
              Payout Status
            </Typography>

            {payoutStatusLoading ? (
              <Card sx={{ p: 2 }}>
                <Box display="flex" alignItems="center" gap={2}>
                  <Skeleton variant="circular" width={24} height={24} />
                  <Skeleton variant="text" width={200} height={32} />
                </Box>
                <Skeleton
                  variant="text"
                  width="100%"
                  height={20}
                  sx={{ mt: 1 }}
                />
                <Skeleton variant="text" width="80%" height={20} />
              </Card>
            ) : payoutStatus ? (
              <Card
                sx={{
                  bgcolor:
                    payoutStatus.status?.toLowerCase() === "completed"
                      ? "#e8f5e8"
                      : "#fff3e0",
                }}
              >
                <CardContent>
                  <Box display="flex" alignItems="center" gap={2} mb={2}>
                    <Chip
                      icon={getStatusDisplay(payoutStatus.status).icon}
                      label={getStatusDisplay(payoutStatus.status).label}
                      color={getStatusDisplay(payoutStatus.status).color}
                      variant="outlined"
                    />
                    <Typography variant="body1" fontWeight="medium">
                      Payout ID: {payoutStatus.payout_id || payoutStatus.id}
                    </Typography>
                  </Box>

                  <Grid container spacing={2}>
                    <Grid item xs={12} md={6}>
                      <Typography variant="body2" gutterBottom>
                        <strong>Amount:</strong> ₹
                        {payoutStatus.amount?.toLocaleString() || "N/A"}
                      </Typography>
                      <Typography variant="body2" gutterBottom>
                        <strong>Created:</strong>{" "}
                        {formatDate(payoutStatus.created_at)}
                      </Typography>
                      <Typography variant="body2" gutterBottom>
                        <strong>Updated:</strong>{" "}
                        {formatDate(payoutStatus.updated_at)}
                      </Typography>
                    </Grid>
                    <Grid item xs={12} md={6}>
                      {payoutStatus.bank_reference && (
                        <Typography variant="body2" gutterBottom>
                          <strong>Bank Reference:</strong>{" "}
                          {payoutStatus.bank_reference}
                        </Typography>
                      )}
                      {payoutStatus.failure_reason && (
                        <Typography variant="body2" gutterBottom color="error">
                          <strong>Failure Reason:</strong>{" "}
                          {payoutStatus.failure_reason}
                        </Typography>
                      )}
                      {payoutStatus.urgent && (
                        <Chip
                          label="Urgent Payout"
                          color="warning"
                          size="small"
                          sx={{ mt: 1 }}
                        />
                      )}
                    </Grid>
                  </Grid>

                  {payoutStatus.notes && (
                    <Box
                      sx={{ mt: 2, p: 1, bgcolor: "#f5f5f5", borderRadius: 1 }}
                    >
                      <Typography variant="body2">
                        <strong>Notes:</strong> {payoutStatus.notes}
                      </Typography>
                    </Box>
                  )}
                </CardContent>
              </Card>
            ) : (
              <Alert severity="info" sx={{ mb: 2 }}>
                No payout has been created for this tournament yet.
              </Alert>
            )}
          </Box>

          <Grid container spacing={3}>
            {/* Tournament Info Card */}
            <Grid item xs={12} md={6}>
              <Card sx={{ bgcolor: "#f5f5f5", height: "100%" }}>
                <CardContent>
                  <Typography
                    variant="subtitle1"
                    fontWeight="medium"
                    gutterBottom
                  >
                    Tournament Information
                  </Typography>
                  <Typography variant="body2" gutterBottom>
                    <strong>Organizer:</strong>{" "}
                    {tournamentData.organizerName || "N/A"}
                  </Typography>
                  <Typography variant="body2" gutterBottom>
                    <strong>Total Collected (Captured):</strong> ₹
                    {tournamentPayments.totalAmount.toLocaleString()}
                  </Typography>
                  <Typography variant="body2" gutterBottom>
                    <strong>Active Participants:</strong>{" "}
                    {tournamentPayments.totalPayers}
                  </Typography>
                  <Typography variant="body2" gutterBottom>
                    <strong>Entry Fee:</strong> ₹
                    {tournamentData.entryFee || "N/A"}
                  </Typography>
                  <Typography variant="body2" gutterBottom>
                    <strong>Expected Total:</strong> ₹
                    {payoutEstimation.breakdown.expectedTotal?.toLocaleString() ||
                      "0"}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>

            {/* Payout Summary Card - Simplified calculation */}
            <Grid item xs={12} md={6}>
              <Card sx={{ bgcolor: "#e3f2fd", height: "100%" }}>
                <CardContent>
                  <Typography
                    variant="subtitle1"
                    fontWeight="medium"
                    gutterBottom
                  >
                    Payout Calculation
                  </Typography>
                  <Typography variant="body2" gutterBottom>
                    Captured Payments: ₹
                    {payoutEstimation.breakdown.totalCollected?.toLocaleString() ||
                      "0"}
                  </Typography>

                  <Divider sx={{ my: 1 }} />
                  <Typography variant="h6" fontWeight="bold" color="primary">
                    Club Payout: ₹
                    {payoutEstimation.estimatedPayout?.toLocaleString() || "0"}
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    (Full amount from captured payments)
                  </Typography>

                  {payoutEstimation.breakdown.totalCollected !==
                    payoutEstimation.breakdown.expectedTotal && (
                    <Alert severity="info" sx={{ mt: 2 }}>
                      <Typography variant="caption">
                        Note: Collected amount (₹
                        {payoutEstimation.breakdown.totalCollected?.toLocaleString()}
                        ) differs from expected (₹
                        {payoutEstimation.breakdown.expectedTotal?.toLocaleString()}
                        )
                      </Typography>
                    </Alert>
                  )}
                </CardContent>
              </Card>
            </Grid>
          </Grid>

          {/* Action Buttons */}
          <Box mt={3} display="flex" gap={2} justifyContent="center">
            {!payoutStatus ||
            payoutStatus.status?.toLowerCase() === "failed" ? (
              <>
                <Button
                  variant="contained"
                  color="primary"
                  size="large"
                  startIcon={<MonetizationOnIcon />}
                  onClick={calculatePayoutPreview}
                  disabled={payoutLoading}
                >
                  {payoutLoading
                    ? "Calculating..."
                    : payoutStatus?.status?.toLowerCase() === "failed"
                    ? "Retry Payout"
                    : "Calculate & Create Payout"}
                </Button>

                <Alert severity="info" sx={{ flex: 1, maxWidth: 400 }}>
                  <Typography variant="body2" sx={{ fontSize: "0.875rem" }}>
                    <strong>Note:</strong> Payout amount equals the total
                    captured payments. Only one payout can be created per
                    tournament. If the payout fails, you can retry.
                  </Typography>
                </Alert>
              </>
            ) : (
              <Alert
                severity={
                  payoutStatus.status?.toLowerCase() === "completed"
                    ? "success"
                    : "warning"
                }
                sx={{ flex: 1 }}
              >
                <Typography variant="body2" sx={{ fontSize: "0.875rem" }}>
                  <strong>
                    {payoutStatus.status?.toLowerCase() === "completed"
                      ? "Payout Completed:"
                      : "Payout In Progress:"}
                  </strong>{" "}
                  {payoutStatus.status?.toLowerCase() === "completed"
                    ? "The payout has been successfully processed and sent to the organizer's account."
                    : "The payout is currently being processed. Please check back later for updates."}
                </Typography>
              </Alert>
            )}
          </Box>
        </Paper>
      )}

      {/* Payout Creation Dialog */}
      <Dialog
        open={showCreatePayoutDialog}
        onClose={() => setShowCreatePayoutDialog(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          <Typography variant="h6" fontWeight="bold">
            {payoutStatus?.status?.toLowerCase() === "failed"
              ? "Retry Tournament Payout"
              : "Create Tournament Payout"}
          </Typography>
        </DialogTitle>
        <DialogContent>
          {payoutCalculation && (
            <Box>
              <Alert severity="info" sx={{ mb: 2 }}>
                Review the payout calculation before processing the payment.
              </Alert>

              {payoutStatus?.status?.toLowerCase() === "failed" && (
                <Alert severity="warning" sx={{ mb: 2 }}>
                  <Typography variant="body2">
                    <strong>Previous payout failed:</strong>{" "}
                    {payoutStatus.failure_reason || "Unknown reason"}
                  </Typography>
                </Alert>
              )}

              <Grid container spacing={2} sx={{ mb: 3 }}>
                <Grid item xs={12} md={6}>
                  <Card sx={{ bgcolor: "#f5f9ff" }}>
                    <CardContent>
                      <Typography
                        variant="subtitle1"
                        fontWeight="medium"
                        gutterBottom
                      >
                        Tournament Details
                      </Typography>
                      <Typography variant="body2">
                        Name:{" "}
                        {payoutCalculation.tournament?.name ||
                          tournamentData?.title?.replace(/-/g, " ")}
                      </Typography>
                      <Typography variant="body2">
                        Participants:{" "}
                        {payoutCalculation.tournament?.participants ||
                          tournamentPayments?.totalPayers}
                      </Typography>
                      <Typography variant="body2">
                        Entry Fee: ₹
                        {payoutCalculation.tournament?.entry_fee ||
                          tournamentData?.entryFee}
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>

                <Grid item xs={12} md={6}>
                  <Card sx={{ bgcolor: "#f1f8e9" }}>
                    <CardContent>
                      <Typography
                        variant="subtitle1"
                        fontWeight="medium"
                        gutterBottom
                      >
                        Payout Summary
                      </Typography>
                      <Typography variant="body2">
                        Total Collected: ₹
                        {payoutCalculation.calculation?.total_collected?.toLocaleString() ||
                          tournamentPayments?.totalAmount?.toLocaleString()}
                      </Typography>
                      <Typography
                        variant="h6"
                        color="primary"
                        fontWeight="bold"
                      >
                        Club Payout: ₹
                        {payoutCalculation.calculation?.club_payout?.toLocaleString()}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        (Full captured amount)
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>

              <Box sx={{ bgcolor: "#fff3e0", p: 2, borderRadius: 1, mb: 2 }}>
                <Typography
                  variant="subtitle2"
                  fontWeight="medium"
                  gutterBottom
                >
                  Detailed Breakdown:
                </Typography>
                {payoutCalculation.breakdown &&
                  Object.entries(payoutCalculation.breakdown).map(
                    ([key, value]) => (
                      <Box
                        key={key}
                        display="flex"
                        justifyContent="space-between"
                        mb={0.5}
                      >
                        <Typography variant="body2">{key}:</Typography>
                        <Typography variant="body2" fontWeight="medium">
                          {value}
                        </Typography>
                      </Box>
                    )
                  )}
              </Box>

              <Alert severity="warning" sx={{ mb: 2 }}>
                <Typography variant="body2">
                  <strong>Important:</strong> Once created, this payout cannot
                  be cancelled. The payout amount equals the total captured
                  payments. Only one payout per tournament is allowed. If it
                  fails, you can retry.
                </Typography>
              </Alert>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowCreatePayoutDialog(false)}>
            Cancel
          </Button>
          <Button
            variant="contained"
            color="primary"
            onClick={() => processPayout(false)}
            disabled={payoutLoading}
            startIcon={
              payoutLoading ? (
                <CircularProgress size={16} />
              ) : (
                <MonetizationOnIcon />
              )
            }
          >
            {payoutLoading
              ? "Processing..."
              : payoutStatus?.status?.toLowerCase() === "failed"
              ? "Retry Payout"
              : "Create Payout"}
          </Button>
          <Button
            variant="outlined"
            color="warning"
            onClick={() => processPayout(true)}
            disabled={payoutLoading}
            startIcon={
              payoutLoading ? (
                <CircularProgress size={16} />
              ) : (
                <MonetizationOnIcon />
              )
            }
          >
            {payoutLoading
              ? "Processing..."
              : payoutStatus?.status?.toLowerCase() === "failed"
              ? "Retry Urgent Payout"
              : "Create Urgent Payout"}
          </Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
};

export default PayoutPage;
