import React, { useState, useEffect, useRef } from "react";
import ReactQuill from "react-quill-new";
import "react-quill-new/dist/quill.snow.css";
import { Box, Paper, Typography } from "@mui/material";

const RichTextEditor = ({
  value = "",
  onChange,
  placeholder = "Start writing...",
  readOnly = false,
  label = "Editor",
  minHeight = 200,
}) => {
  const [editorValue, setEditorValue] = useState(value);
  const quillRef = useRef(null);

  useEffect(() => {
    if (value !== editorValue) {
      setEditorValue(value);
    }
  }, [value]);

  const handleChange = (content) => {
    setEditorValue(content);
    if (onChange) {
      onChange(content);
    }
  };

  // Quill editor modules configuration
  const modules = {
    toolbar: [
      [{ header: [1, 2, 3, 4, 5, 6, false] }],
      ["bold", "italic", "underline", "strike"],
      [{ list: "ordered" }, { list: "bullet" }],
      [{ script: "sub" }, { script: "super" }],
      [{ indent: "-1" }, { indent: "+1" }],
      ["blockquote", "code-block"],
      [{ color: [] }, { background: [] }],
      [{ align: [] }],
      ["link", "image"],
      ["clean"],
    ],
    clipboard: {
      matchVisual: false,
    },
  };

  // Quill editor formats
  const formats = [
    "header",
    "bold",
    "italic",
    "underline",
    "strike",
    "list",
    "bullet",
    "script",
    "indent",
    "blockquote",
    "code-block",
    "color",
    "background",
    "align",
    "link",
    "image",
  ];

  const editorStyles = {
    editor: {
      "& .ql-container": {
        borderBottomLeftRadius: "4px",
        borderBottomRightRadius: "4px",
        backgroundColor: "#fff",
        minHeight: `${minHeight}px`,
      },
      "& .ql-toolbar": {
        borderTopLeftRadius: "4px",
        borderTopRightRadius: "4px",
        backgroundColor: "#f6f7f8",
      },
      "& .ql-editor": {
        fontSize: "16px",
        lineHeight: 1.5,
        padding: "12px 15px",
      },
      "& .ql-editor p": {
        marginBottom: "12px",
      },
    },
    label: {
      marginBottom: "8px",
      fontWeight: 500,
      color: "#333",
      textAlign: "start",
    },
  };

  return (
    <Box sx={{ mb: 3 }}>
      {label && (
        <Typography variant="subtitle1" sx={editorStyles.label}>
          {label}
        </Typography>
      )}
      <Paper elevation={1} sx={editorStyles.editor}>
        <ReactQuill
          ref={quillRef}
          theme="snow"
          value={editorValue}
          onChange={handleChange}
          modules={modules}
          formats={formats}
          placeholder={placeholder}
          readOnly={readOnly}
        />
      </Paper>
    </Box>
  );
};

export default RichTextEditor;