import React, { useEffect, useState } from 'react';
import {
  <PERSON>, But<PERSON>, Card, CardContent, Typography, Stack
} from '@mui/material';
import { Client } from '../../api/client';
import UseToast from '../../lib/hooks/UseToast';
import CustomPagination from '../common/CustomPagination';
import AddSkeleton from './AddSkeleton';

const AddDisplay = () => {
  const [files, setFiles] = useState([]);
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [loading, setLoading] = useState(false)
  const toast = UseToast()

  const fetchFiles = async (pageNum) => {
    setLoading(true)
    try {
      const res = await Client.get(`/admin/add?page=${pageNum}`);
      if (res.data.success) {
        setFiles(res.data.data);
        setTotalPages(res.data.pagination.totalPages);
      }
    } catch (err) {
      console.error("Failed to fetch files", err);
      toast.error("failed to get advertisements")
    } finally {
      setLoading(false)
    }
  };

  const handleActivate = async ({ id, value, type }) => {
    try {
      const data = {
        id,
        isActive: value,
        userType: type
      }
      const res = await Client.put(`/admin/add`, data);

      if (res.data.success) {
        toast.success(res.data.message)
        setTimeout(() => { fetchFiles(page) }, 1000)
      }
    } catch (err) {
      console.error("Activation failed", err.response);
      toast.error(err.response.data.error)
    }
  };

  const handleDelete = async (id) => {
    try {
      const res = await Client.delete(`/admin/add/${id}`);

      if (res.data.success) {
        toast.success(res.data.message)
        setTimeout(() => { fetchFiles(page) }, 1000)
      }
    } catch (err) {
      console.error("Delete failed", err);
      toast.error(err.response.data.error)
    }
  };

  const handlePageChange = (newPage) => {
    setPage(newPage);
    fetchFiles(newPage);
  };

  useEffect(() => {
    fetchFiles(page);
  }, [page]);

  return (
    <Box p={2}
    // sx={{border: '2px dashed',borderColor:  'grey.400',}}
    >
      <Typography variant="h4" mb={2} gutterBottom sx={{ fontWeight: 'bold', color: 'primary.main' }}>
        Previous Uploads
      </Typography>
      <Stack spacing={2}>
        {loading ? (
          <AddSkeleton />
        ) : files.length === 0 ? (
          <Typography>No uploads found.</Typography>
        ) : (
          files.map(file => (
            <Card key={file.id}>
              <CardContent sx={{ display: 'flex', justifyContent: 'space-between' }}>
                <Box>
                  <Typography><strong>File name:</strong> {file.name}</Typography>
                  <Typography><strong>Type:</strong> {file.format}</Typography>
                  <Typography><strong>User Type:</strong> {file.userType}</Typography>
                  <Typography><strong>Add Id:</strong> {file.addId}</Typography>
                </Box>
                <Box mt={1}>
                  <Button variant="contained" color={file.isActive ? "success" : "error"} onClick={() => handleActivate({ id: file.id, value: !file.isActive, type: file.userType })}>
                    {file.isActive ? "Active" : "inActive"}
                  </Button>
                  <Button variant="outlined" color="error" onClick={() => handleDelete(file.id)} sx={{ ml: 2 }}>
                    Delete
                  </Button>
                </Box>
              </CardContent>
            </Card>))
        )}
        <Box mt={3} sx={{ alignItems: 'center', display: 'flex', justifyContent: 'center' }}>
          {!loading && totalPages > 1 && (
            <CustomPagination
              totalPages={totalPages}
              currentPage={page}
              onPageChange={handlePageChange}
            />
          )}
        </Box>
      </Stack>
    </Box>
  );
};

export default AddDisplay;