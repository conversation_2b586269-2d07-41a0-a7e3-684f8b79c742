import React, { useEffect, useState } from 'react';
import {
  Box,
  Typography,
  Grid,
  Paper,
  CircularProgress,
  Card,
  CardContent,
  Chip,
  Divider,
} from '@mui/material';
import { Client } from '../../api/client';
import PaymentIcon from "@mui/icons-material/Payment";
import EmojiEventsIcon from '@mui/icons-material/EmojiEvents';
import TrendingUpIcon from '@mui/icons-material/TrendingUp';
import RefreshIcon from '@mui/icons-material/Refresh';
import AccountBalanceIcon from '@mui/icons-material/AccountBalance';
import ReceiptIcon from '@mui/icons-material/Receipt';
import MoneyOffIcon from '@mui/icons-material/MoneyOff';

const AdminPaymentDetails = () => {
  const [data, setData] = useState({
    tournamentCount: 0,
    totalAmount: 0,
    totalRefundAmount: 0,
    totalPlatformFeeEarnings: 0,
    netRevenue: 0,
    totalTransactions: 0,
    refundRate: "0.00",
    summary: {
      totalCollected: "₹0",
      totalRefunded: "₹0 (tournament fees only)",
      platformEarnings: "₹0",
      netRevenue: "₹0"
    }
  });
  const [loading, setLoading] = useState(true);

  const fetchData = async () => {
    try {
      const response = await Client.get('/admin/details/payment');
     
      setData(response.data.data);
    } catch (error) {
      console.error('Error fetching payment details:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, []);

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" mt={5}>
        <CircularProgress />
      </Box>
    );
  }

  // Calculate additional metrics
  const averageTransactionAmount = data.totalTransactions > 0 
    ? (data.totalAmount / data.totalTransactions).toFixed(2)
    : 0;

  const platformFeePercentage = data.totalAmount > 0 
    ? ((data.totalPlatformFeeEarnings / data.totalAmount) * 100).toFixed(2)
    : 0;

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Paper sx={{ mb: 3, p: 3, bgcolor: "#f5f9ff", borderRadius: 2, boxShadow: 2 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography
            variant="h5"
            sx={{ fontWeight: "bold", color: "#3f51b5" }}
          >
            Payment Dashboard
          </Typography>
          <Chip 
            label={`Refund Rate: ${data.refundRate}`}
            color={parseFloat(data.refundRate) < 5 ? "success" : parseFloat(data.refundRate) < 15 ? "warning" : "error"}
            variant="filled"
          />
        </Box>

        {/* Main Metrics Grid */}
        <Grid container spacing={3}>
          {/* Tournament Count */}
          <Grid item xs={12} sm={6} md={3}>
            <Card sx={{ bgcolor: "#e3f2fd", height: "100%" }}>
              <CardContent>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                  <Box>
                    <Typography variant="subtitle2" color="primary" sx={{ fontWeight: 600 }}>
                      Total Tournaments
                    </Typography>
                    <Typography
                      variant="h3"
                      sx={{ mt: 1, fontWeight: "bold", color: "#1976d2" }}
                    >
                      {data.tournamentCount || 0}
                    </Typography>
                  </Box>
                  <EmojiEventsIcon sx={{ fontSize: 40, color: "#1976d2", opacity: 0.7 }} />
                </Box>
                <Typography variant="body2" color="primary" sx={{ mt: 1 }}>
                  Active tournaments
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          {/* Total Transactions */}
          <Grid item xs={12} sm={6} md={3}>
            <Card sx={{ bgcolor: "#fff3e0", height: "100%" }}>
              <CardContent>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                  <Box>
                    <Typography variant="subtitle2" color="#f57c00" sx={{ fontWeight: 600 }}>
                      Total Transactions
                    </Typography>
                    <Typography
                      variant="h3"
                      sx={{ mt: 1, fontWeight: "bold", color: "#f57c00" }}
                    >
                      {data.totalTransactions || 0}
                    </Typography>
                  </Box>
                  <ReceiptIcon sx={{ fontSize: 40, color: "#f57c00", opacity: 0.7 }} />
                </Box>
                <Typography variant="body2" color="#f57c00" sx={{ mt: 1 }}>
                  Payment attempts
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          {/* Total Amount Collected */}
          <Grid item xs={12} sm={6} md={3}>
            <Card sx={{ bgcolor: "#e8f5e9", height: "100%" }}>
              <CardContent>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                  <Box>
                    <Typography variant="subtitle2" color="#2e7d32" sx={{ fontWeight: 600 }}>
                      Total Collected
                    </Typography>
                    <Typography
                      variant="h3"
                      sx={{ mt: 1, fontWeight: "bold", color: "#2e7d32" }}
                    >
                      ₹{data.totalAmount?.toLocaleString() || 0}
                    </Typography>
                  </Box>
                  <PaymentIcon sx={{ fontSize: 40, color: "#2e7d32", opacity: 0.7 }} />
                </Box>
                <Typography variant="body2" color="#2e7d32" sx={{ mt: 1 }}>
                  Gross revenue
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          {/* Platform Fee Earnings */}
          <Grid item xs={12} sm={6} md={3}>
            <Card sx={{ bgcolor: "#f3e5f5", height: "100%" }}>
              <CardContent>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                  <Box>
                    <Typography variant="subtitle2" color="#7b1fa2" sx={{ fontWeight: 600 }}>
                      Platform Earnings
                    </Typography>
                    <Typography
                      variant="h3"
                      sx={{ mt: 1, fontWeight: "bold", color: "#7b1fa2" }}
                    >
                      ₹{data.totalPlatformFeeEarnings?.toLocaleString() || 0}
                    </Typography>
                  </Box>
                  <AccountBalanceIcon sx={{ fontSize: 40, color: "#7b1fa2", opacity: 0.7 }} />
                </Box>
                <Typography variant="body2" color="#7b1fa2" sx={{ mt: 1 }}>
                  {platformFeePercentage}% of total
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          {/* Total Refunded */}
          <Grid item xs={12} sm={6} md={3}>
            <Card sx={{ bgcolor: "#ffebee", height: "100%" }}>
              <CardContent>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                  <Box>
                    <Typography variant="subtitle2" color="#c62828" sx={{ fontWeight: 600 }}>
                      Total Refunded
                    </Typography>
                    <Typography
                      variant="h3"
                      sx={{ mt: 1, fontWeight: "bold", color: "#c62828" }}
                    >
                      ₹{data.totalRefundAmount?.toLocaleString() || 0}
                    </Typography>
                  </Box>
                  <MoneyOffIcon sx={{ fontSize: 40, color: "#c62828", opacity: 0.7 }} />
                </Box>
                <Typography variant="body2" color="#c62828" sx={{ mt: 1 }}>
                  Tournament fees only
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          {/* Net Revenue */}
          <Grid item xs={12} sm={6} md={3}>
            <Card sx={{ bgcolor: "#e0f2f1", height: "100%" }}>
              <CardContent>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                  <Box>
                    <Typography variant="subtitle2" color="#00695c" sx={{ fontWeight: 600 }}>
                      Net Revenue
                    </Typography>
                    <Typography
                      variant="h3"
                      sx={{ mt: 1, fontWeight: "bold", color: "#00695c" }}
                    >
                      ₹{data.netRevenue?.toLocaleString() || 0}
                    </Typography>
                  </Box>
                  <TrendingUpIcon sx={{ fontSize: 40, color: "#00695c", opacity: 0.7 }} />
                </Box>
                <Typography variant="body2" color="#00695c" sx={{ mt: 1 }}>
                  After refunds
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          {/* Average Transaction */}
          <Grid item xs={12} sm={6} md={3}>
            <Card sx={{ bgcolor: "#fce4ec", height: "100%" }}>
              <CardContent>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                  <Box>
                    <Typography variant="subtitle2" color="#ad1457" sx={{ fontWeight: 600 }}>
                      Avg Transaction
                    </Typography>
                    <Typography
                      variant="h3"
                      sx={{ mt: 1, fontWeight: "bold", color: "#ad1457" }}
                    >
                      ₹{averageTransactionAmount}
                    </Typography>
                  </Box>
                  <ReceiptIcon sx={{ fontSize: 40, color: "#ad1457", opacity: 0.7 }} />
                </Box>
                <Typography variant="body2" color="#ad1457" sx={{ mt: 1 }}>
                  Per transaction
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          {/* Refund Rate Card */}
          <Grid item xs={12} sm={6} md={3}>
            <Card sx={{ bgcolor: "#f1f8e9", height: "100%" }}>
              <CardContent>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                  <Box>
                    <Typography variant="subtitle2" color="#689f38" sx={{ fontWeight: 600 }}>
                      Refund Rate
                    </Typography>
                    <Typography
                      variant="h3"
                      sx={{ mt: 1, fontWeight: "bold", color: "#689f38" }}
                    >
                      {data.refundRate}
                    </Typography>
                  </Box>
                  <RefreshIcon sx={{ fontSize: 40, color: "#689f38", opacity: 0.7 }} />
                </Box>
                <Typography variant="body2" color="#689f38" sx={{ mt: 1 }}>
                  Refund percentage
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </Paper>

      {/* Summary Section */}
      <Paper sx={{ p: 3, borderRadius: 2, boxShadow: 2 }}>
        <Typography variant="h6" sx={{ fontWeight: "bold", mb: 2, color: "#424242" }}>
          Financial Summary
        </Typography>
        
        <Grid container spacing={2}>
          <Grid item xs={12} md={6}>
            <Box sx={{ p: 2, bgcolor: "#f8f9fa", borderRadius: 1 }}>
              <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 1 }}>
                Revenue Breakdown
              </Typography>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                <Typography variant="body2">Total Collected:</Typography>
                <Typography variant="body2" sx={{ fontWeight: 600 }}>
                  {data.summary?.totalCollected || `₹${data.totalAmount?.toLocaleString()}`}
                </Typography>
              </Box>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                <Typography variant="body2">Platform Earnings:</Typography>
                <Typography variant="body2" sx={{ fontWeight: 600, color: '#7b1fa2' }}>
                  {data.summary?.platformEarnings || `₹${data.totalPlatformFeeEarnings?.toLocaleString()}`}
                </Typography>
              </Box>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                <Typography variant="body2">Total Refunded:</Typography>
                <Typography variant="body2" sx={{ fontWeight: 600, color: '#c62828' }}>
                  {data.summary?.totalRefunded || `₹${data.totalRefundAmount?.toLocaleString()} (tournament fees only)`}
                </Typography>
              </Box>
              <Divider sx={{ my: 1 }} />
              <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>Net Revenue:</Typography>
                <Typography variant="subtitle2" sx={{ fontWeight: 600, color: '#00695c' }}>
                  {data.summary?.netRevenue || `₹${data.netRevenue?.toLocaleString()}`}
                </Typography>
              </Box>
            </Box>
          </Grid>

          <Grid item xs={12} md={6}>
            <Box sx={{ p: 2, bgcolor: "#f8f9fa", borderRadius: 1 }}>
              <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 1 }}>
                Key Metrics
              </Typography>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                <Typography variant="body2">Total Tournaments:</Typography>
                <Typography variant="body2" sx={{ fontWeight: 600 }}>
                  {data.tournamentCount}
                </Typography>
              </Box>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                <Typography variant="body2">Total Transactions:</Typography>
                <Typography variant="body2" sx={{ fontWeight: 600 }}>
                  {data.totalTransactions}
                </Typography>
              </Box>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                <Typography variant="body2">Average per Transaction:</Typography>
                <Typography variant="body2" sx={{ fontWeight: 600 }}>
                  ₹{averageTransactionAmount}
                </Typography>
              </Box>
              <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                <Typography variant="body2">Platform Fee Rate:</Typography>
                <Typography variant="body2" sx={{ fontWeight: 600 }}>
                  {platformFeePercentage}%
                </Typography>
              </Box>
            </Box>
          </Grid>
        </Grid>
      </Paper>
    </Box>
  );
};

export default AdminPaymentDetails;