import React, { useState, useEffect, useCallback } from "react";
import {
    Box,
    Button,
    Container,
    Paper,
    Typography,
    Grid,
    TextField,
    Card,
    CardContent,
    Autocomplete,
    CircularProgress,
    MenuItem,
    Dialog,
    DialogTitle,
    DialogContent,
    DialogActions,
    Chip,
    Alert,
    Divider,
    IconButton,
    Tooltip,
    Badge,
    Tab,
    Tabs,
    LinearProgress,
} from "@mui/material";

import { RestartAlt } from "@mui/icons-material";
import { Client } from "../../api/client";
import UseToast from "../../lib/hooks/UseToast";
import { AxiosError } from "axios";
import { Link, useLocation } from "react-router-dom";
import SearchIcon from "@mui/icons-material/Search";
import RefreshIcon from "@mui/icons-material/Refresh";
import FilterListIcon from "@mui/icons-material/Filter";
import GetAppIcon from "@mui/icons-material/GetApp";
import AccountBalanceIcon from "@mui/icons-material/AccountBalance";
import MonetizationOnIcon from "@mui/icons-material/MonetizationOn";
import CheckCircleIcon from "@mui/icons-material/CheckCircle";
import PendingIcon from "@mui/icons-material/Pending";
import ErrorIcon from "@mui/icons-material/Error";
import VisibilityIcon from "@mui/icons-material/Visibility";
import TrendingUpIcon from "@mui/icons-material/TrendingUp";
import AccessTimeIcon from "@mui/icons-material/AccessTime";
import DynamicTable from "../../components/common/DynamicTable";
import BackButton from "../../components/common/BackButton";

const TournamentReferralDetails = () => {

    const [referral, setReferral] = useState([])
    const [tournamentLoading, setTournamentLoading] = useState(false);

    const [tournaments, setTournaments] = useState([]);
    const [selectedTournament, setSelectedTournament] = useState(null);

    // States for form inputs
    const [search, setSearch] = useState({
        cbid: "",
        tournamentTitle: "",
    });

    const toast = UseToast();
    const [loading, setLoading] = useState(false);
    const [page, setPage] = useState(1);
    const [totalPages, setTotalPages] = useState(1);
    const [title, setTitle] = useState('')

    const fetchReferral = async () => {
        setLoading(true);
        try {
            console.log("tournament", selectedTournament)
            if (!selectedTournament) {
                toast.error("Please select a tournament");
                return;
            }
            const Id = selectedTournament.id
            const response = await Client.get(`admin/details/referral?tournamentId=${Id}`, {
            });
            if (!response.data.success) {
                toast.info(response.data.error.massage);
                return;
            }
            if (response.data.status === 204) {
                toast.info("No Player found");
                setReferral([]);
                return;
            }

            setReferral(response.data.data);
        } catch (error) {
            console.error("Error searching players:", error);
            toast.error("An error occurred while searching for players");
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        fetchTournaments();
    }, []);

    const fetchTournaments = useCallback(async (title) => {
        setTournamentLoading(true);
        let newData = {};
        if (title) { newData.title = title }

        try {
            const response = await Client.get("admin/details/referral/tournaments", {
                params: { limit: 100, page: 1, ...newData },
            });

            if (response.status === 204 || !response.data.success) {
                setTournaments([]);
                return;
            }

            setTournaments(response.data.data.tournaments || []);
        } catch (error) {
            console.error("Error fetching tournaments:", error);
            toast.error("Failed to load tournaments");
        } finally {
            setTournamentLoading(false);
        }
    }, []);

    const handleTournamentChange = (_, newValue) => {
        console.log("new Value", newValue)
        setSelectedTournament(newValue);
        if (newValue) {
            setSearch((prev) => ({ ...prev, tournamentTitle: newValue.title }));
        } else {
            setSearch((prev) => ({ ...prev, tournamentTitle: "" }));
        }
    };
    const handleOnchange = (_, value) => {
        setTitle(value);
        fetchTournaments(value)
    }

    const handleReset = () => {
        // Reset main search form
        setSearch({
            cbid: "",
            tournamentTitle: "",
        });
    }

  useEffect(()=>{
    if(search.tournamentTitle){
    fetchReferral()}
  },[search.tournamentTitle])
    const handleSearch = () => {
        fetchReferral();
    };
    const handlePageChange = (newPage) => {
        setPage(newPage);
        // If paginated endpoint, call fetchReferral again with new page
    };

    return (
        <Container maxWidth="xl" sx={{ py: 4, pt: 2, pb: 8, minHeight: "100vh" }}>


            <Paper
                sx={{ mb: 3, p: 3, bgcolor: "#f9f9f9", borderRadius: 2, boxShadow: 3 }}
            >
                <Typography
                    variant="h5"
                    gutterBottom
                    sx={{ mb: 2, fontWeight: "bold", color: "#3f51b5" }}
                >
                    Tournament Referral History
                </Typography>
                <Grid container spacing={3} alignItems="center">
                    <Grid item xs={12} sm={6} md={6}>
                        <Autocomplete
                            options={tournaments}
                            getOptionLabel={(option) =>
                                option && option.title ? option.title.replace(/-/g, " ") : ""
                            }
                            value={selectedTournament}
                            onChange={handleTournamentChange}
                            loading={tournamentLoading}
                            renderInput={(params) => (
                                <TextField
                                    {...params}
                                    variant="outlined"
                                    placeholder="Select a tournament"
                                    onChange={(event) => handleOnchange(event, event.target.value)}
                                    fullWidth
                                    InputProps={{
                                        ...params.InputProps,
                                        endAdornment: (
                                            <React.Fragment>
                                                {tournamentLoading ? (
                                                    <CircularProgress color="inherit" size={20} />
                                                ) : null}
                                                {params.InputProps.endAdornment}
                                            </React.Fragment>
                                        ),
                                    }}
                                    sx={{ bgcolor: "white" }}
                                />
                            )}
                        />
                    </Grid>

                    <Grid item xs={12} sm={6} md={3}>
                        <TextField
                            variant="outlined"
                            fullWidth
                            label="CBID"
                            value={search.cbid}
                            onChange={(e) =>
                                setSearch({ ...search, cbid: e.target.value })
                            }
                            sx={{ bgcolor: "white" }}
                            placeholder="Enter CBID"
                        />
                    </Grid>


                    <Grid item xs={12} sm={6} md={12} sx={{ display: "flex", gap: 1 }}>
                        <Button
                            variant="outlined"
                            color="secondary"
                            sx={{ width: "40px" }}
                            onClick={handleReset}
                            disabled={loading}
                            title="Reset all filters and refresh data"
                        >
                            <RestartAlt />
                        </Button>
                        <Button
                            variant="contained"
                            color="primary"
                            fullWidth
                            onClick={handleSearch}
                            startIcon={<SearchIcon />}
                            sx={{
                                bgcolor: "#3f51b5",
                                textTransform: "none",
                                height: "56px",
                                fontSize: "16px",
                                fontWeight: "bold",
                            }}
                        >
                            Search
                        </Button>
                    </Grid>
                </Grid>
            </Paper>

            {loading && <LinearProgress sx={{ mb: 2 }} />}
            <Typography variant="h6" mb={2}>Tournament Referral </Typography>

            {/* referral Table */}
            <DynamicTable
                columns={[
                    {
                        id: "cbid",
                        label: "Referral ID",
                    },
                    {
                        id: "name",
                        label: "Name",
                        format: (_, row) => row.name || "-",
                    },
                    {
                        id: "email",
                        label: "Email",
                        format: (_, row) => row.email || "-",
                    },
                    {
                        id: "phone",
                        label: "Phone",
                        format: (_, row) => row.phone || "-",
                    },
                    {
                        id: "referralCount",
                        label: "Referral Count",
                        format: (_, row) => row.referralCount ?? 0,
                    },
                ]}
                data={referral}
                loading={loading}
                page={page}
                totalPages={totalPages}
                onPageChange={handlePageChange}
                showDetailsButton={false}
                tableContainerProps={{
                    sx: {
                        minHeight: "400px",
                        maxHeight: "600px",
                    },
                }}
            />
        </Container>
    );
};

export default TournamentReferralDetails;
