import React, { useState } from "react";
import {
  Box,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  ListItemSecondaryAction,
  IconButton,
  Typography,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Tooltip,
  Divider,
} from "@mui/material";
import {
  InsertDriveFile,
  PictureAsPdf,
  Image,
  Delete,
  Download,
  Description,
} from "@mui/icons-material";
import { Client } from "../../api/client";

const DocumentList = ({ documents, onDelete }) => {
  const [confirmDelete, setConfirmDelete] = useState(null);
  const [previewDoc, setPreviewDoc] = useState(null);

  // Function to get the appropriate icon based on file type
  const getFileIcon = (fileType) => {
    if (fileType.startsWith("image/")) {
      return <Image color="primary" />;
    } else if (fileType === "application/pdf") {
      return <PictureAsPdf color="error" />;
    } else if (
      fileType === "application/msword" ||
      fileType ===
        "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
    ) {
      return <Description color="info" />;
    } else {
      return <InsertDriveFile color="action" />;
    }
  };

  // Function to format the date
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return (
      new Intl.DateTimeFormat("en-US", {
        month: "short",
        day: "2-digit",
        year: "numeric",
      }).format(date) || "Unknown date"
    ); // e.g., "May 13, 2025"
  };

  // Function to handle document download
  // Function to handle document download directly from S3 URL
  // Frontend code
  const handleDownload = async (doc) => {
    try {
      // Request a short-lived pre-signed URL from your backend
      const response = await Client.get(
        `/player/documents/${doc.id}/download-url`
      );
      const { presignedUrl } = response.data;
      // Use the pre-signed URL (typically valid for 5-15 minutes)
      const fileResponse = await fetch(presignedUrl);
      const blob = await fileResponse.blob();
      // Download the file
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.setAttribute("download", doc.name + getFileExtension(doc.fileType));
      document.body.appendChild(link);
      link.click();

      // Clean up
      link.parentNode.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error("Error downloading document:", error);
    }
  };

  // Helper function to get file extension from mime type
  const getFileExtension = (mimeType) => {
    const extensions = {
      "image/png": ".png",
      "image/jpeg": ".jpg",
      "image/jpg": ".jpg",
      "application/pdf": ".pdf",
      "application/msword": ".doc",
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document":
        ".docx",
    };
    return extensions[mimeType] || "";
  };

  // Function to handle document preview
  const handlePreview = (document) => {
    if (document.fileType.startsWith("image/")) {
      setPreviewDoc(document);
    } else {
      // For non-image files, just download them
      handleDownload(document);
    }
  };

  return (
    <>
      {documents.length === 0 ? (
        <Box
          display="flex"
          justifyContent="center"
          alignItems="center"
          height="300px"
          flexDirection="column"
        >
          <InsertDriveFile
            sx={{ fontSize: 60, color: "text.disabled", mb: 2 }}
          />
          <Typography variant="h6" color="black" sx={{fontSize:{xs:"0.8rem",md:"1rem"}}}>
            No documents uploaded yet
          </Typography>
        </Box>
      ) : (
        <List>
          {documents.map((document) => (
            <React.Fragment key={document.id}>
              <ListItem
                button
                onClick={() => handlePreview(document)}
                sx={{
                  borderRadius: 1,
                  "&:hover": { bgcolor: "action.hover" },
                  mb: 1,
                }}
              >
                <ListItemIcon>{getFileIcon(document.fileType)}</ListItemIcon>
                <ListItemText
                  primary={document.name}
                  sx={{
                    color: "black",
                    "& .MuiListItemText-primary": {
                      fontSize: 16,
                      fontWeight: 500,
                      textAlign: "left",
                    },
                    "& .MuiListItemText-secondary": {
                      fontSize: 14,
                      color: "black",
                      textAlign: "left",
                    },
                  }}
                  secondary={
                    <>
                      <Typography
                        component="span"
                        sx={{ fontSize: 14, color: "black", textAlign: "left" }}
                      >
                        {(document.size / 1024).toFixed(1)} KB
                        {" • "}
                        {formatDate(document.createdAt)}
                      </Typography>
                    </>
                  }
                />
                <ListItemSecondaryAction>
                  <Tooltip title="Download">
                    <IconButton
                      edge="end"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleDownload(document);
                      }}
                    >
                      <Download />
                    </IconButton>
                  </Tooltip>
                  <Tooltip title="Delete">
                    <IconButton
                      edge="end"
                      onClick={(e) => {
                        e.stopPropagation();
                        setConfirmDelete(document);
                      }}
                    >
                      <Delete />
                    </IconButton>
                  </Tooltip>
                </ListItemSecondaryAction>
              </ListItem>
              <Divider variant="inset" component="li" />
            </React.Fragment>
          ))}
        </List>
      )}

      {/* Delete Confirmation Dialog */}
      <Dialog open={!!confirmDelete} onClose={() => setConfirmDelete(null)}>
        <DialogTitle>Confirm Delete</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to delete the document "{confirmDelete?.name}
            "? This action cannot be undone.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button sx={{ fontSize: 16 }} onClick={() => setConfirmDelete(null)}>
            Cancel
          </Button>
          <Button
            onClick={() => {
              onDelete(confirmDelete.id);
              setConfirmDelete(null);
            }}
            sx={{ fontSize: 16 }}
            color="error"
          >
            Delete
          </Button>
        </DialogActions>
      </Dialog>

      {/* Image Preview Dialog */}
      <Dialog
        open={!!previewDoc}
        onClose={() => setPreviewDoc(null)}
        maxWidth="md"
      >
        <DialogTitle>{previewDoc?.name}</DialogTitle>
        <DialogContent>
          {previewDoc?.fileType.startsWith("image/") && (
            <Box
              component="img"
              src={`${previewDoc.url}`}
              alt={previewDoc.name}
              sx={{ maxWidth: "100%", maxHeight: "50vh" }}
            />
          )}
        </DialogContent>
        <DialogActions>
          <Button sx={{ fontSize: 16 }} onClick={() => setPreviewDoc(null)}>
            Close
          </Button>
          <Button
            sx={{ fontSize: 16 }}
            onClick={() => handleDownload(previewDoc)}
            color="primary"
          >
            Download
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default DocumentList;
