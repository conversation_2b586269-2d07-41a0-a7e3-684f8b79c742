import React, { useState, useCallback, useEffect, useRef } from "react";
import { TextField, Chip, Box, Stack, Typography } from "@mui/material";
import { Button } from "@mui/material";
import { Add } from "@mui/icons-material";

const FieldDownText = ({
  highestValueSetter,
  value,
  name,
  setValue,
  validateField,
  error,
  helperText,
}) => {
  const [chipList, setChipList] = useState(value ? value.split("/") : []);
  const [inputText, setInputText] = useState("");
  const [localError, setLocalError] = useState(""); // For validation errors not from the parent

  // Refs to avoid dependency on function props
  const setValueRef = useRef(setValue);
  const highestValueSetterRef = useRef(highestValueSetter);
  const validateFieldRef = useRef(validateField);
  const nameRef = useRef(name);

  // Update refs when props change
  useEffect(() => {
    setValueRef.current = setValue;
    highestValueSetterRef.current = highestValueSetter;
    validateFieldRef.current = validateField;
    nameRef.current = name;
  }, [setValue, highestValueSetter, validateField, name]);

  // Validate field whenever chipList changes
  useEffect(() => {
    const joinedValue = chipList.join("/");
    validateFieldRef.current(nameRef.current, joinedValue);
  }, [chipList]);

  // Update the parent formData when chipList changes
  useEffect(() => {
    const joinedValue = chipList.join("/");
    setValueRef.current(joinedValue);

    const highestNumber = getHighestNumber();
    if (typeof highestValueSetterRef.current === "function") {
      highestValueSetterRef.current(highestNumber);
    }
  }, [chipList]);

  // Sync with external value changes
  useEffect(() => {
    const valueArray = value ? value.split("/") : [];
    if (JSON.stringify(valueArray) !== JSON.stringify(chipList)) {
      setChipList(valueArray);
    }
  }, [value]);

  const handleInputChange = (event) => {
    setInputText(event.target.value);
  };

  const handleInputSubmit = (event) => {
    if (event.key === "Enter" || event.key === "/") {
      event.preventDefault();
      addChip();
    }
  };

  const handleButtonClick = (event) => {
    event.preventDefault();
    addChip();
  };

  const addChip = useCallback(() => {
    const trimmedValue = inputText.trim().toLowerCase();

    // Validate input and add chip
    if (
      (trimmedValue.match(/^u([5-9]|[1-9][0-9]|1[0-4][0-9]|150)$/) ||
        trimmedValue === "open") &&
      !chipList.includes(trimmedValue)
    ) {
      setChipList((prev) => [...prev, trimmedValue]);
      setInputText(""); // Clear input field after adding
      setLocalError(""); // Clear local error after valid input
    } else {
      setLocalError("Invalid value. Please enter a valid age category.");
    }
  }, [inputText, chipList]);

  const handleDelete = useCallback((chipToDelete) => {
    setChipList((chips) => chips.filter((chip) => chip !== chipToDelete));
  }, []);

  // Function to get the highest number from chipList
  const getHighestNumber = useCallback(() => {
    const numbers = chipList
      .filter((chip) => chip !== "open") // Exclude "open"
      .map((chip) => parseInt(chip.slice(1), 10));

    return numbers.length > 0 ? Math.max(...numbers) : 0;
  }, [chipList]);

  return (
    <Box>
      <Stack
        direction="row"
        spacing={2}
        sx={{
          mt: 1,
          justifyContent: "space-between",
          alignItems: "flex-start",
        }}
      >
        <TextField
          fullWidth
          value={inputText}
          sx={{ minHeight: 70 }}
          onChange={handleInputChange}
          onKeyPress={handleInputSubmit}
          placeholder="U8 / U10 / U12 / U15 / U18 / U20 / Open"
          error={!!localError || error} // Use both error sources
          helperText={localError || helperText} // Show local error first, then parent error
        />
        <Button
          variant="contained"
          sx={{ bgcolor: "green", px: 3, py: 2, backgroundColor: "#2c2891" }}
          startIcon={<Add sx={{ color: "white", fontSize: 20 }} />}
          onClick={handleButtonClick}
        >
          <Typography variant="h6" sx={{ color: "white", fontSize: 16 }}>
            Add
          </Typography>
        </Button>
      </Stack>
      <Box sx={{ display: "flex", flexWrap: "wrap", gap: 1, mt: 2 }}>
        {chipList.map((chip, index) => (
          <Chip
            key={index}
            label={chip}
            sx={{ bgcolor: "lightgreen" }}
            onDelete={() => handleDelete(chip)}
          />
        ))}
      </Box>
    </Box>
  );
};

export default FieldDownText;