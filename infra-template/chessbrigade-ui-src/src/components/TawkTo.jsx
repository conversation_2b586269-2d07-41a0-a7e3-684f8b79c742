import { useEffect, useRef } from "react";

const TawkTo = () => {
  const currentUserRef = useRef(null);

  // Function to set user attributes with comprehensive ID support
  const setUserAttributes = (user) => {
    if (user && typeof user === 'object' && window.Tawk_API && typeof window.Tawk_API.setAttributes === 'function') {
      // Get specific ID based on user type
      let specificId = '';
      if (user.role === 'player') {
        specificId = user.playerId || user.id || user.cbid || 'N/A';
      } else if (user.role === 'club') {
        specificId = user.clubId || user.id || user.cbid || 'N/A';
      } else if (user.role === 'arbiter') {
        specificId = user.arbiterId || user.id || user.cbid || 'N/A';
      } else {
        specificId = user.id || user.cbid || 'N/A';
      }

      const attributes = {
        'User Type': user.role || 'player',
        'Primary ID': specificId,
        'CBID': user.cbid || 'N/A',
        'Phone': user.phoneNumber || 'N/A',
        'Email': user.email || 'N/A',
        'Current Page': window.location?.pathname || 'Unknown',
        'Login Time': new Date().toISOString(),
        'Session ID': Date.now() + '_' + specificId,
      };

      // Add role-specific attributes
      if (user.role === 'player') {
        attributes['Player ID'] = user.playerId || user.id || 'N/A';
        attributes['Player Name'] = user.name || 'N/A';
        attributes['FIDE ID'] = user.fideId || 'N/A';
        attributes['AICF ID'] = user.aicfId || 'N/A';
        attributes['State ID'] = user.stateId || 'N/A';
        attributes['District ID'] = user.districtId || 'N/A';
        attributes['FIDE Rating'] = user.fideRating || 'N/A';
      } else if (user.role === 'club') {
        attributes['Club ID'] = user.clubId || user.id || 'N/A';
        attributes['Club Name'] = user.clubName || user.name || 'N/A';
        attributes['Contact Person'] = user.contactPerson || 'N/A';
        attributes['Contact Person Phone'] = user.contactPersonPhoneNumber || 'N/A';
        attributes['Contact Person Email'] = user.contactPersonEmail || 'N/A';
        attributes['Address'] = user.address || 'N/A';
        attributes['City'] = user.city || 'N/A';
        attributes['State'] = user.state || 'N/A';
        attributes['Country'] = user.country || 'N/A';
      } else if (user.role === 'arbiter') {
        attributes['Arbiter ID'] = user.arbiterId || user.id || 'N/A';
        attributes['Arbiter Name'] = user.arbiterName || user.name || 'N/A';
        attributes['Alternate Contact'] = user.alternateContactNumber || 'N/A';
        attributes['FIDE ID'] = user.fideId || 'N/A';
        attributes['AICF ID'] = user.aicfId || 'N/A';
        attributes['State ID'] = user.stateId || 'N/A';
        attributes['District ID'] = user.districtId || 'N/A';
      }

      window.Tawk_API.setAttributes(attributes, (error) => {
        if (error) {
          console.warn('Tawk.to setAttributes error:', error);
        } else {
          console.log('Tawk.to attributes set for user:', user.role, specificId);
        }
      });
    }
  };

  useEffect(() => {
    try {
      // Initialize Tawk_API safely
      if (typeof window === 'undefined') return;

      window.Tawk_API = window.Tawk_API || {};
      window.Tawk_LoadStart = new Date();

      // Get user data safely
      let user = null;
      try {
        const userData = localStorage.getItem('user');
        user = userData ? JSON.parse(userData) : null;
      } catch (error) {
        console.warn('Failed to get user data for TawkTo:', error);
      }

      // Check if user has changed - use specific ID based on role
      let currentUserId = 'guest';
      if (user && typeof user === 'object') {
        if (user.role === 'player') {
          currentUserId = user.playerId || user.id || user.cbid || 'guest';
        } else if (user.role === 'club') {
          currentUserId = user.clubId || user.id || user.cbid || 'guest';
        } else if (user.role === 'arbiter') {
          currentUserId = user.arbiterId || user.id || user.cbid || 'guest';
        } else {
          currentUserId = user.id || user.cbid || 'guest';
        }
      }
      const previousUserId = currentUserRef.current;

      // If user has changed, end the current session and start a new one
      if (previousUserId && previousUserId !== currentUserId) {
        console.log('User changed from', previousUserId, 'to', currentUserId);

        // End current session
        if (window.Tawk_API && typeof window.Tawk_API.endChat === 'function') {
          window.Tawk_API.endChat();
        }

        // Clear visitor data
        if (window.Tawk_API) {4
          window.Tawk_API.visitor = {};
        }

        // Force reload Tawk.to widget with new user
        setTimeout(() => {
          if (window.Tawk_API && typeof window.Tawk_API.showWidget === 'function') {
            window.Tawk_API.hideWidget();
            setTimeout(() => {
              window.Tawk_API.showWidget();
            }, 500);
          }
        }, 1000);
      }

      // Update current user reference
      currentUserRef.current = currentUserId;

      // Set visitor info with unique session identifier
      const sessionId = Date.now() + '_' + Math.random().toString(36).substring(2, 11);

      if (user && typeof user === 'object') {
        // Create display name with specific ID
        let userDisplayName = '';
        let specificId = '';

        if (user.role === 'player') {
          specificId = user.playerId || user.id || user.cbid || 'N/A';
          userDisplayName = `${user.name || 'Unknown Player'} (Player ID: ${specificId})`;
        } else if (user.role === 'club') {
          specificId = user.clubId || user.id || user.cbid || 'N/A';
          userDisplayName = `${user.clubName || user.name || 'Unknown Club'} (Club ID: ${specificId})`;
        } else if (user.role === 'arbiter') {
          specificId = user.arbiterId || user.id || user.cbid || 'N/A';
          userDisplayName =` ${user.arbiterName || user.name || 'Unknown Arbiter'} (Arbiter ID: ${specificId})`;
        } else {
          specificId = user.id || user.cbid || 'N/A';
          userDisplayName = `${user.name || 'Unknown User'} (${user.role || 'user'})`;
        }

        window.Tawk_API.visitor = {
          name: userDisplayName,
          email: user.email || '',
          custom: {
            userId: user.id || user.cbid || 'guest',
            userType: user.role || 'player',
            specificId: specificId,
            playerId: user.playerId || '',
            clubId: user.clubId || '',
            arbiterId: user.arbiterId || '',
            cbid: user.cbid || '',
            phoneNumber: user.phoneNumber || '',
            clubName: user.clubName || '',
            arbiterName: user.arbiterName || '',
            sessionId: sessionId,
            loginTime: new Date().toISOString(),
          },
        };
      } else {
        // For guest users
        window.Tawk_API.visitor = {
          name: 'Guest User',
          email: '',
          custom: {
            userId: 'guest_' + Date.now(),
            userType: 'guest',
            sessionId: sessionId,
          },
        };
      }

      // Load Tawk.to script only once
      if (!document.getElementById('tawk-script')) {
        const script = document.createElement('script');
        script.id = 'tawk-script';
        script.async = true;
        script.src = 'https://embed.tawk.to/6868dc77ce69b4190f3cf0b7/1ivcpqdgk';
        script.setAttribute('crossorigin', '*');

        // Add onload handler to set visitor info after script loads
        script.onload = () => {
          try {
            // Wait for Tawk.to to fully initialize
            setTimeout(() => {
              setUserAttributes(user);
            }, 2000);
          } catch (error) {
            console.warn('Tawk.to initialization error:', error);
          }
        };

        script.onerror = (error) => {
          console.warn('Failed to load Tawk.to script:', error);
        };

        if (document.body) {
          document.body.appendChild(script);
        }
      }
    } catch (error) {
      console.warn('TawkTo component error:', error);
    }
  }, []);

  // Monitor user changes
  useEffect(() => {
    const checkUserChange = () => {
      try {
        const userData = localStorage.getItem('user');
        const user = userData ? JSON.parse(userData) : null;

        // Get current user ID based on role
        let currentUserId = 'guest';
        if (user && typeof user === 'object') {
          if (user.role === 'player') {
            currentUserId = user.playerId || user.id || user.cbid || 'guest';
          } else if (user.role === 'club') {
            currentUserId = user.clubId || user.id || user.cbid || 'guest';
          } else if (user.role === 'arbiter') {
            currentUserId = user.arbiterId || user.id || user.cbid || 'guest';
          } else {
            currentUserId = user.id || user.cbid || 'guest';
          }
        }

        if (currentUserRef.current && currentUserRef.current !== currentUserId) {
          console.log('User changed detected, updating Tawk.to identity from', currentUserRef.current, 'to', currentUserId);

          // Update visitor info immediately
          if (window.Tawk_API) {
            if (user && typeof user === 'object') {
              // Create display name with specific ID
              let userDisplayName = '';
              let specificId = '';

              if (user.role === 'player') {
                specificId = user.playerId || user.id || user.cbid || 'N/A';
                userDisplayName =` ${user.name || 'Unknown Player'} (Player ID: ${specificId})`;
              } else if (user.role === 'club') {
                specificId = user.clubId || user.id || user.cbid || 'N/A';
                userDisplayName = `${user.clubName || user.name || 'Unknown Club'} (Club ID: ${specificId})`;
              } else if (user.role === 'arbiter') {
                specificId = user.arbiterId || user.id || user.cbid || 'N/A';
                userDisplayName = `${user.arbiterName || user.name || 'Unknown Arbiter'} (Arbiter ID: ${specificId})`;
              } else {
                specificId = user.id || user.cbid || 'N/A';
                userDisplayName = `${user.name || 'Unknown User'} (${user.role || 'user'})`;
              }

              window.Tawk_API.visitor = {
                name: userDisplayName,
                email: user.email || '',
                custom: {
                  userId: user.id || user.cbid || 'guest',
                  userType: user.role || 'player',
                  specificId: specificId,
                  playerId: user.playerId || '',
                  clubId: user.clubId || '',
                  arbiterId: user.arbiterId || '',
                  cbid: user.cbid || '',
                  phoneNumber: user.phoneNumber || '',
                  clubName: user.clubName || '',
                  arbiterName: user.arbiterName || '',
                  sessionId: Date.now() + '_' + Math.random().toString(36).substring(2, 11),
                  loginTime: new Date().toISOString(),
                },
              };
            }

            // Set attributes for new user
            setTimeout(() => {
              setUserAttributes(user);
            }, 1000);
          }

          currentUserRef.current = currentUserId;
        }
      } catch (error) {
        console.warn('Error checking user change:', error);
      }
    };

    // Check for user changes every 2 seconds
    const interval = setInterval(checkUserChange, 2000);

    return () => clearInterval(interval);
  }, []);

  return null;
};

export default TawkTo;