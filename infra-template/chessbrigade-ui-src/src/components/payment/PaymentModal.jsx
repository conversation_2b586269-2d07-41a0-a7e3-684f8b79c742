import React, { useEffect, useState, useCallback, useMemo } from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Box,
  CircularProgress,
  Divider,
  IconButton,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Step,
  StepLabel,
  Select,
  MenuItem,
  TextField,
} from "@mui/material";
import CheckCircleIcon from "@mui/icons-material/CheckCircle";
import CloseIcon from "@mui/icons-material/Close";
import PaymentIcon from "@mui/icons-material/Payment";
import UseToast from "../../lib/hooks/UseToast";
import UseGlobalContext from "../../lib/hooks/UseGlobalContext";
import getEligibleCategories from "../../utils/age";
import { useRazorpayPayment } from "../../lib/hooks/useRazorPayPayment";
import Spinner from "../common/Spinner";
import { Client } from "../../api/client";

// Constants for better maintainability
const STEPS = ["Eligibility Check", "Payment"];
const PAYMENT_STEP_INDEX = 1;

const PaymentModal = ({ open, onClose, tournament, userDetails }) => {
  // State management
  const [activeStep, setActiveStep] = useState(0);
  const [gender, setGender] = useState("");
  const [ageGroup, setAgeGroup] = useState("");
  const [referral, setReferral] = useState("");
  const [eligibilityError, setEligibilityError] = useState("");
  const [isProcessingPayment, setIsProcessingPayment] = useState(false);
  const [loading, setLoading] = useState(false);
  const [amount, setAmount] = useState({
    total: 0,
    platformFee: 0,
    tournamentFee: 0,
  });

  // Hooks
  const { user } = UseGlobalContext();
  const toast = UseToast();
  const {
    initiatePayment,
    isLoading: paymentLoading,
    error: paymentError,
    resetError: resetPaymentError,
    isReady: paymentReady,
    resetState: resetPaymentState,
  } = useRazorpayPayment(toast);

  // Memoized values for performance
  const tournamentCategory = tournament?.tournamentCategory;
  const userGender = userDetails?.gender;
  const userDob = userDetails?.dob;

  const fetchPlatformFee = useCallback(async () => {
    try {
      const response = await Client.get("/tournament/platform-fee");
      if (response.data.success) {
        const platformFeePercentage = parseFloat(response.data.data.fee);
        const tournamentFee = Number(tournament?.entryFee);
        const platformFee = ((tournamentFee * platformFeePercentage) / 100).toFixed(2);

        const total = Number(tournamentFee) + Number(platformFee);
        setAmount((prev) => ({
          ...prev,
          tournamentFee: tournamentFee,
          platformFee: platformFee,
          total: total,
        }));
      }
    } catch (error) {
      console.error("Error fetching platform fee:", error);
    }
  }, []);

  // Calculate eligible categories with dependency optimization
  const eligibleCategories = useMemo(() => {
    if (!userDob || !gender || !tournament) return [];

    return getEligibleCategories(
      userDob,
      gender,
      tournament.maleAgeCategory,
      tournament.femaleAgeCategory
    );
  }, [
    userDob,
    gender,
    tournament?.maleAgeCategory,
    tournament?.femaleAgeCategory,
  ]);

  // Memoized menu items for gender selection
  const genderMenuItems = useMemo(() => {
    if (tournamentCategory === "open") {
      return userGender === "male"
        ? [
          <MenuItem key="male" value="male">
            Male
          </MenuItem>,
        ]
        : [
          <MenuItem key="male" value="male">
            Male
          </MenuItem>,
          <MenuItem key="female" value="female">
            Female
          </MenuItem>,
        ];
    }

    return [
      <MenuItem key={tournamentCategory} value={tournamentCategory}>
        {tournamentCategory?.charAt(0)?.toUpperCase() +
          tournamentCategory?.slice(1)}
      </MenuItem>,
    ];
  }, [tournamentCategory, userGender]);

  // Reset modal state when opening/closing - FIXED
  const handleClose = useCallback(() => {
    setActiveStep(0);
    setGender("");
    setAgeGroup("");
    setEligibilityError("");
    setIsProcessingPayment(false);
    setLoading(false);
    resetPaymentState();
    setReferral('');
    onClose();
  }, [onClose, resetPaymentState]);

  // Reset state when modal opens - FIXED
  useEffect(() => {
    if (open) {
      // Reset all state when modal opens
      setActiveStep(0);
      setGender("");
      setAgeGroup("");
      setEligibilityError("");
      setIsProcessingPayment(false);
      setLoading(false);
      fetchPlatformFee();
    }
  }, [open, fetchPlatformFee]);

  // Initialize gender based on tournament category and user gender - FIXED
  useEffect(() => {
    // Only initialize if modal is open and we have the required data
    if (!open || !tournamentCategory || !userGender) return;

    if (tournamentCategory === "open") {
      setGender(userGender);
    } else if (["male", "female", "children", "children-open"].includes(tournamentCategory)) {
      setGender(tournamentCategory);
    }
  }, [open, tournamentCategory, userGender]); // Added 'open' dependency

  // Check eligibility when categories change - FIXED
  useEffect(() => {
    // Only check eligibility if modal is open
    if (!open) return;

    if (eligibleCategories === false || eligibleCategories.length === 0) {
      setEligibilityError(
        "You are not eligible to register for any age group in this tournament."
      );
    } else {
      setEligibilityError("");
    }
  }, [open, eligibleCategories]); // Added 'open' dependency

  // Reset age group when gender changes - FIXED
  useEffect(() => {
    if (open && gender) {
      setAgeGroup(""); // Reset age group when gender changes
    }
  }, [open, gender]);

  // Validation function for tournament eligibility
  const validateTournamentEligibility = useCallback(() => {
    if (!gender) {
      toast.error("Please select your gender category");
      return false;
    }

    if (!ageGroup) {
      toast.error("Please select an age group");
      return false;
    }

    // Strict gender validation for male/female only tournaments
    if (tournamentCategory === "female" && userGender !== "female") {
      toast.error("Only female players can register for this tournament.");
      return false;
    }

    return true;
  }, [gender, ageGroup, tournamentCategory, userGender, toast]);

  // Handle next step with validationF
  const handleNext =  async() => {
    if (!validateTournamentEligibility()) return;
      console.log("Referral before API:", referral);
    if(referral){
    try {
      const response = await Client.post("/payment/check-referral",{referral});
      if (response.data.success) {
        toast.success(response.data.message)
        setActiveStep(PAYMENT_STEP_INDEX);
      }
    } catch (error) {
      console.log(error.response.data.message)
      toast.error(error.response.data.message)
      console.error("Error fetching platform fee:", error);
    }}else{
      setActiveStep(PAYMENT_STEP_INDEX);
    }
  };
  // Handle payment initiation
  const handleProceedToPayment = useCallback(async () => {
    if (!paymentReady || paymentLoading || isProcessingPayment) {
      toast.warn("Payment system is not ready. Please try again.");
      return;
    }

    if (!tournament?.id || !ageGroup || !gender) {
      toast.error(
        "Missing payment information. Please go back and check your selection."
      );
      return;
    }

    setLoading(true);
    setIsProcessingPayment(true);
    resetPaymentError();

    try {
      const result = await initiatePayment(tournament.id, ageGroup, gender, referral);

      if (!result.success) {
        toast.error(result.error?.message || "Payment initiation failed");
        setLoading(false); // Reset loading on error
      }
    } catch (error) {
      console.error("Payment error:", error);
      toast.error("Failed to initiate payment. Please try again.");
      setLoading(false);
    } finally {
      setIsProcessingPayment(false);
    }
  }, [
    paymentReady,
    paymentLoading,
    isProcessingPayment,
    tournament?.id,
    ageGroup,
    gender,
    initiatePayment,
    resetPaymentError,
    toast,
    referral
  ]);

  // Early return if no tournament data
  if (!tournament || !userDetails) {
    return null;
  }

  const handleChange = (event) =>{
  setReferral(event.target.value)
  }
  // Render eligibility check step
  console.log("referral",referral)
  if (activeStep === 0) {
    return (
      <Dialog open={open} onClose={handleClose} maxWidth="sm" fullWidth>
        <DialogTitle
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
          }}
        >
          <Typography variant="h5">Tournament Registration</Typography>
          <IconButton onClick={handleClose} size="small" aria-label="Close">
            <CloseIcon />
          </IconButton>
        </DialogTitle>

        <DialogContent>
          <Stepper activeStep={0} alternativeLabel sx={{ mb: 4, mt: 2 }}>
            {STEPS.map((label) => (
              <Step key={label}>
                <StepLabel>
                  <Typography variant="h6" sx={{ fontSize: 16 }}>
                    {label}
                  </Typography>
                </StepLabel>
              </Step>
            ))}
          </Stepper>

          {eligibilityError ? (
            <Alert severity="error" sx={{ mb: 2 }}>
              <Typography>{eligibilityError}</Typography>
            </Alert>
          ) : (
            <>
              <Box sx={{ mb: 3 }}>
                <Typography variant="subtitle1" fontWeight="bold" mb={2}>
                  Choose Your Category:
                </Typography>

                <Box sx={{ display: "flex", flexDirection: "column", gap: 2 }}>
                  {/* Gender Category Selection */}
                  <Box>
                    <Typography sx={{ fontWeight: 600, fontSize: 16, mb: 0.5 }}>
                      Category
                    </Typography>
                    <Select
                      fullWidth
                      value={gender}
                      disabled={tournamentCategory !== "open"}
                      size="small"
                      displayEmpty
                      onChange={(e) => setGender(e.target.value)}
                    >
                      <MenuItem value="" disabled>
                        Select Category
                      </MenuItem>
                      {genderMenuItems}
                    </Select>
                  </Box>

                  {/* Age Group Selection */}
                  <Box>
                    <Typography sx={{ fontWeight: 600, fontSize: 16, mb: 0.5 }}>
                      Age Group
                    </Typography>
                    <Select
                      fullWidth
                      value={ageGroup}
                      size="small"
                      displayEmpty
                      disabled={!gender || eligibleCategories.length === 0}
                      onChange={(e) => setAgeGroup(e.target.value)}
                    >
                      <MenuItem value="" disabled>
                        {!gender ? "Select category first" : "Select Age Group"}
                      </MenuItem>
                      {Array.isArray(eligibleCategories) &&
                        eligibleCategories.map((category, index) => (
                          <MenuItem key={index} value={category}>
                            {category}
                          </MenuItem>
                        ))}
                    </Select>
                  </Box>

                  {/* Referral Id */}
                  <Box>
                    <Typography sx={{ fontWeight: 600, fontSize: 16, mb: 0.5 }}>
                      Referral Id
                    </Typography>
                    <TextField
                      variant="outlined"
                      value={referral}
                      onChange={(e)=>setReferral(e.target.value)}
                      fullWidth
                      placeholder="Enter Your Referral Id "
                    />
                  </Box>

                </Box>
              </Box>

              <Box
                sx={{
                  display: "flex",
                  justifyContent: "flex-end",
                  gap: 2,
                  pt: 2,
                }}
              >
                <Button variant="outlined" onClick={handleClose}>
                  Cancel
                </Button>
                <Button
                  variant="contained"
                  onClick={()=>handleNext()}
                  disabled={!gender || !ageGroup || eligibilityError}
                >
                  Next
                </Button>
              </Box>
            </>
          )}
        </DialogContent>
      </Dialog>
    );
  }

  // Render payment step
  return (
    <Dialog open={open} onClose={handleClose} maxWidth="sm" fullWidth>
      <DialogTitle
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
        }}
      >
        <Typography variant="h5">Tournament Registration</Typography>
        <IconButton onClick={handleClose} size="small" aria-label="Close">
          <CloseIcon />
        </IconButton>
      </DialogTitle>
      {loading && (
        <Spinner fullScreen backgroundColor="rgba(255, 255, 255, 0.2)" />
      )}

      <DialogContent>
        <Stepper activeStep={activeStep} alternativeLabel sx={{ mb: 4, mt: 2 }}>
          {STEPS.map((label) => (
            <Step key={label}>
              <StepLabel>
                <Typography variant="h6" sx={{ fontSize: 16 }}>
                  {label}
                </Typography>
              </StepLabel>
            </Step>
          ))}
        </Stepper>

        {paymentError && (
          <Alert severity="error" sx={{ mb: 3, fontSize: "1rem" }}>
            {paymentError.message || "Payment error occurred"}
          </Alert>
        )}

        <Box sx={{ mb: 3 }}>
          <Box sx={{ display: "flex", alignItems: "center", mb: 2 }}>
            <CheckCircleIcon color="success" sx={{ mr: 1 }} />
            <Typography variant="h6">Eligibility Check Successful!</Typography>
          </Box>
          <Typography variant="body1" sx={{ mb: 2 }}>
            You are eligible to participate in this tournament. Please proceed
            to payment to complete your registration.
          </Typography>
          <Alert severity="info" sx={{ fontSize: 16 }}>
            Important: Your registration will only be confirmed after successful
            payment. The system will automatically register you once payment is
            complete.
          </Alert>
        </Box>

        <Divider sx={{ my: 2 }} />

        <Box sx={{ mb: 3 }}>
          <Typography variant="subtitle1" fontWeight="bold" sx={{ mb: 2 }}>
            Payment Details:
          </Typography>

          {[
            { label: "Amount", value: `₹${amount.tournamentFee}` },
            { label: "Platform Fee", value: `₹${amount.platformFee}` },
            {
              label: "Total Amount",
              value: `₹${amount.total}`,
            },
            { label: "Player Name", value: user?.name },
            { label: "Tournament", value: tournament.title },
            { label: "Category", value: gender },
            { label: "Age Group", value: ageGroup },
          ].map(({ label, value }) => (
            <Box
              key={label}
              sx={{ display: "flex", justifyContent: "space-between", mb: 1 }}
            >
              <Typography variant="body1">{label}:</Typography>
              <Typography variant="body1" fontWeight="medium">
                {value}
              </Typography>
            </Box>
          ))}
        </Box>
      </DialogContent>

      <DialogActions sx={{ p: 3 }}>
        <Button
          onClick={() => setActiveStep(0)}
          color="primary"
          disabled={paymentLoading || isProcessingPayment}
        >
          Back
        </Button>
        <Button
          onClick={handleProceedToPayment}
          variant="contained"
          color="primary"
          startIcon={
            paymentLoading || isProcessingPayment ? (
              <CircularProgress size={16} color="inherit" />
            ) : (
              <PaymentIcon />
            )
          }
          disabled={!paymentReady || paymentLoading || isProcessingPayment}
        >
          {paymentLoading || isProcessingPayment
            ? "Processing..."
            : "Proceed to Payment"}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default PaymentModal;
