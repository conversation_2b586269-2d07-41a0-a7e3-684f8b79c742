import React, { useState, useCallback, useMemo } from 'react';
import { Box, CircularProgress, useTheme, useMediaQuery, Skeleton } from '@mui/material';
import Slider from 'react-slick';
import { Client } from '../../api/client';

// Import slick styles
import 'slick-carousel/slick/slick.css';
import 'slick-carousel/slick/slick-theme.css';

const AutoPlayImageCarousel = () => {
  const [images, setImages] = useState([]);
  const [loading, setLoading] = useState(true);
  const [displayTime, setDisplayTime] = useState(3000);

  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const isTablet = useMediaQuery(theme.breakpoints.between('sm', 'md'));

  const fetchImages = useCallback(async () => {
    try {
      const response = await Client.get('/open/banner', { params: { type: 'banner' } });
      setImages(response.data.data || []);
      setLoading(false);

      if (response.data.intervalTime) {
        setDisplayTime(Number(response.data.intervalTime));
      }
    } catch (error) {
      console.error('Failed to load images:', error);
      setLoading(false);
    }
  }, []);

  useMemo(() => {
    fetchImages();
  }, [fetchImages]);

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" height={200}>
        <CircularProgress />
      </Box>
    );
  }

    const getCarouselHeight = () => {
    if (isMobile) return 200;
    if (isTablet) return 300;
    return 450;
  };
  
  if (images.length === 0) return <Skeleton variant="rectangular" height={getCarouselHeight()} />;

  const settings = {
    dots: true,
    infinite: images.length > 1,
    speed: 500,
    slidesToShow: 1,
    slidesToScroll: 1,
    autoplay: true,
    autoplaySpeed: displayTime,
    pauseOnHover: true,
    pauseOnFocus: true,
    arrows: false,
    fade: false,
    cssEase: 'linear',
    responsive: [
      {
        breakpoint: 1024,
        settings: {
          arrows: false,
          dots: true
        }
      },
      {
        breakpoint: 600,
        settings: {
          arrows: false,
          dots: true
        }
      }
    ]
  };

  return (
    <Box
      sx={{
        height: getCarouselHeight(),
        borderRadius: '15px',
        '& .slick-dots': {
          bottom: '10px',
          '& li button:before': {
            color: '#fff',
            fontSize: '12px'
          },
          '& li.slick-active button:before': {
            color: '#fff'
          }
        },
        '& .slick-prev, & .slick-next': {
          zIndex: 1,
          '&:before': {
            fontSize: '20px',
            color: '#fff'
          }
        },
        '& .slick-prev': {
          left: '10px'
        },
        '& .slick-next': {
          right: '10px'
        }
      }}
    >
      <Slider {...settings}>
        {images.map((image, index) => (
          <a
            key={index}
            href={image?.bannerUrl || '#'}
            rel="noopener noreferrer"
            style={{ display: 'block', height: '100%' }}
          >
            <Box sx={{ height: getCarouselHeight() }}>
              <Box
                component="img"
                src={image?.url}
                alt={`carousel-image-${index}`}
                sx={{
                  width: '100%',
                  height: '100%',
                  objectFit: 'cover',
                  display: 'block',
                  borderRadius: '15px'
                }}
                loading="lazy"
                onError={(e) => {
                  console.error(`Failed to load image ${index}:`, e);
                  e.target.style.display = 'none';
                }}
              />
            </Box>
          </a>
        ))}
      </Slider>
    </Box>
  );
};

export default AutoPlayImageCarousel;