import React, { useState, useEffect } from "react";
import {
  Button,
  Grid,
  MenuItem,
  Paper,
  Select,
  TextField,
  CircularProgress,
  Typography,
  Box,
} from "@mui/material";
import {
  ArrowCircleDownRounded,
  RestartAlt,
  Search as SearchIcon,
} from "@mui/icons-material";

import { Client } from "../../api/client";
import useUserGeoInfo from "../../lib/hooks/UseGetlocation";

const CourseSearchForm = ({
  search,
  setSearch,
  handleSearch,
  loading,
  handleReset,
}) => {

  // Handle input changes without triggering search
  const handleInputChange = (field, value) => {
    setSearch((prev) => ({ ...prev, [field]: value }));
  };

  // Handle key press in input fields (for Enter key)
  const handleKeyPress = (e) => {
    if (e.key === "Enter") {
      e.preventDefault();
      handleSearch(1);
    }
  };

  const monthValues = [
    { month: "Jan", value: 1 },
    { month: "Feb", value: 2 },
    { month: "Mar", value: 3 },
    { month: "Apr", value: 4 },
    { month: "May", value: 5 },
    { month: "Jun", value: 6 },
    { month: "Jul", value: 7 },
    { month: "Aug", value: 8 },
    { month: "Sep", value: 9 },
    { month: "Oct", value: 10 },
    { month: "Nov", value: 11 },
    { month: "Dec", value: 12 },
  ];

  const currentYear = new Date().getFullYear();
  const years = Array.from({ length: 7 }, (_, i) => currentYear  + i);

  return (
    <Paper sx={{ mb: 3, p: 2, bgcolor: "#f9f9f9" }}>
      <Box sx={{ mb: 2, display: "flex", alignItems: "center" }}>
        <Typography variant="h5" sx={{ color: "#3f51b5" }}>
          Search Courses
        </Typography>
      </Box>
      <Grid container spacing={2} alignItems="center">
        <Grid item xs={12} sm={4} md={3} lg={2}>
          <TextField
            placeholder="Course Title"
            variant="outlined"
            name="title"
            fullWidth
            size="small"
            value={search.title}
            onChange={(e) => handleInputChange("title", e.target.value)}
            onKeyPress={handleKeyPress}
            sx={{ bgcolor: "white" }}
          />
        </Grid>
        <Grid item xs={12} sm={4} md={3} lg={2}>
          <TextField
            placeholder="Duration (in Weeks)"
            variant="outlined"
            name="duration"
            fullWidth
            size="small"
            type="number"
            value={search.duration}
            onChange={(e) => handleInputChange("duration", e.target.value)}
            onKeyPress={handleKeyPress}
            sx={{ bgcolor: "white" }}
            inputProps={{
              min: 0,
            }}
          />
        </Grid>
        <Grid item xs={12} sm={4} md={3} lg={2}>
          <TextField
            placeholder="Course Fee"
            variant="outlined"
            name="fee"
            fullWidth
            size="small"
            type="number"
            value={search.fee}
            onChange={(e) => handleInputChange("fee", e.target.value)}
            onKeyPress={handleKeyPress}
            sx={{ bgcolor: "white" }}
            inputProps={{
              min: 0,
            }}
          />
        </Grid>
        <Grid item xs={12} sm={4} md={3} lg={2}>
          <Select
            fullWidth
            displayEmpty
            size="small"
            value={search.month}
            onChange={(e) => handleInputChange("month", e.target.value)}
            sx={{ bgcolor: "white" }}
          >
            <MenuItem value="">
              Select Month
            </MenuItem>
            {monthValues.map((month) => (
              <MenuItem key={month.value} value={month.value}>
                {month.month}
              </MenuItem>
            ))}
          </Select>
        </Grid>
        <Grid item xs={12} sm={4} md={3} lg={2}>
          <Select
            fullWidth
            displayEmpty
            size="small"
            value={search.year}
            onChange={(e) => handleInputChange("year", e.target.value)}
            sx={{ bgcolor: "white" }}
          >
            <MenuItem value="">
              Select Year
            </MenuItem>
            {years.map((year) => (
              <MenuItem key={year} value={year}>
                {year}
              </MenuItem>
            ))}
          </Select>
        </Grid>

        <Grid
          item
          xs={12}
          sm={4}
          md={12}
          lg={2}
          sx={{ display: "flex", gap: 1, justifyContent: "flex-end" }}
        >
          <Button
            variant="containedSecondary"
            color="secondary"
            sx={{
              width: "40px",
              minWidth: "40px !important",
            }}
            onClick={handleReset}
            disabled={loading}
          >
            <RestartAlt />
          </Button>
          <Button
            variant="contained"
            color="primary"
            fullWidth
            onClick={() => handleSearch(1)}
            disabled={loading}
            startIcon={
              loading ? (
                <CircularProgress size={20} color="inherit" />
              ) : (
                <SearchIcon />
              )
            }
            sx={{
              bgcolor: "#3f51b5",
              textTransform: "none",
              height: "40px",
              fontSize: "16px",
              maxWidth: {
                xs: "100%",
                sm: "150px",
              },
            }}
          >
            Search
          </Button>
        </Grid>
      </Grid>
    </Paper>
  );
};

export default CourseSearchForm;
