import React from 'react';
import {
  FormControl,
  Select,
  MenuItem,
  Box,
  Typography,
  CircularProgress
} from '@mui/material';
import { useTranslation } from '../../context/TranslationContext';

const LanguageSwitcher = ({ 
  variant = "outlined", 
  size = "small", 
  showLabel = true,
  sx = {} 
}) => {
  const { lang, changeLanguage, loading } = useTranslation();

  const languages = [
    { code: 'en', name: 'En' },
    { code: 'ta', name: 'Ta' },
    { code: 'hi', name: 'Hi' }, //  to show country flag include this - flag: '🇮🇳'
  ];

  const handleLanguageChange = (event) => {
    const newLang = event.target.value;
    changeLanguage(newLang);
  };

  const currentLanguage = languages.find(l => l.code === lang) || languages[0];

  return (
    <Box sx={{ minWidth: 120, ...sx }}>
      {showLabel && (
        <Typography variant="body2" sx={{ mb: 1, fontWeight: 500 }}>
          Language
        </Typography>
      )}
      <FormControl fullWidth size={size} variant={variant}>
        <Select
          value={lang}
          onChange={handleLanguageChange}
          disabled={loading}
          displayEmpty
          sx={{
            '& .MuiSelect-select': {
              display: 'flex',
              alignItems: 'center',
              gap: 1,
            }
          }}
        >
          {languages.map((language) => (
            <MenuItem key={language.code} value={language.code}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                {/* <span style={{ fontSize: '16px' }}>{language.flag}</span> */}
                <Typography variant="body2">
                  {language.name}
                </Typography>
              </Box>
            </MenuItem>
          ))}
        </Select>
        {loading && (
          <Box sx={{ 
            position: 'absolute', 
            right: 30, 
            top: '50%', 
            transform: 'translateY(-50%)' 
          }}>
            <CircularProgress size={16} />
          </Box>
        )}
      </FormControl>
    </Box>
  );
};

export default LanguageSwitcher;
