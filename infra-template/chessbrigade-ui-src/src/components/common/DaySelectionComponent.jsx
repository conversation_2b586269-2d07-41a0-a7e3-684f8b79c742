// src/components/common/DaySelectionComponent.jsx
import React from 'react';
import { Box, Chip, FormControl, FormHelperText, FormLabel } from '@mui/material';
import { Controller } from 'react-hook-form';

const DaySelectionComponent = ({
  control,
  daysOfWeek = [],
  sessionsPerWeek = 0,
  handleDaySelection
}) => {
  return (
    <Controller
      name="selectedDays"
      control={control}
      render={({ field, fieldState: { error } }) => (
        <FormControl fullWidth error={!!error}>
          <FormLabel sx={{ mb: 2, fontWeight: 600, color: 'text.primary' }}>
            Select Days for Sessions
          </FormLabel>
          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 1 }}>
            {daysOfWeek.map((day) => {
              const isSelected = (field.value || []).includes(day.value);
              const isDisabled =
                !isSelected && (field.value || []).length >= (parseInt(sessionsPerWeek) || 0);

              return (
                <Chip
                  key={day.value}
                  label={day.label}
                  onClick={() => handleDaySelection(day.value)}
                  variant={isSelected ? 'filled' : 'outlined'}
                  color={isSelected ? 'primary' : 'default'}
                  disabled={isDisabled}
                  sx={{
                    cursor: isDisabled ? 'not-allowed' : 'pointer',
                    '&:hover': {
                      backgroundColor: isSelected ? 'primary.dark' : 'action.hover'
                    },
                    '&.Mui-disabled': {
                      opacity: 0.5
                    },
                    transition: 'all 0.2s ease-in-out'
                  }}
                />
              );
            })}
          </Box>
          <FormHelperText>
            {sessionsPerWeek > 0
              ? `Select ${sessionsPerWeek} day${sessionsPerWeek > 1 ? 's' : ''} for your sessions (${(field.value || []).length}/${sessionsPerWeek} selected)`
              : 'Please set sessions per week first'}
          </FormHelperText>
          {error && <FormHelperText error>{error.message}</FormHelperText>}
        </FormControl>
      )}
    />
  );
};

export default DaySelectionComponent;
