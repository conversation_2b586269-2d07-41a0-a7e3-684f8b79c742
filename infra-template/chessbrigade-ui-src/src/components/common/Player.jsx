import React from 'react';
import { Box, Typography, Paper } from '@mui/material';
import YouTubePlayer from './YouTubePlayer';
import VideoPlayer from './VideoPlayer';

const Player = ({ url }) => {
  // console.log("url in player:", url);

  const isValidUrl = typeof url === 'string';
  const isYouTube = isValidUrl && (url.includes('youtube.com') || url.includes('youtu.be'));

  return (
    <Box mb={2}>
      <Paper elevation={3} sx={{ borderRadius: 3 }}>
        <Box width="100%">
          {!isValidUrl ? (
            null
          ) : isYouTube ? (
            <YouTubePlayer videoId={url} />
          ) : (
            <VideoPlayer src={url} />
          )}
        </Box>
      </Paper>
    </Box>
  );
};

export default Player;
