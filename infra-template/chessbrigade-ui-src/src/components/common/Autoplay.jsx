import React, { useEffect, useState, useRef } from 'react';
import { Box, CircularProgress, Skeleton, Typography } from '@mui/material';
import { Client } from '../../api/client';
import Player from './Player';

const BillboardPlayer = () => {
    const [videos, setVideos] = useState([]);
    const [isLoading, setIsLoading] = useState(true);
    const [currentVideoIndex, setCurrentVideoIndex] = useState(0);
    const timeoutRef = useRef(null);


    const fetchVideoLinks = async () => {
        try {
            const params = { type: 'video' };
            const response = await Client.get('/open/banner', { params });

            if (response.data.success) {
                const res = response.data.data;
                const sortedVideos = res.sort((a, b) => a.rowIndex - b.rowIndex);
                setVideos(sortedVideos);
            } else {
                setVideos([]);
            }
        } catch (err) {
            console.error('Failed to fetch video links', err);
            setVideos([]);
        } finally {
            setIsLoading(false);
        }
    };

    const startAutoplay = () => {
        if (videos.length === 0) return;

        // Clear existing timeout
        if (timeoutRef.current) {
            clearTimeout(timeoutRef.current);
        }

        const currentVideo = videos[currentVideoIndex];
        const duration = currentVideo.duration * 1000; 

        timeoutRef.current = setTimeout(() => {
            setCurrentVideoIndex(prevIndex => 
                prevIndex === videos.length - 1 ? 0 : prevIndex + 1
            );
        }, duration);
    };

    useEffect(() => {
        fetchVideoLinks();
    }, []);

    // Start autoplay when videos are loaded or current video changes
    useEffect(() => {
        if (videos.length > 0) {
            startAutoplay();
        }

        // Cleanup timeout on unmount
        return () => {
            if (timeoutRef.current) {
                clearTimeout(timeoutRef.current);
            }
        };
    }, [videos, currentVideoIndex]);

    if (isLoading) {
        return (
            <Box display="flex" justifyContent="center" alignItems="center" height="400px">
                <CircularProgress />
            </Box>
        );
    }

    if (videos.length === 0) {
        return (
            <Box display="flex" justifyContent="center" alignItems="center" height="400px">
                  <Skeleton variant="rectangular" height={400} />
            </Box>
        );
    }

    // console.log("videos", videos);
    // console.log("currentVideoIndex", currentVideoIndex);
    // console.log("current video duration", videos[currentVideoIndex]?.duration);

    return (
        <Box width="100%"  overflow="hidden" sx={{height:{lg:'450px'}, borderRadius: '15px' }}>
            <Player key={videos[currentVideoIndex].id} url={videos[currentVideoIndex].url} />
        </Box>
    );
};

export default BillboardPlayer;