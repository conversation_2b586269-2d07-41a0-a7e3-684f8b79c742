import React from "react";
import {
  <PERSON>,
  <PERSON>alog,
  Dialog<PERSON>ontent,
  DialogTitle,
  IconButton,
  Typography,
  useMediaQuery,
  useTheme,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableRow,
  Paper,
  Avatar,
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import PersonIcon from "@mui/icons-material/Person";
import { formatPhoneNumber } from "../../utils/formatters";

const ContactDetailsModel = ({ open, onClose, data, title }) => {
  const theme = useTheme();
  const fullScreen = useMediaQuery(theme.breakpoints.down("sm"));

  const handleClose = () => {
    onClose();
  };

  // Define the player details to display in the table
  const playerDetails = data
    ? [
        { label: "Date of Birth", value: data.dob },
        { label: "Gender", value: data.gender },
        {
          label: "Phone Number",
          value: data.phoneNumber ? formatPhoneNumber(data.phoneNumber) : "-",
        },
        { label: "Parent/Guardian Name", value: data.parentG<PERSON>ianName },
        {
          label: "Parent/Guardian Contact",
          value: data.emergencyContact
            ? formatPhoneNumber(data.emergencyContact)
            : "-",
        },
        {
          label: "Alternate Contact",
          value: data.alternateContact
            ? formatPhoneNumber(data.alternateContact)
            : "-",
        },
      ]
    : [];

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      fullScreen={fullScreen}
      sx={{
        "& .MuiDialog-paper": {
          bgcolor: "#fff",
          color: "#333",
          borderRadius: 2,
          maxWidth: "500px",
          width: "100%",
          overflow: "hidden",
        },
      }}
    >
      <DialogTitle
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          pb: 1,
          bgcolor: "#2c2891",
          color: "white",
        }}
      >
        <Box sx={{ display: "flex", alignItems: "center" }}>
          <Typography variant="h6" component="div" sx={{ fontWeight: "bold" }}>
            {data?.name || title || "Player Details"}
          </Typography>
        </Box>
        <IconButton
          edge="end"
          sx={{ color: "white" }}
          onClick={handleClose}
          aria-label="close"
        >
          <CloseIcon />
        </IconButton>
      </DialogTitle>

      <DialogContent sx={{ p: 0 }}>
        {!data ? (
          <Typography variant="body1" sx={{ p: 3 }}>
            No data available.
          </Typography>
        ) : (
          <TableContainer component={Paper} elevation={0}>
            <Table>
              <TableBody>
                {playerDetails.map((detail, index) => (
                  <TableRow
                    key={detail.label}
                    sx={{
                      "&:nth-of-type(odd)": {
                        bgcolor: "rgba(44, 40, 145, 0.05)",
                      },
                      "&:hover": {
                        bgcolor: "rgba(44, 40, 145, 0.1)",
                      },
                    }}
                  >
                    <TableCell
                      component="th"
                      scope="row"
                      sx={{
                        fontWeight: "bold",
                        width: "40%",
                        borderBottom:
                          index === playerDetails.length - 1
                            ? "none"
                            : "1px solid rgba(224, 224, 224, 1)",
                      }}
                    >
                      {detail.label}
                    </TableCell>
                    <TableCell
                      sx={{
                        borderBottom:
                          index === playerDetails.length - 1
                            ? "none"
                            : "1px solid rgba(224, 224, 224, 1)",
                      }}
                    >
                      {detail.value || "Not provided"}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default ContactDetailsModel;
