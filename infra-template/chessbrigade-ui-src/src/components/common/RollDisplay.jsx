import React from "react";
import { Box, Typography } from "@mui/material";
import { keyframes } from "@emotion/react";

// Keyframes to scroll continuously to the left
const scrollLeft = keyframes`
  0% {
    transform: translateX(0%);
  }
  100% {
    transform: translateX(-50%);
  }
`;

const InfiniteScrollText = () => {
  const message = "Welcome to ChessBrigade — Your Hub for Tournaments, Fair Play & Mastery! Register Today & Make Every Move Count !";

  return (
    <Box
      sx={{
        width: "100%",
        overflow: "hidden",
        whiteSpace: "nowrap",
        // backgroundColor: "#1a1a1a",
        color: "#00",
        py: 2,
        borderTop:'5px solid #000',
        borderBottom:'5px solid #000'
      }}
    >
      <Box
        sx={{
          display: "inline-block",
          whiteSpace: "nowrap",
          animation: `${scrollLeft} 10s linear infinite`,
        }}
      >
        {/* Duplicate text for seamless effect */}
        <Typography
          component="span"
          sx={{ display: "inline-block", mx: 2, fontWeight: 500 }}
        >
          {message}
        </Typography>
        <Typography
          component="span"
          sx={{ display: "inline-block", mx: 2, fontWeight: 500 }}
        >
          {message}
        </Typography>
        {/* Duplicate text for seamless effect */}
        <Typography
          component="span"
          sx={{ display: "inline-block", mx: 2, fontWeight: 500 }}
        >
          {message}
        </Typography>
        <Typography
          component="span"
          sx={{ display: "inline-block", mx: 2, fontWeight: 500 }}
        >
          {message}
        </Typography>
      </Box>
    </Box>
  );
};

export default InfiniteScrollText;
