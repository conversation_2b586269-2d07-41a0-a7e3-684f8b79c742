import { Box, Typography } from "@mui/material";

// Course Header Component
const CourseHeader = ({ title, description }) => (
  <Box
    sx={{
      p: { xs: 2, sm: 4 },
      pb: { xs: 2, sm: 3 },
      background:
        "linear-gradient(135deg, rgb(235, 235, 235) 0%, rgb(228, 228, 228) 100%)",
      borderRadius: { xs: 0, sm: 2 },
      boxShadow: "0 2px 8px rgba(0, 0, 0, 0.1)",
      position: "relative",
      overflow: "hidden",
      "&::before": {
        content: '""',
        position: "absolute",
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        background: "linear-gradient(45deg, rgba(255,255,255,0.1) 0%, transparent 50%)",
        pointerEvents: "none",
      },
    }}
  >
    <Typography
      variant="h1"
      component="h1"
      align="center"
      sx={{
        fontFamily: '"Inter", "Segoe UI", "Roboto", sans-serif',
        fontWeight: 800,
        fontSize: {
          xs: "1.5rem",
          sm: "2rem",
          md: "2.5rem",
        },
        lineHeight: 1.1,
        mb: 3,
        letterSpacing: "-0.02em",
      }}
    >
      {title?.replace(/-/g, " ")}
    </Typography>

    {/* Stylish Divider */}
    <Box
      sx={{
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        mb: 3,
        position: "relative",
      }}
    >
      <Box
        sx={{
          width: "100px",
          height: "2px",
          background: "linear-gradient(90deg, transparent 0%, #000 20%, #333 50%, #000 80%, transparent 100%)",
          position: "relative",
          "&::before": {
            content: '""',
            position: "absolute",
            top: "50%",
            left: "50%",
            transform: "translate(-50%, -50%)",
            width: "8px",
            height: "8px",
            background: "radial-gradient(circle, #000 0%, #333 70%, transparent 100%)",
            borderRadius: "50%",
            boxShadow: "0 0 8px rgba(0, 0, 0, 0.3)",
          },
          "&::after": {
            content: '""',
            position: "absolute",
            top: "-2px",
            left: "20%",
            width: "3px",
            height: "6px",
            background: "#000",
            borderRadius: "1px",
            boxShadow: "40px 0 0 #000, 20px 0 0 #666",
          },
        }}
      />
    </Box>

    <Typography
      variant="body1"
      align="center"
      sx={{
        fontFamily: '"Inter", "Segoe UI", "Roboto", sans-serif',
        fontSize: { xs: "1rem", sm: "1.25rem", md: "1.5rem" },
        fontWeight: 400,
        color: "rgb(82, 82, 82)",
        mb: 3,
        mt: 0,
        maxWidth: "90%",
        mx: "auto",
        lineHeight: 1.4,
        letterSpacing: "0.01em",
      }}
    >
      {description}
    </Typography>
  </Box>
);

export default CourseHeader;