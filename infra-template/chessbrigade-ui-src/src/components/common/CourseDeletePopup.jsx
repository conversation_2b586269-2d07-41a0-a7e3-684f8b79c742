import React, { useState } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogActions,
  Button,
  CircularProgress
} from '@mui/material';
import { Client } from '../../api/client';
import UseToast from '../../lib/hooks/UseToast';

const CourseDeletePopup = ({ 
  open, 
  onClose, 
  title, 
  apiEndpoint,
  onSuccess,
}) => {
  const [loading, setLoading] = useState(false);
  const toast = UseToast()
console.log("render popup")

  const handleConfirm = async () => {
    setLoading(true);
    try {
     const response = await Client.post(apiEndpoint)

      if (response.data.success) {
        onSuccess();
        onClose();
      } else {
        throw new Error(` ${response.status}`);
      }
    } catch (error) {
       console.error("Error", error);
       toast.error('An Unexpected error happen');
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    if (!loading) {
      onClose();
    }
  };

  return (
    <Dialog open={open} onClose={handleCancel}>
      <DialogTitle>{title || 'Are you sure Want to delete this course ?'}</DialogTitle>
      <DialogActions>
        <Button onClick={handleCancel} disabled={loading}>Cancel</Button>
        <Button 
          onClick={handleConfirm} 
          color="primary" 
          variant="contained" 
          disabled={loading}
        >
          {loading ? <CircularProgress size={24} /> : 'Confirm'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default CourseDeletePopup;
