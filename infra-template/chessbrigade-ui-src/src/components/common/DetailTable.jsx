import {
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableRow,
  Button,
  Box,
} from "@mui/material";
import { Link } from "react-router-dom";

// Detail Table Component
export const DetailTable = ({ details, rowColor }) => (
  <TableContainer component={Paper} elevation={0}>
    <Table size="small">
      <TableBody>
        {details.map((detail, index) => (
          <DetailRow
            key={index}
            label={detail.label}
            value={detail.value}
            isLink={detail.isLink}
            url={detail.url}
            rowColor={rowColor}
            hasButton={detail.hasButton}
            buttonText={detail.buttonText}
            buttonColor={detail.buttonColor}
            buttonVariant={detail.buttonVariant}
            buttonSize={detail.buttonSize}
            buttonIcon={detail.buttonIcon}
            onButtonClick={detail.onButtonClick}
            buttonDisabled={detail.buttonDisabled}
            buttons={detail.buttons}
          />
        ))}
      </TableBody>
    </Table>
  </TableContainer>
);

export const DetailRow = ({
  label,
  value,
  isLink,
  url,
  hasButton,
  buttonText,
  buttonColor = "primary",
  buttonVariant = "contained",
  buttonSize = "small",
  buttonIcon,
  onButtonClick,
  buttonDisabled,
  buttons, // Array of multiple buttons
  rowColor = {
    odd: "hsla(257, 63%, 84%, 0.15)",
    even: "hsla(203, 63%, 84%, 0.15)#f9f9f9",
  },
}) => (
  <TableRow
    sx={{
      "&:nth-of-type(odd)": { bgcolor: rowColor.odd },
      "&:nth-of-type(even)": { bgcolor: rowColor.even },
    }}
  >
    <TableCell
      component="th"
      scope="row"
      sx={{
        width: "40%",
        fontWeight: "500",
        pl: { sx: 0, md: "10vw" },
        textWrap: "balance",
        py: 1.5,
        color: "#333",
        borderBottom: "none",
      }}
    >
      {label}
    </TableCell>
    <TableCell
      sx={{
        py: 1.5,
        pl: { xs: 0, md: "10vw !important" },
        wordBreak: "break-word",
        borderBottom: "none",
        display: "flex",
        alignItems: "center",
        flexWrap: "wrap",
        gap: 2,
      }}
    >
      {/* Value content */}
      <Box sx={{ flexGrow: 1, textWrap: "balance" }}>
        {isLink ? (
          <a
            href={url}
            target="_blank"
            rel="noopener noreferrer"
            sx={{
              color: "#1976d2",
              textDecoration: "underline",
              fontWeight: 500,
              cursor: "pointer",
              "&:hover": {
                color: "#1565c0",
                textDecoration: "none",
              },
              "&:focus": {
                outline: "2px solid #1976d2",
                outlineOffset: "2px",
                borderRadius: "2px",
              },
              // Ensure link is accessible
              "&:visited": {
                color: "#7b1fa2",
              },
            }}
            // Add accessibility attributes
            aria-label={`Open ${value} in new tab`}
            title={`Open ${value} in new tab`}
          >
            {value}
          </a>
        ) : (
          <Box
            component="span"
            sx={{
              color: "text.primary",
              fontWeight: 400,
            }}
          >
            {value}
          </Box>
        )}
      </Box>

      {/* Single button - Memoized for performance */}
      {hasButton && (
        <Button
          variant={buttonVariant}
          color={buttonColor}
          size={buttonSize}
          onClick={onButtonClick}
          disabled={buttonDisabled}
          startIcon={buttonIcon}
          sx={{
            whiteSpace: "nowrap",
            // Optimize button performance
            "&:hover": {
              transform: "translateY(-1px)",
              transition: "transform 0.2s ease-in-out",
            },
          }}
        >
          {buttonText}
        </Button>
      )}

      {/* Multiple buttons - Optimized rendering */}
      {buttons?.length > 0 && (
        <Box sx={{ display: "flex", gap: 1, flexWrap: "wrap" }}>
          {buttons.map((button, index) => (
            <Button
              key={button.id || index} // Use unique ID if available
              variant={button.variant || "contained"}
              color={button.color || "primary"}
              size={button.size || "small"}
              onClick={button.onClick}
              disabled={button.disabled}
              startIcon={button.icon}
              sx={{
                whiteSpace: "nowrap",
                "&:hover": {
                  transform: "translateY(-1px)",
                  transition: "transform 0.2s ease-in-out",
                },
                ...button.sx,
              }}
            >
              {button.text}
            </Button>
          ))}
        </Box>
      )}
    </TableCell>
  </TableRow>
);
