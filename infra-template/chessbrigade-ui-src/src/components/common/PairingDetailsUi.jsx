import {
  <PERSON>,
  <PERSON><PERSON>,
  CircularProgress,
  Container,
  ToggleButton,
  ToggleButtonGroup,
  Typography,
  useMediaQuery,
  useTheme,
} from "@mui/material";
import React, { useCallback, useEffect, useMemo, useState, useRef } from "react";

import DynamicTable from "../../components/common/DynamicTable";
import { useParams } from "react-router-dom";
import { Client } from "../../api/client";
import PlayerSearch from "../../pages/tournament/PlayerSearch";

import UseToast from "../../lib/hooks/UseToast";
import UseGlobalContext from "../../lib/hooks/UseGlobalContext";

const PairingDetailsUi = ({
  tournamentDetails,
  loading,
  setLoading,
  currentRound,
  onRoundChange,
}) => {
  const { title: id } = useParams();
  const newTitle = encodeURIComponent(id);
  const [page, setPage] = useState(1);
  const { user } = UseGlobalContext();

  const [totalPages, setTotalPages] = useState(0);
  const [totalRound, setTotalRound] = useState(0);
  const [round, setRound] = useState(1);

  const toast = UseToast();
  const [search, setSearch] = useState({
    playerName: "",
    ageCategory: "",
    genderCategory: "",
  });

  const [data, setData] = useState([]);

  useEffect(() => {
    if (tournamentDetails) {
     
      const category = search.genderCategory === "male" ? "maleAgeCategory" : "femaleAgeCategory";
      const rounds = tournamentDetails?.numberOfRounds?.[category]?.[search.ageCategory] || 0;
      setTotalRound(rounds);
     
    }

  }, [search.ageCategory])

  // Track if component has mounted to prevent initial duplicate calls
  const hasMounted = useRef(false);
  const isInitialLoad = useRef(true);
  
  useEffect(() => {
    if (tournamentDetails) {
     
      const category = search.genderCategory === "male" ? "maleAgeCategory" : "femaleAgeCategory";
      const rounds = tournamentDetails?.numberOfRounds?.[category]?.[search.ageCategory] || 0;
      setTotalRound(rounds);
  
    }

  }, [search.ageCategory])

  // Memoize the API call function to prevent recreation on every render
  const fetchPairingDetails = useCallback(async (pageNum = page, searchFilters = search, roundNum = round) => {
    setLoading(true);
    try {
      const params = {
        page: pageNum,
        round: roundNum,
      };

      if (searchFilters.playerName) {
        params.playerName = searchFilters.playerName;
      }
      if (searchFilters.ageCategory) {
        params.ageCategory = searchFilters.ageCategory;
      }
      if (searchFilters.genderCategory) {
        params.genderCategory = searchFilters.genderCategory;
      }

      const response = await Client.get(`pairing-import/${newTitle}`, {
        params,
      });

      if (!response.data.success) return;

      if (response.data?.data?.length === 0) {
        setData([]);
        setPage(1);
        setTotalPages(0);
        toast.info("Pairing details have not been uploaded by the arbiter.")
        return;
      }

      setData(response.data.data);
      setTotalPages(response.data.Pagination?.totalPages);

      if (response.data.Pagination?.currentPage !== pageNum) {
        setPage(response.data.Pagination?.currentPage);
      }

    } catch (error) {
      console.error("Error fetching tournament details:", error);
      toast.error("An error occurred while fetching tournament details");
    } finally {
      setLoading(false);
    }
  }, [newTitle, setLoading, toast]);

  // Initialize round from currentRound only once
  useEffect(() => {
    if (currentRound && isInitialLoad.current) {
      setRound(currentRound);
      isInitialLoad.current = false;
    }
  }, [currentRound]);

  // Fetch data only when dependencies actually change
  useEffect(() => {
    if (!hasMounted.current) {
      hasMounted.current = true;
      return;
    }

    const timeoutId = setTimeout(() => {
      fetchPairingDetails(page, search, round);
    }, 100); // Small debounce to prevent rapid calls

    return () => clearTimeout(timeoutId);
  }, [page, round, search]);

  // // Initial data fetch after component mounts and round is set
  // useEffect(() => {
  //   if (hasMounted.current && round > 0) {
  //     fetchPairingDetails(1, search, round);
  //   }
  // }, [round]); // Only trigger when round changes from initial state

  const columns = useMemo(() => [
    { id: "board_no", label: "Board No", align: "center" },
    { id: "white_player_ranking", label: "Rank", align: "center" },
    { id: "white_player_fide_rating", label: "FIDE Rating", align: "center" },
    { id: "white_player_points", label: "Points", align: "center" },
    { id: "white_player_name", label: "White", align: "left" },
    { id: "result", label: "Result", align: "center" },
    { id: "black_player_name", label: "Black", align: "left" },
    { id: "black_player_points", label: "Points", align: "center" },
    { id: "black_player_fide_rating", label: "FIDE Rating", align: "center" },
    { id: "black_player_ranking", label: "Rank", align: "center" },
  ], []);
  const theme = useTheme()
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'))
  // Reset search form
  const handleReset = useCallback(() => {
    const resetSearch = {
      playerName: "",
      ageCategory: "",
      genderCategory: "",
    };
    setSearch(resetSearch);
    setPage(1);
    // The useEffect will handle the API call
  }, []);

  const handleSearch = useCallback(() => {
    if (!search.playerName && !search.ageCategory && !search.genderCategory) {
      return;
    }
    setPage(1);
    // The useEffect will handle the API call
  }, [search]);

  const handleRoundChange = useCallback((e, value) => {
    if (value !== null) {
      setRound(value);
      onRoundChange(value)
      setPage(1); // Reset to first page when round changes
    }
  }, []);

  const handlePageChange = useCallback((newPage) => {
    setPage(newPage);
  }, []);

  return (
    <>
    
      <PlayerSearch
        tournamentDetails={tournamentDetails}
        loading={loading}
        search={search}
        setSearch={setSearch}
        handleReset={handleReset}
        handleSearch={handleSearch}
        setLoading={setLoading}
      />
      <Box
        sx={{
          my: 2,
          p: 2,
          borderRadius: 2,
          display: "flex",
          flexDirection: "row",
          justifyContent: "flex-start",
          background: "#A5D8C626",
          alignItems: "center",
          gap: isMobile ? 5: 50,
        }}
      >
        <Typography
          variant="body1"
          color="#000"
          gutterBottom
          sx={{ fontWeight: "500" }}
        >
          Pairing Details
        </Typography>

        <Box>
          <Typography variant="body1" display="inline">
            Round &nbsp;
          </Typography>
          <ToggleButtonGroup
            value={round}
            exclusive
            onChange={handleRoundChange}
            size="small"
            color="primary"
            sx={{
              border: "none",
              fontSize: 16,
              color: "#000",
              "& .MuiButtonBase-root": { fontSize: "1rem" },
              "&.Mui-disabled": { border: "none" },
            }}
          > {totalRound
              ? Array.from({ length: totalRound }, (_, i) => (
                <ToggleButton
                  key={i}
                  value={i + 1}
                  disabled={
                    !user || user?.role === "player" ? i + 1 > currentRound : false
                  }
                  sx={{
                    border: "none",
                    "&.Mui-disabled": { border: "none" },
                    "&.Mui-selected": {
                      backgroundColor: "transparent",
                      color: "#166DA3",
                      fontWeight: "bold",
                    },
                    "&:hover": {
                      backgroundColor: "rgba(0, 128, 0, 0.1)",
                    },
                  }}
                >
                  {i + 1}
                </ToggleButton>
              ))
              : (
                <ToggleButton
                  value={0}
                  sx={{
                    border: "none",
                    "&.Mui-disabled": { border: "none" },
                    "&.Mui-selected": {
                      backgroundColor: "transparent",
                      color: "#166DA3",
                      fontWeight: "bold",
                    },
                    "&:hover": {
                      backgroundColor: "rgba(0, 128, 0, 0.1)",
                    },
                  }}
                >
                  All
                </ToggleButton>
              )}
          </ToggleButtonGroup>
        </Box>
      </Box>

      {/* <PlayerSearch
        tournamentDetails={tournamentDetails}
        loading={loading}
        search={search}
        setSearch={setSearch}
        handleReset={handleReset}
        handleSearch={handleSearch}
        setLoading={setLoading}
      /> */}

      <DynamicTable
        columns={columns}
        data={data}
        loading={false}
        page={page}
        totalPages={totalPages}
        onPageChange={handlePageChange}
        idField="fideId"
        showDetailsButton={false}
      />
    </>
  );
};

export default PairingDetailsUi;