import React, { useState } from 'react';
import {
  Box,
  Button,
  Paper,
  Typography,
  LinearProgress,
  Chip,
  Alert,
} from '@mui/material';
import {
  CloudUpload,
  Description,
  Delete,
} from '@mui/icons-material';
import { toast } from 'react-toastify';

const ExcelUploader = ({
  onFileSelect,
  onUpload,
  uploading = false,
  uploadResult = null,
  acceptedTypes = ['.xlsx', '.xls', '.csv'],
  maxSize = 10, // MB
  title = 'Upload Excel File',
  description = 'Drop your Excel file here or click to browse',
}) => {
  const [selectedFile, setSelectedFile] = useState(null);
  const [dragActive, setDragActive] = useState(false);

  // Handle file selection
  const handleFileSelect = (event) => {
    const file = event.target.files[0];
    validateAndSetFile(file);
  };

  // Handle drag and drop
  const handleDrag = (e) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  };

  const handleDrop = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      validateAndSetFile(e.dataTransfer.files[0]);
    }
  };

  // Validate file type and size
  const validateAndSetFile = (file) => {
    if (!file) return;

    // Check file type
    const allowedTypes = [
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
      'application/vnd.ms-excel', // .xls
      'text/csv' // .csv
    ];

    if (!allowedTypes.includes(file.type)) {
      toast.error(`Please select a valid file type: ${acceptedTypes.join(', ')}`);
      return;
    }

    // Check file size
    if (file.size > maxSize * 1024 * 1024) {
      toast.error(`File size should be less than ${maxSize}MB`);
      return;
    }

    setSelectedFile(file);
    if (onFileSelect) {
      onFileSelect(file);
    }
  };

  // Remove selected file
  const removeFile = () => {
    setSelectedFile(null);
    if (onFileSelect) {
      onFileSelect(null);
    }
  };

  // Handle upload
  const handleUpload = () => {
    if (!selectedFile) {
      toast.error('Please select a file first');
      return;
    }

    if (onUpload) {
      onUpload(selectedFile);
    }
  };

  return (
    <Box>
      {/* File Upload Area */}
      <Paper
        sx={{
          p: 4,
          border: dragActive ? '2px dashed #1976d2' : '2px dashed #ccc',
          backgroundColor: dragActive ? '#f5f5f5' : 'transparent',
          textAlign: 'center',
          cursor: 'pointer',
          transition: 'all 0.3s ease',
          mb: 2,
        }}
        onDragEnter={handleDrag}
        onDragLeave={handleDrag}
        onDragOver={handleDrag}
        onDrop={handleDrop}
        onClick={() => document.getElementById('excel-file-input').click()}
      >
        <input
          id="excel-file-input"
          type="file"
          accept={acceptedTypes.join(',')}
          onChange={handleFileSelect}
          style={{ display: 'none' }}
        />
        
        <CloudUpload sx={{ fontSize: 64, color: '#ccc', mb: 2 }} />
        
        <Typography variant="h6" gutterBottom>
          {title}
        </Typography>
        
        <Typography variant="body2" color="textSecondary" sx={{ mb: 2 }}>
          {description}
        </Typography>
        
        <Typography variant="body2" color="textSecondary">
          Supported formats: {acceptedTypes.join(', ')} (Max size: {maxSize}MB)
        </Typography>
      </Paper>

      {/* Selected File Display */}
      {selectedFile && (
        <Box sx={{ mb: 2 }}>
          <Paper sx={{ p: 2, display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Description color="primary" />
              <Box>
                <Typography variant="body1" fontWeight="medium">
                  {selectedFile.name}
                </Typography>
                <Typography variant="body2" color="textSecondary">
                  {(selectedFile.size / 1024 / 1024).toFixed(2)} MB
                </Typography>
              </Box>
            </Box>
            <Button
              size="small"
              color="error"
              startIcon={<Delete />}
              onClick={removeFile}
            >
              Remove
            </Button>
          </Paper>
        </Box>
      )}

      {/* Upload Button */}
      <Button
        variant="contained"
        size="large"
        startIcon={<CloudUpload />}
        onClick={handleUpload}
        disabled={!selectedFile || uploading}
        fullWidth
        sx={{ mb: 2 }}
      >
        {uploading ? 'Uploading...' : 'Upload File'}
      </Button>

      {/* Upload Progress */}
      {uploading && (
        <Box sx={{ mb: 2 }}>
          <LinearProgress />
          <Typography variant="body2" color="textSecondary" sx={{ mt: 1, textAlign: 'center' }}>
            Processing file...
          </Typography>
        </Box>
      )}

      {/* Upload Result */}
      {uploadResult && (
        <Alert 
          severity={uploadResult.success ? 'success' : 'error'} 
          sx={{ mb: 2 }}
        >
          <Typography variant="body1">
            {uploadResult.message}
          </Typography>
          
          {uploadResult.success && uploadResult.data && (
            <Box sx={{ mt: 1 }}>
              {uploadResult.data.successCount && (
                <Typography variant="body2">
                  Successfully processed: {uploadResult.data.successCount} records
                </Typography>
              )}
              {uploadResult.data.failedCount > 0 && (
                <Typography variant="body2" color="warning.main">
                  Failed: {uploadResult.data.failedCount} records
                </Typography>
              )}
            </Box>
          )}
          
          {!uploadResult.success && uploadResult.errors && uploadResult.errors.length > 0 && (
            <Box sx={{ mt: 1 }}>
              <Typography variant="body2" gutterBottom>First few errors:</Typography>
              {uploadResult.errors.slice(0, 3).map((error, index) => (
                <Typography key={index} variant="body2" sx={{ ml: 1 }}>
                  • {error}
                </Typography>
              ))}
              {uploadResult.errors.length > 3 && (
                <Typography variant="body2" color="textSecondary" sx={{ ml: 1 }}>
                  ... and {uploadResult.errors.length - 3} more errors
                </Typography>
              )}
            </Box>
          )}
        </Alert>
      )}
    </Box>
  );
};

export default ExcelUploader;
