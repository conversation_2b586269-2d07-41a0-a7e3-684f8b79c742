import React from "react";
import { Box, Typography, Paper } from "@mui/material";

/**
 * EmptyState component for displaying when there's no data
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.icon - Icon to display
 * @param {string} props.title - Title text
 * @param {string} props.description - Description text
 * @param {React.ReactNode} props.action - Optional action button or component
 */
const EmptyState = ({ icon, title, description, action }) => {
  return (
    <Paper
      elevation={0}
      variant="outlined"
      sx={{
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        justifyContent: "center",
        p: 4,
        minHeight: 300,
        textAlign: "center",
      }}
    >
      <Box sx={{ mb: 2 }}>{icon}</Box>
      <Typography variant="h6" gutterBottom>
        {title}
      </Typography>
      <Typography variant="body2" color="black" sx={{ mb: 3 }}>
        {description}
      </Typography>
      {action && <Box>{action}</Box>}
    </Paper>
  );
};

export default EmptyState;
