import {
  Box,
  Button,
  Divider,
  Paper,
  Skeleton,
  Stack,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
} from "@mui/material";
import React, { useEffect, useState } from "react";
import { Client } from "../../api/client";
import CustomPagination from "./CustomPagination";
import { Link } from "react-router-dom";
import UseGlobalContext from "../../lib/hooks/UseGlobalContext";

const UpcomingCourses = ({ club = false, arbiter = false }) => {
  const [courseData, setCourseData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [pagination, setPagination] = useState({
    page:1,
    totalPages: 0,
    total: 0,
  });
  const { user } = UseGlobalContext();
  const fetchCourses = async () => {
    setLoading(true);
    try {
      // if (club) {
      console.log("pagination.page",pagination.page)
        const response = await Client.get(`/course/coach`, {
          params: { limit: 3, page: pagination.page },
        });

        setPagination({
          ...pagination,
          page: Number(response.data.data.currentPage),
          totalPages: Number(response.data.data.totalPages),
          total: Number(response.data.data.total),
        });
        setCourseData(response.data.data);
    } catch (error) {
      console.error("Error fetching courses:", error);
    } finally {
      setLoading(false);
    }
  };
  // Initial data fetch
  useEffect(() => {
    fetchCourses();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [pagination.page]);

  return (
    <Box sx={{ mt: 4 }}>
      <TableContainer component={Paper} sx={{ mt: 2, }}>
        <Table sx={{ "& .MuiTable-root": { bgcolor: "#BEDDF026" } }}>
          <TableHead sx={{ bgcolor: "#CCBEF033" }}>
            <TableRow>
              <TableCell>Upcoming Course</TableCell>

              <TableCell align="right"></TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {loading ? (
              // Loading skeletons - always show 3
              Array(3)
                .fill(0)
                .map((_, index) => (
                  <TableRow key={`loading-${index}`}>
                    <TableCell colSpan={2}>
                      <Skeleton variant="rectangular" height={30} />
                    </TableCell>
                  </TableRow>
                ))
            ) : courseData.length === 0 ? (
              // No courses found - show message in first row and empty rows for the rest
              <>
                <TableRow sx={{ bgcolor: "#BEDDF026" }}>
                  <TableCell colSpan={2} align="center">
                    No courses found{" "}
                    {user?.role === "coach" && (
                      <Link to="/dashboard/createcourse">
                        Create a new course
                      </Link>
                    )}
                  </TableCell>
                </TableRow>
                {Array(2)
                  .fill(0)
                  .map((_, index) => (
                    <TableRow
                      key={`empty-${index}`}
                      sx={{ bgcolor: "#BEDDF026" }}
                    >
                      <TableCell
                        colSpan={2}
                        sx={{
                          height: "53px",
                          borderBottom: index === 1 ? "none" : undefined,
                        }}
                      >
                        &nbsp;
                      </TableCell>
                    </TableRow>
                  ))}
              </>
            ) : (
              // Show available courses and fill remaining slots with empty rows
              <>
                {courseData.map((course, index) => {
                  const isLastCourse = index === courseData.length - 1;
                  const isLastRow =
                    isLastCourse && courseData.length === 3;

                  return (
                    <TableRow
                      key={`course-${index}`}
                      sx={{ bgcolor: "#BEDDF026" }}
                    >
                      <TableCell
                        sx={{
                          height: "53px",
                          borderBottom: isLastRow ? "none" : undefined,
                          textTransform: "capitalize",
                          textOverflow: "ellipsis",
                          whiteSpace: "nowrap",
                          overflow: "hidden",
                          maxWidth: "300px",
                        }}
                      >
                        {course.title.toUpperCase().replace(/-/g, " ")}
                      </TableCell>
                      <TableCell
                        align="right"
                        sx={{
                          borderBottom: isLastRow ? "none" : undefined,
                        }}
                      >
                        <Button
                          variant="contained"
                          component={Link}
                          to={`/dashboard/course/${encodeURIComponent(course.title)}`}
                          size="small"
                          sx={{
                            bgcolor: "#2c2891",
                            color: "white",
                            textTransform: "none",
                            textAlign: "center",
                            textWrap: "nowrap",
                          }}
                        >
                          View Details
                        </Button>
                      </TableCell>
                    </TableRow>
                  );
                })}
                {/* Add empty rows to always have 3 rows total */}
                {courseData.length < 3 &&
                  Array(3 - courseData.length)
                    .fill(0)
                    .map((_, index) => {
                      const isLastRow = index === 3 - courseData.length - 1;
                      return (
                        <TableRow
                          key={`empty-${index}`}
                          sx={{ bgcolor: "#BEDDF026" }}
                        >
                          <TableCell
                            colSpan={2}
                            sx={{
                              height: "53px",
                              borderBottom: isLastRow ? "none" : undefined,
                            }}
                          >
                            &nbsp;
                          </TableCell>
                        </TableRow>
                      );
                    })}
              </>
            )}
            <TableRow>
              <TableCell colSpan={2} sx={{ p: 0, borderBottom: "none" }}>
                <Divider />
              </TableCell>
            </TableRow>
          </TableBody>
        </Table>
        <CustomPagination
          totalPages={pagination.totalPages > 5 ? 5 : pagination.totalPages}
          currentPage={pagination.page}
          onPageChange={(page) => setPagination({ ...pagination, page })}
          sx={{ bgcolor: "#BEDDF026", justifyContent: "flex-end" }}
         
        />
      </TableContainer>
    </Box>
  );
};

export default UpcomingCourses;
