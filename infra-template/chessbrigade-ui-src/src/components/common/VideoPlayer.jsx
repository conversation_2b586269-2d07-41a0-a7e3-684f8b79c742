// components/VideoPlayer.jsx
import React, { useRef, useState } from 'react';
import { Box, CircularProgress, useTheme, useMediaQuery } from '@mui/material';

const VideoPlayer = ({ src, height = 450 }) => {
  const videoRef = useRef(null);
  const [loading, setLoading] = useState(true);

  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const isTablet = useMediaQuery(theme.breakpoints.between('sm', 'md'));

  const getResponsiveHeight = () => {
    if (isMobile) return 200;
    if (isTablet) return 300;
    return height; // default for desktop
  };

  return (
    <Box
      position="relative"
      width="100%"
      bgcolor="black"
      height={getResponsiveHeight()}
    >
      {loading && (
        <CircularProgress
          size={40}
          sx={{
            position: 'absolute',
            top: '50%',
            left: '50%',
            zIndex: 2,
            transform: 'translate(-50%, -50%)',
          }}
        />
      )}
      <video
        ref={videoRef}
        src={src}
        onCanPlayThrough={() => setLoading(false)}
        width="100%"
        height="100%"
        style={{ objectFit: 'cover' }}
        playsInline
        autoPlay
      />
    </Box>
  );
};

export default VideoPlayer;
