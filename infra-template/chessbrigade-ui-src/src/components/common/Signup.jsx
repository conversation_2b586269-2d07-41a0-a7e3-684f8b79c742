import CloseIcon from "@mui/icons-material/Close";
import KeyboardArrowDownIcon from "@mui/icons-material/KeyboardArrowDown";
import {
  Box,
  Button,
  FormControl,
  IconButton,
  MenuItem,
  Modal,
  Select,
  TextField,
  Typography,
  Grid,
  CircularProgress,
  InputAdornment,
} from "@mui/material";
import { Link } from "react-router-dom";
import React, { useEffect, useState } from "react";
import backgroundImage from "../../assets/singupbackground.png";
import { z } from "zod";
import { Client } from "../../api/client";
import { Visibility, VisibilityOff } from "@mui/icons-material";

import UseToast from "../../lib/hooks/UseToast";
import { ArrowBack } from "@mui/icons-material";
import { useTranslation } from "../../context/TranslationContext";

const baseSchema = z.object({
  firstName: z
    .string()
    .min(1, "firstName is required")
    .min(3, "firstName must be at least 2 characters"),
  lastName: z.string().optional(),
  referral: z.string().optional(),
  phoneNumber: z
    .string()
    .min(10, { message: "Mobile number is required" })
    .regex(/^(91)?\d{10}$/, {
      message: "Phone number must be 10 digits",
    })
    .max(10, { message: "Mobile number must be 10 digits" }),
  otp: z
    .string()
    .min(4, { message: "OTP is required" })
    .max(6, { message: "OTP must not exceed 6 digits" }),
  email: z
    .string()
    .min(1, { message: "Email is required" })
    .email({ message: "Invalid email format" }),
  password: z
    .string({ errorMap: () => ({ message: "Password is required" }) })
    .min(1, { message: "Password is required" })
    .min(8, { message: "Password must be at least 8 characters long" })
    .regex(/[A-Z]/, {
      message: "Password must contain at least one uppercase letter",
    })
    .regex(/[a-z]/, {
      message: "Password must contain at least one lowercase letter",
    })
    .regex(/[0-9]/, { message: "Password must contain at least one number" })
    .regex(/[^A-Za-z0-9]/, {
      message: "Password must contain at least one special character",
    }),
  confirmPassword: z
    .string({ errorMap: () => ({ message: "Confirm Password is required" }) })
    .min(8, { message: "Password must be at least 8 characters long" }),
  role: z.enum(["player", "club", "arbiter","coach"], {
    errorMap: () => ({ message: "Please select a user type" }),
  }),
});
const signUpSchema = baseSchema
  .omit({ otp: true })
  .superRefine(({ password, confirmPassword, role, lastName }, ctx) => {
    if (password !== confirmPassword) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "Passwords don't match",
        path: ["confirmPassword"],
      });
    }

    if (!role) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "Please select a user type",
        path: ["role"],
      });
    }

    if (lastName.trim() === "" && (role === "player" || role === "arbiter")) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "Last Name is required",
        path: ["lastName"],
      });
    }
  });
const registerSchema = baseSchema.superRefine(
  ({ password, confirmPassword, role, lastName }, ctx) => {
    if (password !== confirmPassword) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "Passwords don't match",
        path: ["confirmPassword"],
      });
    }

    if (!role) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "Please select a user type",
        path: ["role"],
      });
    }

    if (lastName.trim() === "" && (role === "player" || role === "arbiter")) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "Last Name is required",
        path: ["lastName"],
      });
    }
  }
);

// Password strength indicator functions
const getPasswordStrength = (password) => {
  if (!password) return 0;

  let strength = 0;
  if (password.length >= 8) strength += 1;
  if (/[A-Z]/.test(password)) strength += 1;
  if (/[a-z]/.test(password)) strength += 1;
  if (/[0-9]/.test(password)) strength += 1;
  if (/[^A-Za-z0-9]/.test(password)) strength += 1;

  return strength;
};

const getStrengthColor = (strength) => {
  if (strength <= 2) return "error.main";
  if (strength <= 3) return "warning.main";
  if (strength <= 4) return "info.main";
  return "success.main";
};

const getStrengthLabel = (strength) => {
  if (strength <= 1) return "Very Weak";
  if (strength <= 2) return "Weak";
  if (strength <= 3) return "Medium";
  if (strength <= 4) return "Strong";
  return "Very Strong";
};

const SignUp = ({ open, setOpen }) => {
  const { translate } = useTranslation();
  const toast = UseToast();
  const [formData, setFormData] = useState({
    firstName: "",
    lastName: "",
    phoneNumber: "", // No prefix
    otp: "",
    email: "",
    password: "",
    confirmPassword: "",
    role: "",
    referral: "",
  });

  const [sendingOtp, setSendingOtp] = useState(false);
  const [verifyingOtp, setVerifyingOtp] = useState(false);
  const [formStep, setFormStep] = useState(1);
  const [timer, setTimer] = useState(60);
  const [isResendDisabled, setIsResendDisabled] = useState(true);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  // Toggle functions
  const handleClickShowPassword = () => setShowPassword(!showPassword);
  const handleClickShowConfirmPassword = () =>
    setShowConfirmPassword(!showConfirmPassword);

  const roles = [
    { value: "player", label: translate('player', 'Player') },
    { value: "club", label: translate('club', 'Club') },
    { value: "arbiter", label: translate('arbiter', 'Arbiter') },
    { value: "coach", label: translate('coach', 'Coach') },
  ];

  const [errors, setErrors] = useState({});

  // Handle input change
  const handleChange = (e) => {
    setFormData({ ...formData, [e.target.name]: e.target.value });

    // Clear error when user starts typing
    setErrors((prev) => ({ ...prev, [e.target.name]: undefined }));
  };
  useEffect(() => {
    if (open) {
      setFormData({
        firstName: "",
        lastName: "",
        phoneNumber: "", // No prefix
        otp: "",
        email: "",
        password: "",
        confirmPassword: "",
        role: "",
        referral: '',
      });
      setErrors({});

      setFormStep(1);
    }
  }, [open]);

  // Validate form

  // Validate registration form
  const validateRegistrationForm = () => {
    // Create a partial schema without OTP validation

    const result = signUpSchema.safeParse(formData);
    if (!result.success) {
      setErrors(result.error.flatten().fieldErrors);
      const firstErrorField = Object.keys(
        result.error.flatten().fieldErrors
      )[0];
      const firstErrorMessage =
        result.error.flatten().fieldErrors[firstErrorField][0];
      toast.error(firstErrorMessage);
      return false;
    }
    return true;
  };

  // Send OTP to mobile number
  const handleSendOtp = async () => {
    // Validate all form fields first
    if (!validateRegistrationForm()) {
      return;
    }

    // Validate phone number
    if (!formData.phoneNumber || formData.phoneNumber.length !== 10) {
      toast.error("Phone number must be 10 digits");
      return;
    }

    setSendingOtp(true);
    try {
      setTimer(60);
      setIsResendDisabled(true);

      // Reset OTP field if user is regenerating OTP
      if (formStep === 2) {
        setFormData((prev) => ({
          ...prev,
          otp: "", // Clear the OTP field
        }));
      }

      // Call the actual API endpoint to send OTP
      const response = await Client.post("/auth/send-otp", {
        phoneNumber: formData.phoneNumber,
        email: formData.email,
        role: formData.role,
        firstName: formData.firstName,
        lastName: formData.lastName,
        referral: formData.referral,
      });

      if (!response.data.success) {
        toast.error(response.data.data.message || "Failed to send OTP");
        return;
      }

      toast.success("OTP sent successfully to your mobile number");

      setFormStep(2); // Move to OTP verification step
    } catch (error) {
      console.error("Error sending OTP:", error);
      if (error.response?.status === 409) {
        toast.error(
          error.response?.data?.error || "User already exists"
        );
      } else if (error.response?.status === 422) {
        toast.error(
          error.response.data?.error?.message || "Invalid input data"
        );
      } else {
        toast.error(
          error.response?.data?.error?.message ||
          "Failed to send OTP. Please try again."
        );
      }
    } finally {
      setSendingOtp(false);
    }
  };

  // Verify OTP and register user in a single API call
  const handleVerifyOtp = async () => {
    // Validate OTP
    if (!formData.otp || formData.otp.length < 4) {
      toast.error("Please enter a valid OTP");
      return;
    }

    // Validate all form data
    const result = registerSchema.safeParse(formData);
    if (!result.success) {
      setErrors(result.error.flatten().fieldErrors);
      const firstErrorField = Object.keys(
        result.error.flatten().fieldErrors
      )[0];
      const firstErrorMessage =
        result.error.flatten().fieldErrors[firstErrorField][0];
      toast.error(firstErrorMessage);
      return;
    }

    setVerifyingOtp(true);
    try {
      // Call the API endpoint to verify OTP and register user in a single call
      const { email, password, role, firstName, lastName, phoneNumber, otp, referral } =
        formData;
      const response = await Client.post("/auth/register", {
        email,
        password,
        role,
        firstName,
        lastName,
        phoneNumber,
        otp,
        referral
      });



      if (!response.data.success) {
        toast.error(response.data.data.error || "Registration failed");
        return;
      }

      toast.success("Successfully Signed Up");
      sessionStorage.setItem("new_user", true);

      // Reset form and close modal
      setFormData({
        firstName: "",
        lastName: "",
        phoneNumber: "", // No prefix
        otp: "",
        email: "",
        password: "",
        confirmPassword: "",
        role: "",
      });

      setFormStep(1);
      setOpen({ ...open, signup: false, login: true });
    } catch (error) {
      console.error("Error during registration:", error);
      if (error.response?.status === 401) {
        toast.error(error.response.data.error.message || "Invalid OTP");
      } else if (error.response?.status === 404) {
        toast.error(error.response.data.error || "OTP not found or expired");
      } else if (error.response?.status === 422) {
        toast.error(error.response.data.error || "Validation error");
        setFormStep(1);
      } else if (error.response?.status === 409) {
        toast.error(error.response.data?.error || "User already exists");
        // setFormStep(1);
      } else {
        toast.error(
          error.response?.data?.message ||
          "Registration failed. Please try again."
        );
        // setFormStep(1);
      }
    } finally {
      setVerifyingOtp(false);
    }
  };

  // Handle form submit
  const handleSubmit = async (e) => {
    if (e) e.preventDefault();

    // If we're on step 1, send OTP and move to step 2
    if (formStep === 1) {
      handleSendOtp();
      return;
    }

    // If we're on step 2, verify OTP and register user
    if (formStep === 2) {
      handleVerifyOtp();
      return;
    }
  };

  useEffect(() => {
    let interval;

    if (isResendDisabled && timer > 0) {
      interval = setInterval(() => {
        setTimer((prev) => {
          if (prev <= 1) {
            clearInterval(interval);
            setIsResendDisabled(false);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    }

    return () => clearInterval(interval);
  }, [isResendDisabled, formStep]);

  return (
    <Modal
      open={open}
      onClose={() => setOpen({ ...open, signup: false })}
      aria-labelledby="signup-modal"
      aria-describedby="signup-form"
      sx={{
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
      }}
    >
      <Box
        sx={{
          width: "100%",
          height: "100%",
          bgcolor: "rgba(0, 0, 0, 0.5)",
          backdropFilter: "blur(2px)",
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          padding: { xs: "2vh 2vw", sm: "3vh 3vw" },
          overflowY: "auto",
        }}
      >
        <Box
          sx={{
            width: { xs: "95%", sm: "90%", md: 488 },
            maxWidth: 488,
            height: "auto",
            maxHeight: { xs: "95vh", sm: "90vh" },
            transitionBehavior: "smooth",
            transitionDuration: "1s",
            position: "relative",
            overflow: "hidden",
            borderRadius: 2,
            bgcolor: "white",
          }}
        >
          <Box
            sx={{
              width: "100%",
              height: "100%",
              maxHeight: "inherit",
              backgroundImage: `url(${backgroundImage})`,
              backgroundSize: "cover",
              position: "relative",
              display: "flex",
              flexDirection: "column",
              alignItems: "center",
              padding: { xs: 2, sm: 3 },
              overflowY: "auto",
              overflowX: "hidden",
            }}
          >
            <IconButton
              sx={{ position: "absolute", top: 13, right: 21, color: "white" }}
              onClick={() => setOpen({ ...open, signup: false })}
            >
              <CloseIcon fontSize="large" />
            </IconButton>

            <Typography
              variant="h4"
              sx={{
                fontFamily: "Prosto One, Helvetica",
                color: "white",
                fontSize: { xs: 22, sm: 28 },
                mb: 1,
                textAlign: "center",
              }}
            >
              ChessBrigade.com
            </Typography>

            <form onSubmit={handleSubmit}>
              <Grid
                container
                spacing={2}
                sx={{
                  width: "100%",
                  maxWidth: { xs: "100%", sm: 336 },
                  px: { xs: 1, sm: 0 },
                }}
              >
                {formStep === 1 ? (
                  /* Step 1: Registration Form */
                  <>
                    <Typography
                      variant="h5"
                      sx={{
                        fontFamily: "Poppins, Helvetica",
                        color: "white",
                        fontSize: { xs: 20, sm: 24 },
                        textAlign: "center",
                        width: "100%",
                        my: 1,
                        mt: 2,
                      }}
                    >
                      {translate('signUp', 'Sign Up')}
                    </Typography>
                    {/* <Typography
                      sx={{
                        color: "white",
                        textAlign: "start",
                        width: "100%",
                        px: 2,
                        fontSize: 14,
                      }}
                    >
                      All fields are mandatory.
                    </Typography> */}

                    <Grid item xs={12}>
                      <FormControl fullWidth error={!!errors.role}>
                        <Select
                          name="role"
                          value={formData.role}
                          onChange={handleChange}
                          size="small"
                          displayEmpty
                          renderValue={(selected) => {
                            if (selected) {
                              const selectedRole = roles.find(role => role.value === selected);
                              return selectedRole ? selectedRole.label : selected;
                            }
                            return translate('selectUserType', 'Select User Type*');
                          }}
                          MenuProps={{
                            anchorOrigin: {
                              vertical: "bottom",
                              horizontal: "left",
                            },
                            transformOrigin: {
                              vertical: "top",
                              horizontal: "left",
                            },
                          }}
                          IconComponent={KeyboardArrowDownIcon}
                          sx={{
                            bgcolor: "white",
                            borderRadius: 1,
                            textTransform: "capitalize",
                          }}
                        >
                          {roles.map((role) => (
                            <MenuItem
                              key={role.value}
                              value={role.value}
                              selected={role.value === "player"}
                            >
                              <Typography
                                variant="h6"
                                sx={{
                                  textTransform: "capitalize",
                                  fontSize: "16px !important",
                                  fontWeight: "normal",
                                }}
                              >
                                {role.label}
                              </Typography>
                            </MenuItem>
                          ))}
                        </Select>
                        {errors.role && (
                          <Typography
                            variant="caption"
                            sx={{ color: "red", mt: 0.5, ml: 0.5 }}
                          >
                            {errors.role[0]}
                          </Typography>
                        )}
                      </FormControl>
                    </Grid>
                    {formData.role === "club" && (
                      <Grid item xs={12}>
                        <TextField
                          name="firstName"
                          placeholder={translate('clubName', 'Club Name*')}
                          value={formData.firstName}
                          onChange={handleChange}
                          fullWidth
                          variant="outlined"
                          size="small"
                          sx={{ bgcolor: "white", borderRadius: 1 }}
                        />
                        {errors.firstName && (
                          <Typography
                            variant="caption"
                            sx={{ color: "red", mt: 0.5, ml: 0.5 }}
                          >
                            {errors.firstName?.[0]}
                          </Typography>
                        )}
                      </Grid>
                    )}
                    {(formData.role !== "club" || formData.role === "") && (
                      <>
                        {/* First Name and Last Name fields */}
                        <Grid item xs={6}>
                          <TextField
                            name="firstName"
                            placeholder={translate('firstName', 'First Name*')}
                            value={formData.firstName}
                            onChange={handleChange}
                            fullWidth
                            variant="outlined"
                            size="small"
                            sx={{ bgcolor: "white", borderRadius: 1 }}
                          />
                          {errors.firstName && (
                            <Typography
                              variant="caption"
                              sx={{ color: "red", ml: 0.5 }}
                            >
                              {errors.firstName?.[0]}
                            </Typography>
                          )}
                        </Grid>

                        <Grid item xs={6}>
                          <TextField
                            name="lastName"
                            placeholder={translate('lastName', 'Last Name*')}
                            value={formData.lastName}
                            onChange={handleChange}
                            fullWidth
                            variant="outlined"
                            size="small"
                            sx={{ bgcolor: "white", borderRadius: 1 }}
                          />
                          {errors.lastName && (
                            <Typography
                              variant="caption"
                              sx={{ color: "red", mt: 0.5, ml: 0.5 }}
                            >
                              {errors.lastName?.[0]}
                            </Typography>
                          )}
                        </Grid>
                      </>
                    )}



                    {/* Mobile Number */}
                    <Grid item xs={12}>
                      <Box sx={{ display: "flex", width: "100%" }}>
                        {/* Country code field - disabled with predefined value */}
                        <TextField
                          disabled
                          value="+91"
                          size="small"
                          variant="outlined"
                          sx={{
                            width: "75px",
                            mr: 1,
                            bgcolor: "white",
                            borderRadius: 1,
                            "& .MuiInputBase-input": {
                              fontWeight: "bold",
                              textAlign: "center",
                            },
                          }}
                          InputProps={{
                            readOnly: true,
                          }}
                        />

                        {/* Phone number field */}
                        <TextField
                          type="tel"
                          name="phoneNumber"
                          placeholder={translate('mobileNumber', 'Mobile Number*')}
                          value={formData.phoneNumber}
                          onChange={(e) => {
                            // Only allow numbers
                            const value = e.target.value.replace(/[^0-9]/g, "");
                            // Set phone number without prefix
                            setFormData({
                              ...formData,
                              phoneNumber: value,
                            });
                            // Clear error when user starts typing
                            setErrors((prev) => ({
                              ...prev,
                              phoneNumber: undefined,
                            }));
                          }}
                          fullWidth
                          size="small"
                          variant="outlined"
                          inputProps={{
                            maxLength: 10,
                            inputMode: "tel",
                            onKeyDown: (event) => {
                              const allowedKeys = [
                                "Backspace",
                                "Enter",
                                "Delete",
                                "ArrowLeft",
                                "ArrowRight",
                                "Tab",
                                "Home",
                                "End",
                              ];

                              if (
                                !/[0-9]/.test(event.key) &&
                                !allowedKeys.includes(event.key)
                              ) {
                                event.preventDefault();
                              }
                            },
                          }}
                          sx={{
                            bgcolor: "white",
                            borderRadius: 1,
                          }}
                        />
                      </Box>
                      {errors.phoneNumber && (
                        <Typography
                          variant="caption"
                          sx={{ color: "red", mt: 0.5, ml: 0.5 }}
                        >
                          {errors.phoneNumber?.[0]}
                        </Typography>
                      )}
                    </Grid>

                    {formData.role === "player" && (
                      <Grid item xs={12}>
                        <TextField
                          name="referral"
                          placeholder={translate('referralId', 'Referral ID (Enter player\'s CBID)')}
                          value={formData.referral}
                          onChange={handleChange}
                          fullWidth
                          variant="outlined"
                          size="small"
                          sx={{ bgcolor: "white", borderRadius: 1 }}
                        />
                        {errors.referral && (
                          <Typography
                            variant="caption"
                            sx={{ color: "red", mt: 0.5, ml: 0.5 }}
                          >
                            {errors.referral?.[0]}
                          </Typography>
                        )}
                      </Grid>
                    )}

                    {/* Email */}
                    <Grid item xs={12}>
                      <TextField
                        name="email"
                        placeholder={translate('email', 'Email*')}
                        value={formData.email}
                        onChange={handleChange}
                        fullWidth
                        size="small"
                        variant="outlined"
                        sx={{ bgcolor: "white", borderRadius: 1 }}
                      />
                      {errors.email && (
                        <Typography
                          variant="caption"
                          sx={{ color: "red", mt: 0.5, ml: 0.5 }}
                        >
                          {errors.email?.[0]}
                        </Typography>
                      )}
                    </Grid>

                    <Grid item xs={12}>
                      <TextField
                        name="password"
                        placeholder={translate('password', 'Password*')}
                        type={showPassword ? "text" : "password"}
                        value={formData.password}
                        onChange={handleChange}
                        fullWidth
                        size="small"
                        variant="outlined"
                        sx={{ bgcolor: "white", borderRadius: 1 }}
                        InputProps={{
                          endAdornment: (
                            <InputAdornment position="end">
                              <IconButton
                                aria-label="toggle password visibility"
                                onClick={handleClickShowPassword}
                                edge="end"
                                size="small"
                              >
                                {showPassword ? (
                                  <VisibilityOff />
                                ) : (
                                  <Visibility />
                                )}
                              </IconButton>
                            </InputAdornment>
                          ),
                        }}
                      />
                      {errors.password && (
                        <Typography
                          variant="caption"
                          sx={{ color: "red", mt: 0.5, ml: 0.5 }}
                        >
                          {errors.password?.[0]}
                        </Typography>
                      )}

                      {formData.password && (
                        <>
                          <Box sx={{ mt: 1 }}>
                            <Box
                              sx={{
                                display: "flex",
                                alignItems: "center",
                                justifyContent: "space-between",
                              }}
                            >
                              <Typography
                                variant="caption"
                                sx={{ color: "white", fontSize: 12 }}
                              >
                                {translate('passwordStrength', 'Password Strength:')}
                              </Typography>
                              <Typography
                                variant="caption"
                                sx={{
                                  color: getStrengthColor(
                                    getPasswordStrength(formData.password)
                                  ),
                                  fontWeight: "bold",
                                  fontSize: 12,
                                }}
                              >
                                {getStrengthLabel(
                                  getPasswordStrength(formData.password)
                                )}
                              </Typography>
                            </Box>
                            <Box
                              sx={{
                                width: "100%",
                                height: 4,
                                bgcolor: "rgba(255,255,255,0.2)",
                                borderRadius: 2,
                                mt: 0.5,
                                overflow: "hidden",
                              }}
                            >
                              <Box
                                sx={{
                                  height: "100%",
                                  width: `${(getPasswordStrength(formData.password) /
                                    5) *
                                    100
                                    }%`,
                                  bgcolor: getStrengthColor(
                                    getPasswordStrength(formData.password)
                                  ),
                                  transition: "width 0.3s ease",
                                }}
                              />
                            </Box>
                            <Typography
                              variant="caption"
                              sx={{
                                color: "white",
                                mt: 0.5,
                                display: "block",
                                fontSize: 10,
                                opacity: 0.8,
                              }}
                            >
                              {translate('passwordRequirements', 'Password must include 8+ chars, uppercase, lowercase, number & special character.')}
                            </Typography>
                          </Box>
                        </>
                      )}
                    </Grid>

                    {/* Confirm Password with show/hide toggle */}
                    <Grid item xs={12}>
                      <TextField
                        name="confirmPassword"
                        placeholder={translate('confirmPassword', 'Confirm Password*')}
                        type={showConfirmPassword ? "text" : "password"}
                        value={formData.confirmPassword}
                        onChange={handleChange}
                        fullWidth
                        size="small"
                        variant="outlined"
                        sx={{
                          bgcolor: "white",
                          borderRadius: 1,
                          "& .MuiOutlinedInput-root": {
                            "& fieldset": {
                              borderColor: formData.confirmPassword && formData.password !== formData.confirmPassword
                                ? "red"
                                : undefined,
                            },
                            "&:hover fieldset": {
                              borderColor: formData.confirmPassword && formData.password !== formData.confirmPassword
                                ? "red"
                                : undefined,
                            },
                          },
                        }}
                        InputProps={{
                          endAdornment: (
                            <InputAdornment position="end">
                              <IconButton
                                aria-label="toggle confirm password visibility"
                                onClick={handleClickShowConfirmPassword}
                                edge="end"
                                size="small"
                              >
                                {showConfirmPassword ? (
                                  <VisibilityOff />
                                ) : (
                                  <Visibility />
                                )}
                              </IconButton>
                            </InputAdornment>
                          ),
                        }}
                        error={!!errors.confirmPassword || (formData.confirmPassword && formData.password !== formData.confirmPassword)}
                      />
                      {errors.confirmPassword && (
                        <Typography
                          variant="caption"
                          sx={{ color: "red", mt: 0.5, ml: 0.5 }}
                        >
                          {errors.confirmPassword?.[0]}
                        </Typography>
                      )}
                      {!errors.confirmPassword && formData.confirmPassword && formData.password !== formData.confirmPassword && (
                        <Typography variant="caption" sx={{ color: "red", mt: 0.5, ml: 0.5 }}>
                          {translate('passwordMismatch', 'Password mismatch')}
                        </Typography>
                      )}
                    </Grid>

                    {/* Terms */}
                    <Grid
                      item
                      xs={12}
                      sx={{
                        padding: "0px !important",
                        paddingLeft: "15px !important",
                      }}
                    >
                      <Typography
                        sx={{ color: "white", fontSize: 12, mt: 0, mb: 1 }}
                      >
                        {translate('bySigningUpYouAgreeToOur', 'By Signing Up, You agree to our')}{" "}
                        <Link
                          to={"/policy/terms-and-conditions"}
                          style={{ textDecoration: "underline" }}
                          onClick={() => {
                            setOpen({ ...open, signup: false });
                          }}
                        >
                          {translate('terms', 'Terms')}{" "}
                        </Link>
                        &
                        <Link
                          to={"/policy/privacy-policy"}
                          style={{ textDecoration: "underline" }}
                          onClick={() => {
                            setOpen({ ...open, signup: false });
                          }}
                        >
                          {" "}
                          {translate('privacyPolicy', 'Privacy Policy')}
                        </Link>
                      </Typography>
                    </Grid>

                    {/* Send OTP Button */}
                    <Grid item xs={12}>
                      <Button
                        type="submit"
                        variant="contained"
                        size="large"
                        fullWidth
                        disabled={
                          sendingOtp ||
                          !formData.password ||
                          getPasswordStrength(formData.password) < 5 ||
                          formData.password !== formData.confirmPassword
                        }
                        sx={{
                          bgcolor: "#2c2891",
                          color: "white",
                          my: 1,
                          ":hover": { bgcolor: "#1a237e" },
                          "&.Mui-disabled": {
                            bgcolor: "#9e9e9e",
                            color: "white",
                          },
                        }}
                      >
                        {sendingOtp ? (
                          <CircularProgress size={24} color="inherit" />
                        ) : (
                          translate('sendOTP', 'Send OTP')
                        )}
                      </Button>
                      {/* {formData.password &&
                        getPasswordStrength(formData.password) < 5 && (
                          <Typography
                            variant="caption"
                            sx={{
                              color: "orange",
                              mt: 0.5,
                              ml: 0.5,
                              display: "block",
                              textAlign: "center",
                            }}
                          >
                            Please create a stronger password to continue
                          </Typography>
                        )} 
                      {formData.password &&
                        getPasswordStrength(formData.password) < 5 &&
                        formData.password !== formData.confirmPassword && (
                          <Typography
                            variant="caption"
                            sx={{
                              color: "orange",
                              mt: 0.5,
                              ml: 0.5,
                              display: "block",
                              textAlign: "center",
                            }}
                          >
                            Confirm Password must same as Password
                          </Typography>
                        )}*/}
                    </Grid>
                  </>
                ) : (
                  /* Step 2: OTP Verification */
                  <>
                    <Grid item xs={12} sx={{ textAlign: "center", mb: 2 }}>
                      <Typography
                        variant="h6"
                        sx={{
                          color: "white",

                          fontSize: 16,
                        }}
                      >
                        {translate('verifyYourMobileNumber', 'Verify Your Mobile Number')}
                      </Typography>
                      <Typography
                        variant="body2"
                        sx={{ color: "white", fontSize: 14 }}
                      >
                        {translate('weSentVerificationCodeTo', 'We\'ve sent a verification code to')}
                      </Typography>
                      <Typography
                        variant="body1"
                        sx={{ color: "white", fontWeight: "bold" }}
                      >
                        +91 {formData.phoneNumber}
                      </Typography>
                    </Grid>

                    <Grid item xs={12}>
                      <TextField
                        name="otp"
                        placeholder={translate('enterOTP', 'Enter OTP*')}
                        value={formData.otp}
                        onChange={handleChange}
                        fullWidth
                        size="small"
                        variant="outlined"
                        autoFocus
                        inputProps={{
                          maxLength: 6,
                          inputMode: "numeric",
                          onKeyDown: (event) => {
                            const allowedKeys = [
                              "Backspace",
                              "Enter",
                              "Delete",
                              "ArrowLeft",
                              "ArrowRight",
                              "Tab",
                              "Home",
                              "Ctrl",
                              "End",
                            ];

                            if (
                              !/[0-9]/.test(event.key) &&
                              !allowedKeys.includes(event.key) &&
                              !(
                                event.ctrlKey && event.key.toLowerCase() === "v"
                              ) && // Allow Ctrl+V
                              !(
                                event.metaKey && event.key.toLowerCase() === "v"
                              ) // Allow Cmd+V on Mac
                            ) {
                              event.preventDefault();
                            }

                            // Submit OTP on Enter key
                            if (
                              event.key === "Enter" &&
                              formData.otp &&
                              formData.otp.length >= 6
                            ) {
                              event.preventDefault();
                              handleVerifyOtp();
                            }
                          },
                        }}
                        sx={{
                          bgcolor: "white",
                          borderRadius: 1,
                          "& .MuiOutlinedInput-root": {
                            "& fieldset": {
                              borderColor: "#ff9800",
                              borderWidth: "1.5px",
                            },
                            "&:hover fieldset": {
                              borderColor: "#f57c00",
                            },
                            "&.Mui-focused fieldset": {
                              borderColor: "#f57c00",
                            },
                          },
                        }}
                      />
                      {errors.otp && (
                        <Typography
                          variant="caption"
                          sx={{ color: "red", mt: 0.5, ml: 0.5 }}
                        >
                          {errors.otp?.[0]}
                        </Typography>
                      )}
                    </Grid>

                    {/* Verify Button */}
                    <Grid item xs={12}>
                      <Button
                        variant="contained"
                        fullWidth
                        onClick={handleVerifyOtp}
                        disabled={
                          verifyingOtp ||
                          !formData.otp ||
                          formData.otp.length < 4
                        }
                        sx={{
                          bgcolor: "#2c832c",
                          color: "white",
                          height: "40px",

                          "&:hover": {
                            bgcolor: "#1b5e20",
                          },
                          "&.Mui-disabled": {
                            bgcolor: "#9e9e9e",
                          },
                          fontSize: 16,
                        }}
                      >
                        {verifyingOtp ? (
                          <CircularProgress size={24} color="inherit" />
                        ) : (
                          translate('verifyAndSignUp', 'Verify & Sign Up')
                        )}
                      </Button>
                    </Grid>

                    {/* Resend OTP */}
                    <Grid
                      item
                      xs={12}
                      sx={{
                        textAlign: "center",
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "center",
                      }}
                    >
                      <Typography
                        variant="body2"
                        sx={{ color: "white", fontSize: 14 }}
                      >
                        {translate('didntReceiveCode', 'Didn\'t receive the code?')}
                      </Typography>
                      <Button
                        onClick={handleSendOtp}
                        disabled={isResendDisabled}
                        sx={{
                          color: "#ff9800",
                          textTransform: "none",
                          fontSize: 14,
                          "&:hover": {
                            backgroundColor: "transparent",
                            color: "#f57c00",
                          },
                          "&.Mui-disabled": {
                            color: "#ff9800",
                          },
                        }}
                      >
                        {/* {sendingOtp ? (
                          <CircularProgress
                            size={16}
                            color="inherit"
                            sx={{ mr: 1 }}
                          />
                        ) : null} */}
                        {isResendDisabled
                          ? translate('resendOTPIn', `Resend OTP in ${timer}s`)
                          : translate('resendOTP', 'Resend OTP')}
                      </Button>
                    </Grid>

                    {/* Back Button */}
                    <Grid item xs={12} sx={{ textAlign: "center" }}>
                      <Button
                        onClick={() => setFormStep(1)}
                        startIcon={<ArrowBack />}
                        sx={{
                          color: "white",
                          textTransform: "none",
                          fontSize: 14,
                          "&:hover": {
                            backgroundColor: "transparent",
                            color: "#e0e0e0",
                          },
                        }}
                      >
                        {translate('editInformation', 'Edit Information')}
                      </Button>
                    </Grid>

                    {/* Back to Registration Button */}
                    {/* <Grid item xs={12} sx={{ mt: 1 }}>
                      <Button
                        variant="outlined"
                        onClick={handleBackToRegistration}
                        startIcon={<ArrowBack />}
                        sx={{
                          color: "white",
                          borderColor: "white",
                          "&:hover": {
                            borderColor: "#f0f0f0",
                            backgroundColor: "rgba(255, 255, 255, 0.1)",
                          },
                        }}
                      >
                        Back to Registration
                      </Button>
                    </Grid> */}
                  </>
                )}
              </Grid>
            </form>
          </Box>
        </Box>
      </Box>
    </Modal>
  );
};

export default SignUp;
