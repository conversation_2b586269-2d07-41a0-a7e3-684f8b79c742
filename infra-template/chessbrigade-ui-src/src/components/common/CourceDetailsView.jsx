import React, { useEffect, useState } from "react";
import { DetailTable } from "./DetailTable";

import {
  Box,
  Paper,
  Typography,
  Accordion,
  AccordionSummary,
  AccordionDetails,
} from "@mui/material";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import UseGlobalContext from "../../lib/hooks/UseGlobalContext";
import {
  capitalizeFirstLetter,
  formatDateToDMY,
} from "../../utils/formatters";
import CourseHeader from "./CourceHeader";

const CourseDetailsView = ({ course }) => {
  const [expanded, setExpanded] = useState({
    courseDetails: true,
    scheduleDetails: true,
    paymentDetails: true,
  });
  const { user } = UseGlobalContext();
  const [courseData, setCourseData] = useState({});

  useEffect(() => {
    if (!course) return;
    setCourseData(formatCourseData(course));
  }, [course]);

  const handleAccordionChange = (panel) => (_, isExpanded) => {
    setExpanded({
      ...expanded,
      [panel]: isExpanded,
    });
  };

  return (
    <Paper
      elevation={0}
      sx={{
        borderRadius: 2,
        overflow: "hidden",
        mb: 4,
        backgroundColor: "#f5f9f6",
      }}
    >
      {courseData && (
        <CourseHeader
          title={courseData.title || ""}
          description={courseData.description || ""}
        />
      )}

      {courseData &&
        courseData.sections &&
        courseData.sections.map((section) => (
          <SectionAccordion
            key={section.id}
            title={section.title}
            expanded={expanded[section.id]}
            onChange={handleAccordionChange(section.id)}
          >
            <DetailTable details={section.details} />
          </SectionAccordion>
        ))}
    </Paper>
  );
};

export default CourseDetailsView;

// 1. Add text after the value 'weeks'
function addAfterWeeks(value) {
  return `${value} weeks`;
}

// 2. Add text after the value 'sessions'
function addAfterSessions(value) {
  return `${value} sessions`;
}

// 3. Add text after the value 'hours'
function addAfterHours(value ) {
  return `${value} hours`;
}

// 4. Add money symbol before the value
function addMoneySymbol(value, symbol = '₹') {
  return `${symbol} ${value}`;
}

function formatSelectedDays(selectedDays) {
  if (!Array.isArray(selectedDays) || selectedDays.length === 0) {
    return 'No days selected';
  }

  // Capitalize the first letter of each day
  const formattedDays = selectedDays.map(day => 
    day.charAt(0).toUpperCase() + day.slice(1)
  );

  return formattedDays.join(', ');
}

// Format course data for display
function formatCourseData(course) {
  if (!course) {
    return {
      title: "",
      description: "",
      sections: [],
    };
  }

  return {
    title: course.title || "",
    description: course.description || "",
    sections: [
      {
        id: "courseDetails",
        title: "Course Details:",
        details: [
          {
            label: "Course Duration",
            value: course.courseDuration?addAfterWeeks(course.courseDuration):'-',
          },
          {
            label: "Total Sessions",
            value: course.totalSessions?addAfterSessions(course.totalSessions) : "-",
          },
          {
            label: "Sessions Per Week",
            value: course.sessionsPerWeek? addAfterSessions(course.sessionsPerWeek):"-",
          },
          {
            label: "Course Fee",
            value: course.courseFee? addMoneySymbol (course.courseFee): "-",
          },
        ],
      },
      {
        id: "scheduleDetails",
        title: "Schedule Details:",
        details: [
          {
            label: "Start Date",
            value: course.startDate?formatDateToDMY(course.startDate): "-",
          },
          {
            label: "End Date",
            value:  course.endDate? formatDateToDMY(course.endDate) : "-",
          },
          {
            label: "Registration Start Date",
            value: course.registrationStartDate ? formatDateToDMY(course.registrationStartDate) : "-",
          },
          {
            label: "Registration End Date",
            value: course.registrationEndDate? formatDateToDMY(course.registrationEndDate) : "-",
          },
          {
            label: "Session Start Time",
            value: course.sessionStartTime || "-",
          },
          {
            label: "Session End Time",
            value: course.sessionEndTime || "-",
          },
          {
            label: "Session Duration",
            value: course.sessionDuration ?addAfterHours(course.sessionDuration ) : "-",
          },
          {
            label: "Days",
            value: course.selectedDays? formatSelectedDays(course.selectedDays): "-",
          },
        ],
      },
      {
        id: "paymentDetails",
        title: "Payment Details:",
        details: [
          {
            label: "Payment Structure",
            value: course.paymentStructure || "-",
          },
        ],
      },
    ],
  };
}

// Section Accordion Component
const SectionAccordion = ({ title, expanded, onChange, children }) => (
  <Accordion
    expanded={expanded}
    onChange={onChange}
    sx={{
      boxShadow: "none",
      "&:before": {
        display: "none",
        margin: "0 !important",
      },
      "&.MuiAccordion-root.Mui-expanded": {
        margin: "0 !important",
      },
    }}
  >
    <AccordionSummary
      expandIcon={<ExpandMoreIcon />}
      sx={{
        backgroundColor: "#e8f5e9",
        borderTop: "1px solid #c8e6c9",
        borderBottom: "1px solid #c8e6c9",
        minHeight: 56,
        "&.Mui-expanded": {
          minHeight: 56,
        },
      }}
    >
      <Typography
        sx={{
          fontWeight: "bold",
          color: "#000",
        }}
      >
        {title}
      </Typography>
    </AccordionSummary>
    <AccordionDetails sx={{ p: 0 }}>{children}</AccordionDetails>
  </Accordion>
);


