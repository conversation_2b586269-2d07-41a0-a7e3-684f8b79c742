// components/YouTubePlayer.jsx
import React from 'react';
import { Box, Paper, useTheme, useMediaQuery } from '@mui/material';

const YouTubePlayer = ({ videoId, height = 450 }) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const isTablet = useMediaQuery(theme.breakpoints.between('sm', 'md'));

  const getResponsiveHeight = () => {
    if (isMobile) return 200;
    if (isTablet) return 300;
    return height;
  };

  return (
    <Paper elevation={3} sx={{ borderRadius: 3, overflow: 'hidden' }}>
      <Box position="relative" width="100%" height={getResponsiveHeight()}>
        <iframe
          width="100%"
          height="100%"
          src={`${videoId}?autoplay=1&mute=0&controls=0&modestbranding=1&rel=0&showinfo=02`}
          title="YouTube video player"
          frameBorder="0"
          allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
          allowFullScreen
        />
      </Box>
    </Paper>
  );
};

export default YouTubePlayer;
