import {
  Box,
  Button,
  Container,
  Alert,
  Alert<PERSON>itle,
  CircularProgress,
  Paper,
  Typography,
} from "@mui/material";
import React, { useEffect, useState, useCallback, useMemo } from "react";
import DynamicTable from "../common/DynamicTable";
import BackButton from "../common/BackButton";
import { Client } from "../../api/client";
import { useParams, useSearchParams } from "react-router-dom";
import { useRazorpayBulkPayment } from "../../lib/hooks/useRazorpayBulkPayment";
import UseToast from "../../lib/hooks/UseToast";

import Spinner from "../common/Spinner";

const SelectedPlayers = () => {
  const [players, setPlayers] = useState([]);
  const [searchParams] = useSearchParams();
  const [data, setData] = useState({});
  const [loading, setLoading] = useState(false);
  const [apiError, setApiError] = useState(null);
  const [amount, setAmount] = useState({
    tournamentFee: 0,
    platformFee: 0,
    total: 0,
  });

  const { title, id } = useParams();
  const toast = UseToast();

  // Memoize derived values
  const showDetails = useMemo(
    () => searchParams.get("show") === "true",
    [searchParams]
  );

  const {
    initiateBulkPayment,
    isLoading: isPaymentLoading,
    error: paymentError,
    resetError,
    isReady,
    isProcessing,
  } = useRazorpayBulkPayment(toast);

  // Memoize columns to prevent unnecessary re-renders
  const columns = useMemo(
    () => [
      { id: "sno", label: "S.No", format: (value, item, index) => index + 1 },
      { id: "ageCategory", label: "Age Category" },
      { id: "gender", label: "Gender" },
      { id: "playerTitle", label: "Title" },
      { id: "name", label: "Name" },
      { id: "fideId", label: "FIDE ID" },
      { id: "aicfId", label: "National ID" },
      { id: "stateId", label: "State ID" },
      { id: "districtId", label: "District ID" },
    ],
    []
  );

  // Fetch players data with error handling and cleanup
  const fetchRegisteredPlayersList = useCallback(async () => {
    if (!title || !id) {
      console.warn("Missing title or id parameters");
      return;
    }

    setLoading(true);
    setApiError(null);

    try {
      const response = await Client.get(
        `/tournament/${title}/register/bulk-register/${id}`
      );

    

      if (response.data && response.data.success) {
        const responseData = response.data.data || {};
        setData(responseData);
        setPlayers(responseData.players || []);

       
      } else {
        const errorMessage =
          response.data?.message || "Failed to fetch player data";
        setApiError(errorMessage);
        toast.error(errorMessage);
      }
    } catch (error) {
      console.error("Error fetching players:", error);
      const errorMessage =
        error.response?.data?.message ||
        error.message ||
        "Error loading players. Please try again.";
      setApiError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, [title, id]);

  // Fetch platform fee and calculate total amount
  const fetchPlatformFee = useCallback(async () => {
    if (!data?.totalAmount) {
      return;
    }

    try {
      const response = await Client.get("/tournament/platform-fee");
      if (response.data.success) {
        const platformFeePercentage = parseFloat(response.data.data.fee);

        const platformFee = (
          (data.totalAmount * platformFeePercentage) /
          100
        ).toFixed(2);

        const total = Number(data.totalAmount) + Number(platformFee);
        setAmount((prev) => ({
          ...prev,
          tournamentFee: data.totalAmount,
          platformFee: Number(platformFee),
          total: total,
        }));
      }
    } catch (error) {
      console.error("Error fetching platform fee:", error);
      // Fallback to 3.75% if API fails
      const platformFee = ((data.totalAmount * 3.75) / 100).toFixed(2);
      const total = Number(data.totalAmount) + Number(platformFee);
      setAmount((prev) => ({
        ...prev,
        tournamentFee: data.totalAmount,
        platformFee: Number(platformFee),
        total: total,
      }));
    }
  }, [data?.totalAmount]);

  useEffect(() => {
    fetchRegisteredPlayersList();
  }, [fetchRegisteredPlayersList]);

  // Fetch platform fee when tournament amount is available
  useEffect(() => {
    if (Number(data?.totalAmount) > 0) {
      
      fetchPlatformFee();
    }
  }, [data?.totalAmount, fetchPlatformFee]);

  // Handle bulk payment initiation - just trigger payment gateway
  const handleRegister = useCallback(async () => {
    if (showDetails || !data?.bulkRegistrationId) {
      console.warn(
        "Cannot register: showDetails =",
        showDetails,
        "bulkRegistrationId =",
        data?.bulkRegistrationId
      );
      return;
    }

    resetError();
    setLoading(true);

    try {
      // Just initiate payment - success/failure handled by navigation in hook
      await initiateBulkPayment(data.bulkRegistrationId);
    } catch (error) {
      console.error("Payment initiation error:", error);
      toast.error("Failed to start payment process.");
    } finally {
      setLoading(false);
    }
  }, [
    showDetails,
    data?.bulkRegistrationId,
    resetError,
    initiateBulkPayment,
    toast,
  ]);

  // Handle payment retry
  const handleRetry = useCallback(() => {
    resetError();
    handleRegister();
  }, [resetError, handleRegister]);

  // Format currency
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat("en-IN", {
      style: "currency",
      currency: "INR",
      minimumFractionDigits: 0,
      maximumFractionDigits: 2,
    }).format(amount);
  };

  // Early return for loading state
  if (loading && players.length === 0) {
    return (
      <Container maxWidth="xl" sx={{ py: 4, pt: 2, minHeight: "70vh" }}>
        <BackButton />
        <Box
          sx={{
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            minHeight: "50vh",
          }}
        >
          <CircularProgress />
        </Box>
      </Container>
    );
  }

  return (
    <Container maxWidth="xl" sx={{ py: 4, pt: 2, minHeight: "70vh" }}>
      <BackButton />

      {(isPaymentLoading || isProcessing) && (
        <Spinner fullScreen backgroundColor="rgba(255, 255, 255, 0.2)" />
      )}

      {/* API Error Display */}
      {apiError && (
        <Alert severity="error" sx={{ mb: 2, fontSize: "1rem" }}>
          <AlertTitle>Data Loading Error</AlertTitle>
          {apiError}
          <Button
            onClick={fetchRegisteredPlayersList}
            disabled={loading}
            size="small"
            sx={{ mt: 1 }}
            variant="outlined"
            color="error"
          >
            Retry Loading
          </Button>
        </Alert>
      )}

      {/* Payment Error Display - Only for payment initiation errors */}
      {paymentError && (
        <Alert severity="error" sx={{ mb: 2, fontSize: "1rem" }}>
          <AlertTitle>Payment Error</AlertTitle>
          {typeof paymentError === "object"
            ? paymentError.message || JSON.stringify(paymentError)
            : paymentError}
          <Button
            onClick={handleRetry}
            disabled={isPaymentLoading || isProcessing}
            size="small"
            sx={{ mt: 1 }}
            variant="outlined"
            color="error"
          >
            Retry Payment
          </Button>
        </Alert>
      )}

      {/* Tournament Info Display - Show even if some data is missing */}
      <Paper elevation={1} sx={{ p: 2, mb: 2 }}>
        <Typography variant="h6" gutterBottom>
          {data.tournamentTitle || `Tournament: ${title || "Unknown"}`}
        </Typography>
        <Box
          sx={{
            display: "flex",
            gap: 3,
            flexWrap: "wrap",
            alignItems: "center",
          }}
        >
          <Typography variant="body2" color="text.secondary">
            <strong>Players:</strong> {players.length}
          </Typography>

          {amount.tournamentFee > 0 && (
            <Typography variant="body2" color="text.secondary">
              <strong>Tournament Fee:</strong>{" "}
              {formatCurrency(amount.tournamentFee)}
            </Typography>
          )}

          {amount.platformFee > 0 && (
            <Typography variant="body2" color="text.secondary">
              <strong>Platform Fee:</strong>{" "}
              {formatCurrency(amount.platformFee)}
            </Typography>
          )}

          {amount.total > 0 ? (
            <Typography variant="body1" fontWeight="medium" color="primary">
              <strong>Total Amount:</strong> {formatCurrency(amount.total)}
            </Typography>
          ) : (
            <Typography variant="body2" color="text.secondary">
              <strong>Total Amount:</strong> Calculating...
            </Typography>
          )}
        </Box>
      </Paper>

      {/* Players Table */}
      {players.length > 0 ? (
        <DynamicTable
          columns={columns}
          data={players}
          loading={loading}
          page={1}
          totalPages={1}
          onPageChange={() => {}}
          idField="fideId"
          showDetailsButton={false}
        />
      ) : (
        <Paper elevation={1} sx={{ p: 3, textAlign: "center" }}>
          <Typography variant="h6" color="text.secondary">
            No players found
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
            {loading
              ? "Loading players..."
              : "No players registered for this tournament."}
          </Typography>
        </Paper>
      )}

      {/* Registration Button - Only visible when not in details view and has players */}
      {!showDetails && players.length > 0 && (
        <Box
          sx={{
            display: "flex",
            justifyContent: "center",
            mt: 3,
            gap: 2,
            flexDirection: "column",
            alignItems: "center",
          }}
        >
          <Button
            variant="contained"
            color="primary"
            size="large"
            onClick={handleRegister}
            disabled={
              isPaymentLoading ||
              isProcessing ||
              !isReady ||
              !data?.bulkRegistrationId ||
              apiError ||
              amount.total <= 0
            }
            startIcon={
              isPaymentLoading || isProcessing ? (
                <CircularProgress size={20} />
              ) : null
            }
            sx={{
              minWidth: 220,
              height: 48,
              fontSize: "1.1rem",
            }}
          >
            {isPaymentLoading || isProcessing
              ? "Initiating Payment..."
              : `Proceed to Payment ${
                  amount.total > 0 ? `(${formatCurrency(amount.total)})` : ""
                }`}
          </Button>

          {/* Status indicators */}
          {!isReady && !paymentError && !apiError && (
            <Typography variant="caption" color="text.secondary">
              Loading payment system...
            </Typography>
          )}

          {!data?.bulkRegistrationId && (
            <Typography variant="caption" color="error">
              Registration ID not available
            </Typography>
          )}

          {amount.total <= 0 && (
            <Typography variant="caption" color="warning.main">
              Payment amount not calculated
            </Typography>
          )}

          {/* Security Notice */}
          <Box sx={{ textAlign: "center", mt: 1 }}>
            <Typography variant="caption" color="text.secondary">
              Secure payment powered by Razorpay
            </Typography>
          </Box>
        </Box>
      )}
    </Container>
  );
};

export default SelectedPlayers;
