import React from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Checkbox,
  Typography,
  Box,
  Pagination,
  MenuItem,
  Chip,
  Tooltip,
  FormControl,
  Select,
  Skeleton,
  Divider,
  Button,
} from "@mui/material";
import { Warning as WarningIcon } from "@mui/icons-material";
import UseToast from "../../lib/hooks/UseToast";
import CustomPagination from "../common/CustomPagination";

/**
 * Component for displaying player search results with selection functionality
 * @param {Object} props - Component props
 * @param {Array} props.players - List of players to display
 * @param {number} props.totalCount - Total count of players
 * @param {number} props.currentPage - Current page number
 * @param {Function} props.onPageChange - Function to handle page change
 * @param {Function} props.onSelectPlayer - Function to handle player selection
 * @param {Array} props.selectedPlayers - List of already selected players
 * @param {Object} props.tournamentDetails - Tournament details including age and gender categories
 * @param {Object} props.search - Search criteria including ageCategory
 * @param {boolean} props.loading - Whether the data is currently loading
 * @param {Object} props.bulkRegistrationInfo - Information about the bulk registration including status
 */
const PlayerTable = ({
  players,
  totalCount,
  currentPage,
  totalPages,
  onPageChange,
  onSelectPlayer,
  search = {},
  selectedPlayers,
  /* tournamentDetails is kept for future use */
  emptyMessage = "No Results Found. Refine Your Search Criteria.",
  loading,
  bulkRegistrationInfo,
}) => {
  const toast = UseToast();

  const skeletonRows = 4;
  // Log debugging information

  // Check if we have a pending bulk registration
  const hasPendingBulkRegistration =
    bulkRegistrationInfo &&
    bulkRegistrationInfo.registrationStatus === "pending";
  // Check if player is already selected
  const isPlayerSelected = (cbid) => {
    // First check by cbid
    if (selectedPlayers.some((player) => player.cbid === cbid)) {
      return true;
    }

    // If not found by cbid, check by playerId (for backward compatibility)
    return selectedPlayers.some((player) => player.playerId === cbid);
  };

  // Handle checkbox change
  const handleCheckboxChange = (player) => {
    if (isPlayerSelected(player.cbid)) {
      // If already selected, remove from selection
      // Need to check both cbid and playerId to handle both cases
      const updatedPlayers = selectedPlayers.filter((p) => {
        // If either the cbid or playerId matches, remove the player
        const cbidMatch = p.cbid && player.cbid && p.cbid === player.cbid;
        const playerIdMatch =
          p.playerId && player.cbid && p.playerId === player.cbid;

        // Keep the player if neither condition is true
        return !cbidMatch && !playerIdMatch;
      });
      onSelectPlayer(updatedPlayers);

      if (hasPendingBulkRegistration) {
        toast.info(`Removed ${player.name} from pending bulk registration`);
      } else {
        toast.info(`Removed ${player.name} from selection`);
      }
    } else {
      // Safely check if search and ageCategory exist
      // Use optional chaining and nullish coalescing to handle undefined values
      const ageCategory = search?.ageCategory ?? "";
      const genderCategory = search?.genderCategory ?? "";

      // If not selected, check if category is selected
      // Use strict equality to empty string to check if ageCategory is not selected
      if (ageCategory === "") {
        toast.warning("Please select an gender & age category");

        return;
      }

      // Add to selection with the selected category
      const newPlayer = {
        playerId: player.userId || player.id, // Handle both id and playerId
        cbid: player.cbid,
        playerName: player.name || player.name || "Unknown Player", // Handle different name properties
        ageCategory,
        genderCategory,
        // If we have a pending bulk registration, include the bulkRegistrationId
        ...(hasPendingBulkRegistration && bulkRegistrationInfo
          ? {
            bulkRegistrationId: bulkRegistrationInfo.bulkRegistrationId,
          }
          : {}),
      };

      onSelectPlayer([...selectedPlayers, newPlayer]);

      if (hasPendingBulkRegistration) {
        toast.success(
          `Added ${newPlayer.playerName} to pending bulk registration with category ${ageCategory}`
        );
      } else {
        toast.success(
          `Added ${newPlayer.playerName} to selection with category ${ageCategory}`
        );
      }
    }
  };

  // Handle page change
  const handlePageChange = (page) => {
    // CustomPagination passes only the page number, not an event
    onPageChange(page);
  };

  // Render incomplete profile warning
  const renderIncompleteProfileWarning = (player) => {
    if (player.isProfileIncomplete) {
      return (
        <Tooltip title="Profile is incomplete. Click to complete the profile.">
          <Chip
            icon={<WarningIcon />}
            label="Incomplete Profile"
            color="warning"
            size="small"
            sx={{ ml: 1 }}
            onClick={() =>
              window.open(`/players/${player.playerId}/edit`, "_blank")
            }
          />
        </Tooltip>
      );
    }
    return null;
  };

  return (
    <Box width={"100%"}>
      <Box
        sx={{
          mb: 2,
          display: "flex",
          alignItems: "center",
          justifyContent: "space-between",
          p: 2,
        }}
      >
        <Box sx={{ display: "flex", alignItems: "center" }}>
          <Typography variant="h5" sx={{ color: "#3f51b5" }}>
            Search Results
            <Chip
              label={`${totalCount} players found`}
              color="primary"
              size="small"
              sx={{ ml: 1 }}
            />
          </Typography>
        </Box>

        {selectedPlayers.length > 0 && (
          <Chip
            label={`${selectedPlayers.length} players selected`}
            color="success"
            size="medium"
            sx={{ fontWeight: "bold" }}
          />
        )}
      </Box>

      <TableContainer sx={{ minHeight: "400px", overflowX: "hidden" }}>
        <Table>
          <TableHead>
            <TableRow
              sx={{
                bgcolor: "#CCBEF033",
                ".MuiTableCell-root": { textWrap: "nowrap" },
              }}
            >
              <TableCell padding="checkbox">
                {players &&
                  players.length > 0 &&
                  (search?.ageCategory && search?.genderCategory ? (
                    <Tooltip
                      title={`Select all ${players.length} players on this page`}
                    >
                      <Button
                        variant="contained"
                        color="primary"
                        size="small"
                        sx={{
                          bgcolor: "#4caf50", // Green color
                          "&:hover": {
                            bgcolor: "#388e3c", // Darker green on hover
                          },
                          fontWeight: "bold",
                          minWidth: "auto",
                          px: 1,
                        }}
                        onClick={() => {
                          // Select all players with current age and gender category
                          const newSelectedPlayers = [...selectedPlayers];
                          let addedCount = 0;

                          players.forEach((player) => {
                            // Skip if player is already selected
                            if (isPlayerSelected(player.cbid)) return;

                            // Add player with current age and gender category
                            const newPlayer = {
                              playerId: player.userId || player.id,
                              cbid: player.cbid,
                              playerName: player.name || "Unknown Player",
                              ageCategory: search.ageCategory,
                              genderCategory: search.genderCategory,
                              ...(hasPendingBulkRegistration &&
                              bulkRegistrationInfo
                                ? {
                                    bulkRegistrationId:
                                      bulkRegistrationInfo.bulkRegistrationId,
                                  }
                                : {}),
                            };

                            newSelectedPlayers.push(newPlayer);
                            addedCount++;
                          });

                          // Update selected players
                          onSelectPlayer(newSelectedPlayers);

                          // Show success message
                          if (addedCount > 0) {
                            toast.success(
                              `Selected ${addedCount} players on this page with ${search.genderCategory} ${search.ageCategory} category`
                            );
                          } else {
                            toast.info(
                              "All players on this page are already selected"
                            );
                          }
                        }}
                      >
                        Select All
                      </Button>
                    </Tooltip>
                  ) : (
                    <Tooltip title="Please select gender and age category first">
                      <Button
                        variant="outlined"
                        color="primary"
                        size="small"
                        disabled
                        sx={{
                          minWidth: "auto",
                          px: 1,
                        }}
                      >
                        Select All
                      </Button>
                    </Tooltip>
                  ))}
              </TableCell>
              <TableCell>Title</TableCell>
              <TableCell>Name</TableCell>
              <TableCell>DOB</TableCell>
              <TableCell>FIDE ID</TableCell>
              <TableCell>National ID</TableCell>
              <TableCell>Country</TableCell>
              <TableCell>State</TableCell>
              <TableCell>District</TableCell>
              <TableCell>City</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {/* Empty state */}

            {/* Loading skeletons */}
            {loading &&
              Array(skeletonRows)
                .fill(0)
                .map((_, index) => (
                  <TableRow key={`skeleton-${index}`}>
                    <TableCell colSpan={10}>
                      <Skeleton variant="rectangular" height={40} />
                    </TableCell>
                  </TableRow>
                ))}

            {/* Empty state */}
            {!loading && (!players || players.length === 0) ? (
              <TableRow sx={{ bgcolor: "#DAECF81F" }}>
                <TableCell colSpan={10} align="center">
                  <Typography variant="h6">
                    {selectedPlayers.length > 0
                      ? "All matching players have been selected. Try changing your search criteria to find more players."
                      : emptyMessage}
                  </Typography>
                </TableCell>
              </TableRow>
            ) : (
              /* Data rows */
              players.length > 0 &&
              !loading &&
              players.map((player, index) => (
                <React.Fragment key={player.cbid}>
                  <TableRow
                    sx={{
                      "&:hover": { bgcolor: "#f5f5f5" },

                      ".MuiTableCell-root": { textWrap: "nowrap" },
                      bgcolor: isPlayerSelected(player.cbid)
                        ? hasPendingBulkRegistration
                          ? "rgba(255, 152, 0, 0.08)" // Light orange for pending
                          : "rgba(76, 175, 80, 0.08)" // Light green for selected
                        : "#BEDDF026",
                    }}
                  >
                    <TableCell padding="checkbox">
                      <Checkbox
                        checked={isPlayerSelected(player.cbid)}
                        onChange={() => handleCheckboxChange(player)}
                        color="primary"
                        sx={{
                          color: "#bdbdbd",

                          "&.Mui-checked": {
                            color: "#3f51b5",
                          },
                        }}
                      />
                    </TableCell>
                    <TableCell>{player.playerTitle || "-"}</TableCell>
                    <TableCell>
                      <Box sx={{ display: "flex", alignItems: "center" }}>
                        {player.name}
                        {isPlayerSelected(player.cbid) && (
                          <Chip
                            label={
                              hasPendingBulkRegistration
                                ? "Pending"
                                : "Selected"
                            }
                            size="small"
                            color={
                              hasPendingBulkRegistration ? "warning" : "success"
                            }
                            sx={{ ml: 1, height: 20, fontSize: "0.7rem" }}
                          />
                        )}
                        {renderIncompleteProfileWarning(player)}
                      </Box>
                    </TableCell>
                    <TableCell>{player.dob || "-"}</TableCell>
                    <TableCell>{player.fideId || "-"}</TableCell>
                    <TableCell>{player.aicfId || "-"}</TableCell>
                    <TableCell>{player.country || "-"}</TableCell>
                    <TableCell>{player.state || "-"}</TableCell>
                    <TableCell>{player.district || "-"}</TableCell>
                    <TableCell>{player.city || "-"}</TableCell>
                  </TableRow>

                  {/* Divider between rows */}
                  {index < players.length - 1 && (
                    <TableRow>
                      <TableCell
                        colSpan={10}
                        sx={{ p: 0, borderBottom: "none" }}
                      >
                        <Divider />
                      </TableCell>
                    </TableRow>
                  )}
                </React.Fragment>
              ))
            )}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Pagination */}
      {!loading && totalPages > 1 && totalCount > 0 && (
        <>
          <CustomPagination
            totalPages={totalPages}
            currentPage={currentPage}
            onPageChange={handlePageChange}
          />
        </>
      )}
    </Box>
  );
};

export default PlayerTable;
