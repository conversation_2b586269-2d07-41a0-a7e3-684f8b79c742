import React, { useEffect, useState } from "react";
import {
  Box,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
  Button,
  Skeleton,
  FormControl,
  Select,
  MenuItem,
  Chip,
} from "@mui/material";
import KeyboardArrowDownIcon from "@mui/icons-material/KeyboardArrowDown";
import CustomPagination from "../common/CustomPagination";
import { Client } from "../../api/client";
import UseToast from "../../lib/hooks/UseToast";
import UseGlobalContext from "../../lib/hooks/UseGlobalContext";
import { useParams } from "react-router-dom";
import BackButton from "../common/BackButton";
import PlayerSearchForm from "../registration/PlayerSearchForm";
import {
  isTournamentConducted,
  isTournamentOngoing,
  isWithinRegistrationPeriod,
} from "../../utils/utils";

const AttendanceMarkingUI = () => {
  // States
  const [loading, setLoading] = useState(false);
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const { user } = UseGlobalContext();
  const [total, setTotal] = useState(0);
  const [tournamentDetails, setTournamentDetails] = useState({});
  const [players, setPlayers] = useState([]);
  const [dayCount, setDayCount] = useState([]);
  const [tournamentOngoing, setTournamentOngoing] = useState(false);
  const [withinRegistrationPeriod, setWithinRegistrationPeriod] =
    useState(false);
  const [tournamentConducted, setTournamentConducted] = useState(false);
  const { title } = useParams();
  const newTitle = encodeURIComponent(title);
  const [attendanceDates, setAttendanceDates] = useState([]);
  const [selectedDate, setSelectedDate] = useState("");
  const toast = UseToast();

  // Search state
  const [search, setSearch] = useState({
    playerName: "",
    playerId: "",
    ageCategory: "",
    genderCategory: "",
  });

  // Generate dates between start and end dates
  function generateDatesBetween(startDate, endDate) {
    const start = new Date(startDate);
    const end = new Date(endDate);
    const dateArray = [];

    // Ensure the start date is earlier than the end date
    if (start > end) {
      return "Start date should be earlier than end date";
    }

    let count = 1;
    // Loop through the dates from start to end and push to dateArray
    let currentDate = new Date(start);
    while (currentDate <= end) {
      const yy = currentDate.getFullYear().toString();
      const mm = String(currentDate.getMonth() + 1).padStart(2, "0");
      const dd = String(currentDate.getDate()).padStart(2, "0");
      dateArray.push({ date: `${dd}/${mm}/${yy}`, day: `day ${count}` });

      // Create a new date object to avoid modifying the original
      currentDate = new Date(currentDate);
      currentDate.setDate(currentDate.getDate() + 1);
      count++;
    }

    return dateArray;
  }

  // Parse date string to Date object for comparison
  const parseDate = (dateStr) => {
    if (!dateStr) return null;

    // Handle both date formats DD/MM/YYYY and DD-MM-YYYY
    const parts = dateStr.includes("/")
      ? dateStr.split("/")
      : dateStr.split("-");

    if (parts.length !== 3) return null;

    const [day, month, year] = parts.map(Number);
    return new Date(year, month - 1, day);
  };

  // Check if date is in the past compared to today
  const isDatePast = (dateStr) => {
    const date = parseDate(dateStr);
    if (!date) return false;

    const today = new Date();
    today.setHours(0, 0, 0, 0);
    return date < today;
  };

  // Reset search fields
  const handleReset = () => {
    setSearch({
      ...search,
      playerName: "",
      playerId: "",
    });
  };

  // Initial data loading
  useEffect(() => {
    if (tournamentOngoing || tournamentConducted) return;
    fetchTournamentDetails();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Set current date on component mount
  useEffect(() => {
    const date = new Date();
    const formattedDate = `${String(date.getDate()).padStart(2, "0")}/${String(
      date.getMonth() + 1
    ).padStart(2, "0")}/${date.getFullYear()}`;
    setSelectedDate(formattedDate);
  }, []);

  // Fetch tournament details
  const fetchTournamentDetails = async () => {
    try {
      const response = await Client.get(`/tournament/${newTitle}`);
      if (!response.data.success) return;

      const tournamentData = response.data.data;
      setTournamentDetails(tournamentData);

      // Check tournament status
      const startDate = tournamentData?.startDate;
      const endDate = tournamentData?.endDate;
      const reportingTime = tournamentData?.reportingTime;

      const ongoing = isTournamentOngoing(startDate, endDate, reportingTime);
      setTournamentOngoing(ongoing);

      const start = tournamentData?.registrationStartDate;
      const end = tournamentData?.registrationEndDate;
      const endTime = tournamentData?.registrationEndTime;

      const isOpen = isWithinRegistrationPeriod(start, end, endTime);
      setWithinRegistrationPeriod(isOpen);

      const conducted = isTournamentConducted(endDate);
      setTournamentConducted(conducted);

      // Generate all tournament days
      if (startDate && endDate) {
        const allDays = generateDatesBetween(startDate, endDate);
        setDayCount(allDays);

        // Set attendance dates based on tournament days
        const dates = allDays.map((day) => day.date);
        setAttendanceDates(dates);
      }

      // Fetch players after tournament details are loaded
      handleSearch();
    } catch (error) {
      console.error("Error fetching tournament details:", error);
      toast.error("Failed to fetch tournament details");
    }
  };

  // Handle pagination
  const handlePageChange = (newPage) => {
    setPage(newPage);
    handleSearch(newPage);
  };

  // Fetch players data
  const fetchPlayers = async (pageNum = page) => {
    setLoading(true);
    try {
      const params = {
        playerName: search.playerName,
        playerId: search.playerId,
        ageCategory: search.ageCategory,
        genderCategory: search.genderCategory,
        page: pageNum,
        limit: 10,
        tournamentId: title,
        isPlayer: true
      };

      const response = await Client.get(`club/tournament/${newTitle}/players`, {
        params,
      });

      if (!response.data.success) {
        setPlayers([]);
        setTotal(0);
        setTotalPages(0);
        return;
      }

      if (response.data.status === 204 || response.data.total === 0) {
        setPlayers([]);
        setTotal(0);
        setTotalPages(0);
        return;
      }

      const data = response.data.data;

      // Process players data to ensure attendanceMark exists and is properly formatted
      const processedPlayers = data.players.map((player) => {
        // Initialize attendanceMark as an empty object if it's null
        if (!player.attendanceMark) {
          player.attendanceMark = {};
        }

        // If attendanceMark is an array, convert it to object format
        if (Array.isArray(player.attendanceMark)) {
          const attendanceObject = {};
          player.attendanceMark.forEach((mark) => {
            if (mark && mark.date) {
              attendanceObject[mark.date] = {
                attendance: mark.attendance,
                day: mark.day,
              };
            }
          });
          player.attendanceMark = attendanceObject;
        }

        return player;
      });

      // Set players data with processed attendance
      setPlayers(processedPlayers);
      setTotalPages(data.totalPages);
      setTotal(data.total);
    } catch (error) {
      console.error("Error fetching players:", error);
      toast.error("Failed to fetch players");
    } finally {
      setLoading(false);
    }
  };

  // Handle search button click
  const handleSearch = (pageNum = 1) => {
    setPage(pageNum);
    fetchPlayers(pageNum);
  };

  // Update attendance
  const updateAttendance = async ({ index, value, date = selectedDate }) => {
    // Find the current day object that matches the selected date
    const currentDay = dayCount.find((day) => day.date === date);

    if (!currentDay) {
      toast.info("Tournament is not started yet or date not found");
      console.error("Date not found in tournament days:", date, dayCount);
      return;
    }

    // const permanentAttendance = value === "discont" || value === "disQual";
    const permanentAttendance = value === "discont";
    const playerAttendance = {
      date: date,
      attendance: value || "absent",
      userId: user?.userId,
    };

    try {
      const response = await Client.patch(
        `tournament/${newTitle}/register/attendance`,
        {
          attendanceMark: playerAttendance,
          date: currentDay.day,
          registrationId: players[index].registrationId,
          permanentAttendance,
          tournamentDays: dayCount,
        }
      );

      if (response.data.success) {
        // Update local state for immediate UI feedback
        setPlayers((prevPlayers) => {
          const updated = [...prevPlayers];

          // Initialize attendanceMark if it doesn't exist
          if (!updated[index].attendanceMark) {
            updated[index].attendanceMark = {};
          }

          // Update the attendance mark
          updated[index].attendanceMark = {
            ...updated[index].attendanceMark,
            [date]: {
              attendance: value,
              day: currentDay.day,
            },
          };

          return updated;
        });

        toast.success("Attendance updated successfully");
      } else {
        toast.error(response.data.message || "Failed to update attendance");
      }
    } catch (error) {
      console.error("Error updating attendance:", error);
      toast.error("Failed to update attendance");
    }
  };

  // Attendance options
  const attendanceOptions = [
    { value: "present", label: "Present", color: "#389e0d" },
    { value: "absent", label: "Absent", color: "#7e57c2" },
    { value: "discont", label: "Discontinue", color: "#FF0000" },
    { value: "disQual", label: "Disqualified", color: "#FFA500" },
  ];

  // Get color for attendance status
  const getAttendanceColor = (status) => {
    const option = attendanceOptions.find((opt) => opt.value === status);
    return option ? option.color : "#9e9e9e";
  };

  // Get label for attendance status
  const getAttendanceLabel = (status) => {
    const option = attendanceOptions.find((opt) => opt.value === status);
    return option ? option.label : "Not Marked";
  };

  // Handle date selection change
  const handleDateChange = (event) => {
    setSelectedDate(event.target.value);
  };

  // Get player's attendance for a specific date
  const getPlayerAttendance = (player, date) => {
    if (!player.attendanceMark) return null;

    const attendanceData = player.attendanceMark[date];
    return attendanceData ? attendanceData.attendance : null;
  };

  // Fetch players data
  const handleReport = async () => {
    try {

      const response = await Client.get(`tournament/attendance-report?id=${title}`,{
        responseType: "blob",
      });

      const url = window.URL.createObjectURL(new Blob([response.data]));

      const link = document.createElement("a");
      link.href = url;
      link.setAttribute("download", "Attendance_Report.xlsx");
      document.body.appendChild(link);
      link.click();
      link.remove();
    } catch (error) {
      console.error("Error fetching players:", error);
      toast.error("Failed to fetch players");
    } 
  };

  return (
    <Box>
      {withinRegistrationPeriod ? (
        <Box
          sx={{
            width: "100%",
            minHeight: "60dvh",
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
          }}
        >
          <Typography>Tournament is Not Started Yet</Typography>
        </Box>
      ) : (
        <>
        
          <Box mb={2} sx={{ display: 'flex', justifyContent: 'flex-end' }}>
            <Button
              size="small"
              variant="contained"
              disabled={players.length === 0}
              onClick={()=>handleReport()}
            >
              Attendance Report
            </Button>

          </Box>
          <PlayerSearchForm
            tournamentDetails={tournamentDetails}
            loading={loading}
            search={search}
            setSearch={setSearch}
            handleReset={handleReset}
            handleSearch={handleSearch}
            setLoading={setLoading}
          />

          {/* Date Selection Dropdown */}
          {attendanceDates.length > 0 && (
            <Box sx={{ my: 2, display: "flex", alignItems: "center" }}>
              <Typography sx={{ mr: 2 }}>Select Date:</Typography>
              <FormControl size="small" sx={{ minWidth: 150 }}>
                <Select
                  value={selectedDate}
                  onChange={handleDateChange}
                  displayEmpty
                  renderValue={(value) => {
                    if (!value) return "Select Date";
                    return (
                      value +
                      (isDatePast(value) && value !== selectedDate
                        ? " (Past)"
                        : "")
                    );
                  }}
                >
                  {attendanceDates.map((date) => (
                    <MenuItem key={date} value={date}>
                      {date} {isDatePast(date) ? "(Past)" : ""}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Box>
          )}

          {/* Tournament Info Banner
          {tournamentDetails && (
            <Box sx={{ mb: 2, p: 2, bgcolor: "#f9f9f9", borderRadius: 1 }}>
              <Typography variant="subtitle2">
                Tournament Date & Time: {tournamentDetails.startDate} to{" "}
                {tournamentDetails.endDate} at {tournamentDetails.reportingTime}
              </Typography>
            </Box>
          )} */}

          {/* Players Table */}
          <TableContainer component={Paper} sx={{ mb: 2, boxShadow: 2 }}>
            <Table sx={{ minWidth: 650 }}>
              <TableHead>
                <TableRow sx={{ bgcolor: "#CCBEF033", ".MuiTableCell-root": { textWrap: "nowrap" } }}>
                  <TableCell>S.No</TableCell>
                  <TableCell>
                    <Box sx={{ display: "flex", alignItems: "center" }}>
                      Title <KeyboardArrowDownIcon fontSize="small" />
                    </Box>
                  </TableCell>
                  <TableCell>Name</TableCell>
                  <TableCell>FIDE Rating</TableCell>
                  <TableCell>FIDE ID</TableCell>

                  {/* Selected Date Attendance Column */}
                  <TableCell>Attendance - {selectedDate}</TableCell>
                  {/* Past Attendance Columns - Show all dates up to today */}
                  {attendanceDates
                    .filter((date) => isDatePast(date) && date !== selectedDate)
                    .map((date) => <TableCell key={date}>{date}</TableCell>)
                    .reverse()}
                </TableRow>
              </TableHead>
              <TableBody>
                {loading ? (
                  // Loading skeletons
                  Array(6)
                    .fill(0)
                    .map((_, index) => (
                      <TableRow key={`skeleton-${index}`}>
                        <TableCell colSpan={10}>
                          <Skeleton
                            variant="rectangular"
                            height={40}
                            animation="wave"
                          />
                        </TableCell>
                      </TableRow>
                    ))
                ) : !loading && players.length === 0 ? (
                  // No players found message
                  <TableRow sx={{ bgcolor: "#DAECF81F" }}>
                    <TableCell colSpan={10} align="center" sx={{ py: 4 }}>
                      <Typography variant="h6" color="textSecondary">
                        No Results Found. Refine Your Search Criteria.
                      </Typography>
                    </TableCell>
                  </TableRow>
                ) : (
                  !loading &&
                  players.length > 0 &&
                  players.map((player, index) => (
                    <TableRow
                      key={player.registrationId || index}
                      sx={{
                        "&:nth-of-type(odd)": { bgcolor: "#f9f9f9" },
                        "&:hover": { bgcolor: "#f1f1f1" },
                      }}
                    >
                      <TableCell>{index + 1}</TableCell>
                      <TableCell>{player.playerTitle || "-"}</TableCell>
                      <TableCell>{player.playerName}</TableCell>
                      <TableCell>{player.fideRating || "-"}</TableCell>
                      <TableCell>{player.fideId || "-"}</TableCell>

                      {/* Current Date Attendance Options */}
                      <TableCell>
                        {user?.role === "arbiter" ? (
                          // For arbiters - show attendance marking buttons
                          <Box
                            sx={{ display: "flex", flexWrap: "wrap", gap: 1 }}
                          >
                            {attendanceOptions.map((option) => {
                              const currentAttendance = getPlayerAttendance(
                                player,
                                selectedDate
                              );
                              const isSelected =
                                currentAttendance === option.value;

                              return (
                                <Button
                                  key={option.value}
                                  variant={
                                    isSelected ? "contained" : "outlined"
                                  }
                                  size="small"
                                  // disabled={!!currentAttendance}
                                  onClick={() =>
                                    updateAttendance({
                                      index,
                                      value: option.value,
                                      date: selectedDate,
                                    })
                                  }
                                  sx={{
                                    borderColor: option.color,
                                    color: isSelected ? "#fff" : option.color,
                                    backgroundColor: isSelected
                                      ? option.color
                                      : "transparent",
                                    "&:hover": {
                                      backgroundColor: option.color,
                                      color: "#fff",
                                    },
                                  }}
                                >
                                  {option.label}
                                </Button>
                              );
                            })}
                          </Box>
                        ) : (
                          // For non-arbiters - just show the status
                          <Box>
                            {getPlayerAttendance(player, selectedDate) ? (
                              <Chip
                                label={getAttendanceLabel(
                                  getPlayerAttendance(player, selectedDate)
                                )}
                                size="small"
                                sx={{
                                  bgcolor: getAttendanceColor(
                                    getPlayerAttendance(player, selectedDate)
                                  ),
                                  color: "white",
                                  fontWeight: "medium",
                                }}
                              />
                            ) : (
                              <Typography
                                variant="body2"
                                color="text.secondary"
                              >
                                Not Marked
                              </Typography>
                            )}
                          </Box>
                        )}
                      </TableCell>
                      {/* Past Attendance Status */}
                      {attendanceDates
                        .filter(
                          (date) => isDatePast(date) && date !== selectedDate
                        )
                        .map((date) => {
                          const attendanceStatus = getPlayerAttendance(
                            player,
                            date
                          );
                          return (
                            <TableCell key={date}>
                              {attendanceStatus !== undefined ? (
                                <Chip
                                  label={getAttendanceLabel(attendanceStatus)}
                                  size="small"
                                  sx={{
                                    bgcolor:
                                      getAttendanceColor(attendanceStatus),
                                    color: "white",
                                    fontWeight: "medium",
                                  }}
                                />
                              ) : (
                                <Typography
                                  variant="h6"
                                  color="text.primary"
                                  sx={{ fontWeight: "medium", fontSize: 16 }}
                                >
                                  Not Marked
                                </Typography>
                              )}
                            </TableCell>
                          );
                        })
                        .reverse()}
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </TableContainer>

          {/* Pagination and Summary */}
          {!loading && totalPages > 1 && (
            <CustomPagination
              totalPages={totalPages}
              currentPage={page}
              onPageChange={handlePageChange}
            />
          )}
        </>
      )}
    </Box>
  );
};

export default AttendanceMarkingUI;
