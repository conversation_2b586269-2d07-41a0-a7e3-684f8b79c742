import {
  Document,
  Page,
  Text,
  View,
  StyleSheet,
  Image,
  Font,
} from "@react-pdf/renderer";

import { images } from "../../utils/constant";

// Register Lato font family with TTF files
import LatoRegular from "../../assets/fonts/Lato-Regular.ttf";
import LatoBold from "../../assets/fonts/Lato-Bold.ttf";
import LatoItalic from "../../assets/fonts/Lato-Italic.ttf";
import LatoBoldItalic from "../../assets/fonts/Lato-BoldItalic.ttf";

//images for trophy

import Trophy1 from "../../assets/images/trophy-1.png";
import Trophy2 from "../../assets/images/trophy-2.png";
import Trophy3 from "../../assets/images/trophy-3.png";

Font.register({
  family: "Lato",
  format: "truetype",
  fonts: [
    {
      src: LatoRegular,
      fontWeight: 400,
    },
    {
      src: LatoBold,
      fontWeight: 700,
    },
    {
      src: LatoItalic,
      fontWeight: 400,
      fontStyle: "italic",
    },
    {
      src: LatoBoldItalic,
      fontWeight: 700,
      fontStyle: "italic",
    },
  ],
});

const styles = StyleSheet.create({
  page: {
    flexDirection: "column",
    backgroundColor: "#FFFFFF",
    padding: 20,
    fontFamily: "Lato",
    fontWeight: 400,
    fontSize: 14,
    position: "relative",
    border: "10px solid #2c5282",
  },

  // Instead of background watermark, create a separate View component
  watermarkContainer: {
    position: "absolute",
    top: "40%",
    left: "40%",
    width: 200,
    height: 200,
    opacity: 0.1,
    zIndex: 0, // Use 0 instead of negative values
  },

  watermarkText: {
    fontSize: 60, // Reduced from 200 - large fonts can cause issues
    color: "rgba(255, 0, 0, 0.3)",
    fontWeight: "bold",
    textAlign: "center",
  },

  // For trophy, use Image component instead of background
  trophyContainer: {
    position: "absolute",
    top: "20%",
    left: "30%",
    width: 100,
    height: 100,
    opacity: 0.08,
    zIndex: 0,
    alignItems: "center",
    justifyContent: "center",
    transform: "scale(3.33)", // Scale up to ~500px
    transformOrigin: "top left",
  },

  // Badge positioning fix
  badgeContainer: {
    position: "absolute",
    top: 30,
    left: 30,
    width: 80,
    height: 80,
    zIndex: 1,
  },

  // Content container with higher z-index
  contentContainer: {
    position: "relative",
    zIndex: 2,
    backgroundColor: "transparent",
  },

  logoPlaceholder: {
    width: 60,
    height: 60,
    backgroundColor: "#FFD700",
    borderRadius: 30,
    alignItems: "center",
    justifyContent: "center",
    border: "2px solid #B8860B",
  },

  // Updated header style for perfect centering
  header: {
    alignItems: "center",
    marginBottom: 10,
    width: "100%",
  },

  logoContainer: {
    width: 80,
    height: 80,
    alignItems: "center",
    justifyContent: "center",
  },

  logoText: {
    fontSize: 14,
    fontWeight: "bold",
    color: "#B8860B",
  },

  // Updated header content for perfect centering
  headerContent: {
    alignItems: "center",
    display: "flex",
    flexDirection: "column",
    width: "100%",
  },

  schoolName: {
    fontSize: 28,
    fontWeight: "bold",
    color: "#2c5282",
    marginBottom: 5,
    marginTop: 10,
    textAlign: "center",
    fontStyle: "italic",
    fontFamily: "Lato",
    width: "100%",
  },

  tournamentTitle: {
    fontSize: 34,
    fontWeight: "bold",
    color: "#2c5282",
    marginBottom: 5,
    textAlign: "center",
    fontFamily: "Lato",
    fontStyle: "italic",
    width: "100%",
  },

  presentsText: {
    fontSize: 16,
    color: "#2c5282",
    marginBottom: 5,
    textAlign: "center",
    fontStyle: "italic",
    fontFamily: "Lato",
    width: "100%",
  },

  subtitle: {
    fontSize: 18,
    color: "#2c5282",
    marginBottom: 5,
    textAlign: "center",
    fontFamily: "Lato",
    fontStyle: "italic",
    width: "100%",
  },

  poweredBy: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#2c5282",
    textAlign: "center",
    fontFamily: "Lato",
    width: "100%",
    fontStyle: "italic",
  },
  poweredByText: {
    fontSize: 16,
    color: "#2c5282",
    textAlign: "center",
    fontFamily: "Lato",
    width: "100%",
    fontStyle: "italic",
  },

  certificateBody: {
    marginTop: 10,
    marginBottom: 20,
    alignItems: "center",
  },

  certificationText: {
    fontSize: 14,
    color: "#000000",
    marginBottom: 10,
    textAlign: "center",
    fontStyle: "italic",
  },

  playerName: {
    fontSize: 28,
    fontWeight: "bold",
    color: "#2c5282",
    marginBottom: 25,
    textAlign: "center",

    fontFamily: "Lato",
  },

  // signatureLine: {
  //   width: "100%",
  //   height: 1,
  //   backgroundColor: "#000000",
  //   marginBottom: 10,
  // },

  participationText: {
    fontSize: 14,
    color: "#000000",
    marginBottom: 10,
    textAlign: "center",
    lineHeight: 1.5,
    fontFamily: "Lato",
    fontStyle: "italic",
  },

  venueText: {
    fontSize: 14,
    color: "#000000",
    marginBottom: 10,
    textAlign: "center",
    fontStyle: "italic",
    fontFamily: "Lato",
    lineHeight: 1.5,
  },

  performanceText: {
    fontSize: 14,
    color: "#000000",
    marginBottom: 20,
    textAlign: "center",
    lineHeight: 1.5,
    fontFamily: "Lato",
    fontStyle: "italic",
  },

  achievement: {
    fontSize: 30,
    fontWeight: "bold",
    color: "#B8860B",
    textAlign: "center",
    // marginBottom: 30,
    fontStyle: "italic",
    fontFamily: "Lato",
  },

  footer: {
    marginTop: "auto",
    paddingTop: 5,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-end",
  },
  signatureBox: {
    alignItems: "center",
    width: 200,
  },
  signatureImage: {
    width: 120,
    height: 40,
  },

  signatureLabel: {
    fontSize: 10,
    color: "#7f8c8d",
  },
  signatureSection: {
    alignItems: "center",
    width: "40%",
  },

  signatureTitle: {
    fontSize: 20,
    fontWeight: "bold",
    fontStyle: "italic",
    color: "#2c5282",
    textAlign: "center",
  },

  decorativeBorder: {
    position: "absolute",
    top: 20,
    left: 20,
    right: 20,
    bottom: 20,
    border: "1px solid #B8860B",
    borderRadius: 5,
  },

  date: {
    fontSize: 10,
    color: "#666666",
    textAlign: "right",
  },
});

const trophy = {
  1: Trophy1,
  2: Trophy2,
  3: Trophy3,
};

const additionalStyles = {
  rankContainer: {
    flexDirection: "row",
    alignItems: "flex-start",
    justifyContent: "center",
  },
  rankNumber: {
    fontSize: 30,
    fontWeight: "bold",
    color: "#B8860B",
    textAlign: "center",
    fontStyle: "italic",
    fontFamily: "Lato",
  },
  rankSuffix: {
    fontSize: 18, // Smaller than the number
    marginTop: -5, // Raises the suffix up (superscript effect)
    marginLeft: 1,
    fontWeight: "bold",
    color: "#B8860B",
    fontStyle: "italic",
    fontFamily: "Lato",
  },
  rankText: {
    fontSize: 30,
    fontWeight: "bold",
    color: "#B8860B",
    textAlign: "center",
    fontStyle: "italic",
    fontFamily: "Lato",
    marginLeft: 2,
  }
};

// ADD this RankDisplay component inside your CertificateTemplate.jsx file:
const RankDisplay = ({ rankData }) => {
  // If it's an object (ranks 1-20), render with superscript
  if (typeof rankData === 'object' && rankData !== null && rankData.number) {
    return (
      <View style={additionalStyles.rankContainer}>
        <Text style={additionalStyles.rankNumber}>{rankData.number}</Text>
        <Text style={additionalStyles.rankSuffix}>{rankData.suffix}</Text>
        <Text style={additionalStyles.rankText}> Place</Text>
      </View>
    );
  }
  
  // If it's a string ("Participation"), render as is
  return (
    <View style={additionalStyles.rankContainer}>
      <Text style={styles.achievement}>{rankData}</Text>
    </View>
  );
};

const CertificateTemplate = ({ data = {} }) => {
  const {
    organization = "sample organization",
    tournamentTitle = "sample tournament",
    subtitle = "sample subtitle",
    poweredBy = "ChessBrigade.com",
    playerName = "Player Name",
    tournamentDate = "13th July 2025",
    venue = "sample venue",
    points = "05",
    totalRounds = "07",
    category = "U08 Boys",
    position = "Participation",
    rank = 3,
    timestamp = new Date().toLocaleDateString(),
    chiefArbiterSignature = null,
    tournamentDirectorSignature = null,
  } = data;

  const trophyImage = rank <= 20 && trophy[rank];

  return (
    <Document>
      <Page size="A4" style={styles.page} orientation="landscape">
        {/* Decorative border */}
        <View style={styles.decorativeBorder} />

        {/* Trophy background image */}
        <Image style={styles.trophyContainer} src={images.trophy} />

        {/* Badge for top 3 positions only */}
        {trophyImage && (
          <Image style={styles.badgeContainer} src={trophyImage} />
        )}

        {/* Header - Simplified structure for perfect centering */}
        <View style={styles.header}>
          <View style={styles.headerContent}>
            <Text style={styles.schoolName}>{organization}</Text>
            <Text style={styles.presentsText}>Presents</Text>
            <Text style={styles.tournamentTitle}>{tournamentTitle}</Text>
            <Text style={styles.subtitle}>{subtitle}</Text>
            {poweredBy && <Text style={styles.poweredByText}>Powered by</Text>}
            {poweredBy && <Text style={styles.poweredBy}>{poweredBy}</Text>}
          </View>
        </View>

        {/* Certificate Body */}
        <View style={styles.certificateBody}>
          <Text style={styles.certificationText}>This is to certify that</Text>

          <Text style={styles.playerName}>{playerName}</Text>

          <Text style={styles.participationText}>
            participated in the tournament held on the {tournamentDate},
          </Text>

          <Text style={styles.venueText}>at {venue}.</Text>

          <Text style={styles.performanceText}>
            He/She has scored {points} points out of {totalRounds} rounds in the{" "}
            {category} category and secured
          </Text>

         <RankDisplay rankData={position} />
        </View>

        {/* Footer with signatures */}
        <View style={styles.footer}>
          <View style={styles.signatureSection}>
            {tournamentDirectorSignature ? (
              <Image
                src={tournamentDirectorSignature}
                style={styles.signatureImage}
                cache={true}
              />
            ) : null}
            <Text style={styles.signatureTitle}>Tournament Director</Text>
          </View>
          <View style={styles.signatureSection}>
            {chiefArbiterSignature ? (
              <Image
                src={chiefArbiterSignature}
                style={styles.signatureImage}
                // Add cache and error handling
                cache={true}
              />
            ) : null}
            <Text style={styles.signatureTitle}>Chief Arbiter</Text>
          </View>
        </View>

        <Text style={styles.date}>Issued on: {timestamp}</Text>
      </Page>
    </Document>
  );
};

export default CertificateTemplate;
