import { Box, CircularProgress, Typography, Paper } from "@mui/material";
import { GoogleMap, useJsApi<PERSON>oa<PERSON>, Mark<PERSON> } from "@react-google-maps/api";
import { useState, useEffect, useCallback, useMemo } from "react";
import { useController, useWatch } from "react-hook-form";

// Define the API key at module level
const GOOGLE_MAPS_API_KEY = import.meta.env.VITE_MAPS_API_KEY;

// Map container style - memoized to prevent recreation
const mapContainerStyle = {
  width: "100%",
  height: "400px",
  borderRadius: "8px",
};

// Default center
const DEFAULT_CENTER = { lat: 20.5937, lng: 78.9629 };

const AutoNavigateMapComponent = ({
  control,
  name,
  countryField,
  stateField,
  cityField,
  fullAddressField
}) => {
  const { isLoaded, loadError } = useJsApiLoader({
    googleMapsApiKey: GOOGLE_MAPS_API_KEY,
    id: "google-map-script",
  });

  const [selectedLocation, setSelectedLocation] = useState(null);
  const [map, setMap] = useState(null);
  const [isGeocoding, setIsGeocoding] = useState(false);
  const [locationInfo, setLocationInfo] = useState("");

  // Watch the dropdown values
  const country = useWatch({ control, name: countryField });
  const state = useWatch({ control, name: stateField });
  const city = useWatch({ control, name: cityField });
  const fullAddress = useWatch({ control, name: fullAddressField });

  const {
    field,
    fieldState: { error: fieldError },
  } = useController({
    name,
    control,
    defaultValue: null,
  });

  // Extract coordinates from location URL
  const parseLocationUrl = useCallback((url) => {
    if (!url || typeof url !== 'string') return null;
    
    try {
      // Handle Google Maps URLs with query parameter
      const queryMatch = url.match(/query=(-?\d+\.?\d*),(-?\d+\.?\d*)/);
      if (queryMatch) {
        return {
          lat: parseFloat(queryMatch[1]),
          lng: parseFloat(queryMatch[2])
        };
      }
      
      // Handle direct coordinate strings "lat,lng"
      const coordMatch = url.match(/^(-?\d+\.?\d*),\s*(-?\d+\.?\d*)$/);
      if (coordMatch) {
        return {
          lat: parseFloat(coordMatch[1]),
          lng: parseFloat(coordMatch[2])
        };
      }
      
      return null;
    } catch (error) {
      console.error("Error parsing location URL:", error);
      return null;
    }
  }, []);

  // Enhanced map options - memoized to prevent recreation
  const mapOptions = useMemo(() => ({
    zoomControl: true,
    streetViewControl: true,
    mapTypeControl: true,
    fullscreenControl: true,
    gestureHandling: "greedy",
    keyboardShortcuts: true,
    clickableIcons: true,
    disableDoubleClickZoom: false,
  }), []);

  // Update location and form field
  const updateLocation = useCallback(
    (location, address = "") => {
      
      setSelectedLocation(location);
      const locationUrl = `https://www.google.com/maps/search/?api=1&query=${location.lat},${location.lng}`;
      field.onChange(locationUrl);
      setLocationInfo(address);
    },
    [field]
  );

  // Reverse geocode coordinates to get address
  const reverseGeocode = useCallback(async (lat, lng) => {
    if (!window.google) return null;

    try {
      const geocoder = new window.google.maps.Geocoder();
      
      return new Promise((resolve) => {
        geocoder.geocode(
          { location: { lat, lng } },
          (results, status) => {
            if (status === "OK" && results && results[0]) {
              resolve(results[0].formatted_address);
            } else {
              resolve(`${lat.toFixed(6)}, ${lng.toFixed(6)}`);
            }
          }
        );
      });
    } catch (error) {
      console.error("Reverse geocoding error:", error);
      return `${lat.toFixed(6)}, ${lng.toFixed(6)}`;
    }
  }, []);

  // Geocode location using Google Geocoding API
  const geocodeLocation = useCallback(
    async (address) => {
      if (!window.google || !address.trim()) return;

      setIsGeocoding(true);

      try {
        const geocoder = new window.google.maps.Geocoder();

        geocoder.geocode(
          {
            address: address,
            region: country?.code || country?.value || "IN",
          },
          (results, status) => {
            setIsGeocoding(false);

            if (status === "OK" && results && results[0]) {
              const result = results[0];
              const location = {
                lat: result.geometry.location.lat(),
                lng: result.geometry.location.lng(),
              };

              updateLocation(location, result.formatted_address);

              if (map) {
                map.panTo(location);
                // Set zoom based on location type
                let zoomLevel = 10;
                if (city) zoomLevel = 13;
                else if (state) zoomLevel = 8;
                else if (country) zoomLevel = 6;

                map.setZoom(zoomLevel);
              }
            } else {
              console.warn("Geocoding failed:", status);
              setLocationInfo("Location not found");
            }
          }
        );
      } catch (error) {
        console.error("Geocoding error:", error);
        setIsGeocoding(false);
        setLocationInfo("Geocoding error occurred");
      }
    },
    [country, map, updateLocation]
  );

  // Build address string from dropdown values - memoized for performance
  const addressString = useMemo(() => {
    const parts = [];
    //  if (fullAddress?.trim()) return fullAddress;

    if (city?.label || city?.value || city) {
      parts.push(typeof city === "object" ? city.label || city.value : city);
    }
    if (state?.label || state?.value || state) {
      parts.push(
        typeof state === "object" ? state.label || state.value : state
      );
    }
    if (country?.label || country?.value || country) {
      parts.push(
        typeof country === "object" ? country.label || country.value : country
      );
    }

    return parts.join(", ");
  }, [city, state, country]);

  // Initialize map with existing location URL on first load
  useEffect(() => {
    if (!isLoaded || selectedLocation) return;

    const existingLocationUrl = field.value;
    if (existingLocationUrl) {
      const coordinates = parseLocationUrl(existingLocationUrl);
      if (coordinates) {

        setSelectedLocation(coordinates);
        
        // Get address for existing coordinates
        reverseGeocode(coordinates.lat, coordinates.lng).then(address => {
          setLocationInfo(address || `${coordinates.lat.toFixed(6)}, ${coordinates.lng.toFixed(6)}`);
        });

        // Center map on existing location
        if (map) {
          map.panTo(coordinates);
          map.setZoom(13);
        }
        return;
      }
    }
  }, [isLoaded, field.value, parseLocationUrl, reverseGeocode, selectedLocation, map]);

  // Auto-navigate when dropdown values change (only if no existing location URL)
  useEffect(() => {
    if (!isLoaded || !addressString) return;
    
    // Don't override existing location unless user explicitly changed dropdowns
    const hasExistingLocation = field.value && parseLocationUrl(field.value);
    if (hasExistingLocation && !selectedLocation) {
      return; // Let the initialization effect handle existing locations
    }

    const timeoutId = setTimeout(() => {
      geocodeLocation(addressString);
    }, 300); // Debounce geocoding calls

    return () => clearTimeout(timeoutId);
  }, [addressString, isLoaded]);

  const onLoad = useCallback((mapInstance) => {
 
    setMap(mapInstance);
    
    // If we have an existing location URL, center the map on it
    const existingLocationUrl = field.value;
    if (existingLocationUrl) {
      const coordinates = parseLocationUrl(existingLocationUrl);
      if (coordinates) {
        mapInstance.panTo(coordinates);
        mapInstance.setZoom(13);
      }
    }
  }, [field.value, parseLocationUrl]);

  const onUnmount = useCallback(() => {
   
    setMap(null);
  }, []);

  // Handle manual map clicks - Enhanced with better error handling
  const handleMapClick = useCallback(
    async (event) => {
     
      
      if (!event?.latLng) {
        console.warn("Invalid click event");
        return;
      }

      try {
        const location = {
          lat: event.latLng.lat(),
          lng: event.latLng.lng(),
        };

      

        // Get address for the clicked location
        const address = await reverseGeocode(location.lat, location.lng);
        updateLocation(location, address);
      } catch (error) {
        console.error("Error handling map click:", error);
        // Fallback to coordinates only
        const location = {
          lat: event.latLng.lat(),
          lng: event.latLng.lng(),
        };
        updateLocation(
          location,
          `${location.lat.toFixed(6)}, ${location.lng.toFixed(6)}`
        );
      }
    },
    [updateLocation, reverseGeocode]
  );

  // Loading state
  if (!isLoaded) {
    return (
      <Box
        display="flex"
        justifyContent="center"
        alignItems="center"
        height={400}
        bgcolor="grey.100"
        borderRadius={2}
      >
        <CircularProgress />
      </Box>
    );
  }

  // Error state
  if (loadError) {
    return (
      <Box
        display="flex"
        justifyContent="center"
        alignItems="center"
        height={400}
        bgcolor="grey.100"
        borderRadius={2}
        p={2}
      >
        <Typography color="error" align="center">
          Failed to load Google Maps: {loadError.message}
          <br />
          Please check your API key and internet connection.
        </Typography>
      </Box>
    );
  }

  return (
    <Box>
      {/* Status Bar */}
      {(isGeocoding || locationInfo) && (
        <Paper elevation={1} sx={{ p: 2, mb: 2, bgcolor: "grey.50" }}>
          {isGeocoding ? (
            <Box
              display="flex"
              alignItems="center"
              justifyContent="center"
              gap={1}
            >
              <CircularProgress size={16} />
              <Typography
                variant="body2"
                color="text.primary"
                sx={{ fontSize: 14, textAlign: "center" }}
              >
                Locating {addressString}...
              </Typography>
            </Box>
          ) : (
            <Typography variant="body2" color="text.primary" sx={{ fontSize: 14 }}>
              📍 {locationInfo}
            </Typography>
          )}
        </Paper>
      )}

      {/* Map */}
      <Box position="relative">
        <GoogleMap
          mapContainerStyle={mapContainerStyle}
          center={selectedLocation || DEFAULT_CENTER}
          zoom={selectedLocation ? 10 : 6}
          onClick={handleMapClick}
          onLoad={onLoad}
          onUnmount={onUnmount}
          options={mapOptions}
        >
          {selectedLocation && (
            <Marker
              position={selectedLocation}
              animation={window.google?.maps?.Animation?.DROP}
              title={locationInfo || "Selected Location"}
            />
          )}
        </GoogleMap>

        {/* Coordinates Overlay */}
        {selectedLocation && (
          <Paper
            elevation={3}
            sx={{
              position: "absolute",
              top: 10,
              right: 10,
              p: 1.5,
              bgcolor: "rgba(255, 255, 255, 0.95)",
              minWidth: 150,
              backdropFilter: "blur(4px)",
            }}
          >
            <Typography variant="caption" display="block" fontWeight="bold">
              Coordinates:
            </Typography>
            <Typography variant="caption" display="block">
              Lat: {selectedLocation.lat.toFixed(6)}
            </Typography>
            <Typography variant="caption" display="block">
              Lng: {selectedLocation.lng.toFixed(6)}
            </Typography>
          </Paper>
        )}

        {/* Click indicator for better UX */}
        {!selectedLocation && (
          <Box
            sx={{
              position: "absolute",
              bottom: 10,
              left: 10,
              bgcolor: "rgba(0, 0, 0, 0.7)",
              color: "white",
              px: 2,
              py: 1,
              borderRadius: 1,
              fontSize: 12,
            }}
          >
            Click on map to select location
          </Box>
        )}
      </Box>

      {/* Error Display */}
      {fieldError && (
        <Typography color="error" variant="body2" sx={{ mt: 1 }}>
          {fieldError.message}
        </Typography>
      )}

      {/* Instructions */}
      <Typography variant="body2" color="text.secondary" sx={{ mt: 1, fontSize: 12 }}>
        Map automatically centers when you select Country/State/City from
        dropdowns above. Click anywhere on the map to select a specific location.
        {selectedLocation && " Click again to change the selected location."}
      </Typography>

      
      {/* {process.env.NODE_ENV === 'development' && (
        <Box sx={{ mt: 1, p: 1, bgcolor: 'grey.100', fontSize: 10 }}>
          <Typography variant="caption">
            Debug: Map loaded: {isLoaded ? 'Yes' : 'No'}, 
            Selected: {selectedLocation ? 'Yes' : 'No'},
            Address: {addressString || 'None'},
            Field Value: {field.value || 'None'},
            Parsed Coords: {field.value ? JSON.stringify(parseLocationUrl(field.value)) : 'None'}
          </Typography>
        </Box>
      )} */}
    </Box>
  );
};

export default AutoNavigateMapComponent;