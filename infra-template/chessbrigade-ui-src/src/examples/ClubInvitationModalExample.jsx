import React, { useState } from 'react';
import { Box, Button, Container, Paper, Typography, Grid } from '@mui/material';
import ClubInvitationModal from '../components/club/ClubInvitationModal';

/**
 * Example component demonstrating the use of the ClubInvitationModal
 * in both club-invite and join-request modes
 */
const ClubInvitationModalExample = () => {
  // State for controlling modal visibility
  const [clubInviteModalOpen, setClubInviteModalOpen] = useState(false);
  const [joinRequestModalOpen, setJoinRequestModalOpen] = useState(false);
  
  // Example data
  const examplePlayerData = {
    name: "<PERSON>",
    cbid: "CB12345678",
    rating: 2850,
    country: "Norway"
  };
  
  const exampleClubData = {
    clubName: "Chess Masters Club",
    clubId: "CMC-001",
    location: "New York, USA"
  };
  
  return (
    <Container maxWidth="md" sx={{ py: 4 }}>
      <Paper elevation={3} sx={{ p: 4, borderRadius: 2 }}>
        <Typography variant="h4" gutterBottom>
          Club Invitation Modal Examples
        </Typography>
        
        <Typography variant="body1" paragraph>
          This example demonstrates the ClubInvitationModal component in both modes:
          club inviting a player and player requesting to join a club.
        </Typography>
        
        <Grid container spacing={4} sx={{ mt: 2 }}>
          {/* Club Invite Example */}
          <Grid item xs={12} md={6}>
            <Paper elevation={2} sx={{ p: 3, height: '100%' }}>
              <Typography variant="h6" gutterBottom>
                Club Inviting a Player
              </Typography>
              
              <Typography variant="body2" paragraph>
                In this mode, a club admin can send an invitation to a player to join their club.
              </Typography>
              
              <Box sx={{ mt: 3 }}>
                <Button 
                  variant="contained" 
                  color="primary"
                  onClick={() => setClubInviteModalOpen(true)}
                >
                  Invite Player
                </Button>
              </Box>
            </Paper>
          </Grid>
          
          {/* Join Request Example */}
          <Grid item xs={12} md={6}>
            <Paper elevation={2} sx={{ p: 3, height: '100%' }}>
              <Typography variant="h6" gutterBottom>
                Player Requesting to Join Club
              </Typography>
              
              <Typography variant="body2" paragraph>
                In this mode, a player can send a request to join a specific club.
              </Typography>
              
              <Box sx={{ mt: 3 }}>
                <Button 
                  variant="contained" 
                  color="secondary"
                  onClick={() => setJoinRequestModalOpen(true)}
                >
                  Request to Join
                </Button>
              </Box>
            </Paper>
          </Grid>
        </Grid>
      </Paper>
      
      {/* Club Invite Modal */}
      <ClubInvitationModal
        open={clubInviteModalOpen}
        onClose={() => setClubInviteModalOpen(false)}
        playerData={examplePlayerData}
        mode="club-invite"
      />
      
      {/* Join Request Modal */}
      <ClubInvitationModal
        open={joinRequestModalOpen}
        onClose={() => setJoinRequestModalOpen(false)}
        clubData={exampleClubData}
        mode="join-request"
      />
    </Container>
  );
};

export default ClubInvitationModalExample;
