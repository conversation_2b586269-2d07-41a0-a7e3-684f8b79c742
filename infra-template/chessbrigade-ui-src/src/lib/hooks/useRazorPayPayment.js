import { useState, useCallback, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { initiatePayment as razorInitiatePayment } from '../services/razorpayService';
import { useRazorpay } from './useRazorpay';

export const useRazorpayPayment = (toast) => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const { isLoaded, error: sdkError } = useRazorpay();
  const navigate = useNavigate();
  
  // Prevent multiple simultaneous payments using ref
  const isProcessingRef = useRef(false);

  const initiatePayment = useCallback(async (tournamentId, ageCategory, genderCategory,referral) => {
    // Early validation checks
    if (!isLoaded) {
      const loadError = new Error('Payment system not ready');
      setError(loadError);
      return { success: false, error: loadError };
    }

    if (isProcessingRef.current) {
      const processingError = new Error('Payment already in progress');
      setError(processingError);
      return { success: false, error: processingError };
    }

    // Input validation
    if (!tournamentId || !ageCategory || !genderCategory) {
      const validationError = new Error('Missing required payment parameters');
      setError(validationError);
      return { success: false, error: validationError };
    }

    setError(null);
    isProcessingRef.current = true;

    try {
      const result = await razorInitiatePayment({
        tournamentId,
        ageCategory,
        genderCategory,
        referral,
        onStart: () => setIsLoading(true),
        onSuccess: (result) => {
          // URL encoding for safety and use replace to prevent back button issues
          const params = new URLSearchParams({
            txnid: result.transactionId,
            amount: result.amount,
            tournament: result.tournamentTitle
          });
          navigate(`/payment-success?${params.toString()}`, { replace: true });
        },
        onError: (err) => {
          setError(err);
          // Encode error message to prevent URL injection
          const errorMsg = encodeURIComponent(err.message || 'Payment failed');
          navigate(`/payment-failure?error=${errorMsg}`, { replace: true });
        },
        onFinally: () => {
          setIsLoading(false);
          isProcessingRef.current = false;
        },
      }, toast);

      return { success: true, data: result };
    } catch (err) {
      setError(err);
      isProcessingRef.current = false;
      setIsLoading(false);
      return { success: false, error: err };
    }
  }, [isLoaded, navigate]);

  const resetError = useCallback(() => {
    setError(null);
  }, []);

  // Additional utility to reset all state
  const resetState = useCallback(() => {
    setError(null);
    setIsLoading(false);
    isProcessingRef.current = false;
  }, []);

  return {
    initiatePayment,
    isLoading,
    error: error || sdkError,
    resetError,
    resetState,
    isReady: isLoaded && !sdkError,
    isProcessing: isProcessingRef.current,
  };
};