import { useEffect, useState, useCallback } from 'react';

export const useRazorpay = () => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [error, setError] = useState(null);
  const [isLoading, setIsLoading] = useState(false);

  // Memoized script loader to prevent unnecessary re-renders
  const loadRazorpayScript = useCallback(() => {
    // Check if already loaded
    if (window.Razorpay) {
      setIsLoaded(true);
      return Promise.resolve();
    }

    // Check if script is already in DOM
    const existingScript = document.querySelector('script[src="https://checkout.razorpay.com/v1/checkout.js"]');
    if (existingScript) {
      return new Promise((resolve, reject) => {
        if (window.Razorpay) {
          setIsLoaded(true);
          resolve();
        } else {
          existingScript.onload = () => {
            setIsLoaded(true);
            resolve();
          };
          existingScript.onerror = () => {
            setError('Failed to load Razorpay SDK');
            reject(new Error('Script load failed'));
          };
        }
      });
    }

    // Create and load new script
    return new Promise((resolve, reject) => {
      setIsLoading(true);
      setError(null);

      const script = document.createElement('script');
      script.src = 'https://checkout.razorpay.com/v1/checkout.js';
      script.async = true;
      script.defer = true; // Better for non-critical scripts
      
      // Add integrity check for security (optional)
      // script.integrity = "sha384-..."; // Add actual hash if available
      // script.crossOrigin = "anonymous";

      const handleLoad = () => {
        setIsLoaded(true);
        setIsLoading(false);
        resolve();
      };

      const handleError = (err) => {
        console.error('Razorpay script loading failed:', err);
        setError('Failed to load Razorpay SDK. Please check your network connection or disable ad blockers.');
        setIsLoading(false);
        reject(err);
      };

      script.onload = handleLoad;
      script.onerror = handleError;

      // Fallback timeout for better UX
      const timeoutId = setTimeout(() => {
        if (!window.Razorpay) {
          handleError(new Error('Script load timeout'));
        }
      }, 10000); // 10 second timeout

      script.onload = () => {
        clearTimeout(timeoutId);
        handleLoad();
      };

      document.head.appendChild(script); // Use head instead of body for better practice
      
      // Cleanup function
      return () => {
        clearTimeout(timeoutId);
        if (document.head.contains(script)) {
          document.head.removeChild(script);
        }
      };
    });
  }, []);

  useEffect(() => {
    loadRazorpayScript().catch(console.error);
  }, [loadRazorpayScript]);

  // Retry function for failed loads
  const retry = useCallback(() => {
    setError(null);
    setIsLoaded(false);
    loadRazorpayScript().catch(console.error);
  }, [loadRazorpayScript]);

  return { 
    isLoaded, 
    error, 
    isLoading,
    retry 
  };
};