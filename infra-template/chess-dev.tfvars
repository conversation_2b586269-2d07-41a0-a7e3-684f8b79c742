# General
aws_region = "ap-south-1"
app_name   = "chessbrigade"
environment = "Production"

# VPC Configuration
vpc_cidr             = "10.0.0.0/16"
public_subnet_cidrs  = ["10.0.0.0/24", "10.0.1.0/24"]
private_subnet_cidrs = ["10.0.10.0/24", "10.0.11.0/24"]

# Database Configuration
db_name           = "chessbrigade-live"
db_username       = "chessbrigade_admin"
db_password       = "ChssBriga8attwentyFive"
db_instance_class = "db.t3.micro"
db_allocated_storage = 20
db_engine_version = "16.8"

# ECS Configuration
container_image = "public.ecr.aws/bitnami/node:18/app"
container_port  = 3000
ecs_task_cpu    = "256"
ecs_task_memory = "512"
desired_count   = 1
health_check_path = "/health"

# Domain Configuration
domain_name     = "chess-ui.mathiarasan.com"
domain_aliases  = ["www.chess-ui.mathiarasan.com"]
api_domain_name = "chess-api.mathiarasan.com"
bucket_name     = "chess-brigade"
