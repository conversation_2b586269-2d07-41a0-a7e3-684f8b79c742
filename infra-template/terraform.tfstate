{"version": 4, "terraform_version": "1.11.4", "serial": 76, "lineage": "7cc58391-6877-6b0e-2b5f-eb52fc87854b", "outputs": {"api_url": {"value": "https://chessbrigade-alb-*********.ap-south-1.elb.amazonaws.com", "type": "string"}, "cloudfront_domain_name": {"value": "d24rjs8x3nbmlh.cloudfront.net", "type": "string"}, "rds_endpoint": {"value": "chessbrigade-db.c3qqmiooo2ii.ap-south-1.rds.amazonaws.com:5432", "type": "string"}, "website_bucket_name": {"value": "chess-brigade", "type": "string"}, "website_endpoint": {"value": "chess-brigade.s3-website.ap-south-1.amazonaws.com", "type": "string"}}, "resources": [{"mode": "managed", "type": "aws_acm_certificate", "name": "api_cert", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:acm:ap-south-1:************:certificate/8e20c0e9-589a-4159-8f45-b86bd2755492", "certificate_authority_arn": "", "certificate_body": null, "certificate_chain": null, "domain_name": "chess-api.mathiarasan.com", "domain_validation_options": [{"domain_name": "chess-api.mathiarasan.com", "resource_record_name": "_83b860027603ca8bce8606c34d55a86a.chess-api.mathiarasan.com.", "resource_record_type": "CNAME", "resource_record_value": "_108f53a48f7ec451ac6acbc6c3ac0b21.xlfgrmvvlj.acm-validations.aws."}], "early_renewal_duration": "", "id": "arn:aws:acm:ap-south-1:************:certificate/8e20c0e9-589a-4159-8f45-b86bd2755492", "key_algorithm": "RSA_2048", "not_after": "2026-05-22T23:59:59Z", "not_before": "2025-04-23T00:00:00Z", "options": [{"certificate_transparency_logging_preference": "ENABLED"}], "pending_renewal": false, "private_key": null, "renewal_eligibility": "ELIGIBLE", "renewal_summary": [], "status": "ISSUED", "subject_alternative_names": ["chess-api.mathiarasan.com"], "tags": {"Environment": "Production", "Name": "chessbrigade-api-cert"}, "tags_all": {"Environment": "Production", "Name": "chessbrigade-api-cert"}, "type": "AMAZON_ISSUED", "validation_emails": [], "validation_method": "DNS", "validation_option": []}, "sensitive_attributes": [[{"type": "get_attr", "value": "private_key"}]], "private": "bnVsbA==", "create_before_destroy": true}]}, {"mode": "managed", "type": "aws_acm_certificate", "name": "cert", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"].us-east-1", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:acm:us-east-1:************:certificate/7203902a-b2a0-4d3d-97a8-6dfb47226efb", "certificate_authority_arn": "", "certificate_body": null, "certificate_chain": null, "domain_name": "chess-ui.mathiarasan.com", "domain_validation_options": [{"domain_name": "chess-ui.mathiarasan.com", "resource_record_name": "_488f2350bcd5d0ceefede05f2f5593c9.chess-ui.mathiarasan.com.", "resource_record_type": "CNAME", "resource_record_value": "_8c0377d4399cff8287c3c228b2c46cfa.xlfgrmvvlj.acm-validations.aws."}, {"domain_name": "www.chess-ui.mathiarasan.com", "resource_record_name": "_cf2b6673cefd221820a592a95356ab88.www.chess-ui.mathiarasan.com.", "resource_record_type": "CNAME", "resource_record_value": "_5a6dea7af72aa89c7cb4215d19cb0434.xlfgrmvvlj.acm-validations.aws."}], "early_renewal_duration": "", "id": "arn:aws:acm:us-east-1:************:certificate/7203902a-b2a0-4d3d-97a8-6dfb47226efb", "key_algorithm": "RSA_2048", "not_after": "2026-05-22T23:59:59Z", "not_before": "2025-04-23T00:00:00Z", "options": [{"certificate_transparency_logging_preference": "ENABLED"}], "pending_renewal": false, "private_key": null, "renewal_eligibility": "ELIGIBLE", "renewal_summary": [], "status": "ISSUED", "subject_alternative_names": ["chess-ui.mathiarasan.com", "www.chess-ui.mathiarasan.com"], "tags": {}, "tags_all": {}, "type": "AMAZON_ISSUED", "validation_emails": [], "validation_method": "DNS", "validation_option": []}, "sensitive_attributes": [[{"type": "get_attr", "value": "private_key"}]], "private": "bnVsbA==", "create_before_destroy": true}]}, {"mode": "managed", "type": "aws_cloudfront_distribution", "name": "s3_distribution", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 1, "attributes": {"aliases": ["chess-ui.mathiarasan.com", "www.chess-ui.mathiarasan.com"], "arn": "arn:aws:cloudfront::************:distribution/E2E0G9X3I7SJ86", "caller_reference": "terraform-20250423081916892200000002", "comment": null, "continuous_deployment_policy_id": "", "custom_error_response": [{"error_caching_min_ttl": 0, "error_code": 403, "response_code": 200, "response_page_path": "/index.html"}, {"error_caching_min_ttl": 0, "error_code": 404, "response_code": 200, "response_page_path": "/index.html"}], "default_cache_behavior": [{"allowed_methods": ["GET", "HEAD", "OPTIONS"], "cache_policy_id": "", "cached_methods": ["GET", "HEAD"], "compress": false, "default_ttl": 3600, "field_level_encryption_id": "", "forwarded_values": [{"cookies": [{"forward": "none", "whitelisted_names": []}], "headers": [], "query_string": false, "query_string_cache_keys": []}], "function_association": [], "grpc_config": [{"enabled": false}], "lambda_function_association": [], "max_ttl": 86400, "min_ttl": 0, "origin_request_policy_id": "", "realtime_log_config_arn": "", "response_headers_policy_id": "", "smooth_streaming": false, "target_origin_id": "S3-chess-brigade", "trusted_key_groups": [], "trusted_signers": [], "viewer_protocol_policy": "redirect-to-https"}], "default_root_object": "index.html", "domain_name": "d24rjs8x3nbmlh.cloudfront.net", "enabled": true, "etag": "EA12JA0CHNA4G", "hosted_zone_id": "Z2FDTNDATAQYW2", "http_version": "http2", "id": "E2E0G9X3I7SJ86", "in_progress_validation_batches": 0, "is_ipv6_enabled": true, "last_modified_time": "2025-04-23 08:19:25.104 +0000 UTC", "logging_config": [], "ordered_cache_behavior": [], "origin": [{"connection_attempts": 3, "connection_timeout": 10, "custom_header": [], "custom_origin_config": [], "domain_name": "chess-brigade.s3.ap-south-1.amazonaws.com", "origin_access_control_id": "", "origin_id": "S3-chess-brigade", "origin_path": "", "origin_shield": [], "s3_origin_config": [{"origin_access_identity": "origin-access-identity/cloudfront/EXR5UCKSCHUK3"}], "vpc_origin_config": []}], "origin_group": [], "price_class": "PriceClass_200", "restrictions": [{"geo_restriction": [{"locations": [], "restriction_type": "none"}]}], "retain_on_delete": false, "staging": false, "status": "Deployed", "tags": {"Environment": "Production", "Name": "chessbrigade-distribution"}, "tags_all": {"Environment": "Production", "Name": "chessbrigade-distribution"}, "trusted_key_groups": [{"enabled": false, "items": []}], "trusted_signers": [{"enabled": false, "items": []}], "viewer_certificate": [{"acm_certificate_arn": "arn:aws:acm:us-east-1:************:certificate/7203902a-b2a0-4d3d-97a8-6dfb47226efb", "cloudfront_default_certificate": false, "iam_certificate_id": "", "minimum_protocol_version": "TLSv1.2_2021", "ssl_support_method": "sni-only"}], "wait_for_deployment": true, "web_acl_id": ""}, "sensitive_attributes": [], "private": "eyJzY2hlbWFfdmVyc2lvbiI6IjEifQ==", "dependencies": ["aws_acm_certificate.cert", "aws_cloudfront_origin_access_identity.oai", "aws_s3_bucket.website_bucket"]}]}, {"mode": "managed", "type": "aws_cloudfront_origin_access_identity", "name": "oai", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:cloudfront::************:origin-access-identity/EXR5UCKSCHUK3", "caller_reference": "terraform-20250423054315836100000001", "cloudfront_access_identity_path": "origin-access-identity/cloudfront/EXR5UCKSCHUK3", "comment": "OAI for chess-brigade S3 bucket", "etag": "E15CNAIBGT6A4E", "iam_arn": "arn:aws:iam::cloudfront:user/CloudFront Origin Access Identity EXR5UCKSCHUK3", "id": "EXR5UCKSCHUK3", "s3_canonical_user_id": "2ff74f40292652c1f86819562bebca62f1af04b12d0d9fe6d65d8d77e37afbe8d5c9a588deab0ff56654cfedef612218"}, "sensitive_attributes": [], "private": "bnVsbA=="}]}, {"mode": "managed", "type": "aws_cloudwatch_log_group", "name": "app_logs", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:logs:ap-south-1:************:log-group:/ecs/chessbrigade-api", "id": "/ecs/chessbrigade-api", "kms_key_id": "", "log_group_class": "STANDARD", "name": "/ecs/chessbrigade-api", "name_prefix": "", "retention_in_days": 30, "skip_destroy": false, "tags": {"Environment": "Production", "Name": "chessbrigade-log-group"}, "tags_all": {"Environment": "Production", "Name": "chessbrigade-log-group"}}, "sensitive_attributes": [], "private": "bnVsbA=="}]}, {"mode": "managed", "type": "aws_db_instance", "name": "main_db", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 2, "attributes": {"address": "chessbrigade-db.c3qqmiooo2ii.ap-south-1.rds.amazonaws.com", "allocated_storage": 20, "allow_major_version_upgrade": null, "apply_immediately": false, "arn": "arn:aws:rds:ap-south-1:************:db:chessbrigade-db", "auto_minor_version_upgrade": true, "availability_zone": "ap-south-1b", "backup_retention_period": 7, "backup_target": "region", "backup_window": "03:00-04:00", "blue_green_update": [], "ca_cert_identifier": "rds-ca-rsa2048-g1", "character_set_name": "", "copy_tags_to_snapshot": false, "custom_iam_instance_profile": "", "customer_owned_ip_enabled": false, "database_insights_mode": "standard", "db_name": "chessbrigade_db", "db_subnet_group_name": "chessbrigade-db-subnet-group", "dedicated_log_volume": false, "delete_automated_backups": true, "deletion_protection": false, "domain": "", "domain_auth_secret_arn": "", "domain_dns_ips": [], "domain_fqdn": "", "domain_iam_role_name": "", "domain_ou": "", "enabled_cloudwatch_logs_exports": [], "endpoint": "chessbrigade-db.c3qqmiooo2ii.ap-south-1.rds.amazonaws.com:5432", "engine": "postgres", "engine_lifecycle_support": "open-source-rds-extended-support", "engine_version": "16.8", "engine_version_actual": "16.8", "final_snapshot_identifier": null, "hosted_zone_id": "Z2VFMSZA74J7XZ", "iam_database_authentication_enabled": false, "id": "db-RWZU5ERCTXQ4CY7GOTVM3X6X2Y", "identifier": "chessbrigade-db", "identifier_prefix": "", "instance_class": "db.t3.micro", "iops": 0, "kms_key_id": "", "latest_restorable_time": "2025-04-25T09:14:37Z", "license_model": "postgresql-license", "listener_endpoint": [], "maintenance_window": "mon:04:00-mon:05:00", "manage_master_user_password": null, "master_user_secret": [], "master_user_secret_kms_key_id": null, "max_allocated_storage": 0, "monitoring_interval": 0, "monitoring_role_arn": "", "multi_az": false, "nchar_character_set_name": "", "network_type": "IPV4", "option_group_name": "default:postgres-16", "parameter_group_name": "chessbrigade-postgres-params", "password": "ChssBriga8attwentyFive", "password_wo": null, "password_wo_version": null, "performance_insights_enabled": false, "performance_insights_kms_key_id": "", "performance_insights_retention_period": 0, "port": 5432, "publicly_accessible": true, "replica_mode": "", "replicas": [], "replicate_source_db": "", "resource_id": "db-RWZU5ERCTXQ4CY7GOTVM3X6X2Y", "restore_to_point_in_time": [], "s3_import": [], "skip_final_snapshot": true, "snapshot_identifier": null, "status": "available", "storage_encrypted": false, "storage_throughput": 0, "storage_type": "gp2", "tags": {"Environment": "Production", "Name": "chessbrigade-db"}, "tags_all": {"Environment": "Production", "Name": "chessbrigade-db"}, "timeouts": null, "timezone": "", "upgrade_storage_config": null, "username": "chessbrigade_admin", "vpc_security_group_ids": ["sg-07da05e2c74520dad"]}, "sensitive_attributes": [[{"type": "get_attr", "value": "password_wo"}], [{"type": "get_attr", "value": "password"}]], "private": "********************************************************************************************************************************************************************************", "dependencies": ["aws_db_parameter_group.postgres", "aws_db_subnet_group.db_subnet_group", "aws_security_group.fargate_sg", "aws_security_group.rds_sg", "aws_subnet.public", "aws_vpc.main_vpc"]}]}, {"mode": "managed", "type": "aws_db_parameter_group", "name": "postgres", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:rds:ap-south-1:************:pg:chessbrigade-postgres-params", "description": "Managed by Terraform", "family": "postgres16", "id": "chessbrigade-postgres-params", "name": "chessbrigade-postgres-params", "name_prefix": "", "parameter": [{"apply_method": "immediate", "name": "log_connections", "value": "1"}], "skip_destroy": false, "tags": {}, "tags_all": {}}, "sensitive_attributes": [], "private": "bnVsbA=="}]}, {"mode": "managed", "type": "aws_db_subnet_group", "name": "db_subnet_group", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:rds:ap-south-1:************:subgrp:chessbrigade-db-subnet-group", "description": "Managed by Terraform", "id": "chessbrigade-db-subnet-group", "name": "chessbrigade-db-subnet-group", "name_prefix": "", "subnet_ids": ["subnet-02acc00e3583d8b4d", "subnet-0c49bbd2df2685f3d"], "supported_network_types": ["IPV4"], "tags": {"Environment": "Production", "Name": "chessbrigade-db-subnet-group"}, "tags_all": {"Environment": "Production", "Name": "chessbrigade-db-subnet-group"}, "vpc_id": "vpc-061b02e8cc1e3e890"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_subnet.public", "aws_vpc.main_vpc"]}]}, {"mode": "managed", "type": "aws_ecs_cluster", "name": "main_cluster", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:ecs:ap-south-1:************:cluster/chessbrigade-cluster", "configuration": [], "id": "arn:aws:ecs:ap-south-1:************:cluster/chessbrigade-cluster", "name": "chessbrigade-cluster", "service_connect_defaults": [], "setting": [{"name": "containerInsights", "value": "enabled"}], "tags": {"Environment": "Production", "Name": "chessbrigade-ecs-cluster"}, "tags_all": {"Environment": "Production", "Name": "chessbrigade-ecs-cluster"}}, "sensitive_attributes": [], "private": "bnVsbA=="}]}, {"mode": "managed", "type": "aws_ecs_service", "name": "app_service", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 1, "attributes": {"alarms": [], "availability_zone_rebalancing": "DISABLED", "capacity_provider_strategy": [], "cluster": "arn:aws:ecs:ap-south-1:************:cluster/chessbrigade-cluster", "deployment_circuit_breaker": [{"enable": false, "rollback": false}], "deployment_controller": [{"type": "ECS"}], "deployment_maximum_percent": 200, "deployment_minimum_healthy_percent": 100, "desired_count": 1, "enable_ecs_managed_tags": false, "enable_execute_command": false, "force_delete": null, "force_new_deployment": null, "health_check_grace_period_seconds": 0, "iam_role": "/aws-service-role/ecs.amazonaws.com/AWSServiceRoleForECS", "id": "arn:aws:ecs:ap-south-1:************:service/chessbrigade-cluster/chessbrigade-api-service", "launch_type": "FARGATE", "load_balancer": [{"container_name": "chessbrigade-api", "container_port": 3000, "elb_name": "", "target_group_arn": "arn:aws:elasticloadbalancing:ap-south-1:************:targetgroup/chessbrigade-target-group/b71213fffa731c7c"}], "name": "chessbrigade-api-service", "network_configuration": [{"assign_public_ip": true, "security_groups": ["sg-0efcb98cd5994e659"], "subnets": ["subnet-02acc00e3583d8b4d", "subnet-0c49bbd2df2685f3d"]}], "ordered_placement_strategy": [], "placement_constraints": [], "platform_version": "LATEST", "propagate_tags": "NONE", "scheduling_strategy": "REPLICA", "service_connect_configuration": [], "service_registries": [], "tags": {"Environment": "Production", "Name": "chessbrigade-api-service"}, "tags_all": {"Environment": "Production", "Name": "chessbrigade-api-service"}, "task_definition": "arn:aws:ecs:ap-south-1:************:task-definition/chessbrigade-api:1", "timeouts": null, "triggers": {}, "volume_configuration": [], "vpc_lattice_configurations": [], "wait_for_steady_state": false}, "sensitive_attributes": [], "private": "********************************************************************************************************************************************************************************", "dependencies": ["aws_cloudwatch_log_group.app_logs", "aws_db_instance.main_db", "aws_db_parameter_group.postgres", "aws_db_subnet_group.db_subnet_group", "aws_ecs_cluster.main_cluster", "aws_ecs_task_definition.app_task", "aws_iam_role.ecs_task_execution_role", "aws_lb.app_alb", "aws_lb_listener.http", "aws_lb_target_group.app_tg", "aws_security_group.fargate_sg", "aws_security_group.rds_sg", "aws_subnet.public", "aws_vpc.main_vpc"]}]}, {"mode": "managed", "type": "aws_ecs_task_definition", "name": "app_task", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 1, "attributes": {"arn": "arn:aws:ecs:ap-south-1:************:task-definition/chessbrigade-api:1", "arn_without_revision": "arn:aws:ecs:ap-south-1:************:task-definition/chessbrigade-api", "container_definitions": "[{\"environment\":[{\"name\":\"DB_HOST\",\"value\":\"chessbrigade-db.c3qqmiooo2ii.ap-south-1.rds.amazonaws.com\"},{\"name\":\"DB_NAME\",\"value\":\"chessbrigade_db\"},{\"name\":\"DB_PASSWORD\",\"value\":\"ChssBriga8attwentyFive\"},{\"name\":\"DB_USERNAME\",\"value\":\"chessbrigade_admin\"},{\"name\":\"PORT\",\"value\":\"3000\"}],\"essential\":true,\"image\":\"public.ecr.aws/bitnami/node:18/app\",\"logConfiguration\":{\"logDriver\":\"awslogs\",\"options\":{\"awslogs-group\":\"/ecs/chessbrigade-api\",\"awslogs-region\":\"ap-south-1\",\"awslogs-stream-prefix\":\"chessbrigade-api\"}},\"mountPoints\":[],\"name\":\"chessbrigade-api\",\"portMappings\":[{\"containerPort\":3000,\"hostPort\":3000,\"protocol\":\"tcp\"}],\"systemControls\":[],\"volumesFrom\":[]}]", "cpu": "256", "enable_fault_injection": false, "ephemeral_storage": [], "execution_role_arn": "arn:aws:iam::************:role/chessbrigade-Production-ecs-task-execution-role", "family": "chessbrigade-api", "id": "chessbrigade-api", "inference_accelerator": [], "ipc_mode": "", "memory": "512", "network_mode": "awsvpc", "pid_mode": "", "placement_constraints": [], "proxy_configuration": [], "requires_compatibilities": ["FARGATE"], "revision": 1, "runtime_platform": [], "skip_destroy": false, "tags": {"Environment": "Production", "Name": "chessbrigade-task-definition"}, "tags_all": {"Environment": "Production", "Name": "chessbrigade-task-definition"}, "task_role_arn": "arn:aws:iam::************:role/chessbrigade-Production-ecs-task-execution-role", "track_latest": false, "volume": []}, "sensitive_attributes": [[{"type": "get_attr", "value": "container_definitions"}]], "private": "eyJzY2hlbWFfdmVyc2lvbiI6IjEifQ==", "dependencies": ["aws_cloudwatch_log_group.app_logs", "aws_db_instance.main_db", "aws_db_parameter_group.postgres", "aws_db_subnet_group.db_subnet_group", "aws_iam_role.ecs_task_execution_role", "aws_security_group.fargate_sg", "aws_security_group.rds_sg", "aws_subnet.public", "aws_vpc.main_vpc"]}]}, {"mode": "managed", "type": "aws_iam_role", "name": "ecs_task_execution_role", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:iam::************:role/chessbrigade-Production-ecs-task-execution-role", "assume_role_policy": "{\"Statement\":[{\"Action\":\"sts:AssumeRole\",\"Effect\":\"Allow\",\"Principal\":{\"Service\":\"ecs-tasks.amazonaws.com\"}}],\"Version\":\"2012-10-17\"}", "create_date": "2025-04-23T05:43:16Z", "description": "", "force_detach_policies": false, "id": "chessbrigade-Production-ecs-task-execution-role", "inline_policy": [], "managed_policy_arns": ["arn:aws:iam::aws:policy/service-role/AmazonECSTaskExecutionRolePolicy"], "max_session_duration": 3600, "name": "chessbrigade-Production-ecs-task-execution-role", "name_prefix": "", "path": "/", "permissions_boundary": "", "tags": {"Environment": "Production", "Name": "chessbrigade-ecs-task-execution-role"}, "tags_all": {"Environment": "Production", "Name": "chessbrigade-ecs-task-execution-role"}, "unique_id": "AROAXVWZH6RRJSRRQGQXI"}, "sensitive_attributes": [], "private": "bnVsbA=="}]}, {"mode": "managed", "type": "aws_iam_role_policy_attachment", "name": "ecs_task_execution_role_policy", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "chessbrigade-Production-ecs-task-execution-role-20250423054317991300000003", "policy_arn": "arn:aws:iam::aws:policy/service-role/AmazonECSTaskExecutionRolePolicy", "role": "chessbrigade-Production-ecs-task-execution-role"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_iam_role.ecs_task_execution_role"]}]}, {"mode": "managed", "type": "aws_internet_gateway", "name": "main_igw", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:ec2:ap-south-1:************:internet-gateway/igw-091a42cd7d38a2177", "id": "igw-091a42cd7d38a2177", "owner_id": "************", "tags": {"Environment": "Production", "Name": "chessbrigade-igw"}, "tags_all": {"Environment": "Production", "Name": "chessbrigade-igw"}, "timeouts": null, "vpc_id": "vpc-061b02e8cc1e3e890"}, "sensitive_attributes": [], "private": "****************************************************************************************************************************************************", "dependencies": ["aws_vpc.main_vpc"]}]}, {"mode": "managed", "type": "aws_lb", "name": "app_alb", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"access_logs": [{"bucket": "", "enabled": false, "prefix": ""}], "arn": "arn:aws:elasticloadbalancing:ap-south-1:************:loadbalancer/app/chessbrigade-alb/5d3ad2191fff7f37", "arn_suffix": "app/chessbrigade-alb/5d3ad2191fff7f37", "client_keep_alive": 3600, "connection_logs": [{"bucket": "", "enabled": false, "prefix": ""}], "customer_owned_ipv4_pool": "", "desync_mitigation_mode": "defensive", "dns_name": "chessbrigade-alb-*********.ap-south-1.elb.amazonaws.com", "dns_record_client_routing_policy": null, "drop_invalid_header_fields": false, "enable_cross_zone_load_balancing": true, "enable_deletion_protection": false, "enable_http2": true, "enable_tls_version_and_cipher_suite_headers": false, "enable_waf_fail_open": false, "enable_xff_client_port": false, "enable_zonal_shift": false, "enforce_security_group_inbound_rules_on_private_link_traffic": "", "id": "arn:aws:elasticloadbalancing:ap-south-1:************:loadbalancer/app/chessbrigade-alb/5d3ad2191fff7f37", "idle_timeout": 60, "internal": false, "ip_address_type": "ipv4", "ipam_pools": [], "load_balancer_type": "application", "name": "chessbrigade-alb", "name_prefix": "", "preserve_host_header": false, "security_groups": ["sg-0efcb98cd5994e659"], "subnet_mapping": [{"allocation_id": "", "ipv6_address": "", "outpost_id": "", "private_ipv4_address": "", "subnet_id": "subnet-02acc00e3583d8b4d"}, {"allocation_id": "", "ipv6_address": "", "outpost_id": "", "private_ipv4_address": "", "subnet_id": "subnet-0c49bbd2df2685f3d"}], "subnets": ["subnet-02acc00e3583d8b4d", "subnet-0c49bbd2df2685f3d"], "tags": {"Environment": "Production", "Name": "chessbrigade-alb"}, "tags_all": {"Environment": "Production", "Name": "chessbrigade-alb"}, "timeouts": null, "vpc_id": "vpc-061b02e8cc1e3e890", "xff_header_processing_mode": "append", "zone_id": "ZP97RAFLXTNZK"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMDAsImRlbGV0ZSI6NjAwMDAwMDAwMDAwLCJ1cGRhdGUiOjYwMDAwMDAwMDAwMH19", "dependencies": ["aws_security_group.fargate_sg", "aws_subnet.public", "aws_vpc.main_vpc"]}]}, {"mode": "managed", "type": "aws_lb_listener", "name": "http", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"alpn_policy": null, "arn": "arn:aws:elasticloadbalancing:ap-south-1:************:listener/app/chessbrigade-alb/5d3ad2191fff7f37/e582a13713ca1333", "certificate_arn": null, "default_action": [{"authenticate_cognito": [], "authenticate_oidc": [], "fixed_response": [], "forward": [], "order": 1, "redirect": [{"host": "#{host}", "path": "/#{path}", "port": "443", "protocol": "HTTPS", "query": "#{query}", "status_code": "HTTP_301"}], "target_group_arn": "", "type": "redirect"}], "id": "arn:aws:elasticloadbalancing:ap-south-1:************:listener/app/chessbrigade-alb/5d3ad2191fff7f37/e582a13713ca1333", "load_balancer_arn": "arn:aws:elasticloadbalancing:ap-south-1:************:loadbalancer/app/chessbrigade-alb/5d3ad2191fff7f37", "mutual_authentication": [], "port": 80, "protocol": "HTTP", "routing_http_request_x_amzn_mtls_clientcert_header_name": null, "routing_http_request_x_amzn_mtls_clientcert_issuer_header_name": null, "routing_http_request_x_amzn_mtls_clientcert_leaf_header_name": null, "routing_http_request_x_amzn_mtls_clientcert_serial_number_header_name": null, "routing_http_request_x_amzn_mtls_clientcert_subject_header_name": null, "routing_http_request_x_amzn_mtls_clientcert_validity_header_name": null, "routing_http_request_x_amzn_tls_cipher_suite_header_name": null, "routing_http_request_x_amzn_tls_version_header_name": null, "routing_http_response_access_control_allow_credentials_header_value": "", "routing_http_response_access_control_allow_headers_header_value": "", "routing_http_response_access_control_allow_methods_header_value": "", "routing_http_response_access_control_allow_origin_header_value": "", "routing_http_response_access_control_expose_headers_header_value": "", "routing_http_response_access_control_max_age_header_value": "", "routing_http_response_content_security_policy_header_value": "", "routing_http_response_server_enabled": false, "routing_http_response_strict_transport_security_header_value": "", "routing_http_response_x_content_type_options_header_value": "", "routing_http_response_x_frame_options_header_value": "", "ssl_policy": "", "tags": {}, "tags_all": {}, "tcp_idle_timeout_seconds": null, "timeouts": null}, "sensitive_attributes": [], "private": "********************************************************************************************************************", "dependencies": ["aws_lb.app_alb", "aws_security_group.fargate_sg", "aws_subnet.public", "aws_vpc.main_vpc"]}]}, {"mode": "managed", "type": "aws_lb_listener", "name": "https", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"alpn_policy": null, "arn": "arn:aws:elasticloadbalancing:ap-south-1:************:listener/app/chessbrigade-alb/5d3ad2191fff7f37/ed99f181e2216045", "certificate_arn": "arn:aws:acm:ap-south-1:************:certificate/8e20c0e9-589a-4159-8f45-b86bd2755492", "default_action": [{"authenticate_cognito": [], "authenticate_oidc": [], "fixed_response": [], "forward": [], "order": 1, "redirect": [], "target_group_arn": "arn:aws:elasticloadbalancing:ap-south-1:************:targetgroup/chessbrigade-target-group/b71213fffa731c7c", "type": "forward"}], "id": "arn:aws:elasticloadbalancing:ap-south-1:************:listener/app/chessbrigade-alb/5d3ad2191fff7f37/ed99f181e2216045", "load_balancer_arn": "arn:aws:elasticloadbalancing:ap-south-1:************:loadbalancer/app/chessbrigade-alb/5d3ad2191fff7f37", "mutual_authentication": [{"advertise_trust_store_ca_names": "", "ignore_client_certificate_expiry": false, "mode": "off", "trust_store_arn": ""}], "port": 443, "protocol": "HTTPS", "routing_http_request_x_amzn_mtls_clientcert_header_name": "", "routing_http_request_x_amzn_mtls_clientcert_issuer_header_name": "", "routing_http_request_x_amzn_mtls_clientcert_leaf_header_name": "", "routing_http_request_x_amzn_mtls_clientcert_serial_number_header_name": "", "routing_http_request_x_amzn_mtls_clientcert_subject_header_name": "", "routing_http_request_x_amzn_mtls_clientcert_validity_header_name": "", "routing_http_request_x_amzn_tls_cipher_suite_header_name": "", "routing_http_request_x_amzn_tls_version_header_name": "", "routing_http_response_access_control_allow_credentials_header_value": "", "routing_http_response_access_control_allow_headers_header_value": "", "routing_http_response_access_control_allow_methods_header_value": "", "routing_http_response_access_control_allow_origin_header_value": "", "routing_http_response_access_control_expose_headers_header_value": "", "routing_http_response_access_control_max_age_header_value": "", "routing_http_response_content_security_policy_header_value": "", "routing_http_response_server_enabled": false, "routing_http_response_strict_transport_security_header_value": "", "routing_http_response_x_content_type_options_header_value": "", "routing_http_response_x_frame_options_header_value": "", "ssl_policy": "ELBSecurityPolicy-2016-08", "tags": {}, "tags_all": {}, "tcp_idle_timeout_seconds": null, "timeouts": null}, "sensitive_attributes": [], "private": "********************************************************************************************************************", "dependencies": ["aws_acm_certificate.api_cert", "aws_lb.app_alb", "aws_lb_target_group.app_tg", "aws_security_group.fargate_sg", "aws_subnet.public", "aws_vpc.main_vpc"]}]}, {"mode": "managed", "type": "aws_lb_target_group", "name": "app_tg", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:elasticloadbalancing:ap-south-1:************:targetgroup/chessbrigade-target-group/b71213fffa731c7c", "arn_suffix": "targetgroup/chessbrigade-target-group/b71213fffa731c7c", "connection_termination": null, "deregistration_delay": "300", "health_check": [{"enabled": true, "healthy_threshold": 2, "interval": 30, "matcher": "200-399", "path": "/health", "port": "traffic-port", "protocol": "HTTP", "timeout": 5, "unhealthy_threshold": 3}], "id": "arn:aws:elasticloadbalancing:ap-south-1:************:targetgroup/chessbrigade-target-group/b71213fffa731c7c", "ip_address_type": "ipv4", "lambda_multi_value_headers_enabled": false, "load_balancer_arns": ["arn:aws:elasticloadbalancing:ap-south-1:************:loadbalancer/app/chessbrigade-alb/5d3ad2191fff7f37"], "load_balancing_algorithm_type": "round_robin", "load_balancing_anomaly_mitigation": "off", "load_balancing_cross_zone_enabled": "use_load_balancer_configuration", "name": "chessbrigade-target-group", "name_prefix": "", "port": 3000, "preserve_client_ip": null, "protocol": "HTTP", "protocol_version": "HTTP1", "proxy_protocol_v2": false, "slow_start": 0, "stickiness": [{"cookie_duration": 86400, "cookie_name": "", "enabled": false, "type": "lb_cookie"}], "tags": {"Environment": "Production", "Name": "chessbrigade-target-group"}, "tags_all": {"Environment": "Production", "Name": "chessbrigade-target-group"}, "target_failover": [{"on_deregistration": null, "on_unhealthy": null}], "target_group_health": [{"dns_failover": [{"minimum_healthy_targets_count": "1", "minimum_healthy_targets_percentage": "off"}], "unhealthy_state_routing": [{"minimum_healthy_targets_count": 1, "minimum_healthy_targets_percentage": "off"}]}], "target_health_state": [{"enable_unhealthy_connection_termination": null, "unhealthy_draining_interval": null}], "target_type": "ip", "vpc_id": "vpc-061b02e8cc1e3e890"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_vpc.main_vpc"]}]}, {"mode": "managed", "type": "aws_route_table", "name": "private", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:ec2:ap-south-1:************:route-table/rtb-0ad0377f174f6402e", "id": "rtb-0ad0377f174f6402e", "owner_id": "************", "propagating_vgws": [], "route": [], "tags": {"Environment": "Production", "Name": "chessbrigade-private-rt"}, "tags_all": {"Environment": "Production", "Name": "chessbrigade-private-rt"}, "timeouts": null, "vpc_id": "vpc-061b02e8cc1e3e890"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjozMDAwMDAwMDAwMDAsImRlbGV0ZSI6MzAwMDAwMDAwMDAwLCJ1cGRhdGUiOjEyMDAwMDAwMDAwMH19", "dependencies": ["aws_vpc.main_vpc"]}]}, {"mode": "managed", "type": "aws_route_table", "name": "public", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:ec2:ap-south-1:************:route-table/rtb-0c10ba969d228e2c1", "id": "rtb-0c10ba969d228e2c1", "owner_id": "************", "propagating_vgws": [], "route": [{"carrier_gateway_id": "", "cidr_block": "0.0.0.0/0", "core_network_arn": "", "destination_prefix_list_id": "", "egress_only_gateway_id": "", "gateway_id": "igw-091a42cd7d38a2177", "ipv6_cidr_block": "", "local_gateway_id": "", "nat_gateway_id": "", "network_interface_id": "", "transit_gateway_id": "", "vpc_endpoint_id": "", "vpc_peering_connection_id": ""}], "tags": {"Environment": "Production", "Name": "chessbrigade-public-rt"}, "tags_all": {"Environment": "Production", "Name": "chessbrigade-public-rt"}, "timeouts": null, "vpc_id": "vpc-061b02e8cc1e3e890"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjozMDAwMDAwMDAwMDAsImRlbGV0ZSI6MzAwMDAwMDAwMDAwLCJ1cGRhdGUiOjEyMDAwMDAwMDAwMH19", "dependencies": ["aws_internet_gateway.main_igw", "aws_vpc.main_vpc"]}]}, {"mode": "managed", "type": "aws_route_table_association", "name": "private", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": 0, "schema_version": 0, "attributes": {"gateway_id": "", "id": "rtbassoc-0fbdb91ef7bd18620", "route_table_id": "rtb-0ad0377f174f6402e", "subnet_id": "subnet-00e77d9772c3ccdd7", "timeouts": null}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjozMDAwMDAwMDAwMDAsImRlbGV0ZSI6MzAwMDAwMDAwMDAwLCJ1cGRhdGUiOjEyMDAwMDAwMDAwMH19", "dependencies": ["aws_route_table.private", "aws_subnet.private", "aws_vpc.main_vpc"]}, {"index_key": 1, "schema_version": 0, "attributes": {"gateway_id": "", "id": "rtbassoc-0d35628f84f55086d", "route_table_id": "rtb-0ad0377f174f6402e", "subnet_id": "subnet-0a768a9a522e72a02", "timeouts": null}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjozMDAwMDAwMDAwMDAsImRlbGV0ZSI6MzAwMDAwMDAwMDAwLCJ1cGRhdGUiOjEyMDAwMDAwMDAwMH19", "dependencies": ["aws_route_table.private", "aws_subnet.private", "aws_vpc.main_vpc"]}]}, {"mode": "managed", "type": "aws_route_table_association", "name": "public", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": 0, "schema_version": 0, "attributes": {"gateway_id": "", "id": "rtbassoc-031b0985c2269e64a", "route_table_id": "rtb-0c10ba969d228e2c1", "subnet_id": "subnet-0c49bbd2df2685f3d", "timeouts": null}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjozMDAwMDAwMDAwMDAsImRlbGV0ZSI6MzAwMDAwMDAwMDAwLCJ1cGRhdGUiOjEyMDAwMDAwMDAwMH19", "dependencies": ["aws_internet_gateway.main_igw", "aws_route_table.public", "aws_subnet.public", "aws_vpc.main_vpc"]}, {"index_key": 1, "schema_version": 0, "attributes": {"gateway_id": "", "id": "rtbassoc-0d827fc89cc92616d", "route_table_id": "rtb-0c10ba969d228e2c1", "subnet_id": "subnet-02acc00e3583d8b4d", "timeouts": null}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjozMDAwMDAwMDAwMDAsImRlbGV0ZSI6MzAwMDAwMDAwMDAwLCJ1cGRhdGUiOjEyMDAwMDAwMDAwMH19", "dependencies": ["aws_internet_gateway.main_igw", "aws_route_table.public", "aws_subnet.public", "aws_vpc.main_vpc"]}]}, {"mode": "managed", "type": "aws_s3_bucket", "name": "website_bucket", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"acceleration_status": "", "acl": null, "arn": "arn:aws:s3:::chess-brigade", "bucket": "chess-brigade", "bucket_domain_name": "chess-brigade.s3.amazonaws.com", "bucket_prefix": "", "bucket_regional_domain_name": "chess-brigade.s3.ap-south-1.amazonaws.com", "cors_rule": [], "force_destroy": false, "grant": [{"id": "894e5eac523dfb83bd76be4b77c96780bb420dd4b6e0abd01021de19824efa84", "permissions": ["FULL_CONTROL"], "type": "CanonicalUser", "uri": ""}], "hosted_zone_id": "Z11RGJOFQNVJUP", "id": "chess-brigade", "lifecycle_rule": [], "logging": [], "object_lock_configuration": [], "object_lock_enabled": false, "policy": "{\"Statement\":[{\"Action\":\"s3:GetObject\",\"Effect\":\"Allow\",\"Principal\":{\"AWS\":\"arn:aws:iam::cloudfront:user/CloudFront Origin Access Identity EXR5UCKSCHUK3\"},\"Resource\":\"arn:aws:s3:::chess-brigade/*\"}],\"Version\":\"2012-10-17\"}", "region": "ap-south-1", "replication_configuration": [], "request_payer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "server_side_encryption_configuration": [{"rule": [{"apply_server_side_encryption_by_default": [{"kms_master_key_id": "", "sse_algorithm": "AES256"}], "bucket_key_enabled": false}]}], "tags": {"Environment": "Production", "Name": "chessbrigade Static Website Bucket"}, "tags_all": {"Environment": "Production", "Name": "chessbrigade Static Website Bucket"}, "timeouts": null, "versioning": [{"enabled": false, "mfa_delete": false}], "website": [{"error_document": "error.html", "index_document": "index.html", "redirect_all_requests_to": "", "routing_rules": ""}], "website_domain": "s3-website.ap-south-1.amazonaws.com", "website_endpoint": "chess-brigade.s3-website.ap-south-1.amazonaws.com"}, "sensitive_attributes": [], "private": "********************************************************************************************************************************************************************************"}]}, {"mode": "managed", "type": "aws_s3_bucket_acl", "name": "website_bucket_acl", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"access_control_policy": [{"grant": [{"grantee": [{"display_name": "", "email_address": "", "id": "894e5eac523dfb83bd76be4b77c96780bb420dd4b6e0abd01021de19824efa84", "type": "CanonicalUser", "uri": ""}], "permission": "FULL_CONTROL"}], "owner": [{"display_name": "", "id": "894e5eac523dfb83bd76be4b77c96780bb420dd4b6e0abd01021de19824efa84"}]}], "acl": "private", "bucket": "chess-brigade", "expected_bucket_owner": "", "id": "chess-brigade,private"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_s3_bucket.website_bucket", "aws_s3_bucket_ownership_controls.website_bucket_ownership", "aws_s3_bucket_public_access_block.website_bucket_public_access"]}]}, {"mode": "managed", "type": "aws_s3_bucket_ownership_controls", "name": "website_bucket_ownership", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"bucket": "chess-brigade", "id": "chess-brigade", "rule": [{"object_ownership": "BucketOwnerPreferred"}]}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_s3_bucket.website_bucket"]}]}, {"mode": "managed", "type": "aws_s3_bucket_policy", "name": "website_bucket_policy", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"bucket": "chess-brigade", "id": "chess-brigade", "policy": "{\"Statement\":[{\"Action\":\"s3:GetObject\",\"Effect\":\"Allow\",\"Principal\":{\"AWS\":\"arn:aws:iam::cloudfront:user/CloudFront Origin Access Identity EXR5UCKSCHUK3\"},\"Resource\":\"arn:aws:s3:::chess-brigade/*\"}],\"Version\":\"2012-10-17\"}"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_cloudfront_origin_access_identity.oai", "aws_s3_bucket.website_bucket", "aws_s3_bucket_ownership_controls.website_bucket_ownership", "aws_s3_bucket_public_access_block.website_bucket_public_access"]}]}, {"mode": "managed", "type": "aws_s3_bucket_public_access_block", "name": "website_bucket_public_access", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"block_public_acls": true, "block_public_policy": true, "bucket": "chess-brigade", "id": "chess-brigade", "ignore_public_acls": true, "restrict_public_buckets": true}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_s3_bucket.website_bucket", "aws_s3_bucket_ownership_controls.website_bucket_ownership"]}]}, {"mode": "managed", "type": "aws_s3_bucket_website_configuration", "name": "website_config", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"bucket": "chess-brigade", "error_document": [{"key": "error.html"}], "expected_bucket_owner": "", "id": "chess-brigade", "index_document": [{"suffix": "index.html"}], "redirect_all_requests_to": [], "routing_rule": [], "routing_rules": "", "website_domain": "s3-website.ap-south-1.amazonaws.com", "website_endpoint": "chess-brigade.s3-website.ap-south-1.amazonaws.com"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_s3_bucket.website_bucket"]}]}, {"mode": "managed", "type": "aws_security_group", "name": "fargate_sg", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 1, "attributes": {"arn": "arn:aws:ec2:ap-south-1:************:security-group/sg-0efcb98cd5994e659", "description": "Security group for Fargate containers", "egress": [{"cidr_blocks": ["0.0.0.0/0"], "description": "", "from_port": 0, "ipv6_cidr_blocks": [], "prefix_list_ids": [], "protocol": "-1", "security_groups": [], "self": false, "to_port": 0}], "id": "sg-0efcb98cd5994e659", "ingress": [{"cidr_blocks": ["0.0.0.0/0"], "description": "", "from_port": 3000, "ipv6_cidr_blocks": [], "prefix_list_ids": [], "protocol": "tcp", "security_groups": [], "self": false, "to_port": 3000}, {"cidr_blocks": ["0.0.0.0/0"], "description": "", "from_port": 443, "ipv6_cidr_blocks": [], "prefix_list_ids": [], "protocol": "tcp", "security_groups": [], "self": false, "to_port": 443}, {"cidr_blocks": ["0.0.0.0/0"], "description": "", "from_port": 80, "ipv6_cidr_blocks": [], "prefix_list_ids": [], "protocol": "tcp", "security_groups": [], "self": false, "to_port": 80}], "name": "chessbrigade-fargate-sg", "name_prefix": "", "owner_id": "************", "revoke_rules_on_delete": false, "tags": {"Environment": "Production", "Name": "chessbrigade-fargate-sg"}, "tags_all": {"Environment": "Production", "Name": "chessbrigade-fargate-sg"}, "timeouts": null, "vpc_id": "vpc-061b02e8cc1e3e890"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMDAsImRlbGV0ZSI6OTAwMDAwMDAwMDAwfSwic2NoZW1hX3ZlcnNpb24iOiIxIn0=", "dependencies": ["aws_vpc.main_vpc"]}]}, {"mode": "managed", "type": "aws_security_group", "name": "rds_sg", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 1, "attributes": {"arn": "arn:aws:ec2:ap-south-1:************:security-group/sg-07da05e2c74520dad", "description": "Security group for RDS instance", "egress": [{"cidr_blocks": ["0.0.0.0/0"], "description": "", "from_port": 0, "ipv6_cidr_blocks": [], "prefix_list_ids": [], "protocol": "-1", "security_groups": [], "self": false, "to_port": 0}], "id": "sg-07da05e2c74520dad", "ingress": [{"cidr_blocks": ["0.0.0.0/0"], "description": "", "from_port": 5432, "ipv6_cidr_blocks": [], "prefix_list_ids": [], "protocol": "tcp", "security_groups": [], "self": false, "to_port": 5432}, {"cidr_blocks": [], "description": "", "from_port": 5432, "ipv6_cidr_blocks": [], "prefix_list_ids": [], "protocol": "tcp", "security_groups": ["sg-0efcb98cd5994e659"], "self": false, "to_port": 5432}], "name": "chessbrigade-rds-sg", "name_prefix": "", "owner_id": "************", "revoke_rules_on_delete": false, "tags": {"Environment": "Production", "Name": "chessbrigade-rds-sg"}, "tags_all": {"Environment": "Production", "Name": "chessbrigade-rds-sg"}, "timeouts": null, "vpc_id": "vpc-061b02e8cc1e3e890"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMDAsImRlbGV0ZSI6OTAwMDAwMDAwMDAwfSwic2NoZW1hX3ZlcnNpb24iOiIxIn0=", "dependencies": ["aws_security_group.fargate_sg", "aws_vpc.main_vpc"]}]}, {"mode": "managed", "type": "aws_subnet", "name": "private", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": 0, "schema_version": 1, "attributes": {"arn": "arn:aws:ec2:ap-south-1:************:subnet/subnet-00e77d9772c3ccdd7", "assign_ipv6_address_on_creation": false, "availability_zone": "ap-south-1a", "availability_zone_id": "aps1-az1", "cidr_block": "*********/24", "customer_owned_ipv4_pool": "", "enable_dns64": false, "enable_lni_at_device_index": 0, "enable_resource_name_dns_a_record_on_launch": false, "enable_resource_name_dns_aaaa_record_on_launch": false, "id": "subnet-00e77d9772c3ccdd7", "ipv6_cidr_block": "", "ipv6_cidr_block_association_id": "", "ipv6_native": false, "map_customer_owned_ip_on_launch": false, "map_public_ip_on_launch": false, "outpost_arn": "", "owner_id": "************", "private_dns_hostname_type_on_launch": "ip-name", "tags": {"Environment": "Production", "Name": "chessbrigade-private-subnet-0"}, "tags_all": {"Environment": "Production", "Name": "chessbrigade-private-subnet-0"}, "timeouts": null, "vpc_id": "vpc-061b02e8cc1e3e890"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMDAsImRlbGV0ZSI6MTIwMDAwMDAwMDAwMH0sInNjaGVtYV92ZXJzaW9uIjoiMSJ9", "dependencies": ["aws_vpc.main_vpc"]}, {"index_key": 1, "schema_version": 1, "attributes": {"arn": "arn:aws:ec2:ap-south-1:************:subnet/subnet-0a768a9a522e72a02", "assign_ipv6_address_on_creation": false, "availability_zone": "ap-south-1b", "availability_zone_id": "aps1-az3", "cidr_block": "*********/24", "customer_owned_ipv4_pool": "", "enable_dns64": false, "enable_lni_at_device_index": 0, "enable_resource_name_dns_a_record_on_launch": false, "enable_resource_name_dns_aaaa_record_on_launch": false, "id": "subnet-0a768a9a522e72a02", "ipv6_cidr_block": "", "ipv6_cidr_block_association_id": "", "ipv6_native": false, "map_customer_owned_ip_on_launch": false, "map_public_ip_on_launch": false, "outpost_arn": "", "owner_id": "************", "private_dns_hostname_type_on_launch": "ip-name", "tags": {"Environment": "Production", "Name": "chessbrigade-private-subnet-1"}, "tags_all": {"Environment": "Production", "Name": "chessbrigade-private-subnet-1"}, "timeouts": null, "vpc_id": "vpc-061b02e8cc1e3e890"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMDAsImRlbGV0ZSI6MTIwMDAwMDAwMDAwMH0sInNjaGVtYV92ZXJzaW9uIjoiMSJ9", "dependencies": ["aws_vpc.main_vpc"]}]}, {"mode": "managed", "type": "aws_subnet", "name": "public", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": 0, "schema_version": 1, "attributes": {"arn": "arn:aws:ec2:ap-south-1:************:subnet/subnet-0c49bbd2df2685f3d", "assign_ipv6_address_on_creation": false, "availability_zone": "ap-south-1a", "availability_zone_id": "aps1-az1", "cidr_block": "10.0.0.0/24", "customer_owned_ipv4_pool": "", "enable_dns64": false, "enable_lni_at_device_index": 0, "enable_resource_name_dns_a_record_on_launch": false, "enable_resource_name_dns_aaaa_record_on_launch": false, "id": "subnet-0c49bbd2df2685f3d", "ipv6_cidr_block": "", "ipv6_cidr_block_association_id": "", "ipv6_native": false, "map_customer_owned_ip_on_launch": false, "map_public_ip_on_launch": true, "outpost_arn": "", "owner_id": "************", "private_dns_hostname_type_on_launch": "ip-name", "tags": {"Environment": "Production", "Name": "chessbrigade-public-subnet-0"}, "tags_all": {"Environment": "Production", "Name": "chessbrigade-public-subnet-0"}, "timeouts": null, "vpc_id": "vpc-061b02e8cc1e3e890"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMDAsImRlbGV0ZSI6MTIwMDAwMDAwMDAwMH0sInNjaGVtYV92ZXJzaW9uIjoiMSJ9", "dependencies": ["aws_vpc.main_vpc"]}, {"index_key": 1, "schema_version": 1, "attributes": {"arn": "arn:aws:ec2:ap-south-1:************:subnet/subnet-02acc00e3583d8b4d", "assign_ipv6_address_on_creation": false, "availability_zone": "ap-south-1b", "availability_zone_id": "aps1-az3", "cidr_block": "********/24", "customer_owned_ipv4_pool": "", "enable_dns64": false, "enable_lni_at_device_index": 0, "enable_resource_name_dns_a_record_on_launch": false, "enable_resource_name_dns_aaaa_record_on_launch": false, "id": "subnet-02acc00e3583d8b4d", "ipv6_cidr_block": "", "ipv6_cidr_block_association_id": "", "ipv6_native": false, "map_customer_owned_ip_on_launch": false, "map_public_ip_on_launch": true, "outpost_arn": "", "owner_id": "************", "private_dns_hostname_type_on_launch": "ip-name", "tags": {"Environment": "Production", "Name": "chessbrigade-public-subnet-1"}, "tags_all": {"Environment": "Production", "Name": "chessbrigade-public-subnet-1"}, "timeouts": null, "vpc_id": "vpc-061b02e8cc1e3e890"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMDAsImRlbGV0ZSI6MTIwMDAwMDAwMDAwMH0sInNjaGVtYV92ZXJzaW9uIjoiMSJ9", "dependencies": ["aws_vpc.main_vpc"]}]}, {"mode": "managed", "type": "aws_vpc", "name": "main_vpc", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 1, "attributes": {"arn": "arn:aws:ec2:ap-south-1:************:vpc/vpc-061b02e8cc1e3e890", "assign_generated_ipv6_cidr_block": false, "cidr_block": "10.0.0.0/16", "default_network_acl_id": "acl-09288266ad16e36c7", "default_route_table_id": "rtb-08bf5e9317b71daf0", "default_security_group_id": "sg-0a0a2d0e6635012e6", "dhcp_options_id": "dopt-03b3248a04343ef01", "enable_dns_hostnames": true, "enable_dns_support": true, "enable_network_address_usage_metrics": false, "id": "vpc-061b02e8cc1e3e890", "instance_tenancy": "default", "ipv4_ipam_pool_id": null, "ipv4_netmask_length": null, "ipv6_association_id": "", "ipv6_cidr_block": "", "ipv6_cidr_block_network_border_group": "", "ipv6_ipam_pool_id": "", "ipv6_netmask_length": 0, "main_route_table_id": "rtb-08bf5e9317b71daf0", "owner_id": "************", "tags": {"Environment": "Production", "Name": "chessbrigade-vpc"}, "tags_all": {"Environment": "Production", "Name": "chessbrigade-vpc"}}, "sensitive_attributes": [], "private": "eyJzY2hlbWFfdmVyc2lvbiI6IjEifQ=="}]}], "check_results": null}