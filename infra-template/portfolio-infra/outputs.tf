# S3 Bucket Outputs
output "website_bucket_name" {
  description = "Name of the S3 bucket"
  value       = aws_s3_bucket.website_bucket.bucket
}

output "website_bucket_arn" {
  description = "ARN of the S3 bucket"
  value       = aws_s3_bucket.website_bucket.arn
}

output "website_endpoint" {
  description = "S3 website endpoint"
  value       = aws_s3_bucket_website_configuration.website_bucket_website.website_endpoint
}

# CloudFront Outputs
output "cloudfront_distribution_id" {
  description = "CloudFront distribution ID"
  value       = aws_cloudfront_distribution.website_distribution.id
}

output "cloudfront_distribution_arn" {
  description = "CloudFront distribution ARN"
  value       = aws_cloudfront_distribution.website_distribution.arn
}

output "cloudfront_domain_name" {
  description = "CloudFront distribution domain name"
  value       = aws_cloudfront_distribution.website_distribution.domain_name
}

output "website_url" {
  description = "Website URL"
  value       = "https://${var.domain_name}"
}

# Certificate Outputs
output "certificate_arn" {
  description = "ACM certificate ARN"
  value       = aws_acm_certificate.website_cert.arn
}

output "certificate_validation_records" {
  description = "Certificate validation DNS records to add to your external DNS provider"
  value = {
    for dvo in aws_acm_certificate.website_cert.domain_validation_options : dvo.domain_name => {
      name   = dvo.resource_record_name
      type   = dvo.resource_record_type
      value  = dvo.resource_record_value
    }
  }
}

# DNS Configuration Instructions
output "dns_configuration_instructions" {
  description = "Instructions for configuring your external DNS provider"
  value = <<-EOT
    Configure your external DNS provider with the following records:

    1. Certificate Validation Records (add these first):
    ${join("\n    ", [for dvo in aws_acm_certificate.website_cert.domain_validation_options : "${dvo.resource_record_type} ${dvo.resource_record_name} -> ${dvo.resource_record_value}"])}

    2. Website CNAME Records (add these after certificate is validated):
    CNAME www.yalabs.tech -> ${aws_cloudfront_distribution.website_distribution.domain_name}
    CNAME yalabs.tech -> ${aws_cloudfront_distribution.website_distribution.domain_name}
  EOT
}
