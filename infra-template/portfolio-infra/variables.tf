# General Configuration
variable "aws_region" {
  description = "AWS region"
  type        = string
  default     = "ap-south-1"
}

variable "app_name" {
  description = "Application name"
  type        = string
  default     = "portfolio"
}

variable "environment" {
  description = "Environment name"
  type        = string
  default     = "production"
}

# Domain Configuration
variable "domain_name" {
  description = "Primary domain name for the website"
  type        = string
  default     = "www.yalabs.tech"
}



variable "domain_aliases" {
  description = "Additional domain aliases"
  type        = list(string)
  default     = ["yalabs.tech"]
}

# S3 Configuration
variable "website_bucket_name" {
  description = "Name of the S3 bucket for website hosting"
  type        = string
  default     = "yalabs-portfolio"
}

# CloudFront Configuration
variable "cloudfront_price_class" {
  description = "CloudFront distribution price class"
  type        = string
  default     = "PriceClass_All"
  validation {
    condition = contains([
      "PriceClass_100",
      "PriceClass_200", 
      "PriceClass_All"
    ], var.cloudfront_price_class)
    error_message = "CloudFront price class must be PriceClass_100, PriceClass_200, or PriceClass_All."
  }
}

variable "min_ttl" {
  description = "Minimum TTL for CloudFront cache"
  type        = number
  default     = 0
}

variable "default_ttl" {
  description = "Default TTL for CloudFront cache"
  type        = number
  default     = 3600
}

variable "max_ttl" {
  description = "Maximum TTL for CloudFront cache"
  type        = number
  default     = 86400
}
