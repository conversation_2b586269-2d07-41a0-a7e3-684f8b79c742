#!/bin/bash
# Portfolio UI Deployment Parameters

# AWS Configuration
AWS_REGION="ap-south-1"
S3_BUCKET_NAME="yalabs-portfolio"
CLOUDFRONT_DISTRIBUTION_ID="" # This will be populated after terraform apply

# Git Configuration
GIT_REPO_URL="https://github.com/your-username/portfolio.git"  # Update with your actual repository URL
GIT_BRANCH="main"  # Update with your desired branch
GIT_CLONE_DIR="portfolio-ui-src"

# Build Configuration
NODE_VERSION="18"  # Specify the Node.js version to use
BUILD_COMMAND="npm run build"  # The command to build the Vite/React app
BUILD_OUTPUT_DIR="dist"  # The directory where build output is generated (Vite uses 'dist' by default)

# Environment Variables for React/Vite Build
VITE_APP_ENV="production"
VITE_APP_API_URL="https://api.yalabs.tech"  # Update with your API URL if needed
# Add any other environment variables needed for your Vite build
