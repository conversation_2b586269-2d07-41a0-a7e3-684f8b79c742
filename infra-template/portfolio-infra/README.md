# Portfolio Website Infrastructure

This directory contains Terraform configuration and deployment scripts for deploying a React/Vite portfolio website to AWS using S3, CloudFront, and Route53.

## Architecture

- **S3 Bucket**: Hosts the static website files
- **CloudFront**: CDN for global content delivery with HTTPS
- **ACM Certificate**: SSL/TLS certificate for HTTPS
- **External DNS**: You manage DNS records with your external provider

## Prerequisites

1. AWS CLI installed and configured
2. Terraform installed
3. Node.js and npm installed
4. Domain `yalabs.tech` registered with external DNS provider

## Setup Instructions

### 1. Deploy Infrastructure

```bash
# Navigate to the portfolio infrastructure directory
cd infra-template/portfolio-infra

# Initialize Terraform
terraform init

# Review the planned changes
terraform plan

# Apply the infrastructure
terraform apply
```

### 2. Configure DNS Records

After the infrastructure is deployed, you need to configure DNS records with your external DNS provider:

```bash
# Get the DNS configuration instructions
terraform output dns_configuration_instructions

# This will show you exactly which DNS records to add
```

**Important**: Add the certificate validation records first, wait for the certificate to be validated, then add the website CNAME records.

### 3. Update Deployment Parameters

Update the `portfolio-ui-params.sh` file:

```bash
# Get the CloudFront distribution ID from Terraform outputs
terraform output cloudfront_distribution_id

# Update the CLOUDFRONT_DISTRIBUTION_ID in portfolio-ui-params.sh
# Also update the GIT_REPO_URL with your actual repository URL
```

### 4. Deploy Your Portfolio

```bash
# Make sure the deployment script is executable
chmod +x portfolio-ui-deploy.sh

# Deploy your portfolio
./portfolio-ui-deploy.sh
```

## Configuration Files

- `main.tf`: Main Terraform configuration
- `variables.tf`: Variable definitions
- `outputs.tf`: Output definitions
- `terraform.tfvars`: Variable values
- `portfolio-ui-params.sh`: Deployment script parameters
- `portfolio-ui-deploy.sh`: Deployment script

## Customization

### Domain Configuration

Update the following variables in `terraform.tfvars`:
- `domain_name`: Primary domain (www.yalabs.tech)
- `root_domain`: Root domain for Route53 lookup (yalabs.tech)
- `domain_aliases`: Additional domains that should redirect to the primary domain

### Build Configuration

Update the following in `portfolio-ui-params.sh`:
- `GIT_REPO_URL`: Your portfolio repository URL
- `GIT_BRANCH`: The branch to deploy from
- `BUILD_COMMAND`: Build command (default: npm run build)
- `BUILD_OUTPUT_DIR`: Build output directory (default: dist for Vite)

### Environment Variables

Add any environment variables needed for your build in `portfolio-ui-params.sh`:
```bash
export VITE_APP_API_URL="your-api-url"
export VITE_APP_ANALYTICS_ID="your-analytics-id"
```

## Deployment Script Features

The deployment script (`portfolio-ui-deploy.sh`) includes:
- Git repository cloning/updating
- Node.js version management (with nvm support)
- Dependency installation
- Application building
- S3 sync with appropriate cache headers
- CloudFront cache invalidation
- Deployment status reporting

## Usage Examples

```bash
# Deploy with default parameters
./portfolio-ui-deploy.sh

# Deploy from a specific branch
./portfolio-ui-deploy.sh --branch feature/new-design

# Deploy with custom parameters file
./portfolio-ui-deploy.sh --params custom-params.sh

# Show help
./portfolio-ui-deploy.sh --help
```

## Outputs

After deployment, you can access:
- Website URL: https://www.yalabs.tech
- CloudFront domain: Available in Terraform outputs
- S3 bucket: Available in Terraform outputs

## Cleanup

To destroy the infrastructure:
```bash
terraform destroy
```

Note: Make sure to empty the S3 bucket before destroying if it contains files.
