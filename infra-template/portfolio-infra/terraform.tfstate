{"version": 4, "terraform_version": "1.11.4", "serial": 20, "lineage": "deb43a16-1f1c-3882-05b4-ee3487877867", "outputs": {"certificate_arn": {"value": "arn:aws:acm:us-east-1:************:certificate/10d6b823-a17b-4dc6-8b5d-173420ea83ed", "type": "string"}, "certificate_validation_records": {"value": {"portfolio.yalabs.tech": {"name": "_38bff3a49b01e2923a1c5ac09a3dbbd5.portfolio.yalabs.tech.", "type": "CNAME", "value": "_983b7b1773c80bf44a1d2dea65a01a13.xlfgrmvvlj.acm-validations.aws."}, "www.portfolio.yalabs.tech": {"name": "_132fea7fba41ab5f3723989411499183.www.portfolio.yalabs.tech.", "type": "CNAME", "value": "_4c93748c88f5eb1423405278c085c231.xlfgrmvvlj.acm-validations.aws."}}, "type": ["object", {"portfolio.yalabs.tech": ["object", {"name": "string", "type": "string", "value": "string"}], "www.portfolio.yalabs.tech": ["object", {"name": "string", "type": "string", "value": "string"}]}]}, "cloudfront_distribution_arn": {"value": "arn:aws:cloudfront::************:distribution/E1W38BPYDWO4G", "type": "string"}, "cloudfront_distribution_id": {"value": "E1W38BPYDWO4G", "type": "string"}, "cloudfront_domain_name": {"value": "d1rchbtjp60acz.cloudfront.net", "type": "string"}, "dns_configuration_instructions": {"value": "Configure your external DNS provider with the following records:\n\n1. Certificate Validation Records (add these first):\nCNAME _38bff3a49b01e2923a1c5ac09a3dbbd5.portfolio.yalabs.tech. -> _983b7b1773c80bf44a1d2dea65a01a13.xlfgrmvvlj.acm-validations.aws.\n    CNAME _132fea7fba41ab5f3723989411499183.www.portfolio.yalabs.tech. -> _4c93748c88f5eb1423405278c085c231.xlfgrmvvlj.acm-validations.aws.\n\n2. Website CNAME Records (add these after certificate is validated):\nCNAME portfolio.yalabs.tech -> d1rchbtjp60acz.cloudfront.net\nCNAME www.portfolio.yalabs.tech -> d1rchbtjp60acz.cloudfront.net\n", "type": "string"}, "website_bucket_arn": {"value": "arn:aws:s3:::yalabs-portfolio", "type": "string"}, "website_bucket_name": {"value": "yalabs-portfolio", "type": "string"}, "website_endpoint": {"value": "yalabs-portfolio.s3-website.ap-south-1.amazonaws.com", "type": "string"}, "website_url": {"value": "https://portfolio.yalabs.tech", "type": "string"}}, "resources": [{"mode": "managed", "type": "aws_acm_certificate", "name": "website_cert", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"].us_east_1", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:acm:us-east-1:************:certificate/10d6b823-a17b-4dc6-8b5d-173420ea83ed", "certificate_authority_arn": "", "certificate_body": null, "certificate_chain": null, "domain_name": "portfolio.yalabs.tech", "domain_validation_options": [{"domain_name": "portfolio.yalabs.tech", "resource_record_name": "_38bff3a49b01e2923a1c5ac09a3dbbd5.portfolio.yalabs.tech.", "resource_record_type": "CNAME", "resource_record_value": "_983b7b1773c80bf44a1d2dea65a01a13.xlfgrmvvlj.acm-validations.aws."}, {"domain_name": "www.portfolio.yalabs.tech", "resource_record_name": "_132fea7fba41ab5f3723989411499183.www.portfolio.yalabs.tech.", "resource_record_type": "CNAME", "resource_record_value": "_4c93748c88f5eb1423405278c085c231.xlfgrmvvlj.acm-validations.aws."}], "early_renewal_duration": "", "id": "arn:aws:acm:us-east-1:************:certificate/10d6b823-a17b-4dc6-8b5d-173420ea83ed", "key_algorithm": "RSA_2048", "not_after": "", "not_before": "", "options": [{"certificate_transparency_logging_preference": "ENABLED"}], "pending_renewal": false, "private_key": null, "renewal_eligibility": "INELIGIBLE", "renewal_summary": [], "status": "PENDING_VALIDATION", "subject_alternative_names": ["portfolio.yalabs.tech", "www.portfolio.yalabs.tech"], "tags": {"Environment": "production", "Name": "portfolio SSL Certificate"}, "tags_all": {"Environment": "production", "Name": "portfolio SSL Certificate"}, "type": "AMAZON_ISSUED", "validation_emails": [], "validation_method": "DNS", "validation_option": []}, "sensitive_attributes": [[{"type": "get_attr", "value": "private_key"}]], "private": "bnVsbA==", "create_before_destroy": true}]}, {"mode": "managed", "type": "aws_cloudfront_distribution", "name": "website_distribution", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 1, "attributes": {"aliases": [], "arn": "arn:aws:cloudfront::************:distribution/E1W38BPYDWO4G", "caller_reference": "terraform-20250604034854621400000001", "comment": "CloudFront distribution for portfolio", "continuous_deployment_policy_id": "", "custom_error_response": [{"error_caching_min_ttl": 0, "error_code": 403, "response_code": 200, "response_page_path": "/index.html"}, {"error_caching_min_ttl": 0, "error_code": 404, "response_code": 200, "response_page_path": "/index.html"}], "default_cache_behavior": [{"allowed_methods": ["DELETE", "GET", "HEAD", "OPTIONS", "PATCH", "POST", "PUT"], "cache_policy_id": "", "cached_methods": ["GET", "HEAD"], "compress": true, "default_ttl": 3600, "field_level_encryption_id": "", "forwarded_values": [{"cookies": [{"forward": "none", "whitelisted_names": []}], "headers": [], "query_string": false, "query_string_cache_keys": []}], "function_association": [], "grpc_config": [{"enabled": false}], "lambda_function_association": [], "max_ttl": 86400, "min_ttl": 0, "origin_request_policy_id": "", "realtime_log_config_arn": "", "response_headers_policy_id": "", "smooth_streaming": false, "target_origin_id": "S3-yalabs-portfolio", "trusted_key_groups": [], "trusted_signers": [], "viewer_protocol_policy": "redirect-to-https"}], "default_root_object": "index.html", "domain_name": "d1rchbtjp60acz.cloudfront.net", "enabled": true, "etag": "EYNRP5NEHN2KR", "hosted_zone_id": "Z2FDTNDATAQYW2", "http_version": "http2", "id": "E1W38BPYDWO4G", "in_progress_validation_batches": 0, "is_ipv6_enabled": true, "last_modified_time": "2025-06-04 04:41:42.83 +0000 UTC", "logging_config": [], "ordered_cache_behavior": [{"allowed_methods": ["GET", "HEAD", "OPTIONS"], "cache_policy_id": "", "cached_methods": ["GET", "HEAD", "OPTIONS"], "compress": true, "default_ttl": 86400, "field_level_encryption_id": "", "forwarded_values": [{"cookies": [{"forward": "none", "whitelisted_names": []}], "headers": ["Origin"], "query_string": false, "query_string_cache_keys": []}], "function_association": [], "grpc_config": [{"enabled": false}], "lambda_function_association": [], "max_ttl": 31536000, "min_ttl": 0, "origin_request_policy_id": "", "path_pattern": "/assets/*", "realtime_log_config_arn": "", "response_headers_policy_id": "", "smooth_streaming": false, "target_origin_id": "S3-yalabs-portfolio", "trusted_key_groups": [], "trusted_signers": [], "viewer_protocol_policy": "redirect-to-https"}], "origin": [{"connection_attempts": 3, "connection_timeout": 10, "custom_header": [], "custom_origin_config": [], "domain_name": "yalabs-portfolio.s3.ap-south-1.amazonaws.com", "origin_access_control_id": "E331PKNUXOZB1", "origin_id": "S3-yalabs-portfolio", "origin_path": "", "origin_shield": [], "s3_origin_config": [], "vpc_origin_config": []}], "origin_group": [], "price_class": "PriceClass_All", "restrictions": [{"geo_restriction": [{"locations": [], "restriction_type": "none"}]}], "retain_on_delete": false, "staging": false, "status": "Deployed", "tags": {"Environment": "production", "Name": "portfolio CloudFront Distribution"}, "tags_all": {"Environment": "production", "Name": "portfolio CloudFront Distribution"}, "trusted_key_groups": [{"enabled": false, "items": []}], "trusted_signers": [{"enabled": false, "items": []}], "viewer_certificate": [{"acm_certificate_arn": "arn:aws:acm:us-east-1:************:certificate/10d6b823-a17b-4dc6-8b5d-173420ea83ed", "cloudfront_default_certificate": false, "iam_certificate_id": "", "minimum_protocol_version": "TLSv1.2_2021", "ssl_support_method": "sni-only"}], "wait_for_deployment": true, "web_acl_id": ""}, "sensitive_attributes": [], "private": "eyJzY2hlbWFfdmVyc2lvbiI6IjEifQ==", "dependencies": ["aws_acm_certificate.website_cert", "aws_cloudfront_origin_access_control.website_oac", "aws_s3_bucket.website_bucket"]}]}, {"mode": "managed", "type": "aws_cloudfront_origin_access_control", "name": "website_oac", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:cloudfront::************:origin-access-control/E331PKNUXOZB1", "description": "OAC for yalabs-portfolio", "etag": "ETVPDKIKX0DER", "id": "E331PKNUXOZB1", "name": "yalabs-portfolio-oac", "origin_access_control_origin_type": "s3", "signing_behavior": "always", "signing_protocol": "sigv4"}, "sensitive_attributes": [], "private": "bnVsbA=="}]}, {"mode": "managed", "type": "aws_s3_bucket", "name": "website_bucket", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"acceleration_status": "", "acl": null, "arn": "arn:aws:s3:::yalabs-portfolio", "bucket": "yalabs-portfolio", "bucket_domain_name": "yalabs-portfolio.s3.amazonaws.com", "bucket_prefix": "", "bucket_regional_domain_name": "yalabs-portfolio.s3.ap-south-1.amazonaws.com", "cors_rule": [], "force_destroy": false, "grant": [{"id": "894e5eac523dfb83bd76be4b77c96780bb420dd4b6e0abd01021de19824efa84", "permissions": ["FULL_CONTROL"], "type": "CanonicalUser", "uri": ""}], "hosted_zone_id": "Z11RGJOFQNVJUP", "id": "yalabs-portfolio", "lifecycle_rule": [], "logging": [], "object_lock_configuration": [], "object_lock_enabled": false, "policy": "{\"Statement\":[{\"Action\":\"s3:GetObject\",\"Condition\":{\"StringEquals\":{\"AWS:SourceArn\":\"arn:aws:cloudfront::************:distribution/E1W38BPYDWO4G\"}},\"Effect\":\"Allow\",\"Principal\":{\"Service\":\"cloudfront.amazonaws.com\"},\"Resource\":\"arn:aws:s3:::yalabs-portfolio/*\",\"Sid\":\"AllowCloudFrontServicePrincipal\"}],\"Version\":\"2012-10-17\"}", "region": "ap-south-1", "replication_configuration": [], "request_payer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "server_side_encryption_configuration": [{"rule": [{"apply_server_side_encryption_by_default": [{"kms_master_key_id": "", "sse_algorithm": "AES256"}], "bucket_key_enabled": false}]}], "tags": {"Environment": "production", "Name": "portfolio Static Website Bucket"}, "tags_all": {"Environment": "production", "Name": "portfolio Static Website Bucket"}, "timeouts": null, "versioning": [{"enabled": true, "mfa_delete": false}], "website": [{"error_document": "index.html", "index_document": "index.html", "redirect_all_requests_to": "", "routing_rules": ""}], "website_domain": "s3-website.ap-south-1.amazonaws.com", "website_endpoint": "yalabs-portfolio.s3-website.ap-south-1.amazonaws.com"}, "sensitive_attributes": [], "private": "********************************************************************************************************************************************************************************"}]}, {"mode": "managed", "type": "aws_s3_bucket_policy", "name": "website_bucket_policy", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"bucket": "yalabs-portfolio", "id": "yalabs-portfolio", "policy": "{\"Statement\":[{\"Action\":\"s3:GetObject\",\"Condition\":{\"StringEquals\":{\"AWS:SourceArn\":\"arn:aws:cloudfront::************:distribution/E1W38BPYDWO4G\"}},\"Effect\":\"Allow\",\"Principal\":{\"Service\":\"cloudfront.amazonaws.com\"},\"Resource\":\"arn:aws:s3:::yalabs-portfolio/*\",\"Sid\":\"AllowCloudFrontServicePrincipal\"}],\"Version\":\"2012-10-17\"}"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_acm_certificate.website_cert", "aws_cloudfront_distribution.website_distribution", "aws_cloudfront_origin_access_control.website_oac", "aws_s3_bucket.website_bucket", "aws_s3_bucket_public_access_block.website_bucket_pab"]}]}, {"mode": "managed", "type": "aws_s3_bucket_public_access_block", "name": "website_bucket_pab", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"block_public_acls": true, "block_public_policy": true, "bucket": "yalabs-portfolio", "id": "yalabs-portfolio", "ignore_public_acls": true, "restrict_public_buckets": true}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_s3_bucket.website_bucket"]}]}, {"mode": "managed", "type": "aws_s3_bucket_versioning", "name": "website_bucket_versioning", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"bucket": "yalabs-portfolio", "expected_bucket_owner": "", "id": "yalabs-portfolio", "mfa": null, "versioning_configuration": [{"mfa_delete": "", "status": "Enabled"}]}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_s3_bucket.website_bucket"]}]}, {"mode": "managed", "type": "aws_s3_bucket_website_configuration", "name": "website_bucket_website", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"bucket": "yalabs-portfolio", "error_document": [{"key": "index.html"}], "expected_bucket_owner": "", "id": "yalabs-portfolio", "index_document": [{"suffix": "index.html"}], "redirect_all_requests_to": [], "routing_rule": [], "routing_rules": "", "website_domain": "s3-website.ap-south-1.amazonaws.com", "website_endpoint": "yalabs-portfolio.s3-website.ap-south-1.amazonaws.com"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_s3_bucket.website_bucket"]}]}], "check_results": [{"object_kind": "var", "config_addr": "var.cloudfront_price_class", "status": "pass", "objects": [{"object_addr": "var.cloudfront_price_class", "status": "pass"}]}]}