#!/bin/bash
# Carytoo Backend Deployment Parameters

# AWS Configuration
AWS_REGION="ap-south-1"
ECR_REPOSITORY_NAME="carrytoo-api"
IMAGE_TAG="latest"
ECS_CLUSTER_NAME="carrytoo-cluster"
ECS_SERVICE_NAME="carrytoo-api-service"

# Git Configuration
GIT_REPO_URL="https://github.com/mathi0695/carrytoo-api.git"  # Update with your actual repository URL
GIT_BRANCH="Prathap_new_2"  # Update with your desired branch
GIT_CLONE_DIR="carytoo-backend-src"

# Database Configuration
# DB_HOST and DB_PORT will be determined from Terraform outputs (rds_endpoint)
DB_NAME="carrytoo"
DB_USER="carrytoo_user"  # Can be overridden with DB_USER_ENV environment variable
# DB_PASSWORD will be prompted for security reasons

# Application Configuration
CONTAINER_PORT="3000"
AWS_BUCKET_NAME="carrytoo-documents"
AWS_ACCESS_REGION="ap-south-1"
# AWS_ACCESS_KEY_ID and AWS_SECRET_ACCESS_KEY should be set in environment or prompted
JWT_SECRET="mysecretkey"
EMAIL="<EMAIL>"
MAIL_PASSWORD="Carytoo@2024"
SMTP_HOST="smtp.hostinger.com"
FRONTEND_URL="https://www.carytoo.com,http://localhost:5173,http://************:5173,http://localhost:4500,https://carytoo.com"
