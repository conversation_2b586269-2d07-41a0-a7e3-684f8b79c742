variable "aws_region" {
  description = "The AWS region to deploy resources in"
  type        = string
  default     = "us-east-1"
}

variable "app_name" {
  description = "Name of the application (will be used in resource naming)"
  type        = string
}

variable "environment" {
  description = "Environment name (e.g., Production, Staging)"
  type        = string
  default     = "Production"
}

variable "domain_name" {
  description = "Main domain name for the application"
  type        = string
}

variable "domain_aliases" {
  description = "Additional domain aliases (e.g., www subdomain)"
  type        = list(string)
  default     = []
}

variable "bucket_name" {
  description = "Name of the S3 bucket for website hosting"
  type        = string
}

variable "cloudfront_price_class" {
  description = "CloudFront distribution price class"
  type        = string
  default     = "PriceClass_200"
}

variable "default_ttl" {
  description = "Default TTL for CloudFront cache behavior"
  type        = number
  default     = 3600
}

variable "min_ttl" {
  description = "Minimum TTL for CloudFront cache behavior"
  type        = number
  default     = 0
}

variable "max_ttl" {
  description = "Maximum TTL for CloudFront cache behavior"
  type        = number
  default     = 86400
}

# VPC Configuration
variable "vpc_cidr" {
  description = "CIDR block for VPC"
  type        = string
  default     = "10.0.0.0/16"
}

variable "public_subnet_cidrs" {
  description = "CIDR blocks for public subnets"
  type        = list(string)
  default     = ["10.0.0.0/24", "********/24"]
}

variable "private_subnet_cidrs" {
  description = "CIDR blocks for private subnets"
  type        = list(string)
  default     = ["*********/24", "*********/24"]
}

# RDS Configuration
variable "db_name" {
  description = "Name of the database"
  type        = string
}

variable "db_username" {
  description = "Username for the database"
  type        = string
}

variable "db_password" {
  description = "Password for the database"
  type        = string
  sensitive   = true
}

variable "db_instance_class" {
  description = "Instance class for RDS"
  type        = string
  default     = "db.t3.micro"
}

variable "db_allocated_storage" {
  description = "Allocated storage for RDS in GB"
  type        = number
  default     = 20
}

variable "db_engine_version" {
  description = "PostgreSQL engine version"
  type        = string
  default     = "16.8"
}

# ECS Configuration
variable "container_image" {
  description = "Docker image for the application"
  type        = string
}

variable "container_port" {
  description = "Port the container listens on"
  type        = number
  default     = 3000
}

variable "ecs_task_cpu" {
  description = "CPU units for ECS task"
  type        = string
  default     = "256"
}

variable "ecs_task_memory" {
  description = "Memory for ECS task"
  type        = string
  default     = "512"
}

variable "desired_count" {
  description = "Desired count of tasks running"
  type        = number
  default     = 1
}

variable "health_check_path" {
  description = "Path for ALB health check"
  type        = string
  default     = "/"
}

variable "api_domain_name" {
  description = "Domain name for the API"
  type        = string
}
