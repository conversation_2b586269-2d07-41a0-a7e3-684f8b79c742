version: 0.2

# Environment variables for CodeBuild
env:
  variables:
    # These can be set in CodeBuild project or Parameter Store
    AWS_REGION: "ap-south-1"
    ECR_REPOSITORY_NAME: "carrytoo-api"
    IMAGE_TAG: "latest"
    ECS_CLUSTER_NAME: "carrytoo-cluster"
    ECS_SERVICE_NAME: "carrytoo-api-service"
    
  parameter-store:
    # Store sensitive values in Parameter Store
    DB_HOST: "/carrytoo/database/host"
    DB_PORT: "/carrytoo/database/port"
    DB_NAME: "/carrytoo/database/name"
    DB_USER: "/carrytoo/database/user"
    ALB_URL: "/carrytoo/alb/url"
    JWT_SECRET: "/carrytoo/jwt-secret"
    
  secrets-manager:
    # Store secrets in Secrets Manager
    DB_PASSWORD: "/carrytoo/database/password"
    MAIL_PASSWORD: "/carrytoo/email/password"

phases:
  pre_build:
    commands:
      - echo Logging in to Amazon ECR...
      - aws ecr get-login-password --region $AWS_REGION | docker login --username AWS --password-stdin $AWS_ACCOUNT_ID.dkr.ecr.$AWS_REGION.amazonaws.com
      - echo Checking if ECR repository exists...
      - |
        if ! aws ecr describe-repositories --repository-names $ECR_REPOSITORY_NAME --region $AWS_REGION > /dev/null 2>&1; then
          echo Creating ECR repository: $ECR_REPOSITORY_NAME...
          aws ecr create-repository --repository-name $ECR_REPOSITORY_NAME --region $AWS_REGION
        fi

  build:
    commands:
      - echo Build started on `date`
      - echo Building the Docker image...
      - |
        docker build -t $ECR_REPOSITORY_NAME:$IMAGE_TAG \
          --build-arg DB_HOST=$DB_HOST \
          --build-arg DB_PORT=$DB_PORT \
          --build-arg DB_NAME=$DB_NAME \
          --build-arg DB_USER=$DB_USER \
          --build-arg DB_PASSWORD=$DB_PASSWORD \
          .
      - docker tag $ECR_REPOSITORY_NAME:$IMAGE_TAG $AWS_ACCOUNT_ID.dkr.ecr.$AWS_REGION.amazonaws.com/$ECR_REPOSITORY_NAME:$IMAGE_TAG

  post_build:
    commands:
      - echo Build completed on `date`
      - echo Pushing the Docker image...
      - docker push $AWS_ACCOUNT_ID.dkr.ecr.$AWS_REGION.amazonaws.com/$ECR_REPOSITORY_NAME:$IMAGE_TAG
      - echo Updating ECS service...
      - |
        # Get current task definition
        TASK_DEFINITION=$(aws ecs describe-task-definition --task-definition carrytoo-api --region $AWS_REGION)
        
        # Create new task definition with updated image
        NEW_TASK_DEFINITION=$(echo $TASK_DEFINITION | jq --arg IMAGE "$AWS_ACCOUNT_ID.dkr.ecr.$AWS_REGION.amazonaws.com/$ECR_REPOSITORY_NAME:$IMAGE_TAG" \
          '.taskDefinition | .containerDefinitions[0].image = $IMAGE | del(.taskDefinitionArn) | del(.revision) | del(.status) | del(.requiresAttributes) | del(.compatibilities) | del(.registeredAt) | del(.registeredBy)')
        
        # Register new task definition
        NEW_TASK_DEFINITION_ARN=$(aws ecs register-task-definition --region $AWS_REGION --cli-input-json "$(echo $NEW_TASK_DEFINITION)" | jq -r '.taskDefinition.taskDefinitionArn')
        
        # Update service
        aws ecs update-service --cluster $ECS_CLUSTER_NAME --service $ECS_SERVICE_NAME --task-definition $NEW_TASK_DEFINITION_ARN --region $AWS_REGION
        
        # Wait for deployment to complete
        aws ecs wait services-stable --cluster $ECS_CLUSTER_NAME --services $ECS_SERVICE_NAME --region $AWS_REGION
      - echo Deployment completed successfully!
      - echo Your API is now available at: $ALB_URL

artifacts:
  files:
    - '**/*'
