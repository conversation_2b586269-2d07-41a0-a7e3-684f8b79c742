{"pipeline": {"name": "carrytoo-backend-pipeline", "roleArn": "arn:aws:iam::YOUR_ACCOUNT_ID:role/service-role/AWSCodePipelineServiceRole", "artifactStore": {"type": "S3", "location": "carrytoo-codepipeline-artifacts"}, "stages": [{"name": "Source", "actions": [{"name": "SourceAction", "actionTypeId": {"category": "Source", "owner": "ThirdParty", "provider": "GitHub", "version": "1"}, "configuration": {"Owner": "mathi0695", "Repo": "carrytoo-api", "Branch": "dev-new-bugs", "OAuthToken": "{{resolve:secretsmanager:github-token:SecretString:token}}"}, "outputArtifacts": [{"name": "SourceOutput"}]}]}, {"name": "Build", "actions": [{"name": "BuildAction", "actionTypeId": {"category": "Build", "owner": "AWS", "provider": "CodeBuild", "version": "1"}, "configuration": {"ProjectName": "carrytoo-backend-build"}, "inputArtifacts": [{"name": "SourceOutput"}], "outputArtifacts": [{"name": "BuildOutput"}]}]}, {"name": "Deploy", "actions": [{"name": "DeployAction", "actionTypeId": {"category": "Deploy", "owner": "AWS", "provider": "ECS", "version": "1"}, "configuration": {"ClusterName": "carrytoo-cluster", "ServiceName": "carrytoo-api-service", "FileName": "imagedefinitions.json"}, "inputArtifacts": [{"name": "BuildOutput"}]}]}]}}