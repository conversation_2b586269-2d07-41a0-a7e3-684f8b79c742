const nodemailer = require("nodemailer");
const dotenv = require("dotenv");
const renderTemplate = require("./email/template-engine");
dotenv.config();


const transporter = nodemailer.createTransport({
  host: process.env.SMTP_HOST,
  port: 465,
  secure: true,
  auth: {
    user: process.env.EMAIL,
    pass: process.env.MAIL_PASSWORD,
  },
});

async function sendEmail(to, subject, templateName, data) {
  try {
    const html = await renderTemplate(templateName, data);

    const mailOptions = {
      from:`Carytoo.com ${process.env.EMAIL}`,
      to,
      subject,
      html,
    };

    const info = await transporter.sendMail(mailOptions);

    console.log("Email sent successfully:", info.response);
    return { success: true, message: "Email sent", info };
  } catch (error) {
    console.error("Error sending email:", error);
    return { success: false, message: "Email sending failed", error };
  }
}


module.exports = sendEmail;
