// const { Sequelize } = require('sequelize');
// require('dotenv').config();

// const sequelize = new Sequelize(
//   process.env.DB_NAME,
//   process.env.DB_USERNAME,
//   process.env.DB_PASSWORD,
//   {
//     host: process.env.DB_HOST,
//     port: process.env.DB_PORT,
//     dialect: 'postgres',
//     logging: false, // Set to console.log to see SQL queries
//     pool: {
//       max: 5,
//       min: 0,
//       acquire: 30000,
//       idle: 10000
//     }
//   }
// );

// module.exports = sequelize; 



const { Sequelize } = require("sequelize");
// const { database, config } = require("./config");
const { development } = require("../config/database");
const initModels = require("../models/index");
// console.log(production)

const sequelizeOptions = {
  logging: console.log, // Set this to false to disable logging
  pool: {
    max: 5,
    min: 0,
    acquire: 30000,
    idle: 10000,
  },
};
// console.log(database);
// console.log(config.databaseUrl)
// Destructure database configuration
// const Tournament = initTounrament(Sequelize);

// const sequelize = new Sequelize(production, {
//   dialect: "postgres",
//   ...sequelizeOptions, // Spread the options here`
// });

const sequelize = new Sequelize(
  development.database,
  development.username,
  development.password,
  {
    host: development.host,
    port: development.port || 5432,
    dialect: "postgres",
    dialectOptions: {
      ssl: {
        require: true, // This will help you. But you will see nwe error
        rejectUnauthorized: false // This line will fix new error
      }
    },
    ...sequelizeOptions,
  }
);

const models = initModels(sequelize);

Object.keys(models).forEach((modelName) => {
  if (models[modelName].associate) {
    models[modelName].associate(models); // Pass models object to associate
  }
});
const connectDb = async () => {
  try {
    await sequelize.authenticate();
    console.log(sequelize.models);
    console.log("Database connection has been established successfully.");

    // Sync models with the database
    // await sequelize.sync();
    // await sequelize.sync({ force: true });
    // await sequelize.sync({ alter: true });

    // console.log("All models were synchronized successfully.");
  } catch (error) {
    console.error("Unable to connect to the database:", error);
    throw error; // Re-throw the error for proper error handling
  }
};

module.exports = { sequelize, connectDb, models };