'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    /**
     * Add seed commands here.
     *
     * Example:
     * await queryInterface.bulkInsert('People', [{
     *   name: '<PERSON>',
     *   isBetaMember: false
     * }], {});
    */
    await queryInterface.bulkInsert('status_master', [
    { status_name: 'order confirmed'},
    {status_name: 'trip confirmed'}, 
    {status_name: 'order cancelled'}, 
    {status_name: 'trip cancelled'}, 
    {status_name: 'Delivered'},
    {status_name: 'Archived'}, 
    {status_name: 'paid'},
   ], {});
   
    await queryInterface.bulkInsert('package_categories', [
      { category_name: 'books' },
      { category_name: 'electronics' },
      { category_name: 'clothing' },
      { category_name: 'toys' },
      { category_name: 'documents' },
      { category_name: 'medicines' },
      { category_name: 'others' },
    ], {});
  },

  async down(queryInterface, Sequelize) {
    /**
     * Add commands to revert seed here.
     *
     * Example:
     * await queryInterface.bulkDelete('People', null, {});
     */
    await queryInterface.bulkDelete('status_master', null, {});
    await queryInterface.bulkDelete('package_categories', null, {});
  }
};
