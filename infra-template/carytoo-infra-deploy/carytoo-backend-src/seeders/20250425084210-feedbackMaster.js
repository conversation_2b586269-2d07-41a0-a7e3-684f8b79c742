'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    /**
     * Add seed commands here.
     *
     * Example:
     * await queryInterface.bulkInsert('People', [{
     *   name: '<PERSON>',
     *   isBetaMember: false
     * }], {});
    */
    await queryInterface.bulkInsert('feedback_master', [
      // Journey Feedback Titles
      {
        title: 'Delivered and payment received',
        user_type: 'journey',
      },
      {
        title: 'Delivered, but payment not received',
        user_type: 'journey',
      },
      {
        title: 'No customer in destination to collect the courier',
        user_type: 'journey',
      },
      {
        title: 'Customer not arrived in departure',
        user_type: 'journey',
      },
      {
        title: 'Customer gave prohibited item, so rejected',
        user_type: 'journey',
      },
      {
        title: 'Courier size big, so rejected',
        user_type: 'journey',
      },
      {
        title: 'Customer number not reachable',
        user_type: 'journey',
      },
      {
        title: 'Others',
        user_type: 'journey',
      },
      {
        title: 'Rude customer',
        user_type: 'journey',
      },

      // Order Feedback Titles
      {
        title: 'Courier received and payment done',
        user_type: 'order',
      },
      {
        title: 'Parcel received in damaged condition',
        user_type: 'order',
      },
      {
        title: 'Unable to locate traveller in destination',
        user_type: 'order',
      },
      {
        title: 'Unable to locate traveller in departure',
        user_type: 'order',
      },
      {
        title: 'Traveller number not reachable',
        user_type: 'order',
      },
      {
        title: 'Others',
        user_type: 'order',
      },
      {
        title: 'Rude traveller',
        user_type: 'order',
      },
      {
        title: 'Traveller changed deboarding/boarding station',
        user_type: 'order',
      },
    ]);
  },

  async down (queryInterface, Sequelize) {
    /**
     * Add commands to revert seed here.
     *
     * Example:
     * await queryInterface.bulkDelete('People', null, {});
     */
    await queryInterface.bulkDelete('feedback_master', null, {});
  }
};
