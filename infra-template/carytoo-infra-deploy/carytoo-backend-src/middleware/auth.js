const jwt = require('jsonwebtoken');
require('dotenv').config();

const auth = async (req, res, next) => {
  try {

    const token = req.header('Authorization')?.replace('Bearer ', '');
    // console.log(token)
    if (!token) {
      return res.status(401).json({ message: 'No authentication token, access denied' }); ''
    }

    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    // console.log(decoded);
    req.user = decoded;
    next();
  } catch (error) {
    res.status(401).json({ message: 'Token is not valid' });
  }
};

module.exports = auth; 