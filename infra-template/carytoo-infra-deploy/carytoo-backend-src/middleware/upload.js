const multer = require('multer');
const { S3Client } = require('@aws-sdk/client-s3');
const multerS3 = require('multer-s3');
require('dotenv').config();

//  Init AWS S3 Client
const s3 = new S3Client({
  region: process.env.AWS_ACCESS_REGION,
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
  },
});

//  Allowed MIME types
const allowedTypes = ["application/pdf",'image/jpeg', 'image/png', 'image/jpg'];

//  Multer config
const upload = multer({
  storage: multerS3({
    s3,
    bucket: process.env.AWS_BUCKET_NAME,
    acl: 'private',
    contentType: multerS3.AUTO_CONTENT_TYPE,
    key: (req, file, cb) => {
      const filename = `uploads/${Date.now()}_${file.originalname}`;
      cb(null, filename);
    },
  }),
  limits: { fileSize: 5 * 1024 * 1024 }, // 5MB
  fileFilter: (req, file, cb) => {
    if (!allowedTypes.includes(file.mimetype)) {
      return cb(new Error('Only JPEG, PNG, and JPG files are allowed!'), false);
    }
    cb(null, true);
  },
});

//  Middleware: Optional upload
const optionalFileUpload = (req, res, next) => {
  console.log("file", req.body)
  upload.single('file')(req, res, (err) => {
    if (err && err.code === 'LIMIT_FILE_SIZE') {
      return res.status(400).json({ message: 'File too large. Max 5MB allowed.' });
    } else if (err) {
      return res.status(400).json({ message: err.message });
    }

    // 🪄 Optional: Log info if no file was uploaded
    if (!req.file) {
      console.log('[INFO] No file uploaded — continuing without file.');
    } else {
      // console.log('[INFO] File uploaded to S3:', req.file.location);
      console.log('[INFO] File uploaded to S3');
    }

    next();
  });
};

module.exports = optionalFileUpload;
