const { S3Client, DeleteObjectCommand } = require('@aws-sdk/client-s3');
const { Journey, User,Order } = require('../config/database-connection').models;
require('dotenv').config();

// Initialize S3 Client
const s3 = new S3Client({
    region: process.env.AWS_ACCESS_REGION,
    credentials: {
        accessKeyId: process.env.AWS_ACCESS_KEY_ID,
        secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
        // sessionToken: process.env.AWS_SESSION_TOKEN,
    },
});

// Function to delete an old image from S3
const deleteFromS3 = async (fileUrl) => {
    if (!fileUrl) return;

    const fileKey = fileUrl.split('.com/')[1]; // Extract S3 key from URL
    const params = {
        Bucket: process.env.AWS_BUCKET_NAME,
        Key: fileKey,
    };

    try {
        await s3.send(new DeleteObjectCommand(params));
        console.log(`Deleted old file: ${fileKey}`);
    } catch (error) {
        console.error('Error deleting file from S3:', error);
    }
};

// Middleware to handle file update in edit mode
const uploadHandler = async (req, res, next) => {
    try {
        if (req.method === 'PUT') {
            const journey = await Journey.findOne({
                where: { id: req.params.id, user_id: req.user.id },
            });

            if (!journey) {
                return res.status(404).json({ message: 'Journey not found' });
            }

            // If a new file is uploaded, delete the old one
            if (req.file) {
                if (journey.image_url) {
                    await deleteFromS3(journey.image_url);
                }
                req.body.image_url = req.file.location;
            } else {
                req.body.image_url = journey.image_url; // Keep existing image
            }
        }

        next();
    } catch (error) {
        console.error('Upload handler error:', error);
        return res.status(500).json({ message: 'Error processing file update' });
    }
};

module.exports = uploadHandler;
