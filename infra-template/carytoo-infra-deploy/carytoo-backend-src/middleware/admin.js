const jwt = require('jsonwebtoken');
require('dotenv').config();
const { User, Otp } = require('../config/database-connection').models

const isAdmin = async (req, res, next) => {
    const user = await User.findOne({where: {id:req.user.id}}); // Find the user by their id (from the decoded token)
  
    if (!user || user.role !== 'admin') {
      return res.status(403).json({ message: 'Access Denied: Admins only.' });
    }
  
    next();
  };

  module.exports = isAdmin;