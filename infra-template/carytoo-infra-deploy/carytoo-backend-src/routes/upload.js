const express = require('express');
const router = express.Router();
const authMiddleware = require('../middleware/auth');
const auth = require('../middleware/auth');
const upload = require('../middleware/upload');
const { uploadFile, getFileUrl } = require('../controllers/uploadController');
const optionalFileUpload = require('../middleware/upload');

// router.use(authMiddleware)

router.post('/', auth,optionalFileUpload,uploadFile);
router.get('/get-url',auth,getFileUrl);

module.exports = router;
