const router = require('express').Router(); 

const authRouter = require('./authRoutes');
const journeyRoutes = require('./journey');
const orderRoutes = require('./order')
const categoryRouter = require('./category') 
const uploadRouter = require('./upload')
const feedbackMasterRouter = require('./feedbackMaster')
const feedbackRouter = require('./feedback')

router.use('/auth', authRouter);
router.use('/journeys', journeyRoutes);
router.use('/orders',orderRoutes)
router.use('/category',categoryRouter)
router.use('/upload',uploadRouter)
router.use('/feedback-master',feedbackMasterRouter)
router.use('/feedback',feedbackRouter)

module.exports = router;