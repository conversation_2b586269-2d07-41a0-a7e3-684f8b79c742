const express = require('express');
const { body } = require('express-validator');
const authController = require('../controllers/authController');
const auth = require('../middleware/auth');
const rateLimit = require('express-rate-limit');
const isAdmin = require('../middleware/admin');

// Stricter auth limiter for login/register endpoints
const authLimiter = rateLimit({
  windowMs: 1 * 60 * 1000, // 1 minute (changed from 1 hour for testing)
  limit: 3, // 3 attempts per minute (changed from 5 for testing)
  message: {
    error: {
      message: 'Too many attempts, please try again after 1 minute.',
      status: 429
    }
  },
  standardHeaders: true,
  legacyHeaders: false,
  keyGenerator: (req) => {
    // For localhost testing, use a combination of IP and user agent
    const ip = req.ip || 
               req.connection.remoteAddress || 
               req.socket.remoteAddress || 
               req.connection.socket.remoteAddress;
    return `${ip}-${req.originalUrl}-${req.headers['user-agent']}`;
  },
  skipSuccessfulRequests: false
});

const router = express.Router();

// Apply rate limiter to specific paths
router.use(['/login', '/register', '/get-mail', '/get-mail-reg'], authLimiter);

// Validation middleware
const registerValidation = [
  body('username').trim().isLength({ min: 3 }).withMessage('Username must be at least 3 characters long'),
  body('email').isEmail().withMessage('Please enter a valid email'),
  body('password').isLength({ min: 6 }).withMessage('Password must be at least 6 characters long'),
  body('phoneNumber').matches(/^\+?[\d\s-]+$/).withMessage('Please enter a valid phone number')
];

const loginValidation = [
  body('email').isEmail().withMessage('Please enter a valid email'),
  body('password').exists().withMessage('Password is required')
];

// Routes
router.post('/register', registerValidation, authController.register);
router.post('/login', loginValidation, authController.login);
router.get('/me', auth, authController.getCurrentUser);
router.get('/all-user', auth, isAdmin,authController.getAllUser);
router.post('/get-mail', authController.otpGen);
router.post('/get-mail-reg', authController.otGenerationRegister);
router.post('/otp-verify', authController.otpVerify);
router.post('/mail-verify', authController.otpVerifyMail);

module.exports = router; 
