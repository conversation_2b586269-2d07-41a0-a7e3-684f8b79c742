const express = require('express');
const { body } = require('express-validator');
const categoryController = require('../controllers/categoryController');

const router = express.Router();

// Validation rules
const categoryValidation = [
  body('category_name').notEmpty().withMessage('Category name is required'),
];

// Routes
router.get('/',categoryController.getAllCategories)
router.get('/:id',categoryController.getCategoryById)
router.post('/create', categoryValidation, categoryController.createCategory);
router.put('/update/:id', categoryValidation, categoryController.updateCategory);
router.delete('/delete/:id', categoryController.deleteCategory);
router.delete('/delete-all', categoryController.deleteAllCategories);

module.exports = router;