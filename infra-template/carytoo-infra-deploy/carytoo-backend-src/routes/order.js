const express = require('express');
const router = express.Router();
const orderController = require('../controllers/orderController');
const authMiddleware = require('../middleware/auth');
const auth = require('../middleware/auth');
const isAdmin = require('../middleware/admin');

// router.use(authMiddleware)

router.get('/',auth,isAdmin,orderController.getOrders)
router.get('/Details',auth,isAdmin,orderController.AdminDetails)
router.get('/:id',auth,orderController.getOrdersByUser)
router.post('/', auth,orderController.create);
router.patch('/feedback', auth,orderController.feedback);
router.put('/order/:order_id', orderController.update);
router.put('/order/:order_id/status', orderController.changeStatus);
router.delete('/order/:order_id', orderController.delete);

module.exports = router;
