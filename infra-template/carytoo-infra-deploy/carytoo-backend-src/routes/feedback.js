const express = require('express');
const feedbackController = require('../controllers/feedback');
const auth = require('../middleware/auth');
const router = express.Router(); // adjust path as needed

// Create a new feedback master
router.post('/create',auth,feedbackController.create);

// Get all feedback masters
router.get('/', feedbackController.getAll);

// Get a single feedback master by ID
router.get('/:id', feedbackController.getById);

// Update a feedback master by ID
router.put('/:id', feedbackController.update);

// Delete a feedback master by ID
router.delete('/:id', feedbackController.delete);

// Delete all feedback masters
router.delete('/', feedbackController.deleteAll);

// Get feedbacks by journey_id
router.get('/by-journey-id/:id',auth,feedbackController.getByJourneyId);

// Get feedbacks by order_id
router.get('/by-order-id/:id', auth,feedbackController.getByOrderId);

module.exports = router;
