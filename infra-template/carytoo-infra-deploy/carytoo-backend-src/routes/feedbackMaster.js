const express = require('express');
const feedbackMasterController = require('../controllers/feedbackMaster');
const auth = require('../middleware/auth');
const router = express.Router(); // adjust path as needed

// Create a new feedback master
router.post('/',auth,feedbackMasterController.create);

// Get all feedback masters
router.get('/', auth,feedbackMasterController.getAll);

// Get a single feedback master by ID
router.get('/group/:user_type', auth,feedbackMasterController.getByUserType);

// Update a feedback master by ID
router.put('/:id',auth,feedbackMasterController.update);

// Delete a feedback master by ID
router.delete('/:id', auth,feedbackMasterController.delete);

// Delete all feedback masters
router.delete('/', auth,feedbackMasterController.deleteAll);

module.exports = router;
