const express = require('express');
const router = express.Router();
const journeyController = require('../controllers/journeyController');
const { body } = require('express-validator');
const authMiddleware = require('../middleware/auth'); // Assuming you have auth middleware
const auth = require('../middleware/auth');
const upload = require('../middleware/upload');
const uploadHandler = require('../middleware/uploadhandler');
const optionalFileUpload = require('../middleware/upload');
const isAdmin = require('../middleware/admin');

// Validation middleware
const validateJourney = [
  body('start_date').notEmpty().isDate(),
  body('end_date').notEmpty().isDate(),
  body('source').notEmpty().trim(),
  body('destination').notEmpty().trim(),
  body('mode_of_transport').notEmpty().isIn(['train', 'flight', 'flight-inter','bus', 'car']),
  body('departure_time').notEmpty().matches(/^([01]\d|2[0-3]):([0-5]\d)$/),
  body('arrival_time').notEmpty().matches(/^([01]\d|2[0-3]):([0-5]\d)$/),
  // Conditional validation based on mode of transport
  body('train_number').if(body('mode_of_transport').equals('train')).notEmpty(),
  body('coach_number').if(body('mode_of_transport').equals('train')).notEmpty(),
  body('boarding_point').if(body('mode_of_transport').equals('train')).notEmpty(),
  body('de_boarding_point').if(body('mode_of_transport').equals('train')).notEmpty(),
  body('seat_number').if(body('mode_of_transport').equals('train')).notEmpty(),
  body('flight_number').if(body('mode_of_transport').equals('flight')).notEmpty(),
  body('space_availability').if(body('mode_of_transport').equals('flight')).notEmpty(),
  body('arrival_terminal_number').if(body('mode_of_transport').equals('flight')).notEmpty(),
  body('departure_terminal_number').if(body('mode_of_transport').equals('flight')).notEmpty(),
  body('flight_number').if(body('mode_of_transport').equals('flight-inter')).notEmpty(),
  body('space_availability').if(body('mode_of_transport').equals('flight-inter')).notEmpty(),
  body('arrival_terminal_number').if(body('mode_of_transport').equals('flight-inter')).notEmpty(),
  body('departure_terminal_number').if(body('mode_of_transport').equals('flight-inter')).notEmpty(),
];

router.get('/search',auth,journeyController.search);

// Apply auth middleware to all routes
router.use(authMiddleware);

// Routes
router.get('/Boarding-point', journeyController.boarding);
router.post('/',auth,optionalFileUpload,validateJourney,journeyController.create);
router.get('/', isAdmin,journeyController.getAllUserJourneys);
router.get('/:id', journeyController.getJourney);
router.get('/edit/:id', journeyController.get);
router.patch('/arrived/:id', journeyController.arrived);
router.put('/:id', auth, optionalFileUpload,uploadHandler,journeyController.update);
router.patch('/feedback', auth,journeyController.feedback);
router.delete('/:id', journeyController.delete);

module.exports = router; 