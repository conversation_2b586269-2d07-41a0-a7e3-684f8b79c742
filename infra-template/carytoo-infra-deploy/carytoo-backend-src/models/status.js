'use strict';
const { Model } = require('sequelize');
const { DataTypes } = require("sequelize");

module.exports = (sequelize) => {
  class StatusMaster extends Model {
    static associate(models) {
      // Define associations here
      StatusMaster.hasMany(models.Order, { foreignKey: 'order_status', as: 'orders'  });
    }
  }

  StatusMaster.init({
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true
    },
    status_name: {
      type: DataTypes.STRING(20),
      allowNull: false,
      unique: true
    }
  }, {
    sequelize,
    modelName: 'StatusMaster',
    tableName: 'status_master',
    timestamps: false
  });

  return StatusMaster;
};
