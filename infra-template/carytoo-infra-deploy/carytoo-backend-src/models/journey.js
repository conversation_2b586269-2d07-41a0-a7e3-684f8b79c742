'use strict';
const { Model } = require('sequelize');
const { DataTypes } = require("sequelize");

module.exports = (sequelize) => {
  class Journey extends Model {
    static associate(models) {
      Journey.belongsTo(models.User, { foreignKey: 'user_id', as: 'user' });
      // Journey.belongsTo(models.User, {foreignKey: 'order_id', as: 'orders'});
      Journey.hasMany(models.Order, { foreignKey: 'journey_id', as: 'orders' });
      Journey.hasMany(models.Feedback, { foreignKey: 'journey_id', as: 'feedback_entries' });

    }
  }

  Journey.init({
    user_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'Users',
        key: 'id'
      }
    },
    start_date: {
      type: DataTypes.DATEONLY,
      allowNull: false
    },
    end_date: {
      type: DataTypes.DATEONLY,
      allowNull: false
    },
    source: {
      type: DataTypes.STRING,
      allowNull: false
    },
    destination: {
      type: DataTypes.STRING,
      allowNull: false
    },
    boarding_point: {
      type: DataTypes.STRING,
      allowNull: true
    },
    de_boarding_point: {
      type: DataTypes.STRING,
      allowNull: true
    },
    mode_of_transport: {
      type: DataTypes.ENUM('train', 'flight', 'flight-inter','bus', 'car'),
      allowNull: false
    },
    train_number: {
      type: DataTypes.STRING,
      allowNull: true
    },
    coach_number: {
      type: DataTypes.STRING,
      allowNull: true
    },
    seat_number: {
      type: DataTypes.STRING,
      allowNull: true
    },
    flight_number: {
      type: DataTypes.JSON,
      allowNull: true
    },
    departure_time: {
      type: DataTypes.TIME,
      allowNull: false
    },
    arrival_time: {
      type: DataTypes.TIME,
      allowNull: false
    },
    journey_status: {
      type: DataTypes.STRING,
      allowNull: true
    },
    order_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'orders',
        key: 'order_id'
      }
    },
    ticket_image_url: {
      type: DataTypes.STRING(2058),
      allowNull: true,
    },
    feedback: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    is_arrived: {
      type: DataTypes.STRING(10),
      allowNull: true,
      defaultValue: 'false'
    },
    space_availability: {
      type: DataTypes.STRING,
      allowNull: true,
    },   
    departure_terminal_number: {
      type: DataTypes.STRING,
      allowNull: true,
    },
      arrival_terminal_number: {
      type: DataTypes.STRING,
      allowNull: true,
    },
  }, {
    sequelize,
    modelName: 'Journey',
    tableName: 'journeys',
    timestamps: true,
    underscored: true
  });

  return Journey;
}; 
