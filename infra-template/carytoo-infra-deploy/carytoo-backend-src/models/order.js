const { Model } = require('sequelize');
const { DataTypes } = require("sequelize");

module.exports = (sequelize) => {
  class Order extends Model {
    static associate(models) {
      // Define associations
      Order.belongsTo(models.Journey, { foreignKey: 'journey_id' });
      Order.belongsTo(models.User, { foreignKey: 'user_id' });
      Order.belongsTo(models.PackageCategory, { foreignKey: 'package_category' });
      Order.belongsTo(models.StatusMaster, { foreignKey: 'order_status', as:'status_master' });
      Order.hasMany(models.Feedback, { foreignKey: 'order_id', as: 'feedback_entries' });
    }
  }

  Order.init({
    order_id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true
    },
    journey_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    user_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'Users',
        key: 'id'
      }
    },
    package_category: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'package_categories',
        key: 'id'
      }
    },
    price: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false
    },
    order_description: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    payment_status: {
      type: DataTypes.STRING(60),
      allowNull: false
    },
    order_status: {
      type: DataTypes.STRING,
      allowNull: false,
      references: {
        model: 'status_master',
        key: 'status_name'
      }
    },
    feedback: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    other_category: {
      type: DataTypes.STRING,
      allowNull: true
    },
    is_arrived: {
      type: DataTypes.STRING(10),
      allowNull: true,
      defaultValue: 'false'
    },
    created_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW
    },
    updated_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW
    }
  }, {
    sequelize,
    modelName: 'Order',
    tableName: 'orders',
    timestamps: false
  });

  return Order;
};
