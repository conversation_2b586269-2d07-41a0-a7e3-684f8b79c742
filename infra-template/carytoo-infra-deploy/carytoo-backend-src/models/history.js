'use strict';
const { Model } = require('sequelize');
const { DataTypes } = require("sequelize");

module.exports = (sequelize) => {
  class OrderHistory extends Model {
    static associate(models) {
      OrderHistory.belongsTo(models.Order, { foreignKey: 'order_id' });
      OrderHistory.belongsTo(models.StatusMaster, { foreignKey: 'previous_status' });
      OrderHistory.belongsTo(models.StatusMaster, { foreignKey: 'new_status' });
      OrderHistory.belongsTo(models.User, { foreignKey: 'changed_by' });
    }
  }

  OrderHistory.init({
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true
    },
    order_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'orders',
        key: 'order_id'
      }
    },
    previous_status: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'status_master',
        key: 'id'
      }
    },
    new_status: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'status_master',
        key: 'id'
      }
    },
    changed_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW
    },
    changed_by: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'Users',
        key: 'id'
      }
    }
  }, {
    sequelize,
    modelName: 'OrderHistory',
    tableName: 'order_history',
    timestamps: false
  });

  return OrderHistory;
};