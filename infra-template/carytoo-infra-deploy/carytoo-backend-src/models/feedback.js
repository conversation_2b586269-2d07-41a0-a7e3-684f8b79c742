'use strict';
const { Model } = require('sequelize');
const { DataTypes } = require("sequelize");


module.exports = (sequelize) => {
    class feedback extends Model {
      static associate(models) {
      feedback.belongsTo(models.Journey, {foreignKey: 'journey_id',as: 'journey',});
      feedback.belongsTo(models.Order, {foreignKey: 'order_id',as: 'orders',});
    }
    }

    feedback.init({
        id: {
            type: DataTypes.UUID,
            defaultValue: DataTypes.UUIDV4,
            primaryKey: true,
            allowNull: false,
        },
        journey_user_id: {
            type: DataTypes.INTEGER,
            allowNull: false,
            references: {
             model: 'Users',
             key: 'id'
            },
          },
          order_user_id: {
            type: DataTypes.INTEGER,
            allowNull: false,
            references: {
             model: 'Users',
             key: 'id'
            },
          },
        feedback: {
            type: DataTypes.STRING,
            allowNull: true,
        },
        created_by: {
          type: DataTypes.INTEGER,
          allowNull: false,
          references: {
            model: 'Users',
            key: 'id'
           },
      },
        description: {
            type: DataTypes.STRING,
            allowNull: true,
        },
        journey_id: {
            type: DataTypes.INTEGER,
            allowNull: false,
            references: {
              model: 'Journey',
              key: 'id',
            },
          },
          order_id: {
            type: DataTypes.INTEGER,
            allowNull: false,
            references: {
               model: 'orders',
               key: 'order_id'
            },
          },
    }, {
        sequelize,
        modelName: 'feedback',
        tableName: 'feedback',
        timestamps: true,
    });

    return feedback;
}; 
