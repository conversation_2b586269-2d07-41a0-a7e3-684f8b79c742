'use strict';
const { Model } = require('sequelize');
const { DataTypes } = require("sequelize");

module.exports = (sequelize) => {
  class PackageCategory extends Model {
    static associate(models) {
      // Define associations here
      PackageCategory.hasMany(models.Order, { foreignKey: 'package_category', as: 'orders'  });
    }
  }

  PackageCategory.init({
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true
    },
    category_name: {
      type: DataTypes.STRING(50),
      allowNull: false,
      unique: true
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true
    }
  }, {
    sequelize,
    modelName: 'PackageCategory',
    tableName: 'package_categories',
    timestamps: false
  });

  return PackageCategory;
};
