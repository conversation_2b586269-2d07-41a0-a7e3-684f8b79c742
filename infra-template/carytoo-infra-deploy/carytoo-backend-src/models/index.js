

// const fs = require('fs');
// const path = require('path');
// const Sequelize = require('sequelize');
// const process = require('process');
// const basename = path.basename(__filename);
// const env = process.env.NODE_ENV || 'development';
// const config = require(__dirname + '/../config/database.js')[env];
// const db = {};

// let sequelize;
// if (config.use_env_variable) {
//   sequelize = new Sequelize(process.env[config.use_env_variable], config);
// } else {
//   sequelize = new Sequelize(config.database, config.username, config.password, config);
// }
// console.log("Loaded Models:", Object.keys(db)); // Debugging line

// fs
//   .readdirSync(__dirname)
//   .filter(file => {
//     return (
//       file.indexOf('.') !== 0 &&
//       file !== basename &&
//       file.slice(-3) === '.js' &&
//       file.indexOf('.test.js') === -1
//     );
//   })
//   .forEach(file => {
//     const model = require(path.join(__dirname, file))(sequelize, Sequelize.DataTypes);
//     db[model.name] = model;
//   });

// Object.keys(db).forEach(modelName => {
//   if (db[modelName].associate) {
//     db[modelName].associate(db);
//   }
// });

// db.sequelize = sequelize;
// db.Sequelize = Sequelize;

// module.exports = db; 




const initModels = (sequelize) => {
  const DataTypes = sequelize.DataTypes;
  // const Tournament = require("./tournament")(sequelize);
  // const User = require("./user")(sequelize);
  // const PlayerDetail = require("./playerDetail")(sequelize);
  // const ClubDetail = require("./clubDetail")(sequelize);
  const Journey = require("./journey")(sequelize, DataTypes);
  const User = require("./user")(sequelize, DataTypes);
  const StatusMaster = require("./status")(sequelize, DataTypes);
  const PackageCategory = require("./packageCategory")(sequelize, DataTypes);
  const OrderHistory = require("./history")(sequelize, DataTypes);
  const Order = require("./order")(sequelize, DataTypes);
  const Otp = require("./otp")(sequelize, DataTypes);
  const Feedback = require("./feedback")(sequelize, DataTypes);
  const FeedbackMaster = require("./feedbackMaster")(sequelize, DataTypes);
  // const PlayerDetail = require("./playerDetail")(sequelize);

  // // Define associations
  // User.hasOne(PlayerDetail);
  // PlayerDetail.belongsTo(User);

  // Tournament.hasMany(User);
  // User.belongsToMany(Tournament, { through: 'UserTournaments' });

  return {
    Journey,
    User,
    PackageCategory,
    StatusMaster,
    OrderHistory,
    Order,
    Otp,
    Feedback,
    FeedbackMaster
  };
};

module.exports = initModels;