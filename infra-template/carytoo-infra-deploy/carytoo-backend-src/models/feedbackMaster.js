'use strict';
const { Model } = require('sequelize');
const { DataTypes } = require("sequelize");

module.exports = (sequelize) => {
  class feedbackMaster extends Model {
  }

  feedbackMaster.init({
    id: {
        type: DataTypes.INTEGER,
        autoIncrement: true,
        primaryKey: true
      },
    title: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      user_type: {
        type: DataTypes.ENUM('journey','order'),
        allowNull: false,
      }
  }, {
    sequelize,
    modelName: 'feedbackMaster',
    tableName: 'feedback_master',
    timestamps: true,
  });

  return feedbackMaster;
}; 
