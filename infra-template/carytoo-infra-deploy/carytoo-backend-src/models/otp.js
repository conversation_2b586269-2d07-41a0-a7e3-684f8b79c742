const { Model } = require('sequelize');
const { DataTypes } = require("sequelize");

module.exports = (sequelize) => {
  class Otp extends Model {
    static associate(models) {
      // Define associations here
    }
  }
  
  Otp.init({
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    email: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    otp: {
      type: DataTypes.INTEGER,
      allowNull: false,
      validate: {
        len: [6,6],
        isNumeric: true
      }
    },
    expiry_time: {
      type: DataTypes.DATE,
      allowNull: true
    },
    type: {
      type: DataTypes.STRING(60),
      allowNull: false,
    },
    timestamp: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    }
  }, {
    sequelize,
    modelName: 'Otp',
    tableName: 'otp_verify',
    timestamps: false, // Disable timestamps since we don't have these columns
    indexes: [
      {
        unique: true,
        fields: ['email', 'type']
      }
    ]
  });

  return Otp;
}; 
