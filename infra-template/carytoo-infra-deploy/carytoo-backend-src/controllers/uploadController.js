const multer = require('multer');
const { S3Client } = require('@aws-sdk/client-s3');
const multerS3 = require('multer-s3');
require('dotenv').config();

const uploadFile = (req, res) => {
    console.log('File:', req.file);
    console.log('Body:', req.body);
    if (!req.file) {
        return res.status(400).json({ error: 'File upload failed!' });
    }
    res.json({ message: 'File uploaded successfully', fileUrl: req.file.location });
};

module.exports = { uploadFile };

// Initialize S3 Client
const getFileUrl = async (req, res) => {
    const { fileKey } = req.query;
    if (!fileKey) {
        return res.status(400).json({ error: 'Missing fileKey' });
    }

    try {
        const command = new GetObjectCommand({
            Bucket: process.env.AWS_BUCKET_NAME,
            Key: fileKey,
        });

        const url = await getSignedUrl(s3, command, { expiresIn: 60 }); // 60-sec expiry
        res.json({ downloadUrl: url });
    } catch (error) {
        res.status(500).json({ error: 'Failed to generate pre-signed URL' });
    }
};

// module.exports = {,};
module.exports = {uploadFile,getFileUrl};
