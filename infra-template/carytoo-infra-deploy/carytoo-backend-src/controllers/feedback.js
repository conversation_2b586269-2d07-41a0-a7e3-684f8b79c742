const { validationResult } = require('express-validator');
// const db = require('../models');
// const PackageCategory = db.PackageCategory;
const {Feedback} = require('../config/database-connection').models
const dotenv = require('dotenv');
const { where } = require('sequelize');

dotenv.config();

const feedbackController = {
  // Create a new category
  create: async (req, res) => {
    const { journeyUserId, orderUserId, des, Title, journeyId, orderId } = req.body;

    try {
      const user=req.user.id
      // Create feedback entry
      const newFeedback = await Feedback.create({
        journey_user_id:journeyUserId,
        order_user_id:orderUserId,
        feedback: Title, // Use a different name if needed
        description:des,
        journey_id:journeyId,
        order_id:orderId,
        created_by:user
      });
  
      // Send response with the created feedback entry
      return res.status(201).json({
        success: true,
        data: newFeedback,
        message: "Thanks for your feedback!",
      });
    } catch (error) {
      console.error("Error creating feedback:", error);
      return res.status(500).json({
        success: false,
        message: "Failed to create feedback",
      });
    }
  },

  getAll: async (req, res) => {
    try {
        const feedbacks = await Feedback.findAll();
        res.status(200).json(feedbacks);
      } catch (error) {
        console.error('Get all error:', error);
        res.status(500).json({ error: 'Failed to retrieve feedbacks' });
      }
  },

  // Get category by ID
  getById: async (req, res) => {
    try {
        const { id } = req.params;
        const singleFeedback = await Feedback.findByPk(id);
        if (!singleFeedback) return res.status(404).json({ error: 'Feedback not found' });
        res.status(200).json(singleFeedback);
      } catch (error) {
        console.error('Get by ID error:', error);
        res.status(500).json({ error: 'Failed to retrieve feedback' });
      }
  },

  // Update a category
  update: async (req, res) => {
    try {
        const { id } = req.params;
        const feedbackEntry = await Feedback.findByPk(id);
        if (!feedbackEntry) return res.status(404).json({ error: 'Feedback not found' });
    
        await feedbackEntry.update(req.body);
        res.status(200).json(feedbackEntry);
      } catch (error) {
        console.error('Update error:', error);
        res.status(500).json({ error: 'Failed to update feedback' });
      }
  },

  // Delete a category
  delete: async (req, res) => {
    try {
        const { id } = req.params;
        const feedbackEntry = await Feedback.findByPk(id);
        if (!feedbackEntry) return res.status(404).json({ error: 'Feedback not found' });
    
        await feedbackEntry.destroy();
        res.status(200).json({ message: 'Feedback deleted successfully' });
      } catch (error) {
        console.error('Delete error:', error);
        res.status(500).json({ error: 'Failed to delete feedback' });
      }
  },

  // Delete all categories
  deleteAll: async (req, res) => {
    try {
      await feedbackMaster.destroy({ where: {}, truncate: true });
      res.json({ message: 'All categories deleted successfully' });
    } catch (error) {
      res.status(500).json({ message: 'Server error', error: error.message });
    }
  },
   // Get feedbacks by journey_id
   getByJourneyId: async (req, res) => {
    try {
      const { id } = req.params;
      const user=req.user.id
      if (!id) {
        return res.status(400).json({ error: 'journey_id is required' });
      }

      const feedbacks = await Feedback.findOne({
        where: { created_by:user,journey_id:id}
      });

      return res.status(200).json(feedbacks);
    } catch (error) {
      console.error('Error fetching by journey_id:', error);
      return res.status(500).json({ error: 'Failed to fetch feedback by journey_id' });
    }
  },

  // Get feedbacks by order_id
  getByOrderId: async (req, res) => {
    try {
        const { id } = req.params;
        const user=req.user.id

        if (!id) {
          return res.status(400).json({ error: 'journey_id is required' });
        }

      const feedbacks = await Feedback.findOne({
        where: { created_by:user,order_id:id }
      });

      return res.status(200).json(feedbacks);
    } catch (error) {
      console.error('Error fetching by order_id:', error);
      return res.status(500).json({ error: 'Failed to fetch feedback by order_id' });
    }
},
};

module.exports = feedbackController;
