const { validationResult } = require('express-validator');
// const db = require('../models');
// const PackageCategory = db.PackageCategory;
const {PackageCategory} = require('../config/database-connection').models
const dotenv = require('dotenv');
const { where } = require('sequelize');

dotenv.config();

const categoryController = {
  // Create a new category
  createCategory: async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
      }

      const { category_name, description } = req.body;

      // Check if category already exists
      const categoryExists = await PackageCategory.findOne({ where: { category_name } });
      if (categoryExists) {
        return res.status(400).json({ message: 'Category already exists' });
      }

      // Create new category
      const category = await PackageCategory.create({ category_name, description });
      res.status(201).json({ message: 'Category created successfully', category });
    } catch (error) {
      res.status(500).json({ message: 'Server error', error: error.message });
    }
  },

  getAllCategories: async (req, res) => {
    try {
      const categories = await PackageCategory.findAll();
      res.json({ categories });
    } catch (error) {
      res.status(500).json({ message: 'Server error', error: error.message });
    }
  },

  // Get category by ID
  getCategoryById: async (req, res) => {
    try {
      const { id } = req.params;
      const category = await PackageCategory.findByPk(id);

      if (!category) {
        return res.status(404).json({ message: 'Category not found' });
      }

      res.json({ category });
    } catch (error) {
      res.status(500).json({ message: 'Server error', error: error.message });
    }
  },

  // Update a category
  updateCategory: async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
      }

      const { id } = req.params;
      const { category_name, description } = req.body;

      // Find category
      const category = await PackageCategory.findByPk(id);
      if (!category) {
        return res.status(404).json({ message: 'Category not found' });
      }

      // Update category details
      await category.update({ category_name, description });
      res.json({ message: 'Category updated successfully', category });
    } catch (error) {
      res.status(500).json({ message: 'Server error', error: error.message });
    }
  },

  // Delete a category
  deleteCategory: async (req, res) => {
    try {
      const { id } = req.params;

      // Find category
      const category = await PackageCategory.findOne({where : {id:req.params.id}});
      if (!category) {
        return res.status(404).json({ message: 'Category not found' });
      }

      // Delete category
      await category.destroy();
      res.json({ message: 'Category deleted successfully' });
    } catch (error) {
      res.status(500).json({ message: 'Server error', error: error.message });
    }
  },

  // Delete all categories
  deleteAllCategories: async (req, res) => {
    try {
      await PackageCategory.destroy({ where: {}, truncate: true });
      res.json({ message: 'All categories deleted successfully' });
    } catch (error) {
      res.status(500).json({ message: 'Server error', error: error.message });
    }
  }
};

module.exports = categoryController;
