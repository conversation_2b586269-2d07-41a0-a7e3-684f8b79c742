const { Journey, User, Order, Feedback } = require('../config/database-connection').models;
const { validationResult } = require('express-validator');
const { Op } = require('sequelize');
const sequelize = require('sequelize');
const sendEmail = require('../Notification-service');
const status = require('../models/status');
const { fn, col, } = require('sequelize');

const journeyController = {
  // Create a new journey
  create: async (req, res) => {
    try {
      // console.log(req.body)
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
      }

      // Handle file upload
      // if (!req.file) {
      //   return res.status(400).json({ message: "File is required." });
      // }

      const ticketImageUrl = req.file ? req.file.location : null;


      // Parse flight numbers JSON
      let flightNumbers = {};
      if (req.body.flight_numbers) {
        try {
          flightNumbers = JSON.parse(req.body.flight_numbers);
        } catch (error) {
          return res.status(400).json({ message: "Invalid flight_numbers format." });
        }
      }
      //  console.log("flight_numbers"+flightNumbers)
      // Ensure user_id comes from authentication middleware
      const user_id = req.user.id;
      if (!user_id) {
        return res.status(401).json({ message: "Unauthorized" });
      }
      const ExistingJourney = await Journey.findOne({
        where: {
          user_id: user_id,
          start_date: req.body.start_date,
          source: req.body.source,
          destination: req.body.destination,
          journey_status: { [Op.ne]: "Archived" }
        }
      });
      console.log(ExistingJourney)
      if (ExistingJourney) {
        res.status(409).json({ message: "you have already created journey for this date" });
        return;
      }
      const journey = await Journey.create({
        user_id,
        mode_of_transport: req.body.mode_of_transport,
        start_date: req.body.start_date,
        end_date: req.body.end_date,
        journey_status: "trip confirmed",
        source: req.body.source,
        destination: req.body.destination,
        departure_time: req.body.departure_time,
        arrival_time: req.body.arrival_time,
        flight_number: req.body.flight_number,
        boarding_point: req.body.boarding_point,
        de_boarding_point: req.body.de_boarding_point,
        ticket_image_url: ticketImageUrl,
        train_number: req.body.train_number,
        coach_number: req.body.coach_number,
        seat_number: req.body.seat_number,
        space_availability: req.body.space_availability,
        arrival_terminal_number: req.body.arrival_terminal_number,
        departure_terminal_number: req.body.departure_terminal_number
      });

      // return res.status(201).json(journey);
      // const [journey, created] = await Journey.findOrCreate({
      //   where: {
      //     user_id,
      //     journey_status:{[Op.ne]:"Archived"},
      //     start_date, 
      //   },
      //   defaults: {
      //     user_id,
      //   mode_of_transport: req.body.mode_of_transport,
      //   start_date: req.body.start_date,
      //   end_date: req.body.end_date,
      //   journey_status: req.body.journey_status,
      //   source: req.body.source,
      //   destination: req.body.destination,
      //   departure_time: req.body.departure_time,
      //   arrival_time: req.body.arrival_time,
      //   flight_number: req.body.flight_number,
      //   ticket_image_url:  req.file.location,
      //   train_number: req.body.train_number,
      //   coach_number: req.body.coach_number,
      //   seat_number: req.body.seat_number,
      //   },
      // });

      // if (!created) {
      //   throw new Error("A journey with the same user, status, and date already exists.");
      // }

      return res.status(201).json(journey);
    } catch (error) {
      console.error("Create journey error:", error);
      return res.status(500).json({ message: "Error creating journey" });
    }
  },

  // Get all journeys for a user
  // getAllUserJourneys: async (req, res) => {
  //   try {
  //     const page = parseInt(req.query.page) || 1;
  //     const limit = 5;
  //     const offset = (page - 1) * limit;

  //     const {rows:journeys,count} = await Journey.findAndCountAll({
  //       include: [
  //         {
  //           model: Order,
  //           as: 'orders',
  //           required: false,
  //           where: sequelize.and(
  //             sequelize.literal('"Journey"."order_id" IS NOT NULL'),
  //             { order_status: { [Op.ne]: 'order cancelled' } }
  //           ),
  //           include: [
  //             {
  //               model: User,
  //               attributes: ['username', 'phoneNumber']
  //             }
  //           ]
  //         }
  //       ],
  //       order: [['created_at', 'DESC']],
  //       limit,
  //       offset
  //     });
  //     if (!journeys || journeys.length === 0) {
  //       return res.status(404).json({ message: "No journeys was found" });
  //     }
  //     // console.log(users)
  //     const totalPage= Math.ceil(count/limit)

  //     return res.status(200).json({
  //       pagination:{
  //         page:page,
  //         totalPages:totalPage,
  //       },
  //       data:journeys
  //     });
  //   } catch (error) {
  //     console.error('Get journeys error:', error);
  //     return res.status(500).json({ message: 'Error fetching journeys' });
  //   }
  // },

  get: async (req, res) => {

    try {
      const id = req.params.id;
      const journeys = await Journey.findAll({
        where: { id: id },
        order: [['created_at', 'DESC']]
      });
      if (!journeys || journeys.length === 0) {
        return res.status(404).json({ message: "No journeys was found" });
      }
      // console.log(users)

      return res.status(200).json(journeys);
    } catch (error) {
      console.error('Get journeys error:', error);
      return res.status(500).json({ message: 'Error fetching journeys' });
    }
  },

  // Get a specific journey
  getJourney: async (req, res) => {
    try {
      const UserID = req.params.id;
      // Validate ID format (Prevent SQL injections or invalid queries)
      if (!UserID || isNaN(UserID)) {
        return res.status(400).json({ message: "Invalid journey ID" });
      }

      const page = parseInt(req.query.page) || 1;
      const limit = 10
      const offset = (page - 1) * limit;

      // Fetch journey based on its primary key (id)
      const { rows: journeys, count } = await Journey.findAndCountAll({
        where: {
          user_id: UserID,
          journey_status: { [Op.ne]: "Archived" }
        },
        include: [
          {
            model: Order,
            as: 'orders',
            required: false,
            where: sequelize.and(
              sequelize.literal('"Journey"."order_id" IS NOT NULL'),
              { order_status: { [Op.ne]: 'order cancelled' } }
            ),
            include: [
              {
                model: User,
                attributes: ['username', 'phoneNumber']
              }
            ]
          }
        ],
        limit,
        offset
      });


      // if (journeys.length === 0) {
      //     return res.status(204).json({ message: 'Journey not found' });
      // }

      // Fetch order and user details for each journey
      // const journeyWithDetails = await Promise.all(
      //     journeys.map(async (journey) => {
      //         const order = await Order.findOne({ where: { order_id: journey.order_id } });

      //         if (!order) {return { ...journey.toJSON(), order: null, user: null };}

      //         const user = await User.findOne({ where: { id: order.user_id } });
      //         return { ...journey.toJSON(),user: user ? user.toJSON() : null};
      //     })
      // );

      const totalPage = Math.ceil(count / limit)

      return res.status(200).json({
        pagination: {
          page,
          count,
          totalPage
        },
        data: journeys
      });
    } catch (error) {
      console.error('Get journey error:', error);
      return res.status(500).json({ message: 'Error fetching journey' });
    }
  },


  // Update a journey
  update: async (req, res) => {
    try {
      console.log("file", req.file)
      // Validate input fields
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
      }

      // Find the journey by ID and user
      const journey = await Journey.findOne({
        where: { id: req.params.id, user_id: req.user.id },
      });

      if (!journey) {
        return res.status(404).json({ message: 'Journey not found' });
      }

      // Handle file upload (image update)
      if (req.file) {
        if (journey.ticket_image_url) {
          await deleteFromS3(journey.ticket_image_url); // Delete old file
        }
        req.body.ticket_image_url = req.file.location; // Save new file URL
      } else {
        req.body.ticket_image_url = journey.ticket_image_url; // Keep existing image
      }

      // Update the journey details
      // console.log(req.body)
      // return;
      await journey.update(req.body);
      return res.status(200).json(journey);

    } catch (error) {
      console.error('Update journey error:', error);
      return res.status(500).json({ message: 'Error updating journey' });
    }
  },
  // Delete a journey
  delete: async (req, res) => {
    const transaction = await Journey.sequelize.transaction();

    try {
      const id = req.params.id
      const user = req.user.id
      // console.log("details",id,user)
      // return;
      // 1️⃣ Fetch journey + associated order
      const journey = await Journey.findOne({
        where: { id: id, user_id: user },
        include: [
          {
            model: Order,
            as: 'orders',
            include: [
              {
                model: User,
                attributes: ['email', 'username', 'phoneNumber']
              }
            ]
          }
        ],
        transaction,
      });

      if (!journey) {
        return res.status(404).json({ message: 'Journey not found' });
      }
      console.log("journey", journey.orders)
      // res.status(200).json(journey)
      // return;
      let message = 'Journey deleted successfully';
      let notificationMessage = '';
      console.log("message", message, notificationMessage)
      // 2️⃣ Check for reassignment only if journey has an order
      let alternativeJourney = null;
      if (journey.orders) {
        const { source, destination, start_date } = journey;

        alternativeJourney = await Journey.findOne({
          where: {
            id: { [Op.ne]: journey.id },
            source,
            destination,
            start_date,
            journey_status: "trip confirmed",
          },
          order: [['created_at', 'ASC']],
          transaction,
        });
        console.log("alternativeJourney", alternativeJourney)
        // return

        const hasAlternative = !!alternativeJourney;
        const newJourneyId = alternativeJourney?.id ?? null;
        console.log("newJourneyId", newJourneyId)

        // ✅ 3️⃣ Combine order update into single DB hit
        console.log("Trying to assign order to new journey", {
          newJourneyId: alternativeJourney?.id,
          order_id: journey?.order_id,
        });

        await Journey.sequelize.query(
          `
          UPDATE orders
          SET 
            journey_id = CASE WHEN :hasAlternative THEN :newJourneyId ELSE journey_id END,
            order_status = CASE WHEN :hasAlternative THEN 'order confirmed' ELSE 'trip cancelled' END
          WHERE order_id = :order_id
          `,
          {
            replacements: {
              hasAlternative,
              newJourneyId,
              order_id: journey.order_id,
            },
            transaction,
          }
        );

        // Send mail to client
        notificationMessage = hasAlternative
          // ? `Your order has been reassigned to a new trip traveler contact number ${journey.orders[0].User.phoneNumber} `
          ? `  Your order has been reassigned to a new trip. Please check the website for more details`
          : `Unfortunately, the journey associated with your order has been deleted, and we could not find a matching alternative for your destination. As a result, your order has been canceled We sincerely apologize for the inconvenience. Your payment will be refunded shortly`;
        if (journey.orders[0]) {
          sendEmail(journey.orders[0].User.email, 'Update Regarding Your Order', 'general', { username: journey.orders[0].User.username, purpose: "Update Regarding Your Order", details: notificationMessage });
        }

        message = hasAlternative
          ? 'Journey deleted, order reassigned'
          : 'Journey deleted, no matching journey found. Order cancelled.';
      }

      // return 

      // 5️⃣ Archive journey
      await Journey.update({
        order_id: null,
        journey_status: "Archived",
      }, {
        where: { id: journey.id },
        transaction,
      });

      await transaction.commit();
      return res.status(200).json({ message: 'Journey deleted successfully' });

    } catch (error) {
      await transaction.rollback();
      console.error('Error deleting journey:', error);
      return res.status(500).json({ message: 'Error deleting journey', error: error });
    }
  },

  //feedback for order 
  feedback: async (req, res) => {
    try {
      const { journeyId, feedback } = req.body
      const journey = await Journey.findOne({
        where: { id: journeyId, user_id: req.user.id },
      });
      if (!journey) {
        return res.status(404).json({ message: 'Journey not found' });
      }
      await journey.update({
        feedback: feedback,
      }, {
        where: { id: journey.id },
      });
      res.status(200).json({ message: 'Feedback added successfully' });
    } catch (error) {
      console.error('Error in journey feedback:', error);
      return res.status(500).json({ message: 'Error in journey feedback', Error: error });
    }
  },

  // arrived
  arrived: async (req, res) => {
    try {
      const { id } = req.params
      const journey = await Journey.findOne({
        where: { id: id, user_id: req.user.id },
        include: [
          {
            model: Order,
            as: 'orders',
            include: [
              {
                model: User,
                as: 'User', attributes: ['username', 'email', 'phoneNumber']
              }
            ]
          }
        ]
      });
      if (!journey) {
        return res.status(404).json({ message: 'Journey not found' });
      }
      await journey.update({ is_arrived: true });

      if (journey.order_id) {
        const mail = journey.orders[0].User?.email;
        const username = journey.orders[0].User?.username;
        sendEmail(mail, 'Update Regarding Your Order', 'general', { username: username, purpose: "Update Regarding Your Order", details: `The person on your journey has arrived at the location` });
      }

      res.status(200).json({ message: 'Status changed successfully' });
    } catch (error) {
      console.error('Error in journey arrived:', error);
      return res.status(500).json({ message: 'Error in journey arrived', Error: error });
    }
  },
  boarding: async (req, res) => {
    try {
      const { source, destination, mode, trainNumber, startDate } = req.query;

      // Validate incoming data
      if (!source || !destination || !mode || !trainNumber) {
        return res.status(400).json({ message: "Source, Destination,trainNumber and Mode are required" });
      }

      // Fetch journeys matching source, destination, and mode
      // const journeys = await Journey.findAll({
      //   where: {
      //     source,
      //     destination,
      //     train_number: trainNumber,
      //     mode_of_transport:mode,
      //     journey_status: "trip confirmed",
      //   },
      //   attributes: ['boarding_point', 'de_boarding_point'], // Only select boarding and deboarding fields
      // });
      const groupedBoarding = await Journey.findAll({
        where: {
          source,
          destination,
          train_number: trainNumber,
          mode_of_transport: mode,
          journey_status: "trip confirmed",
          start_date: startDate
        },
        attributes: [
          'boarding_point',
          [fn('COUNT', col('boarding_point')), 'boardingCount'],
        ],
        group: ['boarding_point'],
        raw: true,
      });

      // Group by deboarding
      const groupedDeboarding = await Journey.findAll({
        where: {
          source,
          destination,
          train_number: trainNumber,
          mode_of_transport: mode,
          journey_status: "trip confirmed",
        },
        attributes: [
          'de_boarding_point',
          [fn('COUNT', col('de_boarding_point')), 'deboardingCount'],
        ],
        group: ['de_boarding_point'],
        raw: true,
      });

      return res.status(200).json({
        message: "Grouped boarding and deboarding points",
        data: {
          boarding: groupedBoarding,
          deboarding: groupedDeboarding,
        }
      });
      // If no journeys found
    } catch (e) {
      console.error('Error in journey arrived:', e);
      return res.status(500).json({ message: 'Error in journey arrived', Error: e });
    }
  },
  // Search journeys with filters
  search: async (req, res) => {
    try {
      const {
        source,
        destination,
        start_date,
        modeOfTransport,
        departureTime,
        trainNumber,
        flightNumber,
        page = 1,
        limit = 10
      } = req.query;
      // Build where clause
      const whereClause = {};

      if (source) {
        whereClause.source = { [Op.iLike]: `%${source}%` };
      }

      if (destination) {
        whereClause.destination = { [Op.iLike]: `%${destination}%` };
      }

      if (start_date) {
        whereClause.start_date = start_date;
      }

      if (modeOfTransport) {
        whereClause.mode_of_transport = modeOfTransport;
      }

      // if (departureTime) {
      //   whereClause.departureTime = departureTime;
      // }

      // if (trainNumber) {
      //   whereClause.trainNumber = { [Op.iLike]: `%${trainNumber}%` };
      // }

      // if (flightNumber) {
      //   whereClause.flightNumber = { [Op.iLike]: `%${flightNumber}%` };
      // }

      // Calculate offset
      const offset = (page - 1) * limit;

      // // Get journeys with pagination
      // const { count, rows: journeys } = await Journey.findAndCountAll({
      //   where: whereClause,
      //   include: [{
      //     model: User,
      //     as: 'user',
      //     attributes: ['id', 'username', 'phoneNumber'] // Only include necessary user fields
      //   }],
      //   limit: parseInt(limit),
      //   offset: offset,
      //   order: [['created_at', 'DESC']]
      // });

      // Get facets for filters
      const journeys = await Journey.findAll({
        where: {
          ...whereClause,
          source: source && { [Op.iLike]: `%${source}%` } || { [Op.ne]: null }, // Ensure source is provided
          destination: destination && { [Op.iLike]: `%${destination}%` } || { [Op.ne]: null },
          start_date: start_date || { [Op.ne]: null },
          user_id: { [Op.ne]: req.user.id },
          journey_status: "trip confirmed",
        },
        attributes: [
          'source',
          'destination',
          'train_number',
          [sequelize.literal(`CAST("flight_number" AS TEXT)`), 'flight_number'],
          'arrival_time',
          'departure_time',
          'start_date',
          // 'boarding_point',
          // 'de_boarding_point',
          [sequelize.fn('COUNT', sequelize.col('*')), 'journey_count']
        ],
        group: ['source', 'destination', 'train_number', 'arrival_time', 'departure_time', 'start_date', sequelize.literal(`CAST("flight_number" AS TEXT)`),]
      })

      // Unique sources
      // Journey.findAll({
      //   where: whereClause,
      //   attributes: ['source'],
      //   group: ['source']
      // }),
      // // Unique destinations
      // Journey.findAll({
      //   where: whereClause,
      //   attributes: ['destination'],
      //   group: ['destination']
      // }),
      // // Mode of transport counts
      // Journey.findAll({
      //   where: whereClause,
      //   attributes: ['mode_of_transport', [sequelize.fn('COUNT', 'mode_of_transport'), 'count']],
      //   group: ['mode_of_transport']
      // }),
      // // Date range
      // Journey.findAll({
      //   where: whereClause,
      //   attributes: [
      //     [sequelize.fn('MIN', sequelize.col('start_date')), 'minDate'],
      //     [sequelize.fn('MAX', sequelize.col('end_date')), 'maxDate']
      //   ]
      // }),



      // Format response
      const response = {
        journeys,
        // facets,
        // pagination: {
        //   // total: count,
        //   page: parseInt(page),
        //   limit: parseInt(limit),
        //   totalPages: Math.ceil(count / limit)
        // },
        // facets: {
        //   sources: facets[0].map(f => f.source),
        //   destinations: facets[1].map(f => f.destination),
        //   modesOfTransport: facets[2].map(f => ({
        //     mode: f.modeOfTransport,
        //     count: f.get('count')
        //   })),
        //   dateRange: {
        //     min: facets[3][0].get('minDate'),
        //     max: facets[3][0].get('maxDate')
        //   }
        // }
      };

      return res.status(200).json(response);
    } catch (error) {
      console.error('Search journeys error:', error);
      return res.status(500).json({ message: 'Error searching journeys' });
    }
  },

  // Get all journeys for admin
  getAllUserJourneys: async (req, res) => {
    try {
      const {
        start_date,
        end_date,
        source,
        destination,
        status,
        mode,
        page = 1,
        limit = 5
      } = req.query;

      const whereClause = {};

      if (start_date) whereClause.start_date = new Date(start_date);
      if (end_date) whereClause.end_date = new Date(start_date);
      if (source) whereClause.source = source;
      if (destination) whereClause.destination = destination;
      if (status) whereClause.journey_status = status;
      if (mode) whereClause.mode_of_transport = mode;

      // Convert page and limit to integers
      const offset = (page - 1) * limit;
      // limit = parseInt(limit);

      console.log("whereClause", whereClause)
      // Fetch the paginated data
      const { rows: journeys, count } = await Journey.findAndCountAll({
        where: whereClause,
        include: [
          {
            model: Order,
            as: 'orders',
            required: false,
            where: sequelize.and(
              sequelize.literal('"Journey"."order_id" IS NOT NULL'),
              { order_status: { [Op.ne]: 'order cancelled' } }
            ),
            include: [
              {
                model: User,
                attributes: ['username', 'phoneNumber']
              }
            ]
          },
          {
            model: Feedback,
            as: 'feedback_entries', // use correct alias if defined in association
            required: false,
            on: {
              [Op.and]: [
                sequelize.where(sequelize.col('feedback_entries.journey_id'), '=', sequelize.col('Journey.id')),
                sequelize.where(sequelize.col('feedback_entries.created_by'), '=', sequelize.col('Journey.user_id'))
              ]
            }
          }
        ],
        order: [['created_at', 'DESC']],
        limit,
        offset
      });


      // Calculate total pages
      const totalPages = Math.ceil(count / limit);
      return res.status(200).json({
        pagination: {
          page: parseInt(page),
          totalPages: totalPages,
          totalJourneys: count,
        },
        data: journeys
      });

    } catch (err) {
      console.error('Error in journey filter:', err);
      return res.status(500).json({
        success: false,
        message: 'Internal server error',
        error: err.message
      });
    }
  }
};




module.exports = journeyController; 
