const { Order, OrderHistory, Journey, User, Feedback } = require('../config/database-connection').models;
const { sequelize } = require('../config/database-connection');
const { getRandomValue } = require('../utilss/Random');
const sendEmail = require('../Notification-service');
const { Op } = require('sequelize');



const orderController = {

  // Create a new order
  create: async (req, res) => {
    try {
      // console.log("body",req.body)
      const { package_category, price, order_description, payment_status, order_status, source, destination, train_number, flight_number, start_date, others, boarding_point, de_boarding_point } = req.body;

      // console.log("body",package_category, price, order_description, payment_status,order_status,source,destination,train_number,flight_number,start_date)
      const user_id = req.body.user_id; // Assuming authentication middleware sets req.user
      if (!user_id) {
        console.error("can not create without user_id ")
        return res.status(500).json({ message: "user_id is required" });
      }
      const query = { source: source, destination: destination, start_date: start_date, journey_status: 'trip confirmed' }

      if (train_number !== null) {
        query.train_number = train_number
        query.boarding_point = boarding_point
        query.de_boarding_point = de_boarding_point
      } else if (flight_number !== null) {
        let extractedFlightNumber;

        try {
          // If flight_number is a stringified object, parse it
          if (typeof flight_number === "string" && flight_number.startsWith("{")) {
            flight_number = JSON.parse(flight_number);
          }

          // Extract flight_number1 if it exists
          if (typeof flight_number === "object" && flight_number.flight_number1) {
            extractedFlightNumber = flight_number.flight_number1;
          } else if (typeof flight_number === "string") {
            extractedFlightNumber = flight_number;
          } else {
            console.error("Invalid flight_number format:", flight_number);
            extractedFlightNumber = null;
          }
        } catch (error) {
          console.error("Error parsing flight_number:", error);
          extractedFlightNumber = null;
        }

        // Ensure extractedFlightNumber is a valid number
        if (extractedFlightNumber && !isNaN(extractedFlightNumber)) {
          query.flight_number = Number(extractedFlightNumber);
          // console.log("Converted flight_number:", query.flight_number);
        } else {
          console.error("Invalid flight_number, cannot convert:", extractedFlightNumber);
        }
      }
      console.log(" Added query", query)
      const journeyId = await Journey.findAll({
        where: query,
        include: [
          {
            model: User,
            as: 'user',
            attributes: ['email', 'username', 'phoneNumber']
          }
        ]
      })
      console.log("journey details", journeyId)

      if (!journeyId || journeyId.length === 0) {
        console.error(`Error: No journey found for criteria: ${JSON.stringify(query)}`);
        return res.status(404).json({ message: "No Travelers are available for the selected Boarding and De_boarding points. Please try again later." });
      }
      // return;

      const journeysWithTicket = journeyId.filter((j) => j.ticket_image_url)
      const journeysWithoutTicket = journeyId.filter((j) => !j.ticket_image_url)

      const prioritizedJourneys = journeysWithTicket.length > 0 ? journeysWithTicket : journeysWithoutTicket

      const randomValue = getRandomValue(prioritizedJourneys.length)


      const id = await prioritizedJourneys[randomValue].dataValues.id
      const email = await prioritizedJourneys[randomValue].user.email
      const username = await prioritizedJourneys[randomValue].user.username
      const phone = await prioritizedJourneys[randomValue].user.phoneNumber
      console.log("details", id, email, username, phone)
      const newOrder = await Order.create({
        journey_id: id,
        user_id,
        package_category,
        price,
        order_status: "order confirmed",
        order_description,
        other_category: others,
        payment_status: "paid",
      });

      if (!newOrder) {
        console.error("Error: Failed to create new order.");
        return res.status(500).json({ message: "Order creation failed. Please try again later." });
      }

      const user = await User.findOne({ where: { id: newOrder.user_id } })

      console.log("user", user)

      sendEmail(email, "Order Assigned", 'general', { username: `${username}`, purpose: "Order Assigned", details: `Your Order is Assigned Please take order, for more details visit our website`, name: `${username}`, phone: `${phone}` },)

      sendEmail(req.user.email, "order confirmed", 'general', { username: `User`, purpose: "Order Confirmed", details: `Your order is confirmed, below is the details of the person who will be taking your order`, name: `${user.username}`, phone: `${user.phoneNumber}` },)


      await Journey.update(
        { order_id: newOrder.order_id, journey_status: "order confirmed" },
        { where: { id: id } }
      );
      return res.status(201).json({ message: 'Order created successfully' });
    } catch (error) {
      console.error('Create order error:', error);
      return res.status(500).json({ message: 'Error creating order' });
    }
  },

  // getOrders: async (req, res) => {
  //   try {
  //     const page = parseInt(req.query.page) || 1;
  //     const limit = 5;
  //     const offset = (page - 1) * limit;

  //     const {rows:orders, count} = await Order.findAndCountAll({
  //       include:[
  //         {
  //           model: Journey,
  //           as: 'Journey',
  //           include: [
  //             {
  //               model: User,
  //               as:'user',
  //               attributes: [ 'username', 'phoneNumber','email'] 
  //             }
  //           ]
  //         }
  //       ],
  //       order: [['created_at', 'DESC']],limit,offset });
  //     // return res.status(200).json(orders);
  //     const totalPage =Math.ceil(count/limit)
  //     return res.status(200).json({data:orders,
  //       pagination:{
  //         page:page,
  //         count:totalPage
  //       }
  //     });
  //   } catch (error) {
  //     console.error('Get orders error:', error);
  //     return res.status(500).json({ message: 'Error fetching orders' });
  //   }
  // },

  getOrdersByUser: async (req, res) => {
    try {
      const user = req.params.id
      const page = parseInt(req.query.page) || 1;
      const limit = 10
      const offset = (page - 1) * limit;

      const { rows: orders, count } = await Order.findAndCountAll({
        where: { user_id: user, order_status: { [Op.ne]: 'trip cancelled' && 'order cancelled' } },
        order: [['created_at', 'DESC']],
        include: [
          {
            model: Journey,
            as: 'Journey',
            include: [
              {
                model: User,
                as: 'user',
                attributes: ['username', 'phoneNumber', 'email']
              }
            ]
          }
        ],
        limit,
        offset
      });

      const totalPage = Math.ceil(count / limit)
      // return res.status(200).json(orders);
      return res.status(200).json({
        pagination: {
          page,
          count,
          totalPage: totalPage
        },
        data: orders
      });
    } catch (error) {
      console.error('Get orders error:', error);
      return res.status(500).json({ message: 'Error fetching orders' });
    }
  },

  // Update an order
  update: async (req, res) => {
    try {
      const { order_id } = req.params;
      const { price, order_description } = req.body;
      const order = await Order.findByPk(order_id);

      if (!order) {
        return res.status(404).json({ message: 'Order not found' });
      }

      await order.update({ price, order_description });
      return res.status(200).json({ message: 'Order updated successfully' });
    } catch (error) {
      console.error('Update order error:', error);
      return res.status(500).json({ message: 'Error updating order' });
    }
  },

  // Change order status
  changeStatus: async (req, res) => {
    try {
      const { order_id } = req.params;
      const { new_status } = req.body;
      const changed_by = req.user.id; // Assuming authentication middleware

      const order = await Order.findByPk(order_id);
      if (!order) {
        return res.status(404).json({ message: 'Order not found' });
      }

      const previous_status = order.order_status;
      await order.update({ order_status: new_status });

      await OrderHistory.create({
        order_id,
        previous_status,
        new_status,
        changed_by
      });

      return res.status(200).json({ message: 'Order status updated successfully' });
    } catch (error) {
      console.error('Change order status error:', error);
      return res.status(500).json({ message: 'Error updating order status' });
    }
  },

  // feedback
  feedback: async (req, res) => {
    try {
      const { orderId, feedback } = req.body
      const order = await Order.findOne({
        where: { order_id: orderId },
      });
      if (!order) {
        return res.status(404).json({ message: 'order not found' });
      }
      await Order.update({
        feedback: feedback,
      }, {
        where: { order_id: order.order_id },
      });
      res.status(200).json({ message: 'Feedback added successfully' });
    } catch (error) {
      console.error('Error in order feedback:', error);
      return res.status(500).json({ message: 'Error in order feedback', Error: error });
    }
  },

  // Delete an order
  delete: async (req, res) => {
    try {
      const { order_id } = req.params;
      console.log("order_id", order_id)

      const order = await Order.findOne({
        where: {
          order_id: order_id,
          // user_id: req.user.id, // uncomment if needed
        },
        include: [
          {
            model: Journey,
            as: 'Journey',
            include: [
              {
                model: User,
                as: 'user',
                attributes: ['username', 'phoneNumber', 'email']
              }
            ]
          }
        ]
      });
      if (!order) {
        return res.status(404).json({ message: 'Order not found' });
      }
      // console.log("order",order)
      // return;
      if (order.journey_id !== null) {
        await Journey.update(
          { order_id: null, journey_status: "trip confirmed" },
          { where: { id: order.journey_id } }
        );
      }

      // await order.destroy();
      const mail = order.Journey.user.email;
      sendEmail(mail, "order Cancelled", 'general', { username: `User`, purpose: "Order Cancelled Notification", details: `We would like to inform you that the order previously assigned to you has been cancelled by the user. If you have any questions or concerns, please contact support.`, });

      await Order.update({ order_status: 'order cancelled' }, { where: { order_id: order_id } })
      return res.status(200).json({ message: 'Order deleted successfully' });
    } catch (error) {
      console.error('Delete order error:', error);
      return res.status(500).json({ message: 'Error deleting order' });
    }
  },

  // Get all orders for admin
  getOrders: async (req, res) => {
    try {
      const {
        start_date,
        end_date,
        source,
        destination,
        status,
        page = 1,
        limit = 5
      } = req.query;

      const parsedPage = parseInt(page);
      const parsedLimit = parseInt(limit);
      const offset = (parsedPage - 1) * parsedLimit;

      const whereClause = {};

      if (start_date) whereClause.start_date = new Date(start_date);
      if (end_date) whereClause.end_date = new Date(end_date);
      if (source) whereClause.source = source;
      if (destination) whereClause.destination = destination;
      if (status) whereClause.order_status = status;

      const { rows: orders, count } = await Order.findAndCountAll({
        where: whereClause,
        include: [
          {
            model: Journey,
            as: 'Journey',
            include: [
              {
                model: User,
                as: 'user',
                attributes: ['username', 'phoneNumber', 'email']
              }
            ]
          },
          {
            model: Feedback,
            as: 'feedback_entries', // use correct alias if defined in association
            required: false,
            on: {
              [Op.and]: [
                sequelize.where(sequelize.col('feedback_entries.order_id'), '=', sequelize.col('Order.order_id')),
                sequelize.where(sequelize.col('feedback_entries.created_by'), '=', sequelize.col('Order.user_id'))
              ]
            }
          }
        ],
        order: [['created_at', 'DESC']],
        limit: parsedLimit,
        offset
      });
      const totalPages = Math.ceil(count / parsedLimit);
      return res.status(200).json({
        pagination: {
          page: parsedPage,
          totalPages,
          totalOrders: count
        },
        data: orders
      });

    } catch (err) {
      console.error('Error filtering orders:', err);
      return res.status(500).json({
        success: false,
        message: 'Internal server error',
        error: err.message
      });
    }
  },
  AdminDetails: async (req, res) => {
    try {
      // Make sure you're using the sequelize instance, not the class
      const [results] = await sequelize.query(`
        SELECT
          COUNT(CASE WHEN mode_of_transport = 'train' THEN 1 END) AS train_count,
          COUNT(CASE WHEN mode_of_transport = 'flight' THEN 1 END) AS flight_count,
          COUNT(CASE WHEN mode_of_transport = 'flight-inter' THEN 1 END) AS flight_inter_count,
          COUNT(CASE WHEN journey_status = 'trip confirmed' THEN 1 END) AS active_status_count
        FROM "journeys"
      `);

      // Get counts from models
      const userCount = await User.count();
      const journeyCount = await Journey.count();
      const orderCount = await Order.count();

      return res.status(200).json({
        success: true,
        details: {
          userCount,
          journeyCount,
          orderCount,
          journeyStats: results[0]
        }
      });
    } catch (err) {
      console.error('Error in admin details:', err);
      return res.status(500).json({
        success: false,
        message: 'Internal server error',
        error: err.message
      });
    }
  }

};



module.exports = orderController;
