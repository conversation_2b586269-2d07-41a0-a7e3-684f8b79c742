const { validationResult } = require('express-validator');
// const db = require('../models');
const {FeedbackMaster} = require('../config/database-connection').models
const dotenv = require('dotenv');
const { where } = require('sequelize');

dotenv.config();

const feedbackMasterController = {
  // Create a new feedback
  create: async (req, res) => {
    try {
        const { title, user_type } = req.body;
    
        const newFeedbackMaster = await FeedbackMaster.create({ title, user_type });
        return res.status(201).json(newFeedbackMaster);
      } catch (error) {
        console.error('Create error:', error);
        return res.status(500).json({ error: 'Failed to create feedback master' });
      }
  },

  getAll: async (req, res) => {
    try {
        const feedbackList = await FeedbackMaster.findAll();
        return res.status(200).json(feedbackList);
      } catch (error) {
        console.error('Fetch all error:', error);
        return res.status(500).json({ error: 'Failed to fetch feedback master list' });
      }
  },

  // Get feedback by user_type
  getByUserType: async (req, res) => {
    try {
        const { user_type } = req.params;
  
        if (!user_type || !['journey', 'order'].includes(user_type)) {
          return res.status(400).json({ error: 'Invalid or missing user_type' });
        }
  
        const feedbackList = await FeedbackMaster.findAll({
          where: { user_type: user_type }
        });
  
        return res.status(200).json(feedbackList);
      } catch (error) {
        console.error('Fetch by user_type error:', error);
        return res.status(500).json({ error: 'Failed to fetch feedback master by user_type' });
      }
  },

  // Update a feedback
  update: async (req, res) => {
    try {
        const { id } = req.params;
        const { title, user_type } = req.body;
    
        const entry = await FeedbackMaster.findByPk(id);
        if (!entry) return res.status(404).json({ error: 'Feedback master not found' });
    
        entry.title = title ?? entry.title;
        entry.user_type = user_type ?? entry.user_type;
    
        await entry.save();
    
        return res.status(200).json(entry);
      } catch (error) {
        console.error('Update error:', error);
        return res.status(500).json({ error: 'Failed to update feedback master' });
      }
  },

  // Delete a feedback
  delete: async (req, res) => {
    try {
        const { id } = req.params;
        const entry = await FeedbackMaster.findByPk(id);
        if (!entry) return res.status(404).json({ error: 'Feedback master not found' });
    
        await entry.destroy();
    
        return res.status(200).json({ message: 'Feedback master deleted successfully' });
      } catch (error) {
        console.error('Delete error:', error);
        return res.status(500).json({ error: 'Failed to delete feedback master' });
      }
  },

  // Delete all categories
  deleteAll: async (req, res) => {
    try {
      await FeedbackMaster.destroy({ where: {}, truncate: true });
      res.json({ message: 'All categories deleted successfully' });
    } catch (error) {
      res.status(500).json({ message: 'Server error', error: error.message });
    }
  },

 
};

module.exports = feedbackMasterController;
