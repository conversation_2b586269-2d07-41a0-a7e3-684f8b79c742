const jwt = require('jsonwebtoken');
const bcrypt = require('bcrypt');
const { validationResult } = require('express-validator');
const { Op } = require('sequelize');
// const db = require('../models');
// const User = db.User;
const { User, Otp } = require('../config/database-connection').models
const dotenv = require('dotenv');
const { generateOtp } = require('../utilss/otp');
const sendEmail = require('../Notification-service');
const otpStore = require('../utilss/otpStore');

dotenv.config();

const otpGeneration = async (email, type, username) => {
  const { otp, expiry } = generateOtp(3)
  console.log("user", email, type, username)
  try {
    // Store new OTP in the database
    await Otp.upsert({
      email,
      otp,
      expiry_time: new Date(expiry),
      type,
      timestamp: new Date() // Use timestamp instead of createdAt/updatedAt
    }, {
      where: {
        email,
        type
      }
    });

    await sendEmail(email, "Regarding Your Request", 'otp', { 
      username: username?`${username}` : "User", 
      type: `${type}`, 
      otp: `${otp}` 
    });
    return { success: true, otp, expiry };
  } catch (error) {
    console.error('Error in getMail:', error);
    return { success: false, error };
  }
}; 


const authController = {

  getAllUser: async (req, res) => {
    try {
      const users = await User.findAll();
      res.status(200).json(users);
    } catch (error) {
      console.error('Error in getAll-user:', error);
      return res.status(500).json({ message: 'Server error' });
    }
  },

  // Register new user
  register: async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
      }

      const { username, email, phoneNumber, password } = req.body;

      // Check if user already exists
      // console.log("email:",email)
      const existingUser = await User.findOne({
        where: {
          [Op.or]: [{ email }],
        },
      });

      if (existingUser) {
        const conflictField = existingUser.email === email ? "Email" : "Username";
        res.status(409).json({ message: `${conflictField} already exists` });
        return;
      }

      // Hash password before creating user
      const hashedPassword = await bcrypt.hash(password, 10);

      // Create new user with hashed password
      const user = await User.create({
        username,
        email,
        phoneNumber,
        password: hashedPassword
      });

      // Generate JWT token
      const token = jwt.sign(
        { id: user.id, email: user.email },
        process.env.JWT_SECRET,
        { expiresIn: '24h' }
      );

      res.status(201).json({
        message: 'User registered successfully',
        token,
        user: {
          id: user.id,
          username: user.username,
          email: user.email,
          phoneNumber: user.phoneNumber
        }
      });
    } catch (error) {
      console.log("error while registering user", error);
      res.status(500).json({ message: 'Server error', error: error.message });
    }
  },

  // Login user
  login: async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
      }

      const { email, password } = req.body;

      // Find user
      const user = await User.findOne({ where: { email } });
      if (!user) {
        return res.status(401).json({ message: 'Invalid credentials' });
      }

      // Validate password
      const isValidPassword = await bcrypt.compare(password, user.password);
      if (!isValidPassword) {
        return res.status(401).json({ message: 'Invalid credentials' });
      }

      // Update last login
      await user.update({ lastLogin: new Date() });

      // Generate JWT token
      const token = jwt.sign(
        { id: user.id, email: user.email },
        process.env.JWT_SECRET,
        { expiresIn: '24h' }
      );

      res.json({
        message: 'Login successful',
        token,
        user: {
          id: user.id,
          email: user.email,
          username: user.username,
          role: user.role
        }
      });
    } catch (error) {
      console.error('Login error:', error);
      res.status(500).json({ message: 'Server error', error: error.message });
    }
  },

  // Get current user
  getCurrentUser: async (req, res) => {
    try {
      const user = await User.findByPk(req.user.id, {
        attributes: { exclude: ['password'] }
      });

      if (!user) {
        return res.status(404).json({ message: 'User not found' });
      }

      res.json(user);
    } catch (error) {
      res.status(500).json({ message: 'Server error', error: error.message });
    }
  },

  verifyEmail: async (req, res) => {
    try {
      const { userId, verificationCode } = req.body;

      // Add your verification code validation logic here

      const user = await User.findByPk(userId);
      if (!user) {
        return res.status(404).json({ message: 'User not found' });
      }

      user.isEmailVerified = true;
      user.emailVerifiedAt = new Date();
      await user.save();

      return res.status(200).json({ message: 'Email verified successfully' });
    } catch (error) {
      console.error('Email verification error:', error);
      return res.status(500).json({ message: 'Error verifying email' });
    }
  },

  verifyPhone: async (req, res) => {
    try {
      const { userId, verificationCode } = req.body;

      // Add your verification code validation logic here

      const user = await User.findByPk(userId);
      if (!user) {
        return res.status(404).json({ message: 'User not found' });
      }

      user.isPhoneVerified = true;
      user.phoneVerifiedAt = new Date();
      await user.save();

      return res.status(200).json({ message: 'Phone number verified successfully' });
    } catch (error) {
      console.error('Phone verification error:', error);
      return res.status(500).json({ message: 'Error verifying phone number' });
    }
  },

  otpGen: async (req, res) => {
    try {
      const { email, type } = req.body
      if (!email) {
        return res.status(400).json({ message: "Email is required" })
      }

      if (!type) {
        return res.status(400).json({ message: "Type  is required" })
      }

      const EXistMail = await User.findOne({ where: { email: email } })
      if (!EXistMail) {
        return res.status(400).json({ message: "Email does not exist please try other email" })
      }

      console.log("user", email, type, EXistMail.username)

      const result = await otpGeneration(email, type, EXistMail.username);
      if (result.success) {
        return res.status(200).json({ message: "OTP sent successfully" })
      } else {
        return res.status(500).json({ message: "OTP generation failed" })
      }
    } catch (error) {
      console.error('Error in getMail:', error);
      return res.status(500).json({ message: 'Server error' });
    }
  },

  otGenerationRegister: async (req, res) => {
    try {
      const { email, type } = req.body
      if (!email) {
        return res.status(400).json({ message: "Email is required" })
      }

      if (!type) {
        return res.status(400).json({ message: "Type  is required" })
      }

      const EXistMail = await User.findOne({ where: { email: email } })
      if (EXistMail) {
        return res.status(400).json({ message: "Email already exist please try other email" })
      }

      console.log("user", email, type)

      const result = await otpGeneration(email, type);
      if (result.success) {
        return res.status(200).json({ message: "OTP sent successfully" })
      } else {
        return res.status(500).json({ message: "OTP generation failed" })
      }
    } catch (error) {
      console.error('Error in getMail:', error);
      return res.status(500).json({ message: 'Server error' });
    }
  },

  otpVerifyMail: async (req, res) => {
    try {
      const { email, otp } = req.body;

      if (!email || !otp) {
        return res.status(400).json({ message: "Email and OTP are required" })
      }
      console.log("email and otp is:", email, otp)

      // const record = otpStore[email];
      // const record = await Otp.findOne({ where: { email: email } })
      const record = await Otp.findOne({
        where: { email: email },
        order: [['createdAt', 'DESC']]
      });
      // console.log("record",record)
      if (!record) {
        return res.status(400).json({ message: "OTP expired" })
      }
      // console.log("otp",record.otp)

      if (record.otp !== otp) {
        return res.status(401).json({ message: "Invalid OTP" })
      }

      if (record.expiry < Date.now()) {
        await Otp.destroy({ where: { email: email } });
        res.status(400).json({ message: "OTP expired" })
        return;
      }

      res.status(200).json({ message: "OTP verified successfully" })
       Otp.destroy({ where: { email: email } });
    }catch(error){
      console.error('Error in otpVerifyMail:', error);
      return res.status(500).json({ message: 'Server error' });
    }
  },

  otpVerify: async (req, res) => {
    try {
      const { mail, otp, password } = req.body;

      if (!mail || !otp || !password) {
        return res.status(400).json({ message: "Email and OTP are required" })
      }

      const record = await Otp.findOne({
        where: { email: mail },
        order: [['createdAt', 'DESC']]
      });
      console.log("record", record)
      if (!record) {
        return res.status(400).json({ message: "OTP expired" })
      }

    if (record.otp !== otp) {
      return res.status(401).json({ message: "Invalid OTP" })
    }

      if (record.expiry_time < Date.now()) {
        return res.status(400).json({ message: "OTP expired" })
      }

      const hashedPassword = await bcrypt.hash(password, 10);
      const user = await User.findOne({ where: { email: mail } });
      if (!user) {
        return res.status(400).json({ message: "User not found" })
      }

   const updatedUser = await User.update({ password: hashedPassword },{where:{email:mail}});
   if (updatedUser[0] === 0) {
    return res.status(500).json({ message: "Failed to update password" });
  }
    res.status(200).json({ message: "Password updated successfully" })
     Otp.destroy({ where: { email: mail } });
}catch(error){
  console.error("Error in otpVerify:", error);
  return res.status(500).json({ message: "Server error" });
}
  }
};

module.exports = authController; 
