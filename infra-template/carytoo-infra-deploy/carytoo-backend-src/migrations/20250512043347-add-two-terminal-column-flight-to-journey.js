'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    /**
     * Add altering commands here.
     *
     * Example:
     * await queryInterface.createTable('users', { id: Sequelize.INTEGER });
     */
    await queryInterface.addColumn('journeys','arrival_terminal_number',{
      type:Sequelize.STRING,
      allowNull:true
    })

      await queryInterface.addColumn('journeys','departure_terminal_number',{
      type:Sequelize.STRING,
      allowNull:true
    })
    await queryInterface.removeColumn('journeys','terminal_number')
  },

  async down (queryInterface, Sequelize) {
    /**
     * Add reverting commands here.
     *
     * Example:
     * await queryInterface.dropTable('users');
     */
    await queryInterface.removeColumn('journeys','arrival_terminal_number')
    await queryInterface.removeColumn('journeys','departure_terminal_number')
    await queryInterface.addColumn('journeys','terminal_number',{
      type:Sequelize.STRING,
      allowNull:true
    })
  }
};
