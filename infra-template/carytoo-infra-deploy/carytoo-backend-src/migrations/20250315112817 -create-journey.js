'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('journeys', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      user_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'Users',
          key: 'id'
        }
      },
      start_date: {
        type: Sequelize.DATEONLY,
        allowNull: false
      },
      end_date: {
        type: Sequelize.DATEONLY,
        allowNull: false
      },
      source: {
        type: Sequelize.STRING,
        allowNull: false
      },
      destination: {
        type: Sequelize.STRING,
        allowNull: false
      },
      mode_of_transport: {
        type: Sequelize.ENUM('train', 'flight', 'bus', 'car'),
        allowNull: false
      },
      train_number: {
        type: Sequelize.STRING,
        allowNull: true
      },
      coach_number: {
        type: Sequelize.STRING,
        allowNull: true
      },
      seat_number: {
        type: Sequelize.STRING,
        allowNull: true
      },
      flight_number: {
        type: Sequelize.JSON,
        allowNull: true
      },
      departure_time: {
        type: Sequelize.TIME,
        allowNull: false
      },
      arrival_time: {
        type: Sequelize.TIME,
        allowNull: false
      },
      created_at: {
        allowNull: false,
        type: Sequelize.DATE
      },
      updated_at: {
        allowNull: false,
        type: Sequelize.DATE
      },
      journey_status:{
        type:Sequelize.STRING(60),
        allowNull:false,
      },
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('journeys');
  }
}; 