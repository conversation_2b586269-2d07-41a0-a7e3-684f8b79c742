'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    /**
     * Add altering commands here.
     *
     * Example:
     * await queryInterface.createTable('users', { id: Sequelize.INTEGER });
     */
    await queryInterface.addColumn('journeys','terminal_number',{
      type:Sequelize.INTEGER,
      allowNull:true,
    })
  },

  async down (queryInterface, Sequelize) {
    /**
     * Add reverting commands here.
     *1
     * Example:
     * await queryInterface.dropTable('users');
     */  
    await queryInterface.changeColumn('journeys', 'terminal_number', {
      type: Sequelize.INTEGER,
      allowNull: true, // or false depending on original
    });
  }
};
