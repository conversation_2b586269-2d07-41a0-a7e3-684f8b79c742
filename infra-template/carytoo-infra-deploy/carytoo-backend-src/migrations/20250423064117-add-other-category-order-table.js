'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    /**
     * Add altering commands here.
     *
     * Example:
     * await queryInterface.createTable('users', { id: Sequelize.INTEGER });
     */
    await queryInterface.addColumn('orders', 'other_category', {
      type: Sequelize.STRING,
      allowNull: true
    });
    await queryInterface.addColumn('orders', 'is_arrived', {
      type: Sequelize.STRING(10),
      allowNull: true,
      defaultValue: 'false'
    });
  },

  async down (queryInterface, Sequelize) {
    /**
     * Add reverting commands here.
     *
     * Example:
     * await queryInterface.dropTable('users');
     */
    await queryInterface.removeColumn('orders', 'other_category');
    await queryInterface.removeColumn('orders', 'is_arrived');
  }
};
