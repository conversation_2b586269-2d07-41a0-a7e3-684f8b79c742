'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    /**
     * Add altering commands here.
     *
     * Example:
     * await queryInterface.createTable('users', { id: Sequelize.INTEGER });
     */
    await queryInterface.addColumn('journeys', 'ticket_image_url', {
      type: Sequelize.STRING(2058),
      allowNull: true,
    });

    await queryInterface.addColumn('journeys', 'order_id', {
        type:Sequelize.INTEGER,
        allowNull:true,
        references: {
          model: 'orders', 
          key: 'order_id'
        }
    });
  },

  async down (queryInterface, Sequelize) {
    /**
     * Add reverting commands here.
     *
     * Example:
     * await queryInterface.dropTable('users');
     */
    await queryInterface.removeColumn('journeys', 'ticket_image_url');
    await queryInterface.removeColumn('journeys', 'order_id');
  }
};
