'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    /**
     * Add altering commands here.
     *
     * Example:
     * await queryInterface.createTable('users', { id: Sequelize.INTEGER });
     */
    await queryInterface.addColumn('journeys', 'boarding_point', {
      type: Sequelize.STRING,
      allowNull: true,
    });

    await queryInterface.addColumn('journeys', 'de_boarding_point', {
      type: Sequelize.STRING,
      allowNull: true,
    });

    await queryInterface.addColumn('journeys', 'is_arrived', {
      type: Sequelize.STRING(10),
      allowNull: true,
      defaultValue: 'false'
    });
  },

  async down (queryInterface, Sequelize) {
    /**
     * Add reverting commands here.
     *
     * Example:
     * await queryInterface.dropTable('users');
     */
    await queryInterface.removeColumn('journeys', 'boarding_point');
    await queryInterface.removeColumn('journeys', 'de_boarding_point');
    await queryInterface.removeColumn('journeys', 'is_arrived');
  }
};
