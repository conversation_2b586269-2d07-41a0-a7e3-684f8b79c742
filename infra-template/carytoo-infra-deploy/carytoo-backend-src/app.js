const express = require('express');
const { connectDb } = require('./config/database-connection');
const rateLimit = require('express-rate-limit');

const cors = require('cors')
require('dotenv').config();

// Import routes
const appRouter = require('./routes/index')
// const presignRouter = require('./routes/preSign')
const app = express();

app.set('trust proxy', true);
app.enable('trust proxy'); // Enable if you're behind a reverse proxy (Heroku, Nginx, etc)

// Add this middleware to log rate limit info
app.use((req, res, next) => {
  const ip = req.ip || 
             req.connection.remoteAddress || 
             req.socket.remoteAddress || 
             req.connection.socket.remoteAddress;
  console.log('Request IP:', ip);
  console.log('Headers:', req.headers);
  next();
});

// Configure rate limiters
const globalLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  limit: 100, // Limit each IP to 100 requests per windowMs
  message: {
    error: {
      message: 'Too many requests, please try again later.',
      status: 429
    }
  },
  standardHeaders: true, // Return rate limit info in the `RateLimit-*` headers
  legacyHeaders: false, // Disable the `X-RateLimit-*` headers
});



// Apply global rate limiter to all routes
// app.use(globalLimiter);


// app.use(cors())
const whitelist = process.env.FRONTEND_URL.split(',');


app.use(cors({
  origin: function (origin, callback) {
    if (!origin || whitelist.includes(origin)) {
      callback(null, true);
    } else {
      callback(new Error('Not allowed by CORS'));
    }
  },
  methods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'options'],
  credentials: true
}));
// app.options('*', cors())

// app.use(cors({
//   origin: function (origin, callback){
//     if(!origin || whitelist.includes(origin)){
//       callback(null, true)
//       console.log("whitelist",whitelist)
//     }else{
//       callback(new Error('Not allowed by CORS'))
//       // console.log("whitelist",whitelist)
//     }
//   },
//   methods:['GET','POST','PUT','DELETE','options'],
//   credentials: true
// }))

app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Test database connection
// sequelize.authenticate()
//   .then(() => {
//     console.log('Database connection has been established successfully.');
//   })
//   .catch(err => {
//     console.error('Unable to connect to the database:', err);
//   });

// // Apply stricter rate limiting to auth routes
// app.use('/api/auth/login', authLimiter);
// app.use('/api/auth/register', authLimiter);
// app.use('/api/auth/get-mail', authLimiter);
// app.use('/api/auth/get-mail-reg', authLimiter);


connectDb();

// sequelize.sync({force:true}).then(()=>console.log("All tables are synced"))

// Routes

app.use('/api', appRouter);
// app.use('/api/presign',presignRouter)

// Basic route
app.get('/', (req, res) => {
  res.json({ message: 'Welcome to the Express PostgreSQL application.' });
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error('Error:', err.stack);
  console.error('Error Message:', err.message);
  console.error('Error Name:', err.name);

  res.status(err.status || 500).json({
    error: {
      message: err.message || 'Internal Server Error',
      status: err.status || 500
    }
  });
});

const PORT = process.env.PORT || 3000;
app.listen(PORT, () => {
  console.log(`Server is running on port ${PORT}.`);
}); 
