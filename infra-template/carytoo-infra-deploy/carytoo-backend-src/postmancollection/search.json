{"info": {"_postman_id": "your-generated-id", "name": "Journey Search API", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Search Journeys", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/journeys/search?source=New&destination=Los&date=2024-03-20&modeOfTransport=train&page=1&limit=10", "host": ["{{baseUrl}}"], "path": ["api", "journeys", "search"], "query": [{"key": "source", "value": "New"}, {"key": "destination", "value": "Los"}, {"key": "date", "value": "2024-03-20"}, {"key": "modeOfTransport", "value": "train"}, {"key": "page", "value": "1"}, {"key": "limit", "value": "10"}]}, "description": "Search journeys with filters and facets"}, "response": [{"name": "Success Response", "originalRequest": {"method": "GET", "header": []}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [], "cookie": [], "body": {"journeys": [{"id": 1, "date": "2024-03-20", "source": "New York", "destination": "Los Angeles", "modeOfTransport": "train", "trainNumber": "TR123", "departureTime": "09:00", "arrivalTime": "18:30", "user": {"id": 1, "username": "john_doe", "email": "<EMAIL>"}}], "pagination": {"total": 1, "page": 1, "limit": 10, "totalPages": 1}, "facets": {"sources": ["New York", "Newark"], "destinations": ["Los Angeles", "Las Vegas"], "modesOfTransport": [{"mode": "train", "count": 5}, {"mode": "flight", "count": 3}], "dateRange": {"min": "2024-03-20", "max": "2024-04-20"}}}}]}]}