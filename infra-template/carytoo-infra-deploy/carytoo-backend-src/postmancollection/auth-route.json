{"info": {"name": "Authentication API", "description": "Collection for authentication endpoints", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Register User", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/register", "host": ["{{base_url}}"], "path": ["register"]}, "body": {"mode": "raw", "raw": "{\n    \"username\": \"testuser\",\n    \"email\": \"<EMAIL>\",\n    \"password\": \"password123\",\n    \"phoneNumber\": \"+1234567890\"\n}"}, "description": "Register a new user with username, email, password, and phone number"}}, {"name": "Login User", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/login", "host": ["{{base_url}}"], "path": ["login"]}, "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"password\": \"password123\"\n}"}, "description": "Login with email and password"}}, {"name": "Get Current User", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/me", "host": ["{{base_url}}"], "path": ["me"]}, "description": "Get current user details (Protected route - requires authentication)"}}], "variable": [{"key": "base_url", "value": "http://localhost:3000", "type": "string"}, {"key": "auth_token", "value": "your_jwt_token_here", "type": "string"}]}