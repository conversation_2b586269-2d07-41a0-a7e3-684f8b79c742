{"info": {"_postman_id": "your-generated-id", "name": "Journey Management API", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Create Journey", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": "{\n    \"date\": \"2024-03-20\",\n    \"source\": \"New York\",\n    \"destination\": \"Los Angeles\",\n    \"modeOfTransport\": \"train\",\n    \"trainNumber\": \"TR123\",\n    \"coachNumber\": \"C4\",\n    \"seatNumber\": \"12A\",\n    \"departureTime\": \"09:00\",\n    \"arrivalTime\": \"18:30\"\n}"}, "url": {"raw": "{{baseUrl}}/api/journeys", "host": ["{{baseUrl}}"], "path": ["api", "journeys"]}, "description": "Create a new journey"}, "response": [{"name": "Success Response", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"date\": \"2024-03-20\",\n    \"source\": \"New York\",\n    \"destination\": \"Los Angeles\",\n    \"modeOfTransport\": \"train\",\n    \"trainNumber\": \"TR123\",\n    \"coachNumber\": \"C4\",\n    \"seatNumber\": \"12A\",\n    \"departureTime\": \"09:00\",\n    \"arrivalTime\": \"18:30\"\n}"}}, "status": "Created", "code": 201, "_postman_previewlanguage": "json", "header": [], "cookie": [], "body": "{\n    \"id\": 1,\n    \"userId\": 1,\n    \"date\": \"2024-03-20\",\n    \"source\": \"New York\",\n    \"destination\": \"Los Angeles\",\n    \"modeOfTransport\": \"train\",\n    \"trainNumber\": \"TR123\",\n    \"coachNumber\": \"C4\",\n    \"seatNumber\": \"12A\",\n    \"departureTime\": \"09:00\",\n    \"arrivalTime\": \"18:30\",\n    \"updatedAt\": \"2024-03-19T12:00:00.000Z\",\n    \"createdAt\": \"2024-03-19T12:00:00.000Z\"\n}"}]}, {"name": "Get All User Journeys", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/journeys", "host": ["{{baseUrl}}"], "path": ["api", "journeys"]}, "description": "Get all journeys for the authenticated user"}, "response": [{"name": "Success Response", "originalRequest": {"method": "GET", "header": []}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [], "cookie": [], "body": "[\n    {\n        \"id\": 1,\n        \"userId\": 1,\n        \"date\": \"2024-03-20\",\n        \"source\": \"New York\",\n        \"destination\": \"Los Angeles\",\n        \"modeOfTransport\": \"train\",\n        \"trainNumber\": \"TR123\",\n        \"coachNumber\": \"C4\",\n        \"seatNumber\": \"12A\",\n        \"departureTime\": \"09:00\",\n        \"arrivalTime\": \"18:30\",\n        \"createdAt\": \"2024-03-19T12:00:00.000Z\",\n        \"updatedAt\": \"2024-03-19T12:00:00.000Z\"\n    }\n]"}]}, {"name": "Get Single Journey", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/journeys/1", "host": ["{{baseUrl}}"], "path": ["api", "journeys", "1"]}, "description": "Get a specific journey by ID"}, "response": [{"name": "Success Response", "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [], "cookie": [], "body": "{\n    \"id\": 1,\n    \"userId\": 1,\n    \"date\": \"2024-03-20\",\n    \"source\": \"New York\",\n    \"destination\": \"Los Angeles\",\n    \"modeOfTransport\": \"train\",\n    \"trainNumber\": \"TR123\",\n    \"coachNumber\": \"C4\",\n    \"seatNumber\": \"12A\",\n    \"departureTime\": \"09:00\",\n    \"arrivalTime\": \"18:30\",\n    \"createdAt\": \"2024-03-19T12:00:00.000Z\",\n    \"updatedAt\": \"2024-03-19T12:00:00.000Z\"\n}"}]}, {"name": "Update Journey", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": "{\n    \"date\": \"2024-03-21\",\n    \"source\": \"New York\",\n    \"destination\": \"Chicago\",\n    \"modeOfTransport\": \"flight\",\n    \"flightNumber\": \"FL456\",\n    \"departureTime\": \"10:00\",\n    \"arrivalTime\": \"12:30\"\n}"}, "url": {"raw": "{{baseUrl}}/api/journeys/1", "host": ["{{baseUrl}}"], "path": ["api", "journeys", "1"]}, "description": "Update an existing journey"}, "response": [{"name": "Success Response", "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [], "cookie": [], "body": "{\n    \"id\": 1,\n    \"userId\": 1,\n    \"date\": \"2024-03-21\",\n    \"source\": \"New York\",\n    \"destination\": \"Chicago\",\n    \"modeOfTransport\": \"flight\",\n    \"flightNumber\": \"FL456\",\n    \"trainNumber\": null,\n    \"coachNumber\": null,\n    \"seatNumber\": null,\n    \"departureTime\": \"10:00\",\n    \"arrivalTime\": \"12:30\",\n    \"updatedAt\": \"2024-03-19T13:00:00.000Z\",\n    \"createdAt\": \"2024-03-19T12:00:00.000Z\"\n}"}]}, {"name": "Delete Journey", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/journeys/1", "host": ["{{baseUrl}}"], "path": ["api", "journeys", "1"]}, "description": "Delete a journey"}, "response": [{"name": "Success Response", "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [], "cookie": [], "body": "{\n    \"message\": \"Journey deleted successfully\"\n}"}]}], "variable": [{"key": "baseUrl", "value": "http://localhost:3000", "type": "string"}, {"key": "authToken", "value": "your-auth-token-here", "type": "string"}]}