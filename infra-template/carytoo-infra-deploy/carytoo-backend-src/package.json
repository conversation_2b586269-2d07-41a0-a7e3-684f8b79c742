{"name": "express-postgres-app", "version": "1.0.0", "main": "index.js", "scripts": {"migrate": "npx sequelize-cli db:migrate --config ./config/database.js", "start": "npm run migrate &&  node app.js", "dev": "nodemon app.js"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@aws-sdk/client-s3": "^3.774.0", "bcrypt": "^5.1.1", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^16.4.7", "express": "^4.21.2", "express-rate-limit": "^7.5.0", "express-validator": "^7.2.1", "fs": "^0.0.1-security", "handlebars": "^4.7.8", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.2", "multer-s3": "^3.0.1", "nodemailer": "^6.10.0", "path": "^0.12.7", "pg": "^8.13.3", "pg-hstore": "^2.3.4", "sequelize": "^6.37.5", "uuid": "^11.1.0"}, "devDependencies": {"nodemon": "^3.1.9", "sequelize-cli": "^6.6.2"}}