#!/bin/bash
set -e

# Configuration - Edit these variables
AWS_REGION="ap-south-1"
ECR_REPOSITORY_NAME="carrytoo-api"
IMAGE_TAG="latest"
ECS_CLUSTER_NAME="carrytoo-cluster"
ECS_SERVICE_NAME="carrytoo-api-service"

# Get AWS account ID
AWS_ACCOUNT_ID=$(aws sts get-caller-identity --query Account --output text)

# Get RDS endpoint from Terraform outputs
RDS_ENDPOINT=$(terraform output -raw rds_endpoint)
DB_HOST=$(echo $RDS_ENDPOINT | cut -d':' -f1)
DB_PORT=$(echo $RDS_ENDPOINT | cut -d':' -f2)
DB_NAME="carrytoo"
DB_USER="carrytoo_user"

# Prompt for DB password (don't hardcode this in scripts)
echo -n "Enter database password: "
read -s DB_PASSWORD
echo

# Create ECR repository if it doesn't exist
echo "Checking if ECR repository exists..."
if ! aws ecr describe-repositories --repository-names ${ECR_REPOSITORY_NAME} --region ${AWS_REGION} > /dev/null 2>&1; then
  echo "Creating ECR repository: ${ECR_REPOSITORY_NAME}..."
  aws ecr create-repository --repository-name ${ECR_REPOSITORY_NAME} --region ${AWS_REGION}
fi

# Get ECR login token and login to Docker
echo "Logging in to Amazon ECR..."
aws ecr get-login-password --region ${AWS_REGION} | docker login --username AWS --password-stdin ${AWS_ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com

# Build Docker image (assuming Dockerfile is in the current directory)
echo "Building Docker image..."
docker build -t ${ECR_REPOSITORY_NAME}:${IMAGE_TAG} \
  --build-arg DB_HOST=${DB_HOST} \
  --build-arg DB_PORT=${DB_PORT} \
  --build-arg DB_NAME=${DB_NAME} \
  --build-arg DB_USERNAME=${DB_USER} \
  --build-arg DB_PASSWORD=${DB_PASSWORD} \
  .

# Tag and push the image to ECR
echo "Tagging and pushing image to ECR..."
docker tag ${ECR_REPOSITORY_NAME}:${IMAGE_TAG} ${AWS_ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com/${ECR_REPOSITORY_NAME}:${IMAGE_TAG}
docker push ${AWS_ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com/${ECR_REPOSITORY_NAME}:${IMAGE_TAG}

# Get the current task definition
echo "Retrieving current task definition..."
TASK_DEFINITION=$(aws ecs describe-task-definition --task-definition carrytoo-api --region ${AWS_REGION})
TASK_DEFINITION_ARN=$(echo $TASK_DEFINITION | jq -r '.taskDefinition.taskDefinitionArn')

# Create a new task definition revision with the new image
echo "Creating new task definition revision..."
NEW_TASK_DEFINITION=$(echo $TASK_DEFINITION | jq --arg IMAGE "${AWS_ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com/${ECR_REPOSITORY_NAME}:${IMAGE_TAG}" \
  --arg DB_HOST "$DB_HOST" \
  --arg DB_PORT "$DB_PORT" \
  --arg DB_NAME "$DB_NAME" \
  --arg DB_USERNAME "$DB_USER" \
  --arg DB_PASSWORD "$DB_PASSWORD" \
  '.taskDefinition | .containerDefinitions[0].image = $IMAGE | 
  .containerDefinitions[0].environment = [
    {name: "DB_HOST", value: $DB_HOST},
    {name: "DB_PORT", value: $DB_PORT},
    {name: "DB_NAME", value: $DB_NAME},
    {name: "DB_USERNAME", value: $DB_USERNAME},
    {name: "DB_PASSWORD", value: $DB_PASSWORD},
    {name: "PORT", value: "3000"},
    {name:"AWS_BUCKET_NAME",value:"carrytoo-documents"},
    {name:"AWS_ACCESS_REGION",value:"ap-south-1"},
    {name:"AWS_ACCESS_KEY_ID",value:"********************"},
    {name:"AWS_SECRET_ACCESS_KEY",value:"gaovQZIOIFhVFoEx5yJTY4Q3uwcj1JzGC0SEroME"},
    {name:"JWT_SECRET",value:"mysecretkey"},
    {name:"EMAIL",value:"<EMAIL>"},
    {name:"MAIL_PASSWORD",value:"Carytoo@2024"},
    {name:"SMTP_HOST",value:"smtp.hostinger.com"},
    {name:"FRONTEND_URL",value:"https://www.carytoo.com,http://localhost:5173,http://************:5173,http://localhost:4500"}
  ] | del(.taskDefinitionArn) | del(.revision) | del(.status) | del(.requiresAttributes) | del(.compatibilities) | del(.registeredAt) | del(.registeredBy)')

# Register the new task definition
echo "Registering new task definition..."
NEW_TASK_DEFINITION_ARN=$(aws ecs register-task-definition --region ${AWS_REGION} --cli-input-json "$(echo $NEW_TASK_DEFINITION)" | jq -r '.taskDefinition.taskDefinitionArn')

# Update the service to use the new task definition
echo "Updating ECS service to use new task definition..."
aws ecs update-service --cluster ${ECS_CLUSTER_NAME} --service ${ECS_SERVICE_NAME} --task-definition ${NEW_TASK_DEFINITION_ARN} --region ${AWS_REGION}

echo "Waiting for service to stabilize..."
aws ecs wait services-stable --cluster ${ECS_CLUSTER_NAME} --services ${ECS_SERVICE_NAME} --region ${AWS_REGION}

# Get the ALB DNS name
ALB_URL=$(terraform output -raw fargate_service_url)

echo "Deployment completed successfully!"
echo "Your API is now available at: ${ALB_URL}"
echo "You can test it with: curl ${ALB_URL}/"