{"version": 4, "terraform_version": "1.11.4", "serial": 127, "lineage": "80702a68-37a3-2aa3-650f-fb66bab0584b", "outputs": {"cloudfront_domain_name": {"value": "d34624i7loak5z.cloudfront.net", "type": "string"}, "fargate_service_url": {"value": "http://carrytoo-alb-**********.ap-south-1.elb.amazonaws.com", "type": "string"}, "rds_endpoint": {"value": "carrytoo-db.c3qqmiooo2ii.ap-south-1.rds.amazonaws.com:5432", "type": "string"}, "website_bucket_name": {"value": "carrytoo-ui", "type": "string"}, "website_endpoint": {"value": "carrytoo-ui.s3-website.ap-south-1.amazonaws.com", "type": "string"}}, "resources": [{"mode": "managed", "type": "aws_acm_certificate", "name": "api_cert", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:acm:ap-south-1:5276********:certificate/ee78a269-80d8-444f-a578-fc2fc3785a74", "certificate_authority_arn": "", "certificate_body": null, "certificate_chain": null, "domain_name": "api.carytoo.com", "domain_validation_options": [{"domain_name": "api.carytoo.com", "resource_record_name": "_2972509156e96fcce711326a7c97606f.api.carytoo.com.", "resource_record_type": "CNAME", "resource_record_value": "_8b66386dbad2a20451ae0b25a0a232fa.xlfgrmvvlj.acm-validations.aws."}], "early_renewal_duration": "", "id": "arn:aws:acm:ap-south-1:5276********:certificate/ee78a269-80d8-444f-a578-fc2fc3785a74", "key_algorithm": "RSA_2048", "not_after": "2026-05-13T23:59:59Z", "not_before": "2025-04-14T00:00:00Z", "options": [{"certificate_transparency_logging_preference": "ENABLED"}], "pending_renewal": false, "private_key": null, "renewal_eligibility": "ELIGIBLE", "renewal_summary": [], "status": "ISSUED", "subject_alternative_names": ["api.carytoo.com"], "tags": {"Name": "carytoo-api-cert"}, "tags_all": {"Name": "carytoo-api-cert"}, "type": "AMAZON_ISSUED", "validation_emails": [], "validation_method": "DNS", "validation_option": []}, "sensitive_attributes": [[{"type": "get_attr", "value": "private_key"}]], "private": "bnVsbA==", "create_before_destroy": true}]}, {"mode": "managed", "type": "aws_acm_certificate", "name": "cert", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"].us-east-1", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:acm:us-east-1:5276********:certificate/4d17a054-ebe1-4e1a-b173-47a5221b26b5", "certificate_authority_arn": "", "certificate_body": null, "certificate_chain": null, "domain_name": "carytoo.com", "domain_validation_options": [{"domain_name": "carytoo.com", "resource_record_name": "_be9d37b3cc2fc451cd594e6ed7aec3f9.carytoo.com.", "resource_record_type": "CNAME", "resource_record_value": "_a4534e62ad89d1a66a4d1abfed7114f7.xlfgrmvvlj.acm-validations.aws."}, {"domain_name": "www.carytoo.com", "resource_record_name": "_06af3f7f30cdcaad0feaec62d4b4f38e.www.carytoo.com.", "resource_record_type": "CNAME", "resource_record_value": "_851a3ceb3d00238c8e40f225e4dfdb16.xlfgrmvvlj.acm-validations.aws."}], "early_renewal_duration": "", "id": "arn:aws:acm:us-east-1:5276********:certificate/4d17a054-ebe1-4e1a-b173-47a5221b26b5", "key_algorithm": "RSA_2048", "not_after": "2026-05-13T23:59:59Z", "not_before": "2025-04-14T00:00:00Z", "options": [{"certificate_transparency_logging_preference": "ENABLED"}], "pending_renewal": false, "private_key": null, "renewal_eligibility": "ELIGIBLE", "renewal_summary": [], "status": "ISSUED", "subject_alternative_names": ["carytoo.com", "www.carytoo.com"], "tags": {}, "tags_all": {}, "type": "AMAZON_ISSUED", "validation_emails": [], "validation_method": "DNS", "validation_option": []}, "sensitive_attributes": [[{"type": "get_attr", "value": "private_key"}]], "private": "bnVsbA==", "create_before_destroy": true}]}, {"mode": "managed", "type": "aws_cloudfront_distribution", "name": "s3_distribution", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 1, "attributes": {"aliases": ["carytoo.com", "www.carytoo.com"], "arn": "arn:aws:cloudfront::5276********:distribution/E29D4AIJ4WPH1L", "caller_reference": "terraform-20250412084818410300000002", "comment": null, "continuous_deployment_policy_id": "", "custom_error_response": [{"error_caching_min_ttl": 0, "error_code": 403, "response_code": 200, "response_page_path": "/index.html"}, {"error_caching_min_ttl": 0, "error_code": 404, "response_code": 200, "response_page_path": "/index.html"}], "default_cache_behavior": [{"allowed_methods": ["GET", "HEAD", "OPTIONS"], "cache_policy_id": "", "cached_methods": ["GET", "HEAD"], "compress": false, "default_ttl": 3600, "field_level_encryption_id": "", "forwarded_values": [{"cookies": [{"forward": "none", "whitelisted_names": []}], "headers": [], "query_string": false, "query_string_cache_keys": []}], "function_association": [], "grpc_config": [{"enabled": false}], "lambda_function_association": [], "max_ttl": 86400, "min_ttl": 0, "origin_request_policy_id": "", "realtime_log_config_arn": "", "response_headers_policy_id": "", "smooth_streaming": false, "target_origin_id": "S3-carrytoo-ui", "trusted_key_groups": [], "trusted_signers": [], "viewer_protocol_policy": "redirect-to-https"}], "default_root_object": "index.html", "domain_name": "d34624i7loak5z.cloudfront.net", "enabled": true, "etag": "E2LV8KGONUO2NR", "hosted_zone_id": "Z2FDTNDATAQYW2", "http_version": "http2", "id": "E29D4AIJ4WPH1L", "in_progress_validation_batches": 0, "is_ipv6_enabled": true, "last_modified_time": "2025-04-14 06:57:16.739 +0000 UTC", "logging_config": [], "ordered_cache_behavior": [], "origin": [{"connection_attempts": 3, "connection_timeout": 10, "custom_header": [], "custom_origin_config": [], "domain_name": "carrytoo-ui.s3.ap-south-1.amazonaws.com", "origin_access_control_id": "", "origin_id": "S3-carrytoo-ui", "origin_path": "", "origin_shield": [], "s3_origin_config": [{"origin_access_identity": "origin-access-identity/cloudfront/EVMJXA1YI7AZZ"}], "vpc_origin_config": []}], "origin_group": [], "price_class": "PriceClass_200", "restrictions": [{"geo_restriction": [{"locations": [], "restriction_type": "none"}]}], "retain_on_delete": false, "staging": false, "status": "Deployed", "tags": {"Environment": "Production"}, "tags_all": {"Environment": "Production"}, "trusted_key_groups": [{"enabled": false, "items": []}], "trusted_signers": [{"enabled": false, "items": []}], "viewer_certificate": [{"acm_certificate_arn": "arn:aws:acm:us-east-1:5276********:certificate/4d17a054-ebe1-4e1a-b173-47a5221b26b5", "cloudfront_default_certificate": false, "iam_certificate_id": "", "minimum_protocol_version": "TLSv1.2_2021", "ssl_support_method": "sni-only"}], "wait_for_deployment": true, "web_acl_id": ""}, "sensitive_attributes": [], "private": "eyJzY2hlbWFfdmVyc2lvbiI6IjEifQ==", "dependencies": ["aws_acm_certificate.cert", "aws_cloudfront_origin_access_identity.oai", "aws_s3_bucket.website_bucket"]}]}, {"mode": "managed", "type": "aws_cloudfront_origin_access_identity", "name": "oai", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:cloudfront::5276********:origin-access-identity/EVMJXA1YI7AZZ", "caller_reference": "terraform-20250412084816285600000001", "cloudfront_access_identity_path": "origin-access-identity/cloudfront/EVMJXA1YI7AZZ", "comment": "OAI for carrytoo-ui S3 bucket", "etag": "E367DP7M7ZPRLF", "iam_arn": "arn:aws:iam::cloudfront:user/CloudFront Origin Access Identity EVMJXA1YI7AZZ", "id": "EVMJXA1YI7AZZ", "s3_canonical_user_id": "29ad678d166baff65783da97d7e35c186b8289dc1216adf99ead058fa59ea25e69a5406532a3234c1abc446ea2209b6b"}, "sensitive_attributes": [], "private": "bnVsbA=="}]}, {"mode": "managed", "type": "aws_cloudwatch_log_group", "name": "carrytoo_logs", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:logs:ap-south-1:5276********:log-group:/ecs/carrytoo-api", "id": "/ecs/carrytoo-api", "kms_key_id": "", "log_group_class": "STANDARD", "name": "/ecs/carrytoo-api", "name_prefix": "", "retention_in_days": 30, "skip_destroy": false, "tags": {"Name": "carrytoo-log-group"}, "tags_all": {"Name": "carrytoo-log-group"}}, "sensitive_attributes": [], "private": "bnVsbA=="}]}, {"mode": "managed", "type": "aws_db_instance", "name": "carrytoo_db", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 2, "attributes": {"address": "carrytoo-db.c3qqmiooo2ii.ap-south-1.rds.amazonaws.com", "allocated_storage": 20, "allow_major_version_upgrade": null, "apply_immediately": false, "arn": "arn:aws:rds:ap-south-1:5276********:db:carrytoo-db", "auto_minor_version_upgrade": true, "availability_zone": "ap-south-1b", "backup_retention_period": 7, "backup_target": "region", "backup_window": "03:00-04:00", "blue_green_update": [], "ca_cert_identifier": "rds-ca-rsa2048-g1", "character_set_name": "", "copy_tags_to_snapshot": false, "custom_iam_instance_profile": "", "customer_owned_ip_enabled": false, "database_insights_mode": "standard", "db_name": "carrytoo", "db_subnet_group_name": "carrytoo-db-subnet-group", "dedicated_log_volume": false, "delete_automated_backups": true, "deletion_protection": false, "domain": "", "domain_auth_secret_arn": "", "domain_dns_ips": [], "domain_fqdn": "", "domain_iam_role_name": "", "domain_ou": "", "enabled_cloudwatch_logs_exports": [], "endpoint": "carrytoo-db.c3qqmiooo2ii.ap-south-1.rds.amazonaws.com:5432", "engine": "postgres", "engine_lifecycle_support": "open-source-rds-extended-support", "engine_version": "16.8", "engine_version_actual": "16.8", "final_snapshot_identifier": null, "hosted_zone_id": "Z2VFMSZA74J7XZ", "iam_database_authentication_enabled": false, "id": "db-JKPI5LIYNZEYRS4XL7AWVMMEF4", "identifier": "carrytoo-db", "identifier_prefix": "", "instance_class": "db.t3.micro", "iops": 0, "kms_key_id": "", "latest_restorable_time": "2025-04-16T06:39:30Z", "license_model": "postgresql-license", "listener_endpoint": [], "maintenance_window": "mon:04:00-mon:05:00", "manage_master_user_password": null, "master_user_secret": [], "master_user_secret_kms_key_id": null, "max_allocated_storage": 0, "monitoring_interval": 0, "monitoring_role_arn": "", "multi_az": false, "nchar_character_set_name": "", "network_type": "IPV4", "option_group_name": "default:postgres-16", "parameter_group_name": "carrytoo-postgres16-params", "password": "CrryT00!Valid", "password_wo": null, "password_wo_version": null, "performance_insights_enabled": false, "performance_insights_kms_key_id": "", "performance_insights_retention_period": 0, "port": 5432, "publicly_accessible": true, "replica_mode": "", "replicas": [], "replicate_source_db": "", "resource_id": "db-JKPI5LIYNZEYRS4XL7AWVMMEF4", "restore_to_point_in_time": [], "s3_import": [], "skip_final_snapshot": true, "snapshot_identifier": null, "status": "available", "storage_encrypted": false, "storage_throughput": 0, "storage_type": "gp2", "tags": {"Name": "carrytoo-db"}, "tags_all": {"Name": "carrytoo-db"}, "timeouts": null, "timezone": "", "upgrade_storage_config": null, "username": "carrytoo_user", "vpc_security_group_ids": ["sg-00098980e445d651d"]}, "sensitive_attributes": [[{"type": "get_attr", "value": "password_wo"}], [{"type": "get_attr", "value": "password"}]], "private": "********************************************************************************************************************************************************************************", "dependencies": ["aws_db_parameter_group.postgres", "aws_db_subnet_group.carrytoo_db_subnet_group", "aws_security_group.fargate_sg", "aws_security_group.rds_sg", "aws_subnet.public", "aws_vpc.carrytoo_vpc"]}]}, {"mode": "managed", "type": "aws_db_parameter_group", "name": "postgres", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:rds:ap-south-1:5276********:pg:carrytoo-postgres16-params", "description": "Managed by Terraform", "family": "postgres16", "id": "carrytoo-postgres16-params", "name": "carrytoo-postgres16-params", "name_prefix": "", "parameter": [{"apply_method": "immediate", "name": "log_connections", "value": "1"}], "skip_destroy": false, "tags": {}, "tags_all": {}}, "sensitive_attributes": [], "private": "bnVsbA=="}]}, {"mode": "managed", "type": "aws_db_subnet_group", "name": "carrytoo_db_subnet_group", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:rds:ap-south-1:5276********:subgrp:carrytoo-db-subnet-group", "description": "Managed by Terraform", "id": "carrytoo-db-subnet-group", "name": "carrytoo-db-subnet-group", "name_prefix": "", "subnet_ids": ["subnet-01d2c91a3e6747b8d", "subnet-0b665e4117d1edd66"], "supported_network_types": ["IPV4"], "tags": {"Name": "CarryToo DB Subnet Group"}, "tags_all": {"Name": "CarryToo DB Subnet Group"}, "vpc_id": "vpc-0aceced8713a8bccb"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_subnet.public", "aws_vpc.carrytoo_vpc"]}]}, {"mode": "managed", "type": "aws_ecs_cluster", "name": "carrytoo_cluster", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:ecs:ap-south-1:5276********:cluster/carrytoo-cluster", "configuration": [], "id": "arn:aws:ecs:ap-south-1:5276********:cluster/carrytoo-cluster", "name": "carrytoo-cluster", "service_connect_defaults": [], "setting": [{"name": "containerInsights", "value": "enabled"}], "tags": {"Name": "carrytoo-ecs-cluster"}, "tags_all": {"Name": "carrytoo-ecs-cluster"}}, "sensitive_attributes": [], "private": "bnVsbA=="}]}, {"mode": "managed", "type": "aws_ecs_service", "name": "carrytoo_service", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 1, "attributes": {"alarms": [], "availability_zone_rebalancing": "DISABLED", "capacity_provider_strategy": [], "cluster": "arn:aws:ecs:ap-south-1:5276********:cluster/carrytoo-cluster", "deployment_circuit_breaker": [{"enable": false, "rollback": false}], "deployment_controller": [{"type": "ECS"}], "deployment_maximum_percent": 200, "deployment_minimum_healthy_percent": 100, "desired_count": 1, "enable_ecs_managed_tags": false, "enable_execute_command": false, "force_delete": null, "force_new_deployment": null, "health_check_grace_period_seconds": 0, "iam_role": "/aws-service-role/ecs.amazonaws.com/AWSServiceRoleForECS", "id": "arn:aws:ecs:ap-south-1:5276********:service/carrytoo-cluster/carrytoo-api-service", "launch_type": "FARGATE", "load_balancer": [{"container_name": "carrytoo-api", "container_port": 3000, "elb_name": "", "target_group_arn": "arn:aws:elasticloadbalancing:ap-south-1:5276********:targetgroup/carrytoo-target-group/7d3d5f928031975b"}], "name": "carrytoo-api-service", "network_configuration": [{"assign_public_ip": true, "security_groups": ["sg-0963379d5774c89d6"], "subnets": ["subnet-01d2c91a3e6747b8d", "subnet-0b665e4117d1edd66"]}], "ordered_placement_strategy": [], "placement_constraints": [], "platform_version": "LATEST", "propagate_tags": "NONE", "scheduling_strategy": "REPLICA", "service_connect_configuration": [], "service_registries": [], "tags": {"Name": "carrytoo-api-service"}, "tags_all": {"Name": "carrytoo-api-service"}, "task_definition": "arn:aws:ecs:ap-south-1:5276********:task-definition/carrytoo-api:26", "timeouts": null, "triggers": {}, "volume_configuration": [], "vpc_lattice_configurations": [], "wait_for_steady_state": false}, "sensitive_attributes": [], "private": "********************************************************************************************************************************************************************************", "dependencies": ["aws_cloudwatch_log_group.carrytoo_logs", "aws_db_instance.carrytoo_db", "aws_db_parameter_group.postgres", "aws_db_subnet_group.carrytoo_db_subnet_group", "aws_ecs_cluster.carrytoo_cluster", "aws_ecs_task_definition.carrytoo_api", "aws_iam_access_key.api_s3_key", "aws_iam_role.ecs_task_execution_role", "aws_iam_user.api_s3_user", "aws_lb.carrytoo_alb", "aws_lb_listener.http", "aws_lb_target_group.carrytoo_tg", "aws_s3_bucket.documents_bucket", "aws_security_group.fargate_sg", "aws_security_group.rds_sg", "aws_subnet.public", "aws_vpc.carrytoo_vpc"]}]}, {"mode": "managed", "type": "aws_ecs_task_definition", "name": "carrytoo_api", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 1, "attributes": {"arn": "arn:aws:ecs:ap-south-1:5276********:task-definition/carrytoo-api:26", "arn_without_revision": "arn:aws:ecs:ap-south-1:5276********:task-definition/carrytoo-api", "container_definitions": "[{\"environment\":[{\"name\":\"AWS_ACCESS_KEY_ID\",\"value\":\"********************\"},{\"name\":\"AWS_BUCKET_NAME\",\"value\":\"carrytoo-documents\"},{\"name\":\"AWS_REGION\",\"value\":\"ap-south-1\"},{\"name\":\"AWS_SECRET_ACCESS_KEY\",\"value\":\"gaovQZIOIFhVFoEx5yJTY4Q3uwcj1JzGC0SEroME\"},{\"name\":\"DB_HOST\",\"value\":\"carrytoo-db.c3qqmiooo2ii.ap-south-1.rds.amazonaws.com\"},{\"name\":\"DB_NAME\",\"value\":\"carrytoo\"},{\"name\":\"DB_PASSWORD\",\"value\":\"CrryT00!Valid\"},{\"name\":\"DB_USERNAME\",\"value\":\"carrytoo_user\"},{\"name\":\"PORT\",\"value\":\"3000\"}],\"essential\":true,\"image\":\"public.ecr.aws/bitnami/node:18/app\",\"logConfiguration\":{\"logDriver\":\"awslogs\",\"options\":{\"awslogs-group\":\"/ecs/carrytoo-api\",\"awslogs-region\":\"ap-south-1\",\"awslogs-stream-prefix\":\"carrytoo-api\"}},\"mountPoints\":[],\"name\":\"carrytoo-api\",\"portMappings\":[{\"containerPort\":3000,\"hostPort\":3000,\"protocol\":\"tcp\"}],\"systemControls\":[],\"volumesFrom\":[]}]", "cpu": "256", "enable_fault_injection": false, "ephemeral_storage": [], "execution_role_arn": "arn:aws:iam::5276********:role/carrytoo-ecs-task-execution-role", "family": "carrytoo-api", "id": "carrytoo-api", "inference_accelerator": [], "ipc_mode": "", "memory": "512", "network_mode": "awsvpc", "pid_mode": "", "placement_constraints": [], "proxy_configuration": [], "requires_compatibilities": ["FARGATE"], "revision": 26, "runtime_platform": [], "skip_destroy": false, "tags": {"Name": "carrytoo-task-definition"}, "tags_all": {"Name": "carrytoo-task-definition"}, "task_role_arn": "arn:aws:iam::5276********:role/carrytoo-ecs-task-execution-role", "track_latest": false, "volume": []}, "sensitive_attributes": [[{"type": "get_attr", "value": "container_definitions"}]], "private": "eyJzY2hlbWFfdmVyc2lvbiI6IjEifQ==", "dependencies": ["aws_cloudwatch_log_group.carrytoo_logs", "aws_db_instance.carrytoo_db", "aws_db_parameter_group.postgres", "aws_db_subnet_group.carrytoo_db_subnet_group", "aws_iam_access_key.api_s3_key", "aws_iam_role.ecs_task_execution_role", "aws_iam_user.api_s3_user", "aws_s3_bucket.documents_bucket", "aws_security_group.fargate_sg", "aws_security_group.rds_sg", "aws_subnet.public", "aws_vpc.carrytoo_vpc"]}]}, {"mode": "managed", "type": "aws_iam_access_key", "name": "api_s3_key", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"create_date": "2025-04-13T19:05:07Z", "encrypted_secret": null, "encrypted_ses_smtp_password_v4": null, "id": "********************", "key_fingerprint": null, "pgp_key": null, "secret": "gaovQZIOIFhVFoEx5yJTY4Q3uwcj1JzGC0SEroME", "ses_smtp_password_v4": "BHQlu/rGydCIY/nUvcrw1uY76HH1RDHWl+NlDKXja3gD", "status": "Active", "user": "carrytoo-api-s3-user"}, "sensitive_attributes": [[{"type": "get_attr", "value": "ses_smtp_password_v4"}], [{"type": "get_attr", "value": "secret"}]], "private": "bnVsbA==", "dependencies": ["aws_iam_user.api_s3_user"]}]}, {"mode": "managed", "type": "aws_iam_role", "name": "ecs_task_execution_role", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:iam::5276********:role/carrytoo-ecs-task-execution-role", "assume_role_policy": "{\"Statement\":[{\"Action\":\"sts:AssumeRole\",\"Effect\":\"Allow\",\"Principal\":{\"Service\":\"ecs-tasks.amazonaws.com\"}}],\"Version\":\"2012-10-17\"}", "create_date": "2025-04-13T17:26:50Z", "description": "", "force_detach_policies": false, "id": "carrytoo-ecs-task-execution-role", "inline_policy": [], "managed_policy_arns": ["arn:aws:iam::aws:policy/service-role/AmazonECSTaskExecutionRolePolicy"], "max_session_duration": 3600, "name": "carrytoo-ecs-task-execution-role", "name_prefix": "", "path": "/", "permissions_boundary": "", "tags": {}, "tags_all": {}, "unique_id": "AROAXVWZH6RRHCZBOGOBB"}, "sensitive_attributes": [], "private": "bnVsbA=="}]}, {"mode": "managed", "type": "aws_iam_role_policy_attachment", "name": "ecs_task_execution_role_policy", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "carrytoo-ecs-task-execution-role-20250413172651730200000001", "policy_arn": "arn:aws:iam::aws:policy/service-role/AmazonECSTaskExecutionRolePolicy", "role": "carrytoo-ecs-task-execution-role"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_iam_role.ecs_task_execution_role"]}]}, {"mode": "managed", "type": "aws_iam_user", "name": "api_s3_user", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:iam::5276********:user/carrytoo-api-s3-user", "force_destroy": false, "id": "carrytoo-api-s3-user", "name": "carrytoo-api-s3-user", "path": "/", "permissions_boundary": "", "tags": {}, "tags_all": {}, "unique_id": "AIDAXVWZH6RRPFXC4KYQR"}, "sensitive_attributes": [], "private": "bnVsbA=="}]}, {"mode": "managed", "type": "aws_iam_user_policy", "name": "api_s3_policy", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "carrytoo-api-s3-user:carrytoo-api-s3-policy", "name": "carrytoo-api-s3-policy", "name_prefix": "", "policy": "{\"Version\":\"2012-10-17\",\"Statement\":[{\"Action\":[\"s3:PutObject\",\"s3:GetObject\",\"s3:DeleteObject\",\"s3:ListBucket\"],\"Effect\":\"Allow\",\"Resource\":[\"arn:aws:s3:::carrytoo-documents\",\"arn:aws:s3:::carrytoo-documents/*\"]}]}", "user": "carrytoo-api-s3-user"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_iam_user.api_s3_user", "aws_s3_bucket.documents_bucket"]}]}, {"mode": "managed", "type": "aws_internet_gateway", "name": "carrytoo_igw", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:ec2:ap-south-1:5276********:internet-gateway/igw-01480bdef7ecd74fa", "id": "igw-01480bdef7ecd74fa", "owner_id": "5276********", "tags": {"Name": "carrytoo-igw"}, "tags_all": {"Name": "carrytoo-igw"}, "timeouts": null, "vpc_id": "vpc-0aceced8713a8bccb"}, "sensitive_attributes": [], "private": "****************************************************************************************************************************************************", "dependencies": ["aws_vpc.carrytoo_vpc"]}]}, {"mode": "managed", "type": "aws_lb", "name": "carrytoo_alb", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"access_logs": [{"bucket": "", "enabled": false, "prefix": ""}], "arn": "arn:aws:elasticloadbalancing:ap-south-1:5276********:loadbalancer/app/carrytoo-alb/9001ec3610d35456", "arn_suffix": "app/carrytoo-alb/9001ec3610d35456", "client_keep_alive": 3600, "connection_logs": [{"bucket": "", "enabled": false, "prefix": ""}], "customer_owned_ipv4_pool": "", "desync_mitigation_mode": "defensive", "dns_name": "carrytoo-alb-**********.ap-south-1.elb.amazonaws.com", "dns_record_client_routing_policy": null, "drop_invalid_header_fields": false, "enable_cross_zone_load_balancing": true, "enable_deletion_protection": false, "enable_http2": true, "enable_tls_version_and_cipher_suite_headers": false, "enable_waf_fail_open": false, "enable_xff_client_port": false, "enable_zonal_shift": false, "enforce_security_group_inbound_rules_on_private_link_traffic": "", "id": "arn:aws:elasticloadbalancing:ap-south-1:5276********:loadbalancer/app/carrytoo-alb/9001ec3610d35456", "idle_timeout": 60, "internal": false, "ip_address_type": "ipv4", "ipam_pools": [], "load_balancer_type": "application", "name": "carrytoo-alb", "name_prefix": "", "preserve_host_header": false, "security_groups": ["sg-0963379d5774c89d6"], "subnet_mapping": [{"allocation_id": "", "ipv6_address": "", "outpost_id": "", "private_ipv4_address": "", "subnet_id": "subnet-01d2c91a3e6747b8d"}, {"allocation_id": "", "ipv6_address": "", "outpost_id": "", "private_ipv4_address": "", "subnet_id": "subnet-0b665e4117d1edd66"}], "subnets": ["subnet-01d2c91a3e6747b8d", "subnet-0b665e4117d1edd66"], "tags": {"Name": "carrytoo-alb"}, "tags_all": {"Name": "carrytoo-alb"}, "timeouts": null, "vpc_id": "vpc-0aceced8713a8bccb", "xff_header_processing_mode": "append", "zone_id": "ZP97RAFLXTNZK"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMDAsImRlbGV0ZSI6NjAwMDAwMDAwMDAwLCJ1cGRhdGUiOjYwMDAwMDAwMDAwMH19", "dependencies": ["aws_security_group.fargate_sg", "aws_subnet.public", "aws_vpc.carrytoo_vpc"]}]}, {"mode": "managed", "type": "aws_lb_listener", "name": "http", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"alpn_policy": null, "arn": "arn:aws:elasticloadbalancing:ap-south-1:5276********:listener/app/carrytoo-alb/9001ec3610d35456/a8b9b644dc1aeb34", "certificate_arn": null, "default_action": [{"authenticate_cognito": [], "authenticate_oidc": [], "fixed_response": [], "forward": [], "order": 1, "redirect": [{"host": "#{host}", "path": "/#{path}", "port": "443", "protocol": "HTTPS", "query": "#{query}", "status_code": "HTTP_301"}], "target_group_arn": "", "type": "redirect"}], "id": "arn:aws:elasticloadbalancing:ap-south-1:5276********:listener/app/carrytoo-alb/9001ec3610d35456/a8b9b644dc1aeb34", "load_balancer_arn": "arn:aws:elasticloadbalancing:ap-south-1:5276********:loadbalancer/app/carrytoo-alb/9001ec3610d35456", "mutual_authentication": [], "port": 80, "protocol": "HTTP", "routing_http_request_x_amzn_mtls_clientcert_header_name": null, "routing_http_request_x_amzn_mtls_clientcert_issuer_header_name": null, "routing_http_request_x_amzn_mtls_clientcert_leaf_header_name": null, "routing_http_request_x_amzn_mtls_clientcert_serial_number_header_name": null, "routing_http_request_x_amzn_mtls_clientcert_subject_header_name": null, "routing_http_request_x_amzn_mtls_clientcert_validity_header_name": null, "routing_http_request_x_amzn_tls_cipher_suite_header_name": null, "routing_http_request_x_amzn_tls_version_header_name": null, "routing_http_response_access_control_allow_credentials_header_value": "", "routing_http_response_access_control_allow_headers_header_value": "", "routing_http_response_access_control_allow_methods_header_value": "", "routing_http_response_access_control_allow_origin_header_value": "", "routing_http_response_access_control_expose_headers_header_value": "", "routing_http_response_access_control_max_age_header_value": "", "routing_http_response_content_security_policy_header_value": "", "routing_http_response_server_enabled": false, "routing_http_response_strict_transport_security_header_value": "", "routing_http_response_x_content_type_options_header_value": "", "routing_http_response_x_frame_options_header_value": "", "ssl_policy": "", "tags": {}, "tags_all": {}, "tcp_idle_timeout_seconds": null, "timeouts": null}, "sensitive_attributes": [], "private": "********************************************************************************************************************", "dependencies": ["aws_lb.carrytoo_alb", "aws_security_group.fargate_sg", "aws_subnet.public", "aws_vpc.carrytoo_vpc"]}]}, {"mode": "managed", "type": "aws_lb_listener", "name": "https", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"alpn_policy": null, "arn": "arn:aws:elasticloadbalancing:ap-south-1:5276********:listener/app/carrytoo-alb/9001ec3610d35456/a65cecc4cba4bb8c", "certificate_arn": "arn:aws:acm:ap-south-1:5276********:certificate/ee78a269-80d8-444f-a578-fc2fc3785a74", "default_action": [{"authenticate_cognito": [], "authenticate_oidc": [], "fixed_response": [], "forward": [], "order": 1, "redirect": [], "target_group_arn": "arn:aws:elasticloadbalancing:ap-south-1:5276********:targetgroup/carrytoo-target-group/7d3d5f928031975b", "type": "forward"}], "id": "arn:aws:elasticloadbalancing:ap-south-1:5276********:listener/app/carrytoo-alb/9001ec3610d35456/a65cecc4cba4bb8c", "load_balancer_arn": "arn:aws:elasticloadbalancing:ap-south-1:5276********:loadbalancer/app/carrytoo-alb/9001ec3610d35456", "mutual_authentication": [{"advertise_trust_store_ca_names": "", "ignore_client_certificate_expiry": false, "mode": "off", "trust_store_arn": ""}], "port": 443, "protocol": "HTTPS", "routing_http_request_x_amzn_mtls_clientcert_header_name": "", "routing_http_request_x_amzn_mtls_clientcert_issuer_header_name": "", "routing_http_request_x_amzn_mtls_clientcert_leaf_header_name": "", "routing_http_request_x_amzn_mtls_clientcert_serial_number_header_name": "", "routing_http_request_x_amzn_mtls_clientcert_subject_header_name": "", "routing_http_request_x_amzn_mtls_clientcert_validity_header_name": "", "routing_http_request_x_amzn_tls_cipher_suite_header_name": "", "routing_http_request_x_amzn_tls_version_header_name": "", "routing_http_response_access_control_allow_credentials_header_value": "", "routing_http_response_access_control_allow_headers_header_value": "", "routing_http_response_access_control_allow_methods_header_value": "", "routing_http_response_access_control_allow_origin_header_value": "", "routing_http_response_access_control_expose_headers_header_value": "", "routing_http_response_access_control_max_age_header_value": "", "routing_http_response_content_security_policy_header_value": "", "routing_http_response_server_enabled": false, "routing_http_response_strict_transport_security_header_value": "", "routing_http_response_x_content_type_options_header_value": "", "routing_http_response_x_frame_options_header_value": "", "ssl_policy": "ELBSecurityPolicy-2016-08", "tags": {}, "tags_all": {}, "tcp_idle_timeout_seconds": null, "timeouts": null}, "sensitive_attributes": [], "private": "********************************************************************************************************************", "dependencies": ["aws_acm_certificate.api_cert", "aws_lb.carrytoo_alb", "aws_lb_target_group.carrytoo_tg", "aws_security_group.fargate_sg", "aws_subnet.public", "aws_vpc.carrytoo_vpc"]}]}, {"mode": "managed", "type": "aws_lb_target_group", "name": "carrytoo_tg", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:elasticloadbalancing:ap-south-1:5276********:targetgroup/carrytoo-target-group/7d3d5f928031975b", "arn_suffix": "targetgroup/carrytoo-target-group/7d3d5f928031975b", "connection_termination": null, "deregistration_delay": "300", "health_check": [{"enabled": true, "healthy_threshold": 2, "interval": 30, "matcher": "200-399", "path": "/", "port": "traffic-port", "protocol": "HTTP", "timeout": 5, "unhealthy_threshold": 3}], "id": "arn:aws:elasticloadbalancing:ap-south-1:5276********:targetgroup/carrytoo-target-group/7d3d5f928031975b", "ip_address_type": "ipv4", "lambda_multi_value_headers_enabled": false, "load_balancer_arns": ["arn:aws:elasticloadbalancing:ap-south-1:5276********:loadbalancer/app/carrytoo-alb/9001ec3610d35456"], "load_balancing_algorithm_type": "round_robin", "load_balancing_anomaly_mitigation": "off", "load_balancing_cross_zone_enabled": "use_load_balancer_configuration", "name": "carrytoo-target-group", "name_prefix": "", "port": 3000, "preserve_client_ip": null, "protocol": "HTTP", "protocol_version": "HTTP1", "proxy_protocol_v2": false, "slow_start": 0, "stickiness": [{"cookie_duration": 86400, "cookie_name": "", "enabled": false, "type": "lb_cookie"}], "tags": {"Name": "carrytoo-target-group"}, "tags_all": {"Name": "carrytoo-target-group"}, "target_failover": [{"on_deregistration": null, "on_unhealthy": null}], "target_group_health": [{"dns_failover": [{"minimum_healthy_targets_count": "1", "minimum_healthy_targets_percentage": "off"}], "unhealthy_state_routing": [{"minimum_healthy_targets_count": 1, "minimum_healthy_targets_percentage": "off"}]}], "target_health_state": [{"enable_unhealthy_connection_termination": null, "unhealthy_draining_interval": null}], "target_type": "ip", "vpc_id": "vpc-0aceced8713a8bccb"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_vpc.carrytoo_vpc"]}]}, {"mode": "managed", "type": "aws_route_table", "name": "private", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:ec2:ap-south-1:5276********:route-table/rtb-05b3672e075bf44f8", "id": "rtb-05b3672e075bf44f8", "owner_id": "5276********", "propagating_vgws": [], "route": [{"carrier_gateway_id": "", "cidr_block": "0.0.0.0/0", "core_network_arn": "", "destination_prefix_list_id": "", "egress_only_gateway_id": "", "gateway_id": "", "ipv6_cidr_block": "", "local_gateway_id": "", "nat_gateway_id": "nat-0f4cae75a0d589662", "network_interface_id": "", "transit_gateway_id": "", "vpc_endpoint_id": "", "vpc_peering_connection_id": ""}], "tags": {"Name": "carrytoo-private-rt"}, "tags_all": {"Name": "carrytoo-private-rt"}, "timeouts": null, "vpc_id": "vpc-0aceced8713a8bccb"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjozMDAwMDAwMDAwMDAsImRlbGV0ZSI6MzAwMDAwMDAwMDAwLCJ1cGRhdGUiOjEyMDAwMDAwMDAwMH19", "dependencies": ["aws_vpc.carrytoo_vpc"]}]}, {"mode": "managed", "type": "aws_route_table", "name": "public", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:ec2:ap-south-1:5276********:route-table/rtb-0449a8b2afff82c67", "id": "rtb-0449a8b2afff82c67", "owner_id": "5276********", "propagating_vgws": [], "route": [{"carrier_gateway_id": "", "cidr_block": "0.0.0.0/0", "core_network_arn": "", "destination_prefix_list_id": "", "egress_only_gateway_id": "", "gateway_id": "igw-01480bdef7ecd74fa", "ipv6_cidr_block": "", "local_gateway_id": "", "nat_gateway_id": "", "network_interface_id": "", "transit_gateway_id": "", "vpc_endpoint_id": "", "vpc_peering_connection_id": ""}], "tags": {"Name": "carrytoo-public-rt"}, "tags_all": {"Name": "carrytoo-public-rt"}, "timeouts": null, "vpc_id": "vpc-0aceced8713a8bccb"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjozMDAwMDAwMDAwMDAsImRlbGV0ZSI6MzAwMDAwMDAwMDAwLCJ1cGRhdGUiOjEyMDAwMDAwMDAwMH19", "dependencies": ["aws_internet_gateway.carrytoo_igw", "aws_vpc.carrytoo_vpc"]}]}, {"mode": "managed", "type": "aws_route_table_association", "name": "private", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": 0, "schema_version": 0, "attributes": {"gateway_id": "", "id": "rtbassoc-0adc73d24691093db", "route_table_id": "rtb-05b3672e075bf44f8", "subnet_id": "subnet-067595da1ef92634e", "timeouts": null}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjozMDAwMDAwMDAwMDAsImRlbGV0ZSI6MzAwMDAwMDAwMDAwLCJ1cGRhdGUiOjEyMDAwMDAwMDAwMH19", "dependencies": ["aws_route_table.private", "aws_subnet.private", "aws_vpc.carrytoo_vpc"]}, {"index_key": 1, "schema_version": 0, "attributes": {"gateway_id": "", "id": "rtbassoc-03aaa70970c379490", "route_table_id": "rtb-05b3672e075bf44f8", "subnet_id": "subnet-0a6719dca04318e2f", "timeouts": null}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjozMDAwMDAwMDAwMDAsImRlbGV0ZSI6MzAwMDAwMDAwMDAwLCJ1cGRhdGUiOjEyMDAwMDAwMDAwMH19", "dependencies": ["aws_route_table.private", "aws_subnet.private", "aws_vpc.carrytoo_vpc"]}]}, {"mode": "managed", "type": "aws_route_table_association", "name": "public", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": 0, "schema_version": 0, "attributes": {"gateway_id": "", "id": "rtbassoc-071fcd6dd6677c0a5", "route_table_id": "rtb-0449a8b2afff82c67", "subnet_id": "subnet-01d2c91a3e6747b8d", "timeouts": null}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjozMDAwMDAwMDAwMDAsImRlbGV0ZSI6MzAwMDAwMDAwMDAwLCJ1cGRhdGUiOjEyMDAwMDAwMDAwMH19", "dependencies": ["aws_internet_gateway.carrytoo_igw", "aws_route_table.public", "aws_subnet.public", "aws_vpc.carrytoo_vpc"]}, {"index_key": 1, "schema_version": 0, "attributes": {"gateway_id": "", "id": "rtbassoc-08be73fc152adf04f", "route_table_id": "rtb-0449a8b2afff82c67", "subnet_id": "subnet-0b665e4117d1edd66", "timeouts": null}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjozMDAwMDAwMDAwMDAsImRlbGV0ZSI6MzAwMDAwMDAwMDAwLCJ1cGRhdGUiOjEyMDAwMDAwMDAwMH19", "dependencies": ["aws_internet_gateway.carrytoo_igw", "aws_route_table.public", "aws_subnet.public", "aws_vpc.carrytoo_vpc"]}]}, {"mode": "managed", "type": "aws_s3_bucket", "name": "documents_bucket", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"acceleration_status": "", "acl": null, "arn": "arn:aws:s3:::carrytoo-documents", "bucket": "carrytoo-documents", "bucket_domain_name": "carrytoo-documents.s3.amazonaws.com", "bucket_prefix": "", "bucket_regional_domain_name": "carrytoo-documents.s3.ap-south-1.amazonaws.com", "cors_rule": [], "force_destroy": false, "grant": [{"id": "894e5eac523dfb83bd76be4b77c96780bb420dd4b6e0abd01021de19824efa84", "permissions": ["FULL_CONTROL"], "type": "CanonicalUser", "uri": ""}], "hosted_zone_id": "Z11RGJOFQNVJUP", "id": "carrytoo-documents", "lifecycle_rule": [], "logging": [], "object_lock_configuration": [], "object_lock_enabled": false, "policy": "", "region": "ap-south-1", "replication_configuration": [], "request_payer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "server_side_encryption_configuration": [{"rule": [{"apply_server_side_encryption_by_default": [{"kms_master_key_id": "", "sse_algorithm": "AES256"}], "bucket_key_enabled": false}]}], "tags": {"Environment": "Production", "Name": "CarryToo Documents Bucket"}, "tags_all": {"Environment": "Production", "Name": "CarryToo Documents Bucket"}, "timeouts": null, "versioning": [{"enabled": false, "mfa_delete": false}], "website": [], "website_domain": null, "website_endpoint": null}, "sensitive_attributes": [], "private": "********************************************************************************************************************************************************************************"}]}, {"mode": "managed", "type": "aws_s3_bucket", "name": "website_bucket", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"acceleration_status": "", "acl": null, "arn": "arn:aws:s3:::carrytoo-ui", "bucket": "carrytoo-ui", "bucket_domain_name": "carrytoo-ui.s3.amazonaws.com", "bucket_prefix": "", "bucket_regional_domain_name": "carrytoo-ui.s3.ap-south-1.amazonaws.com", "cors_rule": [], "force_destroy": false, "grant": [{"id": "894e5eac523dfb83bd76be4b77c96780bb420dd4b6e0abd01021de19824efa84", "permissions": ["FULL_CONTROL"], "type": "CanonicalUser", "uri": ""}], "hosted_zone_id": "Z11RGJOFQNVJUP", "id": "carrytoo-ui", "lifecycle_rule": [], "logging": [], "object_lock_configuration": [], "object_lock_enabled": false, "policy": "{\"Statement\":[{\"Action\":\"s3:GetObject\",\"Effect\":\"Allow\",\"Principal\":{\"AWS\":\"arn:aws:iam::cloudfront:user/CloudFront Origin Access Identity EVMJXA1YI7AZZ\"},\"Resource\":\"arn:aws:s3:::carrytoo-ui/*\"}],\"Version\":\"2012-10-17\"}", "region": "ap-south-1", "replication_configuration": [], "request_payer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "server_side_encryption_configuration": [{"rule": [{"apply_server_side_encryption_by_default": [{"kms_master_key_id": "", "sse_algorithm": "AES256"}], "bucket_key_enabled": false}]}], "tags": {"Environment": "Production", "Name": "Static Website Bucket"}, "tags_all": {"Environment": "Production", "Name": "Static Website Bucket"}, "timeouts": null, "versioning": [{"enabled": false, "mfa_delete": false}], "website": [{"error_document": "error.html", "index_document": "index.html", "redirect_all_requests_to": "", "routing_rules": ""}], "website_domain": "s3-website.ap-south-1.amazonaws.com", "website_endpoint": "carrytoo-ui.s3-website.ap-south-1.amazonaws.com"}, "sensitive_attributes": [], "private": "********************************************************************************************************************************************************************************"}]}, {"mode": "managed", "type": "aws_s3_bucket_acl", "name": "website_bucket_acl", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"access_control_policy": [{"grant": [{"grantee": [{"display_name": "", "email_address": "", "id": "894e5eac523dfb83bd76be4b77c96780bb420dd4b6e0abd01021de19824efa84", "type": "CanonicalUser", "uri": ""}], "permission": "FULL_CONTROL"}], "owner": [{"display_name": "", "id": "894e5eac523dfb83bd76be4b77c96780bb420dd4b6e0abd01021de19824efa84"}]}], "acl": "private", "bucket": "carrytoo-ui", "expected_bucket_owner": "", "id": "carrytoo-ui,private"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_s3_bucket.website_bucket", "aws_s3_bucket_ownership_controls.website_bucket_ownership", "aws_s3_bucket_public_access_block.website_bucket_public_access"]}]}, {"mode": "managed", "type": "aws_s3_bucket_ownership_controls", "name": "documents_bucket_ownership", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"bucket": "carrytoo-documents", "id": "carrytoo-documents", "rule": [{"object_ownership": "BucketOwnerPreferred"}]}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_s3_bucket.documents_bucket"]}]}, {"mode": "managed", "type": "aws_s3_bucket_ownership_controls", "name": "website_bucket_ownership", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"bucket": "carrytoo-ui", "id": "carrytoo-ui", "rule": [{"object_ownership": "BucketOwnerPreferred"}]}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_s3_bucket.website_bucket"]}]}, {"mode": "managed", "type": "aws_s3_bucket_policy", "name": "website_bucket_policy", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"bucket": "carrytoo-ui", "id": "carrytoo-ui", "policy": "{\"Statement\":[{\"Action\":\"s3:GetObject\",\"Effect\":\"Allow\",\"Principal\":{\"AWS\":\"arn:aws:iam::cloudfront:user/CloudFront Origin Access Identity EVMJXA1YI7AZZ\"},\"Resource\":\"arn:aws:s3:::carrytoo-ui/*\"}],\"Version\":\"2012-10-17\"}"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_cloudfront_origin_access_identity.oai", "aws_s3_bucket.website_bucket", "aws_s3_bucket_ownership_controls.website_bucket_ownership", "aws_s3_bucket_public_access_block.website_bucket_public_access"]}]}, {"mode": "managed", "type": "aws_s3_bucket_public_access_block", "name": "documents_bucket_public_access", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"block_public_acls": true, "block_public_policy": true, "bucket": "carrytoo-documents", "id": "carrytoo-documents", "ignore_public_acls": true, "restrict_public_buckets": true}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_s3_bucket.documents_bucket"]}]}, {"mode": "managed", "type": "aws_s3_bucket_public_access_block", "name": "website_bucket_public_access", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"block_public_acls": true, "block_public_policy": true, "bucket": "carrytoo-ui", "id": "carrytoo-ui", "ignore_public_acls": true, "restrict_public_buckets": true}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_s3_bucket.website_bucket", "aws_s3_bucket_ownership_controls.website_bucket_ownership"]}]}, {"mode": "managed", "type": "aws_s3_bucket_website_configuration", "name": "website_config", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"bucket": "carrytoo-ui", "error_document": [{"key": "error.html"}], "expected_bucket_owner": "", "id": "carrytoo-ui", "index_document": [{"suffix": "index.html"}], "redirect_all_requests_to": [], "routing_rule": [], "routing_rules": "", "website_domain": "s3-website.ap-south-1.amazonaws.com", "website_endpoint": "carrytoo-ui.s3-website.ap-south-1.amazonaws.com"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_s3_bucket.website_bucket"]}]}, {"mode": "managed", "type": "aws_security_group", "name": "fargate_sg", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 1, "attributes": {"arn": "arn:aws:ec2:ap-south-1:5276********:security-group/sg-0963379d5774c89d6", "description": "Allow HTTP/HTTPS inbound and all outbound traffic", "egress": [{"cidr_blocks": ["0.0.0.0/0"], "description": "", "from_port": 0, "ipv6_cidr_blocks": [], "prefix_list_ids": [], "protocol": "-1", "security_groups": [], "self": false, "to_port": 0}], "id": "sg-0963379d5774c89d6", "ingress": [{"cidr_blocks": ["0.0.0.0/0"], "description": "", "from_port": 3000, "ipv6_cidr_blocks": [], "prefix_list_ids": [], "protocol": "tcp", "security_groups": [], "self": false, "to_port": 3000}, {"cidr_blocks": ["0.0.0.0/0"], "description": "", "from_port": 443, "ipv6_cidr_blocks": [], "prefix_list_ids": [], "protocol": "tcp", "security_groups": [], "self": false, "to_port": 443}, {"cidr_blocks": ["0.0.0.0/0"], "description": "", "from_port": 80, "ipv6_cidr_blocks": [], "prefix_list_ids": [], "protocol": "tcp", "security_groups": [], "self": false, "to_port": 80}], "name": "carrytoo-fargate-sg", "name_prefix": "", "owner_id": "5276********", "revoke_rules_on_delete": false, "tags": {"Name": "carrytoo-fargate-sg"}, "tags_all": {"Name": "carrytoo-fargate-sg"}, "timeouts": null, "vpc_id": "vpc-0aceced8713a8bccb"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMDAsImRlbGV0ZSI6OTAwMDAwMDAwMDAwfSwic2NoZW1hX3ZlcnNpb24iOiIxIn0=", "dependencies": ["aws_vpc.carrytoo_vpc"]}]}, {"mode": "managed", "type": "aws_security_group", "name": "rds_sg", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 1, "attributes": {"arn": "arn:aws:ec2:ap-south-1:5276********:security-group/sg-00098980e445d651d", "description": "Allow PostgreSQL access from specific IPs and Fargate containers", "egress": [{"cidr_blocks": ["0.0.0.0/0"], "description": "", "from_port": 0, "ipv6_cidr_blocks": [], "prefix_list_ids": [], "protocol": "-1", "security_groups": [], "self": false, "to_port": 0}], "id": "sg-00098980e445d651d", "ingress": [{"cidr_blocks": ["0.0.0.0/0"], "description": "", "from_port": 5432, "ipv6_cidr_blocks": [], "prefix_list_ids": [], "protocol": "tcp", "security_groups": [], "self": false, "to_port": 5432}, {"cidr_blocks": [], "description": "", "from_port": 5432, "ipv6_cidr_blocks": [], "prefix_list_ids": [], "protocol": "tcp", "security_groups": ["sg-0963379d5774c89d6"], "self": false, "to_port": 5432}], "name": "carrytoo-rds-sg", "name_prefix": "", "owner_id": "5276********", "revoke_rules_on_delete": false, "tags": {"Name": "carrytoo-rds-sg"}, "tags_all": {"Name": "carrytoo-rds-sg"}, "timeouts": null, "vpc_id": "vpc-0aceced8713a8bccb"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMDAsImRlbGV0ZSI6OTAwMDAwMDAwMDAwfSwic2NoZW1hX3ZlcnNpb24iOiIxIn0=", "dependencies": ["aws_security_group.fargate_sg", "aws_vpc.carrytoo_vpc"]}]}, {"mode": "managed", "type": "aws_subnet", "name": "private", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": 0, "schema_version": 1, "attributes": {"arn": "arn:aws:ec2:ap-south-1:5276********:subnet/subnet-067595da1ef92634e", "assign_ipv6_address_on_creation": false, "availability_zone": "ap-south-1a", "availability_zone_id": "aps1-az1", "cidr_block": "*********/24", "customer_owned_ipv4_pool": "", "enable_dns64": false, "enable_lni_at_device_index": 0, "enable_resource_name_dns_a_record_on_launch": false, "enable_resource_name_dns_aaaa_record_on_launch": false, "id": "subnet-067595da1ef92634e", "ipv6_cidr_block": "", "ipv6_cidr_block_association_id": "", "ipv6_native": false, "map_customer_owned_ip_on_launch": false, "map_public_ip_on_launch": false, "outpost_arn": "", "owner_id": "5276********", "private_dns_hostname_type_on_launch": "ip-name", "tags": {"Name": "carrytoo-private-subnet-0"}, "tags_all": {"Name": "carrytoo-private-subnet-0"}, "timeouts": null, "vpc_id": "vpc-0aceced8713a8bccb"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMDAsImRlbGV0ZSI6MTIwMDAwMDAwMDAwMH0sInNjaGVtYV92ZXJzaW9uIjoiMSJ9", "dependencies": ["aws_vpc.carrytoo_vpc"]}, {"index_key": 1, "schema_version": 1, "attributes": {"arn": "arn:aws:ec2:ap-south-1:5276********:subnet/subnet-0a6719dca04318e2f", "assign_ipv6_address_on_creation": false, "availability_zone": "ap-south-1b", "availability_zone_id": "aps1-az3", "cidr_block": "*********/24", "customer_owned_ipv4_pool": "", "enable_dns64": false, "enable_lni_at_device_index": 0, "enable_resource_name_dns_a_record_on_launch": false, "enable_resource_name_dns_aaaa_record_on_launch": false, "id": "subnet-0a6719dca04318e2f", "ipv6_cidr_block": "", "ipv6_cidr_block_association_id": "", "ipv6_native": false, "map_customer_owned_ip_on_launch": false, "map_public_ip_on_launch": false, "outpost_arn": "", "owner_id": "5276********", "private_dns_hostname_type_on_launch": "ip-name", "tags": {"Name": "carrytoo-private-subnet-1"}, "tags_all": {"Name": "carrytoo-private-subnet-1"}, "timeouts": null, "vpc_id": "vpc-0aceced8713a8bccb"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMDAsImRlbGV0ZSI6MTIwMDAwMDAwMDAwMH0sInNjaGVtYV92ZXJzaW9uIjoiMSJ9", "dependencies": ["aws_vpc.carrytoo_vpc"]}]}, {"mode": "managed", "type": "aws_subnet", "name": "public", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": 0, "schema_version": 1, "attributes": {"arn": "arn:aws:ec2:ap-south-1:5276********:subnet/subnet-01d2c91a3e6747b8d", "assign_ipv6_address_on_creation": false, "availability_zone": "ap-south-1a", "availability_zone_id": "aps1-az1", "cidr_block": "10.0.0.0/24", "customer_owned_ipv4_pool": "", "enable_dns64": false, "enable_lni_at_device_index": 0, "enable_resource_name_dns_a_record_on_launch": false, "enable_resource_name_dns_aaaa_record_on_launch": false, "id": "subnet-01d2c91a3e6747b8d", "ipv6_cidr_block": "", "ipv6_cidr_block_association_id": "", "ipv6_native": false, "map_customer_owned_ip_on_launch": false, "map_public_ip_on_launch": true, "outpost_arn": "", "owner_id": "5276********", "private_dns_hostname_type_on_launch": "ip-name", "tags": {"Name": "carrytoo-public-subnet-0"}, "tags_all": {"Name": "carrytoo-public-subnet-0"}, "timeouts": null, "vpc_id": "vpc-0aceced8713a8bccb"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMDAsImRlbGV0ZSI6MTIwMDAwMDAwMDAwMH0sInNjaGVtYV92ZXJzaW9uIjoiMSJ9", "dependencies": ["aws_vpc.carrytoo_vpc"]}, {"index_key": 1, "schema_version": 1, "attributes": {"arn": "arn:aws:ec2:ap-south-1:5276********:subnet/subnet-0b665e4117d1edd66", "assign_ipv6_address_on_creation": false, "availability_zone": "ap-south-1b", "availability_zone_id": "aps1-az3", "cidr_block": "********/24", "customer_owned_ipv4_pool": "", "enable_dns64": false, "enable_lni_at_device_index": 0, "enable_resource_name_dns_a_record_on_launch": false, "enable_resource_name_dns_aaaa_record_on_launch": false, "id": "subnet-0b665e4117d1edd66", "ipv6_cidr_block": "", "ipv6_cidr_block_association_id": "", "ipv6_native": false, "map_customer_owned_ip_on_launch": false, "map_public_ip_on_launch": true, "outpost_arn": "", "owner_id": "5276********", "private_dns_hostname_type_on_launch": "ip-name", "tags": {"Name": "carrytoo-public-subnet-1"}, "tags_all": {"Name": "carrytoo-public-subnet-1"}, "timeouts": null, "vpc_id": "vpc-0aceced8713a8bccb"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMDAsImRlbGV0ZSI6MTIwMDAwMDAwMDAwMH0sInNjaGVtYV92ZXJzaW9uIjoiMSJ9", "dependencies": ["aws_vpc.carrytoo_vpc"]}]}, {"mode": "managed", "type": "aws_vpc", "name": "carrytoo_vpc", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 1, "attributes": {"arn": "arn:aws:ec2:ap-south-1:5276********:vpc/vpc-0aceced8713a8bccb", "assign_generated_ipv6_cidr_block": false, "cidr_block": "10.0.0.0/16", "default_network_acl_id": "acl-0ceebc4b23cf27ae4", "default_route_table_id": "rtb-09b18b9059c41a054", "default_security_group_id": "sg-0f4777fe35ca5c56a", "dhcp_options_id": "dopt-03b3248a04343ef01", "enable_dns_hostnames": true, "enable_dns_support": true, "enable_network_address_usage_metrics": false, "id": "vpc-0aceced8713a8bccb", "instance_tenancy": "default", "ipv4_ipam_pool_id": null, "ipv4_netmask_length": null, "ipv6_association_id": "", "ipv6_cidr_block": "", "ipv6_cidr_block_network_border_group": "", "ipv6_ipam_pool_id": "", "ipv6_netmask_length": 0, "main_route_table_id": "rtb-09b18b9059c41a054", "owner_id": "5276********", "tags": {"Name": "carrytoo-vpc"}, "tags_all": {"Name": "carrytoo-vpc"}}, "sensitive_attributes": [], "private": "eyJzY2hlbWFfdmVyc2lvbiI6IjEifQ=="}]}], "check_results": null}