#!/bin/bash
set -e

# Display usage information
function show_usage {
  echo "Usage: $0 [OPTIONS]"
  echo "Deploy the Carytoo Backend application to AWS ECS"
  echo ""
  echo "Options:"
  echo "  -p, --params FILE    Path to parameters file (default: carytoo-backend-params.sh)"
  echo "  -b, --branch NAME    Git branch to checkout (overrides the one in params file)"
  echo "  -h, --help           Display this help message and exit"
  echo ""
}

# Parse command line arguments
PARAMS_FILE="carytoo-backend-params.sh"
BRANCH_OVERRIDE=""

while [[ $# -gt 0 ]]; do
  case "$1" in
    -p|--params)
      PARAMS_FILE="$2"
      shift 2
      ;;
    -b|--branch)
      BRANCH_OVERRIDE="$2"
      shift 2
      ;;
    -h|--help)
      show_usage
      exit 0
      ;;
    *)
      echo "Unknown option: $1"
      show_usage
      exit 1
      ;;
  esac
done

# Check if parameters file exists
if [ ! -f "$PARAMS_FILE" ]; then
  echo "Error: Parameters file '$PARAMS_FILE' not found."
  exit 1
fi

# Load parameters
echo "Loading parameters from $PARAMS_FILE..."
source "$PARAMS_FILE"

# Override branch if specified
if [ -n "$BRANCH_OVERRIDE" ]; then
  echo "Overriding Git branch with: $BRANCH_OVERRIDE"
  GIT_BRANCH="$BRANCH_OVERRIDE"
fi

# Get AWS account ID
echo "Getting AWS account ID..."
AWS_ACCOUNT_ID=$(aws sts get-caller-identity --query Account --output text)
if [ -z "$AWS_ACCOUNT_ID" ]; then
  echo "Error: Failed to get AWS account ID. Make sure you're authenticated with AWS CLI."
  exit 1
fi

# Get RDS endpoint from Terraform outputs
echo "Getting RDS endpoint from Terraform outputs..."
RDS_ENDPOINT=$(terraform output -raw rds_endpoint)
if [ -z "$RDS_ENDPOINT" ]; then
  echo "Error: Failed to get RDS endpoint from Terraform outputs."
  exit 1
fi

DB_HOST=$(echo $RDS_ENDPOINT | cut -d':' -f1)
DB_PORT=$(echo $RDS_ENDPOINT | cut -d':' -f2)

# Check if DB_USER environment variable is set, otherwise use the one from params file
if [ -n "$DB_USER_ENV" ]; then
  echo "Overriding DB_USER from environment variable"
  DB_USER="$DB_USER_ENV"
else
  echo "Using DB_USER from parameters file: $DB_USER"
fi

# Prompt for DB password (don't hardcode this in scripts)
echo -n "Enter database password: "
read -s DB_PASSWORD
echo

# Prompt for AWS credentials if not set in parameters
if [ -z "$AWS_ACCESS_KEY_ID" ]; then
  echo -n "Enter AWS Access Key ID: "
  read AWS_ACCESS_KEY_ID
fi

if [ -z "$AWS_SECRET_ACCESS_KEY" ]; then
  echo -n "Enter AWS Secret Access Key: "
  read -s AWS_SECRET_ACCESS_KEY
  echo
fi

# Clone or update the Git repository
echo "Setting up source code from Git repository..."
if [ -d "$GIT_CLONE_DIR" ]; then
  echo "Source directory exists, updating..."
  cd "$GIT_CLONE_DIR"
  git fetch --all
  git checkout "$GIT_BRANCH"
  git pull origin "$GIT_BRANCH"
  cd ..
else
  echo "Cloning repository from $GIT_REPO_URL (branch: $GIT_BRANCH)..."
  git clone -b "$GIT_BRANCH" "$GIT_REPO_URL" "$GIT_CLONE_DIR"
fi

# Change to the source directory
cd "$GIT_CLONE_DIR"

# Create ECR repository if it doesn't exist
echo "Checking if ECR repository exists..."
if ! aws ecr describe-repositories --repository-names ${ECR_REPOSITORY_NAME} --region ${AWS_REGION} > /dev/null 2>&1; then
  echo "Creating ECR repository: ${ECR_REPOSITORY_NAME}..."
  aws ecr create-repository --repository-name ${ECR_REPOSITORY_NAME} --region ${AWS_REGION}
fi

# Get ECR login token and login to Docker
echo "Logging in to Amazon ECR..."
aws ecr get-login-password --region ${AWS_REGION} | docker login --username AWS --password-stdin ${AWS_ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com

# Build Docker image (assuming Dockerfile is in the current directory)
echo "Building Docker image..."
docker build -t ${ECR_REPOSITORY_NAME}:${IMAGE_TAG} \
  --build-arg DB_HOST=${DB_HOST} \
  --build-arg DB_PORT=${DB_PORT} \
  --build-arg DB_NAME=${DB_NAME} \
  --build-arg DB_USER=${DB_USER} \
  --build-arg DB_PASSWORD=${DB_PASSWORD} \
  .

# Tag and push the image to ECR
echo "Tagging and pushing image to ECR..."
docker tag ${ECR_REPOSITORY_NAME}:${IMAGE_TAG} ${AWS_ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com/${ECR_REPOSITORY_NAME}:${IMAGE_TAG}
docker push ${AWS_ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com/${ECR_REPOSITORY_NAME}:${IMAGE_TAG}

# Get the current task definition
echo "Retrieving current task definition..."
TASK_DEFINITION_NAME="carrytoo-api"
TASK_DEFINITION=$(aws ecs describe-task-definition --task-definition ${TASK_DEFINITION_NAME} --region ${AWS_REGION})
TASK_DEFINITION_ARN=$(echo $TASK_DEFINITION | jq -r '.taskDefinition.taskDefinitionArn')

# Create a new task definition revision with the new image
echo "Creating new task definition revision..."
NEW_TASK_DEFINITION=$(echo $TASK_DEFINITION | jq --arg IMAGE "${AWS_ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com/${ECR_REPOSITORY_NAME}:${IMAGE_TAG}" \
  --arg DB_HOST "$DB_HOST" \
  --arg DB_PORT "$DB_PORT" \
  --arg DB_NAME "$DB_NAME" \
  --arg DB_USER "$DB_USER" \
  --arg DB_PASSWORD "$DB_PASSWORD" \
  --arg PORT "$CONTAINER_PORT" \
  --arg BUCKET "$AWS_BUCKET_NAME" \
  --arg REGION "$AWS_ACCESS_REGION" \
  --arg KEY_ID "$AWS_ACCESS_KEY_ID" \
  --arg SECRET "$AWS_SECRET_ACCESS_KEY" \
  --arg JWT "$JWT_SECRET" \
  --arg EMAIL "$EMAIL" \
  --arg MAIL_PWD "$MAIL_PASSWORD" \
  --arg SMTP "$SMTP_HOST" \
  --arg FRONTEND "$FRONTEND_URL" \
  '.taskDefinition | .containerDefinitions[0].image = $IMAGE |
  .containerDefinitions[0].environment = [
    {name: "DB_HOST", value: $DB_HOST},
    {name: "DB_PORT", value: $DB_PORT},
    {name: "DB_NAME", value: $DB_NAME},
    {name: "DB_USERNAME", value: $DB_USER},
    {name: "DB_PASSWORD", value: $DB_PASSWORD},
    {name: "PORT", value: $PORT},
    {name: "AWS_BUCKET_NAME", value: $BUCKET},
    {name: "AWS_ACCESS_REGION", value: $REGION},
    {name: "AWS_ACCESS_KEY_ID", value: $KEY_ID},
    {name: "AWS_SECRET_ACCESS_KEY", value: $SECRET},
    {name: "JWT_SECRET", value: $JWT},
    {name: "EMAIL", value: $EMAIL},
    {name: "MAIL_PASSWORD", value: $MAIL_PWD},
    {name: "SMTP_HOST", value: $SMTP},
    {name: "FRONTEND_URL", value: $FRONTEND}
  ] | del(.taskDefinitionArn) | del(.revision) | del(.status) | del(.requiresAttributes) | del(.compatibilities) | del(.registeredAt) | del(.registeredBy)')

# Register the new task definition
echo "Registering new task definition..."
NEW_TASK_DEFINITION_ARN=$(aws ecs register-task-definition --region ${AWS_REGION} --cli-input-json "$(echo $NEW_TASK_DEFINITION)" | jq -r '.taskDefinition.taskDefinitionArn')

# Update the service to use the new task definition
echo "Updating ECS service to use new task definition..."
aws ecs update-service --cluster ${ECS_CLUSTER_NAME} --service ${ECS_SERVICE_NAME} --task-definition ${NEW_TASK_DEFINITION_ARN} --region ${AWS_REGION}

echo "Waiting for service to stabilize..."
aws ecs wait services-stable --cluster ${ECS_CLUSTER_NAME} --services ${ECS_SERVICE_NAME} --region ${AWS_REGION}

# Get the ALB DNS name
ALB_URL=$(terraform output -raw fargate_service_url)

echo "Deployment completed successfully!"
echo "Your API is now available at: ${ALB_URL}"
echo "You can test it with: curl ${ALB_URL}/"

# Return to original directory
cd ..

echo "Deployment script completed at $(date)"
