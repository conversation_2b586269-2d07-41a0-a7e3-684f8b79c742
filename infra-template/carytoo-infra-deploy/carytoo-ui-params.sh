#!/bin/bash
# Carytoo UI Deployment Parameters

# AWS Configuration
AWS_REGION="ap-south-1"
S3_BUCKET_NAME="carrytoo-ui"
CLOUDFRONT_DISTRIBUTION_ID="E29D4AIJ4WPH1L" # Replace with your actual CloudFront distribution ID

# Git Configuration
GIT_REPO_URL="https://github.com/mathi0695/carytoo-ui.git"  # Update with your actual repository URL
GIT_BRANCH="development-vite"  # Update with your desired branch
GIT_CLONE_DIR="carytoo-ui-src"

# Build Configuration
NODE_VERSION="18"  # Specify the Node.js version to use
BUILD_COMMAND="npm run build"  # The command to build the React app
BUILD_OUTPUT_DIR="dist"  # The directory where build output is generated (usually 'dist' or 'build')

# Environment Variables for React Build
REACT_APP_API_URL="https://api.carytoo.com"
REACT_APP_ENV="production"
# Add any other environment variables needed for your React build
