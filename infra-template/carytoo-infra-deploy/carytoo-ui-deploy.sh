#!/bin/bash
set -e

# Display usage information
function show_usage {
  echo "Usage: $0 [OPTIONS]"
  echo "Deploy the Carytoo UI application to AWS S3 and CloudFront"
  echo ""
  echo "Options:"
  echo "  -p, --params FILE    Path to parameters file (default: carytoo-ui-params.sh)"
  echo "  -b, --branch NAME    Git branch to checkout (overrides the one in params file)"
  echo "  -h, --help           Display this help message and exit"
  echo ""
}

# Parse command line arguments
PARAMS_FILE="carytoo-ui-params.sh"
BRANCH_OVERRIDE=""

while [[ $# -gt 0 ]]; do
  case "$1" in
    -p|--params)
      PARAMS_FILE="$2"
      shift 2
      ;;
    -b|--branch)
      BRANCH_OVERRIDE="$2"
      shift 2
      ;;
    -h|--help)
      show_usage
      exit 0
      ;;
    *)
      echo "Unknown option: $1"
      show_usage
      exit 1
      ;;
  esac
done

# Check if parameters file exists
if [ ! -f "$PARAMS_FILE" ]; then
  echo "Error: Parameters file '$PARAMS_FILE' not found."
  exit 1
fi

# Load parameters
echo "Loading parameters from $PARAMS_FILE..."
source "$PARAMS_FILE"

# Override branch if specified
if [ -n "$BRANCH_OVERRIDE" ]; then
  echo "Overriding Git branch with: $BRANCH_OVERRIDE"
  GIT_BRANCH="$BRANCH_OVERRIDE"
fi

# Verify AWS CLI is installed and configured
echo "Verifying AWS CLI configuration..."
if ! aws --version > /dev/null 2>&1; then
  echo "Error: AWS CLI is not installed or not in PATH."
  exit 1
fi

# Verify S3 bucket exists
echo "Verifying S3 bucket exists..."
if ! aws s3api head-bucket --bucket ${S3_BUCKET_NAME} --region ${AWS_REGION} 2>/dev/null; then
  echo "Error: S3 bucket '${S3_BUCKET_NAME}' does not exist or you don't have access to it."
  exit 1
fi

# Verify CloudFront distribution exists
echo "Verifying CloudFront distribution exists..."
if ! aws cloudfront get-distribution --id ${CLOUDFRONT_DISTRIBUTION_ID} > /dev/null 2>&1; then
  echo "Error: CloudFront distribution '${CLOUDFRONT_DISTRIBUTION_ID}' does not exist or you don't have access to it."
  exit 1
fi

# Clone or update the Git repository
echo "Setting up source code from Git repository..."
if [ -d "$GIT_CLONE_DIR" ]; then
  echo "Source directory exists, updating..."
  cd "$GIT_CLONE_DIR"
  git fetch --all
  git checkout "$GIT_BRANCH"
  git pull origin "$GIT_BRANCH"
else
  echo "Cloning repository from $GIT_REPO_URL (branch: $GIT_BRANCH)..."
  git clone -b "$GIT_BRANCH" "$GIT_REPO_URL" "$GIT_CLONE_DIR"
  cd "$GIT_CLONE_DIR"
fi

# Check if nvm is installed and use it to set Node.js version
if [ -s "$HOME/.nvm/nvm.sh" ]; then
  echo "Using nvm to set Node.js version..."
  export NVM_DIR="$HOME/.nvm"
  [ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"  # This loads nvm
  nvm use ${NODE_VERSION} || nvm install ${NODE_VERSION}
else
  echo "nvm not found. Using system Node.js version."
  # Check if system Node.js version is compatible
  NODE_CURRENT_VERSION=$(node -v | cut -d 'v' -f 2 | cut -d '.' -f 1)
  if [ "$NODE_CURRENT_VERSION" != "$NODE_VERSION" ]; then
    echo "Warning: Current Node.js version (v$NODE_CURRENT_VERSION) differs from specified version (v$NODE_VERSION)."
    echo "This might cause compatibility issues. Consider installing nvm."
  fi
fi

# Install dependencies
echo "Installing dependencies..."
npm ci || npm install

# Set environment variables for the build
echo "Setting environment variables for the build..."
export VITE_API_URL="$REACT_APP_API_URL"
export REACT_APP_ENV="$REACT_APP_ENV"
# Add any other environment variables needed for your React build

# Build the application
echo "Building the application..."
eval "$BUILD_COMMAND"

# Check if build was successful
if [ ! -d "$BUILD_OUTPUT_DIR" ]; then
  echo "Error: Build failed. Output directory '$BUILD_OUTPUT_DIR' not found."
  exit 1
fi

# Sync build output to S3 bucket
echo "Syncing build output to S3 bucket..."
aws s3 sync "$BUILD_OUTPUT_DIR" "s3://${S3_BUCKET_NAME}" \
  --delete \
  --region ${AWS_REGION} \
  --cache-control "max-age=31536000,public,immutable" \
  --exclude "index.html" \
  --exclude "*.json"

# Upload index.html and JSON files with no-cache headers
echo "Uploading index.html and JSON files with no-cache headers..."
aws s3 sync "$BUILD_OUTPUT_DIR" "s3://${S3_BUCKET_NAME}" \
  --region ${AWS_REGION} \
  --cache-control "no-cache,no-store,must-revalidate" \
  --include "index.html" \
  --include "*.json"

# Create CloudFront invalidation
echo "Creating CloudFront invalidation..."
INVALIDATION_ID=$(aws cloudfront create-invalidation \
  --distribution-id ${CLOUDFRONT_DISTRIBUTION_ID} \
  --paths "/*" \
  --query "Invalidation.Id" \
  --output text)

echo "Waiting for CloudFront invalidation to complete..."
aws cloudfront wait invalidation-completed \
  --distribution-id ${CLOUDFRONT_DISTRIBUTION_ID} \
  --id ${INVALIDATION_ID}

# Get the CloudFront domain name
CLOUDFRONT_DOMAIN=$(aws cloudfront get-distribution \
  --id ${CLOUDFRONT_DISTRIBUTION_ID} \
  --query "Distribution.DomainName" \
  --output text)

# Return to original directory
cd ..

echo "Deployment completed successfully!"
echo "Your application is now available at: https://${CLOUDFRONT_DOMAIN}"
echo "Deployment script completed at $(date)"
