#!/bin/bash
# Chess Backend Deployment Parameters

# AWS Configuration
AWS_REGION="ap-south-1"
ECR_REPOSITORY_NAME="chessbrigade-api"
IMAGE_TAG="latest"
ECS_CLUSTER_NAME="chessbrigade-cluster"
ECS_SERVICE_NAME="chessbrigade-api-service"

# Git Configuration
GIT_REPO_URL="https://github.com/mathi0695/chessbrigade-api.git"
GIT_BRANCH="may-release-jagan"
GIT_CLONE_DIR="chess-backend-src"

# Database Configuration
# DB_HOST and DB_PORT will be determined from Terraform outputs (rds_endpoint)
DB_NAME="chessbrigade-live"
DB_USER="chessbrigade_admin"  # Can be overridden with DB_USER_ENV environment variable
# DB_PASSWORD will be prompted for security reasons

# Application Configuration
CONTAINER_PORT="3000"
AWS_BUCKET_NAME="chess-brigade"
AWS_ACCESS_REGION="ap-south-1"
# AWS_ACCESS_KEY_ID and AWS_SECRET_ACCESS_KEY should be set in environment or prompted
JWT_SECRET="your-jwt-secret"
EMAIL="<EMAIL>"
MAIL_PASSWORD="your-mail-password"
SMTP_HOST="smtp.example.com"
FRONTEND_URL="https://chess-ui.mathiarasan.com,http://localhost:5173"
